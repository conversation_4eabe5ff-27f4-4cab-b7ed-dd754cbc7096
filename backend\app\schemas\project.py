"""
项目数据模式定义
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class ProjectBase(BaseModel):
    """项目基本信息模式"""
    project_name: str
    person_name: Optional[str] = None
    description: Optional[str] = None


class ProjectCreate(ProjectBase):
    """创建项目的请求模式"""
    pass


class ProjectUpdate(BaseModel):
    """更新项目的请求模式"""
    project_name: Optional[str] = None
    person_name: Optional[str] = None
    description: Optional[str] = None


class ProjectResponse(ProjectBase):
    """项目响应模式"""
    project_id: str
    db_path: str
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True 