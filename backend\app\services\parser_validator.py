"""
解析验证工具
用于测试模板与实际数据的匹配情况，生成验证报告
"""
import os
import sys
import json
import logging
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime

# 添加项目根目录到Python路径，以便导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from .parser.result_validator import ParsingResultValidator
from .parser.pre_parser_system import PreParserSystem

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ParserValidationService:
    """
    解析器验证服务 - 使用新Enhanced解析器架构进行验证
    """
    
    def __init__(self):
        """
        初始化验证服务
        """
        self.result_validator = ParsingResultValidator()
        self.pre_parser_system = PreParserSystem()
    
    def validate_parser_on_file(self, file_path: str, expected_results: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        验证Enhanced解析器在指定文件上的解析效果
        
        Args:
            file_path: 测试文件路径
            expected_results: 预期结果（可选）
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        validation_result = {
            "file_path": file_path,
            "file_exists": False,
            "parsers_tested": [],
            "successful_parsers": [],
            "failed_parsers": [],
            "best_parser": None,
            "validation_summary": {},
            "errors": []
        }
        
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                validation_result["errors"].append(f"文件不存在: {file_path}")
                return validation_result
            
            validation_result["file_exists"] = True
            
            # 使用预解析系统选择最佳解析器
            logger.info(f"开始验证文件: {os.path.basename(file_path)}")
            pre_parse_result = self.pre_parser_system.select_best_parser(file_path)
            
            if not pre_parse_result.get("best_parser"):
                validation_result["errors"].append("未找到合适的Enhanced解析器")
                return validation_result
            
            # 测试所有候选解析器
            all_parsers = [pre_parse_result["best_parser"]] + pre_parse_result.get("fallback_parsers", [])
            
            for parser_info in all_parsers:
                parser_name = parser_info["name"]
                parser_class = parser_info["class"]
                
                logger.info(f"测试解析器: {parser_name}")
                
                test_result = self.pre_parser_system.test_parser_on_file(file_path, parser_class)
                test_result["parser_name"] = parser_name
                test_result["confidence"] = parser_info["confidence"]
                
                validation_result["parsers_tested"].append(test_result)
                
                if test_result["success"]:
                    validation_result["successful_parsers"].append(test_result)
                    logger.info(f"解析器 {parser_name} 测试成功")
                else:
                    validation_result["failed_parsers"].append(test_result)
                    logger.warning(f"解析器 {parser_name} 测试失败: {test_result.get('error', '未知错误')}")
            
            # 确定最佳解析器
            if validation_result["successful_parsers"]:
                validation_result["best_parser"] = validation_result["successful_parsers"][0]
                logger.info(f"最佳解析器: {validation_result['best_parser']['parser_name']}")
            
            # 生成验证摘要
            validation_result["validation_summary"] = {
                "total_parsers_tested": len(validation_result["parsers_tested"]),
                "successful_parsers_count": len(validation_result["successful_parsers"]),
                "failed_parsers_count": len(validation_result["failed_parsers"]),
                "has_successful_parser": len(validation_result["successful_parsers"]) > 0,
                "validation_time": datetime.now().isoformat()
            }
            
            return validation_result
            
        except Exception as e:
            logger.error(f"验证过程出错: {str(e)}")
            validation_result["errors"].append(f"验证过程出错: {str(e)}")
            return validation_result
    
    def validate_parsing_result(self, parsing_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证解析结果的质量
        
        Args:
            parsing_result: 解析结果
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            return self.result_validator.validate_result(parsing_result, {})
        except Exception as e:
            logger.error(f"验证解析结果时出错: {str(e)}")
            return {
                "is_valid": False,
                "errors": [f"验证过程出错: {str(e)}"],
                "warnings": []
            }
    
    def get_supported_file_formats(self) -> List[str]:
        """
        获取支持的文件格式
        
        Returns:
            List[str]: 支持的文件扩展名列表
        """
        return [".xlsx", ".xls"]
    
    def get_enhanced_parsers_info(self) -> List[Dict[str, Any]]:
        """
        获取所有Enhanced解析器的信息
        
        Returns:
            List[Dict]: 解析器信息列表
        """
        return self.pre_parser_system.get_supported_parsers()

def main():
    """
    主函数，用于命令行调用
    """
    import argparse
    
    parser = argparse.ArgumentParser(description='银行流水解析验证工具')
    parser.add_argument('file_path', help='银行流水文件路径')
    parser.add_argument('--bank', help='银行名称')
    parser.add_argument('--template', help='模板ID')
    parser.add_argument('--output', help='输出结果到JSON文件')
    
    args = parser.parse_args()
    
    validator = ParserValidationService()
    try:
        result = validator.validate_parser_on_file(args.file_path)
        
        if "error" in result:
            print(f"验证失败: {result['error']}")
            return
        
        # 打印验证报告
        report = result["validation_report"]
        print(f"\n===== 解析验证报告 =====")
        print(f"模板: {report['template_used']}")
        print(f"银行: {report['bank_name']}")
        print(f"元数据字段数: {report['metadata_extraction']['fields_extracted']}")
        print(f"识别账户数: {report['accounts_identified']}")
        
        # 账户详情
        for i, account in enumerate(report['account_details']):
            print(f"\n账户 {i+1}: {account.get('account_name', 'N/A')}")
            print(f"卡号: {account.get('card_number', 'N/A')}")
            print(f"户名: {account.get('account_name', 'N/A')}")
        
        # 交易统计
        for account_key, stats in report['transaction_stats'].items():
            print(f"\n账户 {account_key} 交易统计:")
            print(f"总记录数: {stats['total_records']}")
            print(f"日期范围: {stats['date_range']['earliest']} ~ {stats['date_range']['latest']}")
            print(f"总金额: {stats['amount_stats']['total_amount']:.2f}")
            print(f"总收入: {stats['amount_stats']['total_income']:.2f}")
            print(f"总支出: {stats['amount_stats']['total_expense']:.2f}")
        
        # 潜在问题
        if report['potential_issues']:
            print("\n潜在问题:")
            for issue in report['potential_issues']:
                if issue['issue_type'] == 'missing_field':
                    print(f"账户 {issue['account_key']} 缺失字段 '{issue['field']}': {issue['count']}条记录 ({issue['percentage']:.1f}%)")
                elif issue['issue_type'] == 'amount_flag_mismatch':
                    print(f"账户 {issue['account_key']} 金额与收支标记不一致: {issue['count']}条记录 ({issue['percentage']:.1f}%)")
        
        # 输出到文件
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"\n完整结果已保存到: {args.output}")
            
    except Exception as e:
        print(f"验证过程出错: {str(e)}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    main() 