import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Row, Col, Card, Statistic, Skeleton, Button, Descriptions, Divider, List, Typography, Alert, Space, Tag } from 'antd';
import { 
  BankOutlined, 
  TeamOutlined, 
  FileTextOutlined, 
  Bar<PERSON>hartOutlined, 
  ArrowUpOutlined,
  ArrowDownOutlined,
  CommentOutlined
} from '@ant-design/icons';
import { buildApiUrl, API_ENDPOINTS } from '../../config/api';
import { getCurrentUser } from '../../services/api';

const { Title, Paragraph, Text } = Typography;

/**
 * 项目概览/仪表盘组件
 * 
 * @returns {JSX.Element} 项目概览页面
 */
const ProjectDashboard = () => {
  const { projectId } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [projectData, setProjectData] = useState(null);
  
  useEffect(() => {
    // 从API获取真实项目数据
    const fetchProjectData = async () => {
      try {
        setLoading(true);
        
        // 获取当前用户
        const currentUser = getCurrentUser();
        console.log('🔍 ProjectDashboard 获取用户:', currentUser);
        
        // 调用API获取项目信息 - 添加用户认证头部
        const response = await fetch(buildApiUrl(`${API_ENDPOINTS.projects}/${projectId}`), {
          headers: {
            'X-Current-User': currentUser
          }
        });
        
        if (response.ok) {
          const projectInfo = await response.json();
          
          // 调用API获取项目统计数据 - 添加用户认证头部
          const statsResponse = await fetch(buildApiUrl(`${API_ENDPOINTS.projects}/${projectId}/stats`), {
            headers: {
              'X-Current-User': currentUser
            }
          });
          let statsData = {};
          
          if (statsResponse.ok) {
            statsData = await statsResponse.json();
            console.log('📊 获取到统计数据:', statsData);
          } else {
            console.error('❌ 获取统计数据失败:', statsResponse.status, statsResponse.statusText);
          }
          
          // 构建项目数据
          setProjectData({
            project_id: projectId,
            project_name: projectInfo.project_name || `项目 ${projectId}`,
            person_name: projectInfo.person_name || '未设置',
            created_at: projectInfo.created_at || new Date().toISOString(),
            updated_at: projectInfo.updated_at || new Date().toISOString(),
            description: projectInfo.description || '项目描述暂未添加',
            
            // 案情简要数据（从API获取或使用默认值）
            case_brief: {
              subject_name: statsData.case_brief?.subject_name || '待录入',
              subject_id_card: statsData.case_brief?.subject_id_card || '待录入',
              clues_summary: statsData.case_brief?.clues_summary || '案情简要待录入',
              related_persons_count: statsData.case_brief?.related_persons_count || 0,
              assets_info: {
                vehicles: statsData.case_brief?.assets_info?.vehicles || 0,
                houses: statsData.case_brief?.assets_info?.houses || 0,
                others: statsData.case_brief?.assets_info?.others || 0
              }
            },
            
            // 银行流水统计（从API获取或使用默认值）
            bank_statements: {
              accounts_count: statsData.bank_statements?.accounts_count || 0,
              transactions_count: statsData.bank_statements?.transactions_count || 0,
              total_inflow: statsData.bank_statements?.total_inflow || 0,
              total_outflow: statsData.bank_statements?.total_outflow || 0,
              date_range: {
                start: statsData.bank_statements?.date_range?.start || '未知',
                end: statsData.bank_statements?.date_range?.end || '未知'
              },
              banks: statsData.bank_statements?.banks || []
            },

            // 谈话笔录统计（从API获取或使用默认值）
            transcripts: {
              count: statsData.transcripts?.count || 0,
              latest_date: statsData.transcripts?.latest_date || new Date().toISOString()
            }
          });
        } else {
          // API调用失败时使用默认数据
          setProjectData({
            project_id: projectId,
            project_name: `项目 ${projectId}`,
            person_name: '未设置',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            description: '项目描述暂未添加',
            case_brief: {
              subject_name: '待录入',
              subject_id_card: '待录入',
              clues_summary: '案情简要待录入，请在案情简要模块中补充相关信息',
              related_persons_count: 0,
              assets_info: { vehicles: 0, houses: 0, others: 0 }
            },
            bank_statements: {
              accounts_count: 0,
              transactions_count: 0,
              total_inflow: 0,
              total_outflow: 0,
              date_range: { start: '未知', end: '未知' },
              banks: []
            },
            transcripts: {
              count: 0,
              latest_date: new Date().toISOString()
            }
          });
        }
        
      } catch (error) {
        console.error('获取项目数据失败:', error);
        // 错误时使用默认数据
        setProjectData({
          project_id: projectId,
          project_name: `项目 ${projectId}`,
          person_name: '未设置',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          description: '项目描述暂未添加',
          case_brief: {
            subject_name: '待录入',
            subject_id_card: '待录入',
            clues_summary: '案情简要待录入，请在案情简要模块中补充相关信息',
            related_persons_count: 0,
            assets_info: { vehicles: 0, houses: 0, others: 0 }
          },
          bank_statements: {
            accounts_count: 0,
            transactions_count: 0,
            total_inflow: 0,
            total_outflow: 0,
            date_range: { start: '未知', end: '未知' },
            banks: []
          },
          transcripts: {
            count: 0,
            latest_date: new Date().toISOString()
          }
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchProjectData();
  }, [projectId]);

  if (loading) {
    return (
      <Card>
        <Skeleton active />
      </Card>
    );
  }

  const netBalance = projectData.bank_statements.total_inflow - projectData.bank_statements.total_outflow;
  const isPositiveBalance = netBalance >= 0;

  return (
    <div className="compact-spacing">
      {/* 项目基本信息 - 紧凑布局 */}
      <Card className="content-section">
        <h1 className="page-title">{projectData.project_name}</h1>
        <Paragraph style={{ fontSize: '14px', color: '#595959', marginBottom: '12px' }}>
          {projectData.description}
        </Paragraph>
        <Descriptions bordered size="small" column={2}>
          <Descriptions.Item label="创建时间" span={1}>
            {new Date(projectData.created_at).toLocaleString('zh-CN')}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间" span={1}>
            {new Date(projectData.updated_at).toLocaleString('zh-CN')}
          </Descriptions.Item>
        </Descriptions>

        <Alert 
          type="info" 
          message="项目工作区" 
          description="通过左侧菜单访问案情简要、银行流水、分析工具、谈话笔录等功能模块" 
          showIcon 
          style={{ marginTop: '12px' }}
        />
      </Card>

      {/* 案情简要 - 紧凑布局 */}
      <div className="section-title">案情简要</div>
      <Row gutter={[12, 12]}>
        <Col span={12}>
          <Card 
            title={<><FileTextOutlined className="highlight-text" /> 被反映人信息</>}
            size="small"
            className="content-section"
          >
            {projectData.case_brief.subject_name !== '待录入' ? (
              <Descriptions bordered size="small" column={1}>
                <Descriptions.Item label="主要被反映人">
                  <span className="highlight-text">{projectData.case_brief.subject_name}</span>
                </Descriptions.Item>
                <Descriptions.Item label="身份证号">
                  {projectData.case_brief.subject_id_card}
                </Descriptions.Item>
                <Descriptions.Item label="问题线索摘要">
                  <Paragraph 
                    ellipsis={{ rows: 2, expandable: true, symbol: '展开' }}
                    style={{ fontSize: '13px', margin: 0 }}
                  >
                    {projectData.case_brief.clues_summary}
                  </Paragraph>
                </Descriptions.Item>
              </Descriptions>
            ) : (
              <div style={{ textAlign: 'center', padding: '20px 0' }}>
                <Text type="secondary">暂无被反映人资料</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  请在被反映人资料模块中添加相关信息
                </Text>
              </div>
            )}
            <div style={{ marginTop: '12px', textAlign: 'right' }}>
              <Button 
                type="primary" 
                size="small"
                onClick={() => navigate(`/projects/${projectId}/case-brief/subject`)}
              >
                {projectData.case_brief.subject_name !== '待录入' ? '管理资料' : '添加资料'}
              </Button>
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card 
            title={<><TeamOutlined className="highlight-text" /> 关联信息</>}
            size="small"
            className="content-section"
          >
            <Row gutter={[8, 8]}>
              <Col span={12}>
                <Statistic
                  title="相关人员数量"
                  value={projectData.case_brief.related_persons_count}
                  prefix={<TeamOutlined />}
                  valueStyle={{ fontSize: '20px', fontWeight: '700', color: '#1890ff' }}
                />
                <Button 
                  size="small" 
                  style={{ marginTop: '8px' }}
                  onClick={() => navigate(`/projects/${projectId}/case-brief/related-persons`)}
                >
                  管理相关人员
                </Button>
              </Col>
              <Col span={12}>
                <Statistic 
                  title="车辆数量" 
                  value={projectData.case_brief.assets_info.vehicles}
                  valueStyle={{ fontSize: '18px', fontWeight: '600', color: '#52c41a' }}
                />
                <Statistic 
                  title="房产数量" 
                  value={projectData.case_brief.assets_info.houses} 
                  style={{ marginTop: '8px' }}
                  valueStyle={{ fontSize: '18px', fontWeight: '600', color: '#fa8c16' }}
                />
              </Col>
            </Row>
            <div style={{ marginTop: '12px', textAlign: 'right' }}>
              <Space size="small">
                <Button 
                  size="small"
                  onClick={() => navigate(`/projects/${projectId}/case-brief/assets`)}
                >
                  财产信息
                </Button>
                <Button 
                  size="small"
                  onClick={() => navigate(`/projects/${projectId}/case-brief/relationship`)}
                >
                  关系图谱
                </Button>
              </Space>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 银行流水概览 - 紧凑布局 */}
      <div className="section-title" style={{ marginTop: '16px' }}>银行流水概览</div>
      <Row gutter={[12, 12]}>
        <Col span={12}>
          <Card 
            title={<><BankOutlined className="highlight-text" /> 流水统计</>}
            size="small"
            className="content-section"
          >
            <Row gutter={[8, 8]}>
              <Col span={12}>
                <Statistic
                  title="账户数量"
                  value={projectData.bank_statements.accounts_count}
                  precision={0}
                  valueStyle={{ fontSize: '20px', fontWeight: '700', color: '#1890ff' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="交易记录数"
                  value={projectData.bank_statements.transactions_count}
                  precision={0}
                  valueStyle={{ fontSize: '20px', fontWeight: '700', color: '#1890ff' }}
                />
              </Col>
            </Row>
            <Row gutter={[8, 8]} style={{ marginTop: '12px' }}>
              <Col span={12}>
                <Statistic
                  title="总收入"
                  value={projectData.bank_statements.total_inflow}
                  precision={2}
                  valueStyle={{ color: '#3f8600', fontSize: '18px', fontWeight: '600' }}
                  prefix="¥"
                  suffix={<ArrowUpOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="总支出"
                  value={projectData.bank_statements.total_outflow}
                  precision={2}
                  valueStyle={{ color: '#cf1322', fontSize: '18px', fontWeight: '600' }}
                  prefix="¥"
                  suffix={<ArrowDownOutlined />}
                />
              </Col>
            </Row>
            <Statistic
              title="收支净额"
              value={netBalance}
              precision={2}
              valueStyle={{ 
                color: isPositiveBalance ? '#3f8600' : '#cf1322',
                fontSize: '20px',
                fontWeight: '700'
              }}
              prefix="¥"
              suffix={isPositiveBalance ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
              style={{ marginTop: '12px', textAlign: 'center', background: '#fafafa', padding: '8px', borderRadius: '4px' }}
            />
            <div style={{ marginTop: '12px', textAlign: 'right' }}>
              <Space size="small">
                <Button 
                  size="small"
                  onClick={() => navigate(`/projects/${projectId}/bank-statements/query`)}
                >
                  查询明细
                </Button>
                <Button 
                  type="primary" 
                  size="small"
                  onClick={() => navigate(`/projects/${projectId}/bankStatements/import`)}
                >
                  导入流水
                </Button>
              </Space>
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card 
            title={<><BarChartOutlined className="highlight-text" /> 流水信息</>}
            size="small"
            className="content-section"
          >
            <Descriptions bordered size="small" column={1}>
              <Descriptions.Item label="流水时间范围">
                <span style={{ fontSize: '13px' }}>
                  {new Date(projectData.bank_statements.date_range.start).toLocaleDateString()} 至 {' '}
                  {new Date(projectData.bank_statements.date_range.end).toLocaleDateString()}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="涉及银行">
                <div>
                  {projectData.bank_statements.banks.map(bank => (
                    <Tag key={bank} color="blue" style={{ margin: '2px' }}>{bank}</Tag>
                  ))}
                </div>
              </Descriptions.Item>
              <Descriptions.Item label="谈话笔录">
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span style={{ fontSize: '13px' }}>
                    共 <span className="important-number">{projectData.transcripts.count}</span> 份
                    <br />
                    最近更新: {new Date(projectData.transcripts.latest_date).toLocaleDateString()}
                  </span>
                  <Button 
                    size="small" 
                    icon={<CommentOutlined />}
                    onClick={() => navigate(`/projects/${projectId}/transcripts`)}
                  >
                    查看笔录
                  </Button>
                </div>
              </Descriptions.Item>
            </Descriptions>
            <div style={{ marginTop: '12px', textAlign: 'right' }}>
              <Space size="small">
                <Button 
                  size="small"
                  onClick={() => navigate(`/projects/${projectId}/analysis/tactics`)}
                >
                  常用战法分析
                </Button>
                <Button 
                  type="primary" 
                  size="small"
                  onClick={() => navigate(`/projects/${projectId}/analysis/ai`)}
                >
                  AI智能分析
                </Button>
              </Space>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ProjectDashboard; 