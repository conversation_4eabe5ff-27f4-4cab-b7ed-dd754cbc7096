{"success": true, "message": "解析成功", "parse_result": {"accounts": [{"account_id": "baf3a4ae-ca71-4e92-8ae7-7d158af009d3", "person_name": "杨蕊瑜", "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_name": "杨蕊瑜", "account_number": "**************", "card_number": "****************", "currency": "RMB", "date_range": "2018-03-29 到 2021-02-08", "transactions_count": 257, "total_inflow": 1909842.**********, "total_outflow": 1909842.**********, "account_balance": 0.0}], "transactions": [{"transaction_id": "1da8bf74-46e3-442c-b23f-27959a69f825", "sequence_number": 1, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-03-29", "transaction_time": "17:02:21", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 5.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 5.0, "balance_amount": 5.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "", "remark1": ""}, {"transaction_id": "a9b94eae-ae4e-4e29-8793-aaaaa62c24a3", "sequence_number": 2, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-03-29", "transaction_time": "17:28:52", "transaction_method": "车贷放款", "transaction_type": "车贷放款", "transaction_amount": 657300.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 657305.0, "balance_amount": 657305.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "2df21ddf-8800-42ef-ab8d-74155adfa221", "sequence_number": 3, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-03-29", "transaction_time": "17:28:52", "transaction_method": "车贷放款", "transaction_type": "车贷放款", "transaction_amount": 657300.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 5.0, "balance_amount": 5.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "1f76537f-2fba-4cea-926d-24e861619719", "sequence_number": 4, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-03-29", "transaction_time": "17:28:58", "transaction_method": "车贷放款", "transaction_type": "车贷放款", "transaction_amount": 15300.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 15305.0, "balance_amount": 15305.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "196ae8c2-5a51-40fb-9219-d410c18ea1c1", "sequence_number": 5, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-03-29", "transaction_time": "17:28:58", "transaction_method": "车贷放款", "transaction_type": "车贷放款", "transaction_amount": 15300.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 5.0, "balance_amount": 5.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "af35449e-cb12-4cd9-a78b-c252763545c5", "sequence_number": 6, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-04-26", "transaction_time": "18:33:03", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 20970.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 20975.0, "balance_amount": 20975.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "平安车贷", "remark1": "平安车贷"}, {"transaction_id": "dc87b87c-5d84-4d2f-a3dc-6536f36db385", "sequence_number": 7, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-04-29", "transaction_time": "02:10:38", "transaction_method": "批量扣款业务", "transaction_type": "批量扣款业务", "transaction_amount": 493.69, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 784.39, "balance_amount": 784.39, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "3d2f54d8-e595-4db1-b488-90d914d0253a", "sequence_number": 8, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-04-29", "transaction_time": "02:10:38", "transaction_method": "批量扣款业务", "transaction_type": "批量扣款业务", "transaction_amount": 19696.92, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 1278.08, "balance_amount": 1278.08, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "d8c98b6a-470d-4fa5-8fde-6671c83f6174", "sequence_number": 9, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-05-28", "transaction_time": "12:44:21", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 20500.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 21284.39, "balance_amount": 21284.39, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "平安车", "remark1": "平安车"}, {"transaction_id": "e116f92a-b539-41bb-b601-b71b6b9ce0b9", "sequence_number": 10, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-05-29", "transaction_time": "02:08:58", "transaction_method": "批量扣款业务", "transaction_type": "批量扣款业务", "transaction_amount": 19696.92, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 1587.47, "balance_amount": 1587.47, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "1e3663dd-8ee8-4240-87e8-f713bc0718a6", "sequence_number": 11, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-05-29", "transaction_time": "02:08:58", "transaction_method": "批量扣款业务", "transaction_type": "批量扣款业务", "transaction_amount": 493.69, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 1093.78, "balance_amount": 1093.78, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "1aad029e-4fd4-4016-8f24-c95ba6396891", "sequence_number": 12, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-06-21", "transaction_time": "00:35:10", "transaction_method": "支付利息", "transaction_type": "支付利息", "transaction_amount": 1.1, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 1094.88, "balance_amount": 1094.88, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "ec0d5771-4a14-4139-a537-174306e16af2", "sequence_number": 13, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-06-28", "transaction_time": "16:39:56", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 20000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 21094.88, "balance_amount": 21094.88, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "覃车贷", "remark1": "覃车贷"}, {"transaction_id": "a286357d-92fb-4325-b16a-24fe79cb37eb", "sequence_number": 14, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-06-29", "transaction_time": "02:08:46", "transaction_method": "批量扣款业务", "transaction_type": "批量扣款业务", "transaction_amount": 19696.92, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 904.27, "balance_amount": 904.27, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "8d499853-9844-424d-8fdc-277be25813f2", "sequence_number": 15, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-06-29", "transaction_time": "02:08:46", "transaction_method": "批量扣款业务", "transaction_type": "批量扣款业务", "transaction_amount": 493.69, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 20601.19, "balance_amount": 20601.19, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "58d425da-d2d3-46f1-bd59-d1badcf2f311", "sequence_number": 16, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-07-28", "transaction_time": "20:54:08", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 20000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 20904.27, "balance_amount": 20904.27, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "车", "remark1": "车"}, {"transaction_id": "633e7fe9-f552-4bc3-9e43-abed99d0b76e", "sequence_number": 17, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-07-29", "transaction_time": "02:04:54", "transaction_method": "批量扣款业务", "transaction_type": "批量扣款业务", "transaction_amount": 19696.92, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 1207.35, "balance_amount": 1207.35, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "da54d394-670c-4fcb-890e-416928369c39", "sequence_number": 18, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-07-29", "transaction_time": "02:04:54", "transaction_method": "批量扣款业务", "transaction_type": "批量扣款业务", "transaction_amount": 493.69, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 713.66, "balance_amount": 713.66, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "1965002e-c839-4b74-9c61-2e18403dd3c8", "sequence_number": 19, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-08-29", "transaction_time": "02:27:20", "transaction_method": "批量扣款业务", "transaction_type": "批量扣款业务", "transaction_amount": 713.66, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "9a3da07c-48db-4a05-b626-e5faffe41abb", "sequence_number": 20, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-08-29", "transaction_time": "09:07:32", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 20000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 20000.0, "balance_amount": 20000.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "", "remark1": ""}, {"transaction_id": "4781cd44-87ae-41ee-87a6-f697a328ed14", "sequence_number": 21, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-08-29", "transaction_time": "09:07:32", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 18983.26, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 1016.74, "balance_amount": 1016.74, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "bfd62c97-96fb-48a5-9e46-dc4c65205787", "sequence_number": 22, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-08-29", "transaction_time": "09:07:32", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 493.69, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 523.05, "balance_amount": 523.05, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "cc8350da-e92e-4d1e-8640-35277ccb6c32", "sequence_number": 23, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-09-21", "transaction_time": "00:52:05", "transaction_method": "支付利息", "transaction_type": "支付利息", "transaction_amount": 0.92, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 523.97, "balance_amount": 523.97, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "bb1015e8-d355-4e5b-9c3b-f010c28d55d9", "sequence_number": 24, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-09-27", "transaction_time": "23:05:04", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 20000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 20523.97, "balance_amount": 20523.97, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "", "remark1": ""}, {"transaction_id": "16090ad9-e1f5-4e97-aed3-88d20e30ae7f", "sequence_number": 25, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-09-29", "transaction_time": "02:33:54", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 493.69, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 20030.28, "balance_amount": 20030.28, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "63aa2dad-a155-4db1-9709-4cde94a5abb9", "sequence_number": 26, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-09-29", "transaction_time": "02:33:54", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 19696.92, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 333.36, "balance_amount": 333.36, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "67850b74-6c4b-4671-a81e-1b226658a895", "sequence_number": 27, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-10-29", "transaction_time": "02:31:39", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 333.36, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "a8bca7f3-f964-4afa-b72a-3c88b9730582", "sequence_number": 28, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-10-29", "transaction_time": "09:25:45", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 20190.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 20190.0, "balance_amount": 20190.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "", "remark1": ""}, {"transaction_id": "2b4d84c5-86d3-49f1-a5b3-7a087e7a7fcf", "sequence_number": 29, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-10-29", "transaction_time": "09:25:47", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 19363.56, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 826.44, "balance_amount": 826.44, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "2948980e-9f18-464b-ab88-ae2ef22175d4", "sequence_number": 30, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-10-29", "transaction_time": "09:25:47", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 493.69, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 332.75, "balance_amount": 332.75, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "d5441231-f79b-43ef-9514-734ae09cb83a", "sequence_number": 31, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-11-28", "transaction_time": "08:19:29", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 20000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 20332.75, "balance_amount": 20332.75, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "代覃木坚付车贷", "remark1": "代覃木坚付车贷"}, {"transaction_id": "11184a9e-b7d4-4275-9c27-28deda273ed2", "sequence_number": 32, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-11-29", "transaction_time": "01:57:15", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 493.69, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 19839.06, "balance_amount": 19839.06, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "fba1a60a-7fc0-4b7b-9d3a-dc91cd84bff2", "sequence_number": 33, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-11-29", "transaction_time": "01:57:15", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 19696.92, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 142.14, "balance_amount": 142.14, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "d57cc5e9-b503-466b-89b8-b07a156dedb4", "sequence_number": 34, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-12-21", "transaction_time": "00:47:13", "transaction_method": "支付利息", "transaction_type": "支付利息", "transaction_amount": 0.73, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 142.87, "balance_amount": 142.87, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "41e537e8-0a10-402d-bff8-5a9efcddd050", "sequence_number": 35, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-12-29", "transaction_time": "02:12:05", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 142.87, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "c1510fa4-149f-4839-982b-66c17a6e836f", "sequence_number": 36, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-12-29", "transaction_time": "09:40:28", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 20000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 20000.0, "balance_amount": 20000.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "", "remark1": ""}, {"transaction_id": "04d7eea8-764d-407a-bcc7-142e309f7cc7", "sequence_number": 37, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-12-29", "transaction_time": "09:40:30", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 19554.05, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 445.95, "balance_amount": 445.95, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "0adeabc6-04c9-48b6-8378-cf2d06e9a919", "sequence_number": 38, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-12-29", "transaction_time": "09:40:30", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 445.95, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "ab2af891-34f1-4246-914d-7ee269fe82ff", "sequence_number": 39, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-12-31", "transaction_time": "17:56:30", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 100.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 100.0, "balance_amount": 100.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "", "remark1": ""}, {"transaction_id": "7b394a5f-130f-48b2-8160-8ee1f0baf2f7", "sequence_number": 40, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2018-12-31", "transaction_time": "19:18:18", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 47.78, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 52.22, "balance_amount": 52.22, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "55176a77-6ac2-4576-a829-135b1347b742", "sequence_number": 41, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-01-25", "transaction_time": "16:52:00", "transaction_method": "车贷放款", "transaction_type": "车贷放款", "transaction_amount": 290000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 290052.22, "balance_amount": 290052.22, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "********-798f-4dde-bedb-e713c46b029e", "sequence_number": 42, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-01-25", "transaction_time": "16:52:31", "transaction_method": "车贷放款", "transaction_type": "车贷放款", "transaction_amount": 15300.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 305352.22, "balance_amount": 305352.22, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "c03d19eb-16c1-45e1-8687-0f98482adfa3", "sequence_number": 43, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-01-25", "transaction_time": "16:52:31", "transaction_method": "车贷放款", "transaction_type": "车贷放款", "transaction_amount": 15300.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 290052.22, "balance_amount": 290052.22, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "c7c1f50a-5207-4892-8ab0-534be179902e", "sequence_number": 44, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-01-25", "transaction_time": "16:56:28", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 50000.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 240052.22, "balance_amount": 240052.22, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "中国工商银行", "remark": "", "remark1": ""}, {"transaction_id": "26d8aaaa-3319-4192-89ee-609269fd7cc6", "sequence_number": 45, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-01-25", "transaction_time": "16:58:37", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 50000.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 190052.22, "balance_amount": 190052.22, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "中国民生银行", "remark": "", "remark1": ""}, {"transaction_id": "6a4bb76e-d681-407a-993e-92a53f5860b1", "sequence_number": 46, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-01-25", "transaction_time": "16:59:34", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 50000.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 140052.22, "balance_amount": 140052.22, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "中国民生银行", "remark": "", "remark1": ""}, {"transaction_id": "00631f36-4f72-45b0-bafc-ff57ea1c9c39", "sequence_number": 47, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-01-25", "transaction_time": "17:00:42", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 50000.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 90052.22, "balance_amount": 90052.22, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "中国民生银行", "remark": "", "remark1": ""}, {"transaction_id": "62936b71-f828-4cbe-b036-1c5a8eec5f6e", "sequence_number": 48, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-01-25", "transaction_time": "17:01:18", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 50000.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 40052.22, "balance_amount": 40052.22, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "中国民生银行", "remark": "", "remark1": ""}, {"transaction_id": "f737eccf-61ec-4a82-be80-b61d76e36e31", "sequence_number": 49, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-01-25", "transaction_time": "17:01:53", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 40000.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 52.22, "balance_amount": 52.22, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "中国民生银行", "remark": "", "remark1": ""}, {"transaction_id": "c09105ee-78f0-4812-8add-b9bba31630f1", "sequence_number": 50, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-01-29", "transaction_time": "01:50:09", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 52.22, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "36b7415e-3a1e-4d3b-96ba-20c8aab9cff1", "sequence_number": 51, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-01-29", "transaction_time": "09:43:55", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 20200.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 20200.0, "balance_amount": 20200.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "覃木坚", "remark1": "覃木坚"}, {"transaction_id": "1151e243-4606-4f53-8319-af214d3d834b", "sequence_number": 52, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-01-29", "transaction_time": "09:43:58", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 19696.92, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 503.08, "balance_amount": 503.08, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "9b84090d-bc58-405f-9bbf-c952f5577205", "sequence_number": 53, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-01-29", "transaction_time": "09:43:59", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 441.47, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 61.61, "balance_amount": 61.61, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "c5e6674d-1034-426e-875f-38b3aa8de0bc", "sequence_number": 54, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-02-25", "transaction_time": "01:48:11", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 61.61, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "084ea03a-98de-48ab-9f7a-9dfd379842a3", "sequence_number": 55, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-02-25", "transaction_time": "13:52:52", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 11000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 11000.0, "balance_amount": 11000.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "600", "remark1": "600"}, {"transaction_id": "1c009bd6-96d2-4a7b-bf86-8b149d6eae1f", "sequence_number": 56, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-02-25", "transaction_time": "13:52:52", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 446.57, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 10553.43, "balance_amount": 10553.43, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "c3573659-953a-48d7-8c91-1656370a062f", "sequence_number": 57, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-02-25", "transaction_time": "13:52:53", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 10484.19, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 69.24, "balance_amount": 69.24, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "585a2996-2bd8-4287-bd30-36bdced229b1", "sequence_number": 58, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-02-28", "transaction_time": "02:12:34", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 69.24, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "19c1024e-5508-44d4-ad28-3199b91ca314", "sequence_number": 59, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-02-28", "transaction_time": "16:06:28", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 20000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 20000.0, "balance_amount": 20000.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "覃木坚借用还车贷", "remark1": "覃木坚借用还车贷"}, {"transaction_id": "797e459d-a7c6-42f7-850e-a2907b3a6a7d", "sequence_number": 60, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-02-28", "transaction_time": "16:06:28", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 424.45, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 19575.55, "balance_amount": 19575.55, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "********-5ad4-4a1b-86ee-f15da634d6d7", "sequence_number": 61, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-02-28", "transaction_time": "16:06:29", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 19575.55, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "b0b7b26f-a615-415c-a24f-e5cd1fac3779", "sequence_number": 62, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-02-28", "transaction_time": "16:07:35", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 1000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 1000.0, "balance_amount": 1000.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "覃木坚借用还车贷", "remark1": "覃木坚借用还车贷"}, {"transaction_id": "ebbde60a-cfb9-49a0-b20e-5b3a9040b5ab", "sequence_number": 63, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-02-28", "transaction_time": "16:07:35", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 121.37, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 878.63, "balance_amount": 878.63, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "01df0806-bd14-4dc2-b910-f300d4e425d9", "sequence_number": 64, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-03-21", "transaction_time": "00:42:40", "transaction_method": "支付利息", "transaction_type": "支付利息", "transaction_amount": 0.19, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 878.82, "balance_amount": 878.82, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "e9b82975-d0f5-4ae2-9bf6-3d44968e78f1", "sequence_number": 65, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-03-25", "transaction_time": "02:57:47", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 370.64, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "28d6fd4b-88d7-4ecd-b5dc-b93670fcb7e2", "sequence_number": 66, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-03-25", "transaction_time": "02:57:47", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 508.18, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 370.64, "balance_amount": 370.64, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "fc641823-0663-4b88-b104-1d797267fe15", "sequence_number": 67, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-03-25", "transaction_time": "09:35:28", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 10500.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 10500.0, "balance_amount": 10500.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "", "remark1": ""}, {"transaction_id": "99bc4adf-0e40-4933-8844-08093c631251", "sequence_number": 68, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-03-25", "transaction_time": "09:35:29", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 10113.55, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 386.45, "balance_amount": 386.45, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "3b36a08e-3401-42ad-9309-adcf5e5adf3e", "sequence_number": 69, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-03-29", "transaction_time": "02:07:35", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 386.45, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "********-34e9-4078-b67e-13d410911eaf", "sequence_number": 70, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-03-29", "transaction_time": "16:40:14", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 10000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 10000.0, "balance_amount": 10000.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "借覃木坚付车贷", "remark1": "借覃木坚付车贷"}, {"transaction_id": "b97c49fb-6d98-4b8c-ac38-1a5f24f155e4", "sequence_number": 71, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-03-29", "transaction_time": "16:40:18", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 107.24, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 9892.76, "balance_amount": 9892.76, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "d166d7c5-d701-4b7c-ab02-82f5555738ef", "sequence_number": 72, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-03-29", "transaction_time": "16:40:18", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 9892.76, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "db59a9f9-1d29-4b7b-9523-984c3e07cd64", "sequence_number": 73, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-03-29", "transaction_time": "16:46:50", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 9900.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 9900.0, "balance_amount": 9900.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "借覃木坚付车贷", "remark1": "借覃木坚付车贷"}, {"transaction_id": "56c0ef5b-7969-40b9-9748-f2adc474b37e", "sequence_number": 74, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-03-29", "transaction_time": "16:47:03", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 9804.16, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 95.84, "balance_amount": 95.84, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "c5df4aa9-dd45-4329-8e2f-13275f2bafdf", "sequence_number": 75, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-04-24", "transaction_time": "17:15:43", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 10000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 10095.84, "balance_amount": 10095.84, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "", "remark1": ""}, {"transaction_id": "0625ca2c-54e2-4f32-8700-8e974cb96043", "sequence_number": 76, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-04-25", "transaction_time": "03:07:59", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 508.18, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 9587.66, "balance_amount": 9587.66, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "59a8554d-97e2-449f-ab2d-f8c2cdbc1a00", "sequence_number": 77, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-04-25", "transaction_time": "03:07:59", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 9587.66, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "3e058710-4d81-46fb-a826-e802c8504824", "sequence_number": 78, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-04-25", "transaction_time": "08:20:29", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 600.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 600.0, "balance_amount": 600.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "", "remark1": ""}, {"transaction_id": "594cbb01-5a01-46da-a9f0-e398312885bc", "sequence_number": 79, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-04-25", "transaction_time": "08:20:30", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 600.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "ed0cf236-9ad2-4be5-907b-9d19dae263d8", "sequence_number": 80, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-04-25", "transaction_time": "08:22:08", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 200.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 200.0, "balance_amount": 200.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "", "remark1": ""}, {"transaction_id": "6fe86b5c-8ed9-4966-b15f-8166135620a3", "sequence_number": 81, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-04-25", "transaction_time": "08:22:08", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 200.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "7dc9598a-b791-4949-9257-85244a865129", "sequence_number": 82, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-04-26", "transaction_time": "08:32:24", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 500.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 500.0, "balance_amount": 500.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "", "remark1": ""}, {"transaction_id": "bc44feb5-0cc0-41bb-a04b-733b30ece16c", "sequence_number": 83, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-04-26", "transaction_time": "08:32:25", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 96.53, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 403.47, "balance_amount": 403.47, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "0b7dd759-3169-4c8a-96dc-437ec18a5498", "sequence_number": 84, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-04-29", "transaction_time": "02:27:08", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 403.47, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "b6764965-03a3-476c-8a41-f53a48d022b0", "sequence_number": 85, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-04-29", "transaction_time": "17:29:10", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 5000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 5000.0, "balance_amount": 5000.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "覃木坚借付车贷", "remark1": "覃木坚借付车贷"}, {"transaction_id": "ac2f64bd-1fc4-4c97-bf4b-57bb460f17c7", "sequence_number": 86, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-04-29", "transaction_time": "17:29:10", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 90.22, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 4909.78, "balance_amount": 4909.78, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "cde43843-337c-4fb7-8307-ab4be28380a1", "sequence_number": 87, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-04-29", "transaction_time": "17:29:11", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 4909.78, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "7b122636-2926-4ea8-b313-a15b18374608", "sequence_number": 88, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-04-29", "transaction_time": "17:29:38", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 5000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 5000.0, "balance_amount": 5000.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "覃木坚借付车贷", "remark1": "覃木坚借付车贷"}, {"transaction_id": "eb9b0331-900c-46e8-a391-0576ea0fd42e", "sequence_number": 89, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-04-29", "transaction_time": "17:29:39", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 5000.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "e8e038d7-0e7e-4bbe-a6f6-97b4787680f1", "sequence_number": 90, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-04-29", "transaction_time": "17:40:53", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 10190.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 10190.0, "balance_amount": 10190.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "覃木坚借付车贷", "remark1": "覃木坚借付车贷"}, {"transaction_id": "58cf6e99-cc8a-4421-9310-42148a3d40e7", "sequence_number": 91, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-04-29", "transaction_time": "17:40:54", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 9787.14, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 402.86, "balance_amount": 402.86, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "58aa4fca-5a86-4566-9298-889cd4eccf6f", "sequence_number": 92, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-05-25", "transaction_time": "02:20:52", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 402.86, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "dbfb2085-79b8-4bfe-bef8-7ef90b132131", "sequence_number": 93, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-05-25", "transaction_time": "11:30:19", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 11000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 11000.0, "balance_amount": 11000.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "", "remark1": ""}, {"transaction_id": "e1e07fbb-2f4c-4555-9897-0dbc0e3c863a", "sequence_number": 94, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-05-25", "transaction_time": "11:30:20", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 105.32, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 10894.68, "balance_amount": 10894.68, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "7ca3c1eb-eb4c-4ad4-b95e-32783ea85483", "sequence_number": 95, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-05-25", "transaction_time": "11:30:20", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 10484.19, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 410.49, "balance_amount": 410.49, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "1744f4a5-bf1a-4333-9b68-82f94e86752c", "sequence_number": 96, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-05-29", "transaction_time": "02:31:36", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 410.49, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "d31c908a-40e3-4344-a450-2de2db607790", "sequence_number": 97, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-06-03", "transaction_time": "12:41:30", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 20000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 20000.0, "balance_amount": 20000.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "覃木坚转来付款（黄宇平）", "remark1": "覃木坚转来付款（黄宇平）"}, {"transaction_id": "c7fefda4-edf3-4d1d-9d7e-b6877879a0a9", "sequence_number": 98, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-06-03", "transaction_time": "12:41:30", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 83.36, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 19916.64, "balance_amount": 19916.64, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "5006ab49-cbf2-4ae8-a83e-c8f75ea7ab12", "sequence_number": 99, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-06-03", "transaction_time": "12:41:31", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 19731.13, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 185.51, "balance_amount": 185.51, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "d8754db3-8ac9-45b5-b782-c1f5f1a94116", "sequence_number": 100, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-06-21", "transaction_time": "00:45:19", "transaction_method": "支付利息", "transaction_type": "支付利息", "transaction_amount": 0.29, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 185.8, "balance_amount": 185.8, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "3062fe12-895a-4583-aab6-03ce80cc6bd2", "sequence_number": 101, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-06-25", "transaction_time": "08:59:08", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 185.8, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "3e9e0c84-75dd-4a56-8123-f21bbde34cfa", "sequence_number": 102, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-06-25", "transaction_time": "14:38:32", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 10500.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 10500.0, "balance_amount": 10500.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "", "remark1": ""}, {"transaction_id": "64ecd0f2-572a-4056-b3cc-0010c04481a4", "sequence_number": 103, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-06-25", "transaction_time": "14:38:33", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 322.38, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 10177.62, "balance_amount": 10177.62, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "d4a941de-5f55-44a2-8973-1d5f13b0eb85", "sequence_number": 104, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-06-25", "transaction_time": "14:38:33", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 10177.62, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "88c47014-490d-4608-ba57-cd8bdb529ece", "sequence_number": 105, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-06-25", "transaction_time": "14:45:35", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 400.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 400.0, "balance_amount": 400.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "", "remark1": ""}, {"transaction_id": "d93bc9c4-96d6-4171-b008-fa12f8ae4322", "sequence_number": 106, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-06-25", "transaction_time": "14:45:35", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 306.57, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 93.43, "balance_amount": 93.43, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "485a1a03-6e28-4948-b0b4-f352e020b4ed", "sequence_number": 107, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-06-29", "transaction_time": "03:02:36", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 93.43, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "7071bf14-c618-4ac5-8aa2-dce3defc7b33", "sequence_number": 108, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-06-29", "transaction_time": "09:02:24", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 20190.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 20190.0, "balance_amount": 20190.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "代木坚付", "remark1": "代木坚付"}, {"transaction_id": "a92f383c-f93c-4045-901c-4caaa8bd022e", "sequence_number": 109, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-06-29", "transaction_time": "09:02:24", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 400.26, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 19789.74, "balance_amount": 19789.74, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "02e51634-d1fb-46d1-9fc7-b9c99fe6305c", "sequence_number": 110, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-06-29", "transaction_time": "09:02:24", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 19696.92, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 92.82, "balance_amount": 92.82, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "9f096471-cd6b-4efa-b547-bf4300b1ffcc", "sequence_number": 111, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-07-25", "transaction_time": "02:44:32", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 92.82, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "100040e9-afa6-4379-bbbf-f1bb1cbcfb43", "sequence_number": 112, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-07-25", "transaction_time": "07:06:38", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 11800.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 11800.0, "balance_amount": 11800.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "", "remark1": ""}, {"transaction_id": "040b89ca-d9c8-4526-9cc9-42d9e9ff6006", "sequence_number": 113, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-07-25", "transaction_time": "07:06:38", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 415.36, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 11384.64, "balance_amount": 11384.64, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "0114616d-04a8-410e-b788-d010c57332eb", "sequence_number": 114, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-07-25", "transaction_time": "23:03:33", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 10484.19, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 900.45, "balance_amount": 900.45, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "d4a9481a-0690-491c-a95a-e8763963e298", "sequence_number": 115, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-07-28", "transaction_time": "12:56:22", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 19800.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 20700.45, "balance_amount": 20700.45, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "覃木坚借用付车贷款", "remark1": "覃木坚借用付车贷款"}, {"transaction_id": "8bdb15ff-d056-4f9d-874a-c09659967ff0", "sequence_number": 116, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-07-29", "transaction_time": "02:32:36", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 19696.92, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 509.84, "balance_amount": 509.84, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "de046a85-f321-4b72-b834-c6aa20b01e5c", "sequence_number": 117, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-07-29", "transaction_time": "02:32:36", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 493.69, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 20206.76, "balance_amount": 20206.76, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "a338f29a-a28f-4897-ab27-6d5098c12b15", "sequence_number": 118, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-08-25", "transaction_time": "02:30:26", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 508.18, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 1.66, "balance_amount": 1.66, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "29fdf7d7-100f-4f48-beec-12566665a276", "sequence_number": 119, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-08-25", "transaction_time": "02:30:26", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 1.66, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "bc527467-ac6f-4ea3-8470-a0babc5d97bf", "sequence_number": 120, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-08-29", "transaction_time": "17:22:11", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 11000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 11000.0, "balance_amount": 11000.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "", "remark1": ""}, {"transaction_id": "8f8738e4-8c3b-4a18-b06d-777db2f473d9", "sequence_number": 121, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-08-29", "transaction_time": "17:22:12", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 493.69, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 10506.31, "balance_amount": 10506.31, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "3730e8c2-5731-404b-b8d9-2eed3f669b69", "sequence_number": 122, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-08-29", "transaction_time": "17:22:12", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 10506.31, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "c0885cec-24df-4682-8fea-0f8647fbf878", "sequence_number": 123, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-08-29", "transaction_time": "17:25:35", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 300.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 300.0, "balance_amount": 300.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "", "remark1": ""}, {"transaction_id": "110d3e38-9533-4249-a976-8ee6d02d01b5", "sequence_number": 124, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-08-29", "transaction_time": "17:25:35", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 4.17, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 295.83, "balance_amount": 295.83, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "bc7d128b-eda8-4e0c-902d-645400a42c32", "sequence_number": 125, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-08-29", "transaction_time": "17:25:36", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 295.83, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "e401d7d1-41a7-49fd-b94c-88d4df548b22", "sequence_number": 126, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-08-29", "transaction_time": "20:18:50", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 20000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 20000.0, "balance_amount": 20000.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "", "remark1": ""}, {"transaction_id": "df2f45bb-03ec-48c7-89ce-9d849abe6cfd", "sequence_number": 127, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-08-29", "transaction_time": "20:18:50", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 19401.09, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 598.91, "balance_amount": 598.91, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "08e6842e-5dbb-4cfb-971a-75839e21b85d", "sequence_number": 128, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-09-21", "transaction_time": "00:38:35", "transaction_method": "支付利息", "transaction_type": "支付利息", "transaction_amount": 0.45, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 599.36, "balance_amount": 599.36, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "b8e198b2-aaf0-481d-a9c7-d317ebc351b5", "sequence_number": 129, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-09-25", "transaction_time": "01:57:32", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 91.18, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "c245cb6d-0f00-46f1-a8aa-159051bc27b1", "sequence_number": 130, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-09-25", "transaction_time": "01:57:32", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 508.18, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 91.18, "balance_amount": 91.18, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "58258da3-583a-4989-9584-1b38bde0a89e", "sequence_number": 131, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-09-25", "transaction_time": "10:53:32", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 10600.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 10600.0, "balance_amount": 10600.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "600", "remark1": "600"}, {"transaction_id": "3ba2db09-4847-4858-8c78-513bad00ec0c", "sequence_number": 132, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-09-25", "transaction_time": "10:53:33", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 10393.01, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 206.99, "balance_amount": 206.99, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "032c2bff-7c89-43e8-bc8d-5632e046ff0e", "sequence_number": 133, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-09-29", "transaction_time": "01:42:16", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 206.99, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "96269c94-0e8b-4f21-8d8c-7614c18fdbfd", "sequence_number": 134, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-09-29", "transaction_time": "10:02:17", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 20000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 20000.0, "balance_amount": 20000.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "覃木坚借用还车贷", "remark1": "覃木坚借用还车贷"}, {"transaction_id": "dcd77072-72a6-4826-af34-e145cb11c3e1", "sequence_number": 135, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-09-29", "transaction_time": "10:02:18", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 286.7, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 19713.3, "balance_amount": 19713.3, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "066fdc28-db46-4a8d-ac10-cce32e3de7d7", "sequence_number": 136, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-09-29", "transaction_time": "10:02:18", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 19696.92, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 16.38, "balance_amount": 16.38, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "4eba16b8-8d2f-400b-9daa-23f996c5a074", "sequence_number": 137, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-10-25", "transaction_time": "01:43:20", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 16.38, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "b7fcb375-ad40-45ff-823a-09eb47ded7ce", "sequence_number": 138, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-10-26", "transaction_time": "10:43:14", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 10600.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 10600.0, "balance_amount": 10600.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "", "remark1": ""}, {"transaction_id": "5daeeeb5-11e8-4eab-87d9-aef15ff6430b", "sequence_number": 139, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-10-26", "transaction_time": "10:43:15", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 491.8, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 10108.2, "balance_amount": 10108.2, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "0de38651-c15c-42a0-8739-36be34a66781", "sequence_number": 140, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-10-26", "transaction_time": "10:43:16", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 10108.2, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "5d4a706c-83fc-4cf0-b940-868c18018ba0", "sequence_number": 141, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-10-26", "transaction_time": "10:48:54", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 260.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 260.0, "balance_amount": 260.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "", "remark1": ""}, {"transaction_id": "ac3b2183-023c-450a-ab17-1baa8ac7d0b8", "sequence_number": 142, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-10-26", "transaction_time": "10:48:54", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 260.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "8b4ba949-9089-40b6-9675-204c821313d6", "sequence_number": 143, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-10-26", "transaction_time": "10:49:28", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 100.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 100.0, "balance_amount": 100.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "", "remark1": ""}, {"transaction_id": "9f29d40d-cd35-4067-870d-10720b2af5bf", "sequence_number": 144, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-10-26", "transaction_time": "10:49:28", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 100.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "30b9c508-1fe6-4be5-b3dc-59777fa47c3b", "sequence_number": 145, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-10-26", "transaction_time": "10:53:37", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 20.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 20.0, "balance_amount": 20.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "", "remark1": ""}, {"transaction_id": "20f6e552-511f-49f8-b5de-421e4a1aa0a5", "sequence_number": 146, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-10-26", "transaction_time": "10:53:37", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 15.99, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 4.01, "balance_amount": 4.01, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "c723ba7c-a78c-4072-bbc5-36e17ab2924e", "sequence_number": 147, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-10-29", "transaction_time": "01:49:15", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 4.01, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "d1d4d1ec-4bd9-4b98-8071-f7544c97bf53", "sequence_number": 148, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-10-30", "transaction_time": "00:08:58", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 10000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 10000.0, "balance_amount": 10000.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "", "remark1": ""}, {"transaction_id": "9116b15b-14ea-4da8-81c4-34e0058f5d79", "sequence_number": 149, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-10-30", "transaction_time": "00:11:22", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 10200.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 20200.0, "balance_amount": 20200.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "覃木坚借款", "remark1": "覃木坚借款"}, {"transaction_id": "8a5bee8f-191e-40af-92ec-988c9eaaa4b2", "sequence_number": 150, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-10-30", "transaction_time": "01:39:54", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 489.68, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 19710.32, "balance_amount": 19710.32, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "aaf70bd4-084d-4056-823e-a80ecacd5a15", "sequence_number": 151, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-10-30", "transaction_time": "01:39:54", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 19696.92, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 13.4, "balance_amount": 13.4, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "6ffb90fb-42ac-4c24-9221-cefea11c4e35", "sequence_number": 152, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-11-25", "transaction_time": "01:42:04", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 13.4, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "b3e94269-66e6-4221-b336-1ec346c34ed0", "sequence_number": 153, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-11-25", "transaction_time": "11:00:43", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 4500.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 4500.0, "balance_amount": 4500.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "", "remark1": ""}, {"transaction_id": "9e934d2f-d010-4ef3-9418-22c0c95e84f1", "sequence_number": 154, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-11-25", "transaction_time": "11:00:43", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 494.78, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 4005.22, "balance_amount": 4005.22, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "82de56c2-b395-4275-b38c-8cb7b2f19f79", "sequence_number": 155, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-11-25", "transaction_time": "11:00:43", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 4005.22, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "cd15a027-fedc-4288-8a30-9b7fbca06f85", "sequence_number": 156, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-11-25", "transaction_time": "11:01:15", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 4500.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 4500.0, "balance_amount": 4500.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "", "remark1": ""}, {"transaction_id": "a2576cee-8525-4703-8d3c-864f8ce09d76", "sequence_number": 157, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-11-25", "transaction_time": "11:01:16", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 4500.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "83a0ac96-fa12-4039-aa92-f7f3729ecf0e", "sequence_number": 158, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-11-25", "transaction_time": "11:01:48", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 2500.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 2500.0, "balance_amount": 2500.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "", "remark1": ""}, {"transaction_id": "24e5e54b-69d9-4534-b1e7-ae87e2a7cfbb", "sequence_number": 159, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-11-25", "transaction_time": "11:01:48", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 1978.97, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 521.03, "balance_amount": 521.03, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "105c03a8-8ee6-4103-854d-c672a9fb0307", "sequence_number": 160, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-11-29", "transaction_time": "02:38:34", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 27.34, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "2f4b083f-6fc9-4f18-995c-2194e76954be", "sequence_number": 161, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-11-29", "transaction_time": "02:38:34", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 493.69, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 27.34, "balance_amount": 27.34, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "648ddf5a-5d65-4283-869c-fe90d5d06ba6", "sequence_number": 162, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-11-29", "transaction_time": "14:44:53", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 19500.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 19500.0, "balance_amount": 19500.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "", "remark1": ""}, {"transaction_id": "1019f34f-fb01-4405-8316-9bc65dc8b380", "sequence_number": 163, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-11-29", "transaction_time": "14:44:53", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 19500.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "bd0e4979-4f40-4480-9710-07e98761bdd9", "sequence_number": 164, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-11-29", "transaction_time": "14:46:36", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 200.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 200.0, "balance_amount": 200.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "", "remark1": ""}, {"transaction_id": "cc8c0b96-ce66-4744-9216-211c5affa26f", "sequence_number": 165, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-11-29", "transaction_time": "14:46:37", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 169.58, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 30.42, "balance_amount": 30.42, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "3ec07a34-eb81-408e-98a4-db6a20e93eb6", "sequence_number": 166, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-12-21", "transaction_time": "00:43:24", "transaction_method": "支付利息", "transaction_type": "支付利息", "transaction_amount": 0.06, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 30.48, "balance_amount": 30.48, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "1b425f00-12a8-4517-9b90-373d907b6d0f", "sequence_number": 167, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-12-25", "transaction_time": "01:33:59", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 30.48, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "bfee129e-5380-49f0-b6ed-7a4829d17a0a", "sequence_number": 168, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-12-25", "transaction_time": "18:12:26", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 11000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 11000.0, "balance_amount": 11000.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "", "remark1": ""}, {"transaction_id": "122fb153-b7e1-49d0-bd26-8e5ec308864b", "sequence_number": 169, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-12-25", "transaction_time": "18:12:27", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 477.7, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 10522.3, "balance_amount": 10522.3, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "5effd7a5-f743-445e-b558-5a9fbc47e97e", "sequence_number": 170, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-12-25", "transaction_time": "18:12:27", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 10484.19, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 38.11, "balance_amount": 38.11, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "04d7d11d-1903-47cb-8f98-48f733429047", "sequence_number": 171, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-12-29", "transaction_time": "01:39:34", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 38.11, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "8b3a84a1-5d67-43d4-92f2-3fede55426d5", "sequence_number": 172, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-12-29", "transaction_time": "10:34:57", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 20000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 20000.0, "balance_amount": 20000.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "覃木坚借款", "remark1": "覃木坚借款"}, {"transaction_id": "92ac3a35-15e4-4f8f-b8b7-250ee8d4847b", "sequence_number": 173, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-12-29", "transaction_time": "10:34:58", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 455.58, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 19544.42, "balance_amount": 19544.42, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "b29d37ec-2949-4332-aa87-fea49b30c79b", "sequence_number": 174, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-12-29", "transaction_time": "10:34:58", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 19544.42, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "f37f0ea1-e1db-4e6b-90d9-6b6af0a689af", "sequence_number": 175, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-12-29", "transaction_time": "10:37:24", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 100.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 100.0, "balance_amount": 100.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "覃木坚借款", "remark1": "覃木坚借款"}, {"transaction_id": "6a1301ff-907b-4d2d-b69d-86bc87f26c4c", "sequence_number": 176, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-12-29", "transaction_time": "10:37:24", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 100.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "********-fe54-4765-a674-e46e69ab97bf", "sequence_number": 177, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-12-29", "transaction_time": "10:40:58", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 53.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 53.0, "balance_amount": 53.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "", "remark1": ""}, {"transaction_id": "4778c3e2-ec38-4033-be7d-f0bdc42b3014", "sequence_number": 178, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2019-12-29", "transaction_time": "10:40:58", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 52.5, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.5, "balance_amount": 0.5, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "814a355d-8aeb-4272-a9c3-e405b75c181a", "sequence_number": 179, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-01-25", "transaction_time": "01:33:15", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 0.5, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "68b58416-8165-4a3a-bdbb-ced5a9886e8a", "sequence_number": 180, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-01-25", "transaction_time": "18:41:32", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 11000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 11000.0, "balance_amount": 11000.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "", "remark1": ""}, {"transaction_id": "fd832d35-33a1-4b86-bdf0-89b5545b5557", "sequence_number": 181, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-01-25", "transaction_time": "18:41:33", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 507.68, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 10492.32, "balance_amount": 10492.32, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "ac111b15-faa0-46f2-a637-c60c331e0bc6", "sequence_number": 182, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-01-25", "transaction_time": "18:41:33", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 10484.19, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 8.13, "balance_amount": 8.13, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "0f690361-ae8a-485c-983f-c49b49178144", "sequence_number": 183, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-01-29", "transaction_time": "01:32:59", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 8.13, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "4500195b-1c03-49e7-a158-fcf1b1670d53", "sequence_number": 184, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-01-29", "transaction_time": "20:11:31", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 10000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 10000.0, "balance_amount": 10000.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "", "remark1": ""}, {"transaction_id": "aeff3e7f-e1be-489c-ad14-81f19f1ed742", "sequence_number": 185, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-01-29", "transaction_time": "20:11:31", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 485.56, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 9514.44, "balance_amount": 9514.44, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "86ae793b-9d0e-4d79-a9cd-e52125869a7c", "sequence_number": 186, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-01-29", "transaction_time": "20:11:31", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 9514.44, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "cff3dafa-8557-43ae-8874-cf7f6656b55c", "sequence_number": 187, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-01-29", "transaction_time": "23:26:02", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 9600.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 9600.0, "balance_amount": 9600.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "", "remark1": ""}, {"transaction_id": "09008b32-e83a-4c55-8fe6-ae55165416a0", "sequence_number": 188, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-01-29", "transaction_time": "23:35:12", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 600.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 10200.0, "balance_amount": 10200.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "", "remark1": ""}, {"transaction_id": "36dfd3cb-2f0c-4969-b164-875266ffe7f7", "sequence_number": 189, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-01-29", "transaction_time": "23:39:10", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 200.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 10400.0, "balance_amount": 10400.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "", "remark1": ""}, {"transaction_id": "c1932a32-a53b-43bf-932f-************", "sequence_number": 190, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-01-30", "transaction_time": "01:19:11", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 10182.48, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 217.52, "balance_amount": 217.52, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "6e4bde5a-1e7a-4b34-a57c-960de0e1e68d", "sequence_number": 191, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-02-25", "transaction_time": "01:29:19", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 217.52, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "31dfd2fa-d124-4e9f-ab59-030ae42a625a", "sequence_number": 192, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-02-26", "transaction_time": "02:43:20", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 10800.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 10800.0, "balance_amount": 10800.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "建设银行", "remark": "车贷", "remark1": "车贷"}, {"transaction_id": "a95345ff-be26-4dd2-ac91-11b4f395660e", "sequence_number": 193, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-02-26", "transaction_time": "23:06:35", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 290.66, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 10509.34, "balance_amount": 10509.34, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "f3a08ff8-37f5-47d5-93d1-815ebbff8f7b", "sequence_number": 194, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-02-26", "transaction_time": "23:06:35", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 10484.19, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 25.15, "balance_amount": 25.15, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "018b2152-488a-4fb4-b17b-9595e1c8c5f2", "sequence_number": 195, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-02-29", "transaction_time": "01:43:25", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 25.15, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "db2c969d-b10f-4802-a940-282e9f2999fb", "sequence_number": 196, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-03-04", "transaction_time": "17:33:50", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 100.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 100.0, "balance_amount": 100.0, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行总行", "remark": "", "remark1": ""}, {"transaction_id": "eecb845e-9d0d-4814-8f79-de49fcf6813c", "sequence_number": 197, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-03-04", "transaction_time": "17:33:50", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 100.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "50f50bee-5607-4360-ab89-f5de3dbcca7f", "sequence_number": 198, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-03-04", "transaction_time": "17:35:26", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 19800.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 19800.0, "balance_amount": 19800.0, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行总行", "remark": "", "remark1": ""}, {"transaction_id": "d0d7be1e-ee12-46ee-b8b0-b2e48cc6c75b", "sequence_number": 199, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-03-04", "transaction_time": "17:35:26", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 369.31, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 19430.69, "balance_amount": 19430.69, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "c9d1213f-bae8-4363-ae11-3dfaee43ca6d", "sequence_number": 200, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-03-04", "transaction_time": "17:35:27", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 19430.69, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "9ea48609-b720-4a2f-83fc-65697eeeb5c2", "sequence_number": 201, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-03-04", "transaction_time": "17:38:10", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 300.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 300.0, "balance_amount": 300.0, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行总行", "remark": "", "remark1": ""}, {"transaction_id": "94677e3c-df81-442b-a17b-100df764c9a3", "sequence_number": 202, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-03-04", "transaction_time": "17:38:10", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 293.59, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 6.41, "balance_amount": 6.41, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "b2e96d7b-b234-4d2b-957b-a5c99cdeaf99", "sequence_number": 203, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-03-05", "transaction_time": "11:49:54", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 10000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 10006.41, "balance_amount": 10006.41, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行总行", "remark": "", "remark1": ""}, {"transaction_id": "c68e4b92-e8a3-4202-a995-f1c1d8e827a5", "sequence_number": 204, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-03-05", "transaction_time": "15:29:17", "transaction_method": "车贷提前还款", "transaction_type": "车贷提前还款", "transaction_amount": 6067.3, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 3939.11, "balance_amount": 3939.11, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "3669186e-071b-4e62-8752-345bc6b44b0e", "sequence_number": 205, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-03-05", "transaction_time": "15:45:59", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 245000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 248939.11, "balance_amount": 248939.11, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行总行", "remark": "", "remark1": ""}, {"transaction_id": "9e15ea58-a594-411f-a3c5-8ed3bbff7317", "sequence_number": 206, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-03-05", "transaction_time": "15:51:50", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 290.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 249229.11, "balance_amount": 249229.11, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行总行", "remark": "", "remark1": ""}, {"transaction_id": "b41e946c-aaab-4668-b898-ac3a40a72a65", "sequence_number": 207, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-03-05", "transaction_time": "16:59:58", "transaction_method": "车贷提前还款", "transaction_type": "车贷提前还款", "transaction_amount": 248937.2, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 291.91, "balance_amount": 291.91, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "7f0bddd9-2b5f-44e8-bd88-ae00cfc65da3", "sequence_number": 208, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-03-21", "transaction_time": "00:26:57", "transaction_method": "支付利息", "transaction_type": "支付利息", "transaction_amount": 0.18, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 292.09, "balance_amount": 292.09, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "b675bbdc-9de4-47fc-802f-0a3f5ff602ef", "sequence_number": 209, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-03-25", "transaction_time": "01:53:19", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 292.09, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "43772b4d-ae70-4b4d-ad80-0651a6af2c72", "sequence_number": 210, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-03-27", "transaction_time": "13:29:50", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 10500.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 10500.0, "balance_amount": 10500.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "", "remark1": ""}, {"transaction_id": "d91db5c2-34d4-4f0f-b4f2-5fa5ce3f4ce1", "sequence_number": 211, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-03-27", "transaction_time": "13:29:50", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 216.2, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 10283.8, "balance_amount": 10283.8, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "ec487635-5336-425a-92d4-65c08ee55d4d", "sequence_number": 212, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-03-27", "transaction_time": "13:29:50", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 10283.8, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "b662c322-a7ce-46a0-8ac0-5b48f39d1529", "sequence_number": 213, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-03-27", "transaction_time": "13:31:50", "transaction_method": "移动收款", "transaction_type": "移动收款", "transaction_amount": 208.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 208.0, "balance_amount": 208.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "工商银行", "remark": "", "remark1": ""}, {"transaction_id": "fe3a815a-fe57-4fa6-9232-0adc5b9ee542", "sequence_number": 214, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-03-27", "transaction_time": "13:31:50", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 207.38, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.62, "balance_amount": 0.62, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "179f3991-9fca-44f7-af0d-d1fcf7c727fa", "sequence_number": 215, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-04-25", "transaction_time": "01:26:07", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 0.62, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "145469e9-11b7-404f-b494-eb5bf6ccbc96", "sequence_number": 216, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-04-28", "transaction_time": "14:36:36", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 10500.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 10500.0, "balance_amount": 10500.0, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行总行", "remark": "", "remark1": ""}, {"transaction_id": "aa3aa267-f5f0-4e0e-8e7f-5d77c7e9eace", "sequence_number": 217, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-04-28", "transaction_time": "14:36:37", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 508.32, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 9991.68, "balance_amount": 9991.68, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "fbaa4955-c856-431d-b161-af2b5e4d11f8", "sequence_number": 218, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-04-28", "transaction_time": "14:36:37", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 9991.68, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "43fd3b24-edc3-4590-aba5-136e3af1a51c", "sequence_number": 219, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-04-28", "transaction_time": "14:40:27", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 520.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 520.0, "balance_amount": 520.0, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行总行", "remark": "", "remark1": ""}, {"transaction_id": "68879c9e-a727-45ba-b4bd-e3cbdf46b7c2", "sequence_number": 220, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-04-28", "transaction_time": "14:40:27", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 513.47, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 6.53, "balance_amount": 6.53, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "011c71f3-c56d-4ccb-8d5d-5fbec57e17ef", "sequence_number": 221, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-05-23", "transaction_time": "10:50:37", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 10800.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 10806.53, "balance_amount": 10806.53, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行总行", "remark": "", "remark1": ""}, {"transaction_id": "a065050f-969d-4353-b73b-e81ef80625fe", "sequence_number": 222, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-05-23", "transaction_time": "10:53:01", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 80.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 10886.53, "balance_amount": 10886.53, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行总行", "remark": "", "remark1": ""}, {"transaction_id": "94749b6b-ff96-4fc2-aff4-73aad1a25a38", "sequence_number": 223, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-05-23", "transaction_time": "10:56:47", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 150.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 11036.53, "balance_amount": 11036.53, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行总行", "remark": "", "remark1": ""}, {"transaction_id": "e22fec83-1820-43c3-8bfa-0c0cdf8aef4f", "sequence_number": 224, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-05-25", "transaction_time": "01:47:54", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 10484.19, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 44.16, "balance_amount": 44.16, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "8e176533-e1f7-4412-ad95-db71bce0d4ea", "sequence_number": 225, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-05-25", "transaction_time": "01:47:54", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 508.18, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 10528.35, "balance_amount": 10528.35, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "27ed3b67-cc69-4f62-8fce-f95ac4589d70", "sequence_number": 226, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-06-21", "transaction_time": "00:54:50", "transaction_method": "支付利息", "transaction_type": "支付利息", "transaction_amount": 0.21, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 44.37, "balance_amount": 44.37, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "726bddc9-14fc-4923-b002-841afc80f553", "sequence_number": 227, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-06-25", "transaction_time": "01:28:14", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 44.37, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "c4b21641-a05f-4317-a9a3-edce9778d1ab", "sequence_number": 228, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-06-25", "transaction_time": "11:00:09", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 11500.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 11500.0, "balance_amount": 11500.0, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行总行", "remark": "", "remark1": ""}, {"transaction_id": "d05fd9b5-6682-4701-a085-49de50f7dfba", "sequence_number": 229, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-06-25", "transaction_time": "11:00:09", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 463.81, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 11036.19, "balance_amount": 11036.19, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "23263a79-a0cb-45a1-9068-6364073daadf", "sequence_number": 230, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-06-25", "transaction_time": "11:00:10", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 10484.19, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 552.0, "balance_amount": 552.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "b3a07fe3-2439-4ff6-b30a-1ef0272d64e6", "sequence_number": 231, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-07-25", "transaction_time": "01:42:51", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 508.18, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 43.82, "balance_amount": 43.82, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "b55473e4-4b1f-4f13-9b91-9dc527294e8f", "sequence_number": 232, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-07-25", "transaction_time": "01:42:51", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 43.82, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "905333c6-22b3-484e-bb85-769ca7895635", "sequence_number": 233, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-07-29", "transaction_time": "13:44:24", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 10600.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 10600.0, "balance_amount": 10600.0, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行总行", "remark": "", "remark1": ""}, {"transaction_id": "728ff06a-cd4c-4d57-b55f-8866110587ec", "sequence_number": 234, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-07-29", "transaction_time": "13:44:24", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 10468.21, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 131.79, "balance_amount": 131.79, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "956700d8-01a2-4128-914a-065c68a83027", "sequence_number": 235, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-08-25", "transaction_time": "01:31:11", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 131.79, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "7cd7291a-62a8-41dd-b5d6-0fc57f4f33d1", "sequence_number": 236, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-09-09", "transaction_time": "12:01:50", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 3000.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 3000.0, "balance_amount": 3000.0, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行总行", "remark": "", "remark1": ""}, {"transaction_id": "368d6c5f-57a0-4807-b2dd-976753ad7e1e", "sequence_number": 237, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-09-09", "transaction_time": "12:01:50", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 379.24, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 2620.76, "balance_amount": 2620.76, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "8e6c317a-0771-458b-8967-2bc6e7af1d37", "sequence_number": 238, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-09-09", "transaction_time": "12:01:51", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 2620.76, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "fed70d46-f6af-4e65-8e70-1340e57f7749", "sequence_number": 239, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-09-14", "transaction_time": "15:36:15", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 7990.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 7990.0, "balance_amount": 7990.0, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行总行", "remark": "", "remark1": ""}, {"transaction_id": "3abc3460-9458-41a5-8a0d-cdb415ac4cfb", "sequence_number": 240, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-09-14", "transaction_time": "15:36:15", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 7990.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "b553ad88-f98e-45fc-b03b-1dabcd73b67e", "sequence_number": 241, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-09-14", "transaction_time": "16:01:18", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 5.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 5.0, "balance_amount": 5.0, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行总行", "remark": "", "remark1": ""}, {"transaction_id": "d678d1ff-2a2c-4914-9a38-7e6017d3b297", "sequence_number": 242, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-09-14", "transaction_time": "16:01:18", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 4.83, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.17, "balance_amount": 0.17, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "5a1cb215-4ba2-4863-a18c-0e3499704650", "sequence_number": 243, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-09-21", "transaction_time": "00:28:12", "transaction_method": "支付利息", "transaction_type": "支付利息", "transaction_amount": 0.17, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 0.34, "balance_amount": 0.34, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "********-05b7-4adf-9f4f-b9fc126104b4", "sequence_number": 244, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-09-25", "transaction_time": "01:38:00", "transaction_method": "车贷批扣还款", "transaction_type": "车贷批扣还款", "transaction_amount": 0.34, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "批量还款", "remark1": "批量还款"}, {"transaction_id": "a314224f-4608-4043-b811-ed237db4c50c", "sequence_number": 245, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-10-21", "transaction_time": "15:35:59", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 317.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 317.0, "balance_amount": 317.0, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行", "remark": "", "remark1": ""}, {"transaction_id": "4137c80d-4d09-4cb7-ba19-a0665b5eb954", "sequence_number": 246, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-10-21", "transaction_time": "15:35:59", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 317.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "320d47ae-af12-4edf-a1b0-13d49eb29988", "sequence_number": 247, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-10-23", "transaction_time": "17:29:18", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 500.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 500.0, "balance_amount": 500.0, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行", "remark": "", "remark1": ""}, {"transaction_id": "d830781c-fcbf-49cd-9229-cb404e84f78e", "sequence_number": 248, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-10-23", "transaction_time": "17:29:18", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 197.55, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 302.45, "balance_amount": 302.45, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "b25befcc-2800-4029-b547-28c6b257d3b2", "sequence_number": 249, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-10-23", "transaction_time": "17:29:18", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 302.45, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "b3ec7be5-ecce-42ca-b2f2-d426456a8685", "sequence_number": 250, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-11-24", "transaction_time": "16:11:31", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 200.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 200.0, "balance_amount": 200.0, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行", "remark": "", "remark1": ""}, {"transaction_id": "a7805d46-4d67-4518-8707-f38e1d91b291", "sequence_number": 251, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-11-24", "transaction_time": "16:11:31", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 200.0, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "a36e0f2d-341d-457b-9388-d211d8a8a9e1", "sequence_number": 252, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-12-24", "transaction_time": "10:50:35", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 900.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 900.0, "balance_amount": 900.0, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行", "remark": "", "remark1": ""}, {"transaction_id": "27c7767d-86da-4d10-8c15-cc4ebef7082f", "sequence_number": 253, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-12-24", "transaction_time": "10:50:35", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 836.47, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 63.53, "balance_amount": 63.53, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "ff900949-824c-4cdb-9b2f-c0703ecd4fcc", "sequence_number": 254, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2020-12-24", "transaction_time": "10:50:36", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 63.53, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "bdd6e2f3-e736-4bd7-9650-f5aa5aaeed62", "sequence_number": 255, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2021-02-08", "transaction_time": "12:45:51", "transaction_method": "网银转款本金", "transaction_type": "网银转款本金", "transaction_amount": 1500.0, "transaction_direction": "收入", "dr_cr_flag": "收", "account_balance": 1500.0, "balance_amount": 1500.0, "counterparty_name": "杨蕊瑜", "counterparty_account": "****************", "counterparty_bank": "平安银行", "remark": "", "remark1": ""}, {"transaction_id": "c302983a-eff4-4265-b718-a2748d497941", "sequence_number": 256, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2021-02-08", "transaction_time": "12:45:52", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 1031.27, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 468.73, "balance_amount": 468.73, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}, {"transaction_id": "c14f989e-4b4f-405f-a70a-3cd2633bd326", "sequence_number": 257, "holder_name": "杨蕊瑜", "cardholder_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "currency": "RMB", "transaction_date": "2021-02-08", "transaction_time": "12:45:52", "transaction_method": "车贷还欠款", "transaction_type": "车贷还欠款", "transaction_amount": 468.73, "transaction_direction": "支出", "dr_cr_flag": "支", "account_balance": 0.0, "balance_amount": 0.0, "counterparty_name": "", "counterparty_account": "", "counterparty_bank": "", "remark": "", "remark1": ""}], "summary": {"total_accounts": 1, "total_transactions": 257}, "metadata": {}}, "parser_used": "pingan_format1_plugin", "final_confidence": 100.0, "confidence_details": {"pingan_format1_plugin": {"confidence": 100.0, "confidence_percentage": 100.0, "match_reason": "4维度智能评估完成 (总分:100.0/100)", "details": {"cardholder_name_score": {"score": 25.0, "max_score": 25, "description": "持卡人姓名识别", "details": "有效姓名 4/4", "percentage": 100.0}, "time_format_score": {"score": 25.0, "max_score": 25, "description": "时间格式准确性", "details": "有效日期 3/3", "percentage": 100.0}, "account_number_score": {"score": 25.0, "max_score": 25, "description": "账号或卡号识别", "details": "有效账号 4/4", "percentage": 100.0}, "amount_parsing_score": {"score": 25.0, "max_score": 25, "description": "金额解析能力", "details": "有效金额 3/3", "percentage": 100.0}, "cardholder_recognition": {"score": 25.0, "percentage": 100.0}, "time_format_accuracy": {"score": 25.0, "percentage": 100.0}, "account_recognition": {"score": 25.0, "percentage": 100.0}, "amount_parsing": {"score": 25.0, "percentage": 100.0}}, "evaluation_breakdown": {"total_score": 100.0, "max_possible_score": 100, "overall_percentage": 100.0, "account_count": 1, "transaction_count": 3}, "sample_analysis": {"sample_accounts": [{"person_name": "杨蕊瑜", "bank_name": "平安银行", "account_number": "**************", "card_number": "****************", "account_name": "杨蕊瑜的账户"}], "sample_transactions": [{"cardholder_name": "杨蕊瑜", "account_number": "**************", "transaction_date": "2018-03-29", "transaction_amount": 5.0, "balance": 5.0, "dr_cr_flag": "收", "card_number": "**************", "currency": "CNY", "transaction_method": "移动收款", "bank_name": "平安银行", "summary": "移动收款"}, {"cardholder_name": "杨蕊瑜", "account_number": "**************", "transaction_date": "2018-03-29", "transaction_amount": 657300.0, "balance": 657305.0, "dr_cr_flag": "收", "card_number": "**************", "currency": "CNY", "transaction_method": "车贷放款", "bank_name": "平安银行", "summary": "车贷放款"}, {"cardholder_name": "杨蕊瑜", "account_number": "**************", "transaction_date": "2018-03-29", "transaction_amount": 657300.0, "balance": 5.0, "dr_cr_flag": "支", "card_number": "**************", "currency": "CNY", "transaction_method": "车贷放款", "bank_name": "平安银行", "summary": "车贷放款"}], "data_extraction_success": true}, "analysis_time": "2025-08-25T16:11:14.821316"}}}