#!/usr/bin/env python3
"""
建设银行格式1解析器 - 重构版
参考工商银行成功模式，专门处理建设银行流水Excel文件
支持标准的银行流水数据结构和置信度评估
"""

import pandas as pd
import logging
from typing import Dict, List, Any, Tuple
from datetime import datetime
import re
import os

logger = logging.getLogger(__name__)

class CCBFormat1Parser:
    """建设银行格式1解析器 - 基于工商银行成功模式重构"""
    
    def __init__(self, file_path: str):
        """
        初始化解析器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.accounts = []
        self.transactions = []
        
        # 建设银行特定的字段映射
        self.field_mapping = {
            'date_field': '交易日期',
            'time_field': '交易时间', 
            'amount_field': '交易金额',
            'balance_field': '账户余额',
            'summary_field': '摘要',
            'remark_field': '扩充备注',
            'cardholder_field': '客户名称',
            'card_field': '交易卡号',
            'direction_field': '借贷方向',  # 修正：实际字段名是'借贷方向'
            'account_field': '账号',
            'opposite_account_field': '对方账号',
            'opposite_name_field': '对方户名'
        }
    
    def _clean_field(self, value) -> str:
        """清理字段值"""
        if value is None or pd.isna(value):
            return ""
        return str(value).strip().replace('\t', '').replace('\n', '').replace('\r', '')
    
    def _clean_amount_string(self, value) -> str:
        """清理金额字符串，移除空格、逗号等格式字符"""
        if value is None:
            return ""
        
        # 转换为字符串并清理各种格式字符
        clean_value = str(value).replace(" ", "").replace(",", "").replace("，", "").replace("\t", "").replace("\n", "").replace("\r", "").strip()
        
        # 移除可能的货币符号
        clean_value = clean_value.replace("¥", "").replace("$", "").replace("€", "").replace("￥", "")
        
        return clean_value
    
    def _parse_amount(self, amount_str: str, dr_cr_flag: str = "") -> float:
        """
        解析金额
        
        Args:
            amount_str: 金额字符串
            dr_cr_flag: 借贷标志
            
        Returns:
            float: 解析后的金额（负数表示支出）
        """
        if not amount_str:
            return 0.0
        
        clean_amount = self._clean_amount_string(amount_str)
        
        try:
            amount = float(clean_amount)
            
            # 根据借贷标志调整正负
            if dr_cr_flag == "借":  # 借方=支出，转为负数
                return -abs(amount)
            elif dr_cr_flag == "贷":  # 贷方=收入，保持正数
                return abs(amount)
            else:
                return amount
        except (ValueError, TypeError):
            logger.warning(f"无法解析金额: {amount_str}")
            return 0.0
    
    def _standardize_time_format(self, date_str: str, time_str: str = "") -> str:
        """
        标准化时间格式为 'YYYY-MM-DD HH:MM:SS'
        
        Args:
            date_str: 日期字符串
            time_str: 时间字符串（可选）
            
        Returns:
            str: 标准化后的时间字符串
        """
        if not date_str:
            return ""
        
        try:
            # 清理输入
            date_clean = self._clean_field(date_str)
            time_clean = self._clean_field(time_str) if time_str else ""
            
            # 如果没有单独的时间字段，尝试从日期字符串中提取
            if not time_clean and ' ' in date_clean:
                parts = date_clean.split(' ', 1)
                date_clean = parts[0]
                time_clean = parts[1] if len(parts) > 1 else ""
            
            # 解析日期
            if date_clean:
                # 尝试解析不同的日期格式
                date_obj = pd.to_datetime(date_clean).date()
                formatted_date = date_obj.strftime("%Y-%m-%d")
            else:
                formatted_date = ""
            
            # 解析时间
            if time_clean:
                # 处理时间格式（可能是 HH.MM.SS 或 HH:MM:SS）
                time_clean = time_clean.replace('.', ':')
                
                # 补充秒数
                if len(time_clean.split(':')) == 2:
                    time_clean += ":00"
                
                formatted_time = time_clean
            else:
                formatted_time = "00:00:00"
            
            return f"{formatted_date} {formatted_time}"
            
        except Exception as e:
            logger.warning(f"时间格式化失败: {date_str} {time_str}, 错误: {str(e)}")
            return date_str
    
    def _find_data_start_row(self, df: pd.DataFrame) -> int:
        """查找数据开始行"""
        for i, row in df.iterrows():
            # 寻找包含关键字段的行作为表头
            row_str = ' '.join([str(cell) for cell in row if pd.notna(cell)])
            if '交易日期' in row_str and '交易金额' in row_str:
                logger.info(f"在第{i+1}行找到数据表头")
                return i
        
        logger.warning("未找到数据表头，使用默认第0行")
        return 0
    
    def _extract_account_info(self, df: pd.DataFrame) -> Tuple[str, str, str]:
        """从DataFrame中提取账户信息"""
        account_name = ""
        account_number = ""
        card_number = ""  # 建设银行原表无卡号字段，保持空值
        
        # 从数据行中提取客户名称和账户信息
        if not df.empty:
            # 查找客户名称
            if self.field_mapping['cardholder_field'] in df.columns:
                first_valid_name = df[self.field_mapping['cardholder_field']].dropna().iloc[0] if not df[self.field_mapping['cardholder_field']].dropna().empty else ""
                account_name = self._clean_field(first_valid_name)
            
            # 查找账户号
            if self.field_mapping['account_field'] in df.columns:
                first_valid_account = df[self.field_mapping['account_field']].dropna().iloc[0] if not df[self.field_mapping['account_field']].dropna().empty else ""
                account_number = self._clean_field(first_valid_account)
            
            # 卡号字段保持空值 - 建设银行原表无卡号字段
            # 不再尝试从任何字段中提取卡号
            card_number = ""
        
        return account_name, account_number, card_number
    
    def _process_transaction_data(self, df: pd.DataFrame, account_name: str, account_number: str, card_number: str) -> None:
        """处理交易数据 - 修复：动态提取每条交易的真实持卡人信息"""
        transactions_added = 0
        
        for _, row in df.iterrows():
            # 提取交易数据
            trade_date = self._clean_field(row.get(self.field_mapping['date_field'], ''))
            trade_time = self._clean_field(row.get(self.field_mapping['time_field'], ''))
            amount_str = self._clean_field(row.get(self.field_mapping['amount_field'], ''))
            balance_str = self._clean_field(row.get(self.field_mapping['balance_field'], ''))
            summary = self._clean_field(row.get(self.field_mapping['summary_field'], ''))
            remark = self._clean_field(row.get(self.field_mapping['remark_field'], ''))
            direction = self._clean_field(row.get(self.field_mapping['direction_field'], ''))
            opposite_account = self._clean_field(row.get(self.field_mapping['opposite_account_field'], ''))
            opposite_name = self._clean_field(row.get(self.field_mapping['opposite_name_field'], ''))
            
            # 🔥 关键修复：动态提取每条交易的真实持卡人和账户信息
            current_cardholder = self._clean_field(row.get(self.field_mapping['cardholder_field'], ''))
            current_account = self._clean_field(row.get(self.field_mapping['account_field'], ''))
            
            # 如果当前行有持卡人和账户信息，使用当前行的信息；否则使用默认值
            actual_cardholder = current_cardholder if current_cardholder else account_name
            actual_account = current_account if current_account else account_number
            
            # 跳过无效行
            if not trade_date or not amount_str:
                continue
            
            # 解析金额和余额
            amount = self._parse_amount(amount_str, direction)
            balance = self._parse_amount(balance_str) if balance_str else 0.0
            
            # 处理收支符号 - 统一转换为"收/支"格式（与工商银行解析器一致）
            dr_cr_flag = "收" if direction in ["贷", "收入"] else "支"
            
            # 保持日期和时间独立显示（不合并）
            # 只有 transaction_datetime 包含完整的日期时间
            standardized_datetime = self._standardize_time_format(trade_date, trade_time)
            
            # 分离日期和时间
            formatted_date = trade_date  # 保持原始日期格式
            formatted_time = trade_time
            
            # 添加序号字段
            sequence_number = len(self.transactions) + 1
            
            # 🔥 修复：构建交易记录时使用动态提取的持卡人和账户信息
            transaction = {
                'sequence_number': sequence_number,            # 序号字段
                'transaction_datetime': standardized_datetime, # 完整日期时间（用于排序等）
                'transaction_date': formatted_date,            # 独立的日期显示
                'transaction_time': formatted_time,            # 独立的时间显示
                'transaction_amount': amount,                  # transaction_amount
                'amount': amount,                              # 保持兼容性
                'balance_amount': balance,                     # balance_amount
                'balance': balance,                            # 保持兼容性
                'transaction_method': summary,                 # transaction_method
                'summary': summary,                            # 保持兼容性
                'remark1': remark,                             # remark1
                'remark': remark,                              # 保持兼容性
                'dr_cr_flag': dr_cr_flag,                      # 统一为"收/支"格式
                'direction': direction,                        # 保持原始值兼容性
                'counterparty_account': opposite_account,      # counterparty_account
                'opposite_account': opposite_account,          # 保持兼容性
                'counterparty_name': opposite_name,            # counterparty_name
                'opposite_name': opposite_name,                # 保持兼容性
                'holder_name': actual_cardholder,              # 🔥 使用动态提取的持卡人
                'cardholder_name': actual_cardholder,          # 🔥 使用动态提取的持卡人
                'account_number': actual_account,              # 🔥 使用动态提取的账户号
                'card_number': card_number,                    # 卡号通常在汇总表中，使用默认值
                'bank_name': '中国建设银行',
                'currency': 'CNY'                              # 币种字段
            }
            
            self.transactions.append(transaction)
            transactions_added += 1
        
        logger.info(f"成功处理 {transactions_added} 条交易记录")
    
    def _process_sheet(self, sheet_name: str) -> None:
        """处理单个工作表"""
        try:
            logger.info(f"分析工作表: {sheet_name}")
            
            # 读取工作表
            df = pd.read_excel(self.file_path, sheet_name=sheet_name)
            
            if df.empty:
                logger.warning(f"工作表 {sheet_name} 为空")
                return
            
            # 查找数据开始行
            data_start_row = self._find_data_start_row(df)
            
            # 重新读取，从数据开始行作为表头
            df = pd.read_excel(self.file_path, sheet_name=sheet_name, header=data_start_row)
            
            logger.info(f"从工作表'{sheet_name}'提取到{len(df)}行数据")
            logger.info(f"检查列名: {list(df.columns)}")
            
            # 验证必要字段是否存在
            required_fields = [self.field_mapping['date_field'], self.field_mapping['amount_field']]
            missing_fields = [field for field in required_fields if field not in df.columns]
            
            if missing_fields:
                logger.error(f"工作表 {sheet_name} 缺少必需字段: {missing_fields}")
                return
            
            # 提取账户信息
            account_name, account_number, card_number = self._extract_account_info(df)
            
            # 添加账户记录（恢复简单逻辑）
            if account_name:
                account = {
                    'holder_name': account_name,
                    'cardholder_name': account_name,
                    'account_number': account_number,
                    'card_number': card_number,
                    'bank_name': '中国建设银行',
                    'account_type': '个人账户'
                }
                self.accounts.append(account)
                logger.info(f"找到账户: {account_name} - {account_number or card_number}")
            
            # 处理交易数据
            self._process_transaction_data(df, account_name, account_number, card_number)
            
            # 计算汇总数据并更新账户信息
            if account_name and self.accounts:
                # 计算汇总数据
                income_total = 0.0
                expense_total = 0.0
                start_date = None
                end_date = None
                
                # 从交易数据中计算汇总
                account_transactions = [t for t in self.transactions if t.get('account_number') == account_number]
                
                # 重新从原始数据计算汇总 - 这样可以避免处理转换后的负数问题
                for _, row in df.iterrows():
                    # 获取原始数据
                    original_date = self._clean_field(row.get(self.field_mapping['date_field'], ''))
                    original_amount_str = self._clean_field(row.get(self.field_mapping['amount_field'], ''))
                    original_direction = self._clean_field(row.get(self.field_mapping['direction_field'], ''))
                    
                    # 跳过无效行
                    if not original_date or not original_amount_str:
                        continue
                    
                    # 解析原始金额（不进行方向转换）
                    try:
                        original_amount = float(self._clean_amount_string(original_amount_str))
                    except (ValueError, TypeError):
                        continue
                    
                                        # 使用原始借贷方向计算汇总
                    if original_direction == "贷":
                        # 贷方 = 收入
                        income_total += abs(original_amount)
                    elif original_direction == "借":
                        # 借方 = 支出，包括负数（冲正）
                        expense_total += original_amount
                    
                    # 更新日期范围
                    if original_date:
                        date_part = original_date.split(' ')[0] if ' ' in original_date else original_date
                        if start_date is None or date_part < start_date:
                            start_date = date_part
                        if end_date is None or date_part > end_date:
                            end_date = date_part
                
                # 确保支出总额为正数（取绝对值）
                expense_total = abs(expense_total)
                
                # 计算净流水
                net_amount = income_total - expense_total
                
                # 更新账户信息，添加汇总数据
                account = self.accounts[-1]  # 获取刚添加的账户
                account.update({
                    # 修正汇总数据字段名，与工商银行解析器保持一致
                    'total_inflow': income_total,      # 修正：income_total -> total_inflow
                    'total_outflow': expense_total,    # 修正：expense_total -> total_outflow
                    'net_amount': net_amount,
                    'date_range': f"{start_date} 至 {end_date}" if start_date and end_date else "",  # 修正：格式化日期范围
                    'transactions_count': len(account_transactions),
                    'transactions': account_transactions
                })
                
                logger.info(f"汇总数据: 收入¥{income_total:,.2f}, 支出¥{expense_total:,.2f}, 净额¥{net_amount:,.2f}")
                logger.info(f"时间范围: {start_date} ~ {end_date}, 交易数: {len(account_transactions)}")
            
        except Exception as e:
            logger.error(f"处理工作表 {sheet_name} 时出错: {str(e)}")
    
    def parse(self) -> Dict[str, Any]:
        """
        执行解析
        
        Returns:
            Dict[str, Any]: 解析结果
        """
        try:
            logger.info(f"开始解析建设银行Format1文件: {self.file_path}")
            
            if not os.path.exists(self.file_path):
                return {
                    'success': False,
                    'error': f'文件不存在: {self.file_path}'
                }
            
            # 获取所有工作表
            excel_file = pd.ExcelFile(self.file_path)
            sheet_names = excel_file.sheet_names
            logger.info(f"发现工作表: {sheet_names}")
            
            # 处理每个工作表
            for sheet_name in sheet_names:
                self._process_sheet(sheet_name)
            
            # 计算置信度
            confidence_score = self._calculate_confidence_score(self.accounts, self.transactions)
            
            logger.info(f"解析完成，置信度: {confidence_score}%，账户数: {len(self.accounts)}，交易数: {len(self.transactions)}")
            
            return self._build_result()
            
        except Exception as e:
            logger.error(f"解析文件失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': f'解析失败: {str(e)}'
            }
    
    def _calculate_confidence_score(self, accounts: List[Dict], transactions: List[Dict]) -> float:
        """计算置信度分数"""
        score = 0.0
        
        # 基础分数：找到数据
        if accounts:
            score += 20
        if transactions:
            score += 20
        
        # 字段完整性评分
        if accounts:
            account = accounts[0]
            if account.get('holder_name'):
                score += 15
            if account.get('account_number') or account.get('card_number'):
                score += 10
        
        # 交易数据质量评分
        if transactions:
            valid_transactions = 0
            for trans in transactions[:10]:  # 检查前10条
                if trans.get('transaction_date') and trans.get('amount'):
                    valid_transactions += 1
            
            if valid_transactions >= 5:
                score += 15
            elif valid_transactions >= 2:
                score += 10
            else:
                score += 5
        
        return min(score, 100.0)
    
    def _build_result(self) -> Dict[str, Any]:
        """构建返回结果 - 修复：按账户分组交易，参考CCB Format 2和ICBC解析器的成功模式"""
        confidence_score = self._calculate_confidence_score(self.accounts, self.transactions)
        
        # 🔥 关键修复：按账户分组交易记录，避免硬编码问题
        transactions_by_account = {}
        corrected_accounts = []
        
        # 为每条交易分配序号并按账户分组
        for index, transaction in enumerate(self.transactions, 1):
            transaction['sequence_number'] = index
            
            # 🔧 使用持卡人姓名+账号作为分组键，确保多持卡人正确分组
            cardholder_name = transaction['cardholder_name']
            account_number = transaction['account_number']
            group_key = f"{cardholder_name}_{account_number}" if account_number else cardholder_name
            
            if group_key not in transactions_by_account:
                transactions_by_account[group_key] = []
                
                # 🔧 为每个账户创建独立的账户记录
                account_data = {
                    'account_number': account_number,
                    'card_number': transaction['card_number'],
                    'holder_name': cardholder_name,
                    'cardholder_name': cardholder_name,  # 前端期望的字段名
                    'bank_name': '中国建设银行',
                    'account_type': '个人账户',
                    
                    # 统计信息（将在后续计算）
                    'transactions_count': 0,
                    'total_inflow': 0.0,
                    'total_outflow': 0.0,
                    'date_range': '',
                    
                    # 兼容字段
                    'account_id': account_number,
                    'account_name': cardholder_name,
                    'currency': 'CNY',
                    'is_primary': len(corrected_accounts) == 0
                }
                corrected_accounts.append(account_data)
                logger.info(f"创建账户记录: {cardholder_name} - {account_number}")
            
            # 🔥 关键：将交易记录分配给正确的账户
            transactions_by_account[group_key].append(transaction)
        
        # 🔧 计算每个账户的统计信息
        for account in corrected_accounts:
            cardholder_name = account['cardholder_name']
            account_number = account['account_number']
            group_key = f"{cardholder_name}_{account_number}" if account_number else cardholder_name
            account_transactions = transactions_by_account.get(group_key, [])
            
            # 计算交易笔数
            account['transactions_count'] = len(account_transactions)
            
            # 计算收支统计
            total_inflow = sum(t['transaction_amount'] for t in account_transactions if t['dr_cr_flag'] == '收')
            total_outflow = sum(t['transaction_amount'] for t in account_transactions if t['dr_cr_flag'] == '支')
            
            account['total_inflow'] = total_inflow
            account['total_outflow'] = total_outflow
            
            # 计算日期范围
            if account_transactions:
                dates = [t['transaction_date'] for t in account_transactions if t['transaction_date']]
                if dates:
                    min_date = min(dates)
                    max_date = max(dates)
                    account['date_range'] = f"{min_date} 至 {max_date}"
            
            logger.info(f"账户 {cardholder_name} ({account_number}): {account['transactions_count']}笔交易，收入¥{total_inflow:.2f}，支出¥{total_outflow:.2f}")
        
        # 构建汇总信息（保持与前端兼容）
        summary = {
            'total_accounts': len(corrected_accounts),
            'total_transactions': len(self.transactions),
            'confidence_score': confidence_score
        }
        
        # 如果有账户信息，添加第一个账户的汇总信息（为了保持兼容性）
        if corrected_accounts:
            first_account = corrected_accounts[0]
            summary.update({
                'account_name': first_account.get('cardholder_name', ''),
                'account_number': first_account.get('account_number', ''),
                'card_number': '',  # 建设银行原表无卡号，强制为空字符串
                'total_inflow': first_account.get('total_inflow', 0.0),
                'total_outflow': first_account.get('total_outflow', 0.0),
                'date_range': first_account.get('date_range', '')
            })
        
        # 计算全局统计信息
        total_income = sum(t['transaction_amount'] for t in self.transactions if t['dr_cr_flag'] == '收')
        total_expense = sum(t['transaction_amount'] for t in self.transactions if t['dr_cr_flag'] == '支')
        
        logger.info(f"解析完成: {len(corrected_accounts)}个账户，{len(self.transactions)}条交易记录")
        
        # 🔥 返回修复后的结构，包含transactions_by_account
        return {
            'success': True,
            'message': f'成功解析 {len(corrected_accounts)} 个账户，{len(self.transactions)} 条交易记录',
            'summary': summary,  # 保持兼容性
            'accounts': corrected_accounts,                    # 🔥 使用修正后的账户列表
            'transactions': self.transactions,                 # 保持所有交易记录用于兼容性
            'transactions_by_account': transactions_by_account, # 🔥 关键：按账户分组的交易记录
            'statistics': {
                'total_accounts': len(corrected_accounts),
                'total_transactions': len(self.transactions),
                'total_income': total_income,
                'total_expense': total_expense,
                'net_flow': total_income - total_expense
            },
            'confidence': confidence_score,  # 前端期望的字段名
            'metadata': {
                'total_accounts': len(corrected_accounts),
                'total_transactions': len(self.transactions),
                'confidence_score': confidence_score,
                'parser_name': '建设银行格式1解析器(修复版)',
                'file_path': self.file_path,
                'parse_time': datetime.now().isoformat()
            },
            'data': {
                'accounts': corrected_accounts,
                'transactions_by_account': transactions_by_account  # 🔥 前端使用这个字段来显示账户特定的交易
            }
        }
    
    def extract_sample(self, limit: int = 10) -> Dict[str, Any]:
        """
        提取样本数据进行快速评估
        
        Args:
            limit: 限制返回的交易数量
            
        Returns:
            Dict[str, Any]: 样本数据
        """
        try:
            logger.info(f"建设银行解析器开始提取样本数据，限制条数: {limit}")
            
            # 执行完整解析
            result = self.parse()
            
            if not result.get('success'):
                logger.warning(f"解析失败，无法提取样本: {result.get('error')}")
                return {}
            
            # 获取样本数据
            accounts = result.get('accounts', [])
            all_transactions = result.get('transactions', [])
            
            # 限制交易数量
            sample_transactions = all_transactions[:limit] if all_transactions else []
            
            logger.info(f"成功提取样本数据: {len(accounts)}个账户, {len(sample_transactions)}条交易")
            
            return {
                'accounts': accounts,
                'transactions': sample_transactions,
                'metadata': {
                    'total_accounts': len(accounts),
                    'total_transactions': len(all_transactions),
                    'sample_size': len(sample_transactions)
                }
            }
            
        except Exception as e:
            logger.error(f"提取样本数据失败: {str(e)}")
            return {} 