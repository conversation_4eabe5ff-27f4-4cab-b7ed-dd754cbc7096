"""
银行账户查询API
提供账户数据查询和管理功能
"""
from fastapi import APIRouter, Query, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime, date
import logging

from ..middleware.auth_middleware import get_current_user, get_user_db
from ..models.duckdb_models import DuckDBAccount as Account, DuckDBTransaction as Transaction

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/")
async def get_accounts(
    project_id: str = Query(..., description="项目ID"),
    holder_name: Optional[str] = Query(None, description="持卡人姓名"),
    account_number: Optional[str] = Query(None, description="账号"),
    card_number: Optional[str] = Query(None, description="卡号"),
    bank_name: Optional[str] = Query(None, description="银行名称"),
    start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD"),
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> Dict[str, Any]:
    """
    查询项目的账户数据
    
    Args:
        project_id: 项目ID
        holder_name: 持卡人姓名（可选）
        account_number: 账号（可选）
        card_number: 卡号（可选）
        bank_name: 银行名称（可选）
        start_date: 开始日期（可选）
        end_date: 结束日期（可选）
        current_user: 当前用户
        db: 用户专属数据库会话
        
    Returns:
        Dict: 账户列表数据
    """
    try:
        logger.info(f"用户 '{current_user}' 查询账户数据，项目ID: {project_id}")
        
        # 构建查询条件
        query = db.query(Account).filter(Account.project_id == project_id)
        
        if holder_name:
            query = query.filter(Account.person_name.ilike(f"%{holder_name}%"))
        
        if account_number:
            query = query.filter(Account.account_number.ilike(f"%{account_number}%"))
        
        if card_number:
            query = query.filter(Account.card_number.ilike(f"%{card_number}%"))
        
        if bank_name:
            query = query.filter(Account.bank_name.ilike(f"%{bank_name}%"))
        
        if start_date:
            query = query.filter(Account.creation_timestamp >= start_date)
        
        if end_date:
            query = query.filter(Account.creation_timestamp <= end_date)
        
        # 执行查询
        accounts = query.all()
        
        # 转换为前端需要的格式
        result_accounts = []
        for account in accounts:
            # 计算该账户的交易统计（当卡号为空时，不使用卡号过滤，避免误判为0）
            tx_query = db.query(Transaction).filter(
                Transaction.project_id == project_id,
                Transaction.account_id == account.account_id,
                Transaction.account_number == account.account_number
            )
            if account.card_number not in (None, "", " ", "-"):
                tx_query = tx_query.filter(Transaction.card_number == account.card_number)
            transactions = tx_query.all()

            # 计算收入和支出（优先依据借贷标识；无标识时按正负号回退）
            def _is_inflow(t):
                flag = (t.dr_cr_flag or "").strip().upper()
                if flag in ("收", "D", "借", "IN"):
                    return True
                if flag in ("支", "C", "贷", "OUT"):
                    return False
                return float(t.transaction_amount or 0) >= 0
            total_inflow = sum(abs(float(t.transaction_amount or 0)) for t in transactions if _is_inflow(t))
            total_outflow = sum(abs(float(t.transaction_amount or 0)) for t in transactions if not _is_inflow(t))
            
            # 计算时间范围
            if transactions:
                dates = []
                for t in transactions:
                    if t.transaction_datetime:
                        # 只提取日期部分，不包含时间
                        if " " in t.transaction_datetime:
                            date_part = t.transaction_datetime.split(" ")[0]
                        else:
                            date_part = t.transaction_datetime
                        dates.append(date_part)
                
                if dates:
                    min_date = min(dates)
                    max_date = max(dates)
                    if min_date == max_date:
                        date_range = min_date
                    else:
                        date_range = f"{min_date} 至 {max_date}"
                else:
                    date_range = "未知"
            else:
                date_range = "未知"
            
            account_data = {
                "account_id": account.account_id,
                "account_number": account.account_number,
                "holder_name": account.person_name,
                "bank_name": account.bank_name,
                "card_number": account.card_number,
                "transactions_count": len(transactions),
                "total_inflow": total_inflow,
                "total_outflow": total_outflow,
                "date_range": date_range,
                "created_at": account.creation_timestamp if account.creation_timestamp else None
            }
            result_accounts.append(account_data)
        
        logger.info(f"用户 '{current_user}' 查询到{len(result_accounts)}个账户")
        
        return {
            "accounts": result_accounts,
            "total": len(result_accounts),
            "project_id": project_id,
            "filters": {
                "holder_name": holder_name,
                "account_number": account_number,
                "bank_name": bank_name,
                "start_date": start_date,
                "end_date": end_date
            },
            "message": f"查询成功，找到{len(result_accounts)}个账户"
        }
        
    except Exception as e:
        logger.error(f"用户 '{current_user}' 查询账户数据失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"查询账户数据失败: {str(e)}"
        )

@router.get("/transactions")
async def get_account_transactions(
    account_number: str = Query(..., description="账号"),
    project_id: str = Query(..., description="项目ID"),
    card_number: str = Query(None, description="卡号，可选但建议传入"),
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> Dict[str, Any]:
    """
    查询指定账户的交易明细
    
    Args:
        account_number: 账号
        project_id: 项目ID
        current_user: 当前用户
        db: 用户专属数据库会话
        
    Returns:
        Dict: 交易明细数据
    """
    try:
        logger.info(f"用户 '{current_user}' 查询账户交易明细，账号: {account_number}, 项目ID: {project_id}")
        
        # 查找账户
        account = db.query(Account).filter(
            Account.account_number == account_number,
            Account.project_id == project_id
        ).first()
        
        if not account:
            raise HTTPException(
                status_code=404,
                detail=f"未找到账号为 {account_number} 的账户"
            )
        
        # 查询交易记录（按三元组过滤）
        tx_query = db.query(Transaction).filter(
            Transaction.account_id == account.account_id,
            Transaction.account_number == account.account_number
        )
        if card_number:
            tx_query = tx_query.filter(Transaction.card_number == card_number)
        transactions = tx_query.order_by(Transaction.transaction_datetime.desc()).all()
        
        # 转换为前端需要的格式
        result_transactions = []
        for transaction in transactions:
            # 从transaction_datetime中提取日期和时间
            transaction_datetime = transaction.transaction_datetime or ""
            if " " in transaction_datetime:
                transaction_date, transaction_time = transaction_datetime.split(" ", 1)
            else:
                transaction_date = transaction_datetime
                transaction_time = ""
            
            transaction_data = {
                "transaction_id": transaction.transaction_id,
                "sequence_number": getattr(transaction, 'sequence_number', ''),
                "holder_name": transaction.person_name,  # 保持原字段名
                "cardholder_name": transaction.person_name,  # 🔧 修复：添加前端期望的字段名
                "bank_name": transaction.bank_name,
                "account_number": transaction.account_number,
                "card_number": transaction.card_number,
                "transaction_date": transaction_date,
                "transaction_time": transaction_time,
                "transaction_method": transaction.transaction_method,
                "transaction_amount": transaction.transaction_amount,
                "balance_amount": transaction.balance,
                "dr_cr_flag": transaction.dr_cr_flag,
                "counterparty_name": transaction.counterparty_account_name,
                "counterparty_account": transaction.counterparty_account_number,
                "counterparty_bank": transaction.counterparty_bank_name,
                "remark1": transaction.remarks,
                "remark2": "",
                "remark3": ""
            }
            result_transactions.append(transaction_data)
        
        logger.info(f"用户 '{current_user}' 查询到{len(result_transactions)}条交易记录")
        
        return {
            "account": {
                "account_id": account.account_id,
                "account_number": account.account_number,
                "holder_name": account.person_name,
                "bank_name": account.bank_name,
                "card_number": account.card_number
            },
            "transactions": result_transactions,
            "total": len(result_transactions),
            "message": f"查询成功，找到{len(result_transactions)}条交易记录"
        }
        
    except Exception as e:
        logger.error(f"用户 '{current_user}' 查询交易明细失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"查询交易明细失败: {str(e)}"
        )

@router.delete("/{account_id}")
async def delete_account(
    account_id: str,
    account_number: Optional[str] = Query(None, description="账号，可选"),
    card_number: Optional[str] = Query(None, description="卡号，可选"),
    db: Session = Depends(get_user_db)
) -> Dict[str, Any]:
    """
    删除指定账户及其所有交易记录
    
    Args:
        account_id: 账户ID
        db: 数据库会话
        
    Returns:
        Dict: 删除结果
    """
    try:
        logger.info(f"删除账户，账户ID: {account_id}")
        
        # 查找账户
        account = db.query(Account).filter(Account.account_id == account_id).first()
        
        if not account:
            raise HTTPException(
                status_code=404,
                detail=f"未找到账户ID为 {account_id} 的账户"
            )
        
        # 查找并删除相关交易记录（若提供卡号/账号则精确限定）
        tx_query = db.query(Transaction).filter(Transaction.account_id == account_id)
        if account_number:
            tx_query = tx_query.filter(Transaction.account_number == account_number)
        if card_number:
            tx_query = tx_query.filter(Transaction.card_number == card_number)
        transactions = tx_query.all()
        deleted_transactions = len(transactions)
        
        for transaction in transactions:
            db.delete(transaction)
        
        # 如果该账户下仍有其它交易（不同卡号），则保留账户记录
        remaining = db.query(Transaction).filter(Transaction.account_id == account_id).count()
        if remaining == 0:
            db.delete(account)
        db.commit()
        
        logger.info(f"成功删除账户及{deleted_transactions}条交易记录")
        
        return {
            "success": True,
            "account_id": account_id,
            "deleted_transactions": deleted_transactions,
            "message": f"成功删除账户及{deleted_transactions}条交易记录"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除账户失败: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"删除账户失败: {str(e)}"
        )

@router.post("/check-duplicates")
async def check_duplicate_accounts(
    accounts: List[Dict[str, str]],
    db: Session = Depends(get_user_db)
) -> Dict[str, Any]:
    """
    检查重复账户
    
    Args:
        accounts: 要检查的账户列表
        db: 数据库会话
        
    Returns:
        Dict: 重复检查结果
    """
    try:
        logger.info(f"检查{len(accounts)}个账户的重复情况")
        
        duplicates = []
        unique = []
        
        for account in accounts:
            holder_name = account.get('holder_name')
            account_number = account.get('account_number')
            card_number = account.get('card_number')
            
            # 查询数据库中是否存在相同的账户
            existing = db.query(Account).filter(
                Account.person_name == holder_name,
                Account.account_number == account_number,
                Account.card_number == card_number
            ).first()
            
            if existing:
                duplicates.append({
                    "holder_name": holder_name,
                    "account_number": account_number,
                    "card_number": card_number,
                    "existing_account_id": existing.account_id
                })
            else:
                unique.append(account)
        
        logger.info(f"发现{len(duplicates)}个重复账户，{len(unique)}个唯一账户")
        
        return {
            "duplicates": duplicates,
            "unique": unique,
            "total_checked": len(accounts),
            "duplicate_count": len(duplicates),
            "unique_count": len(unique),
            "message": f"检查完成，发现{len(duplicates)}个重复账户"
        }
        
    except Exception as e:
        logger.error(f"检查重复账户失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"检查重复账户失败: {str(e)}"
        ) 