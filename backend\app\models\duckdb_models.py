"""
DuckDB专用数据模型定义
针对DuckDB的特性进行优化
"""
import uuid
from datetime import datetime
from sqlalchemy import Column, String, Float, DateTime, JSON, Boolean, Text, Index
from sqlalchemy.orm import declarative_base

# 创建DuckDB专用基类
DuckDBBase = declarative_base()


class DuckDBBank(DuckDBBase):
    """银行信息表 - DuckDB版本"""
    __tablename__ = "banks"

    bank_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    bank_name = Column(String, nullable=False, unique=True, index=True)
    is_predefined = Column(Boolean, default=False)
    is_enabled = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class DuckDBProject(DuckDBBase):
    """项目表 - DuckDB版本"""
    __tablename__ = "projects"

    project_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    project_name = Column(String, nullable=False, index=True)
    person_name = Column(String, nullable=True)
    db_path = Column(String, nullable=True)  # 保留兼容
    is_active = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    description = Column(Text, nullable=True)


class DuckDBParsingTemplate(DuckDBBase):
    """解析模板表 - DuckDB版本（简化外键关系）"""
    __tablename__ = "parsing_templates"

    template_id = Column(String, primary_key=True)
    bank_name = Column(String, nullable=False, index=True)  # 移除外键约束
    account_type = Column(String, nullable=True)
    file_type = Column(JSON, nullable=False)
    template_config = Column(JSON, nullable=False)
    is_default = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class DuckDBAccount(DuckDBBase):
    """账户表 - DuckDB版本"""
    __tablename__ = "accounts"

    account_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    project_id = Column(String, nullable=False, index=True)  # 移除外键约束
    person_name = Column(String, nullable=False, index=True)
    bank_name = Column(String, nullable=False)
    account_name = Column(String, nullable=True)
    account_number = Column(String, nullable=True, index=True)
    card_number = Column(String, nullable=True, index=True)
    import_file_source = Column(String, nullable=True)
    creation_timestamp = Column(String, nullable=False)
    updated_at = Column(DateTime, nullable=True, onupdate=datetime.utcnow)


class DuckDBRawTransaction(DuckDBBase):
    """原始交易流水表 - DuckDB版本"""
    __tablename__ = "raw_transactions"

    raw_transaction_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    account_id = Column(String, nullable=False, index=True)  # 移除外键约束
    project_id = Column(String, nullable=False, index=True)  # 移除外键约束
    
    # 基本信息
    person_name = Column(String, nullable=False)
    bank_name = Column(String, nullable=False)
    account_name = Column(String, nullable=True)
    account_number = Column(String, nullable=True, index=True)
    card_number = Column(String, nullable=True, index=True)
    
    # 原始交易数据
    raw_transaction_date = Column(String, nullable=True)
    raw_transaction_time = Column(String, nullable=True)
    raw_transaction_method = Column(String, nullable=True)
    raw_amount_debit = Column(String, nullable=True)
    raw_amount_credit = Column(String, nullable=True)
    raw_amount_single = Column(String, nullable=True)
    raw_sign_keyword = Column(String, nullable=True)
    raw_balance = Column(String, nullable=True)
    raw_counterparty_account = Column(String, nullable=True)
    raw_counterparty_name = Column(String, nullable=True)
    raw_counterparty_bank = Column(String, nullable=True)
    raw_remarks = Column(Text, nullable=True)
    raw_other_fields = Column(JSON, nullable=True)
    
    # 元数据
    import_file_source = Column(String, nullable=True)
    import_timestamp = Column(String, nullable=False)


class DuckDBTransaction(DuckDBBase):
    """交易流水表 - DuckDB版本"""
    __tablename__ = "transactions"

    transaction_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    raw_transaction_id = Column(String, nullable=True, index=True)  # 移除外键约束
    account_id = Column(String, nullable=False, index=True)  # 移除外键约束
    project_id = Column(String, nullable=False, index=True)  # 移除外键约束
    
    # 冗余字段
    person_name = Column(String, nullable=False)
    bank_name = Column(String, nullable=False)
    account_name = Column(String, nullable=True)
    account_number = Column(String, nullable=True, index=True)
    card_number = Column(String, nullable=True, index=True)
    
    # 交易数据
    transaction_datetime = Column(String, nullable=False, index=True)
    transaction_method = Column(String, nullable=True)
    transaction_amount = Column(Float, nullable=False)
    balance = Column(Float, nullable=True)
    dr_cr_flag = Column(String, nullable=True)
    counterparty_account_number = Column(String, nullable=True)
    counterparty_account_name = Column(String, nullable=True)
    counterparty_bank_name = Column(String, nullable=True)
    remarks = Column(Text, nullable=True)
    original_currency = Column(String, default="CNY")
    raw_data_snapshot = Column(JSON, nullable=True)
    
    # 清洗信息
    cleaning_rules_applied = Column(JSON, nullable=True)
    cleaning_timestamp = Column(String, nullable=True)


class DuckDBClue(DuckDBBase):
    """问题线索表 - DuckDB版本"""
    __tablename__ = "case_clues"

    clue_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    project_id = Column(String, nullable=False, index=True)  # 移除外键约束
    clue_number = Column(String, nullable=False, index=True)  # 线索编号
    
    # 被反映人信息
    subject_name = Column(String, nullable=False, index=True)  # 被反映人姓名
    subject_position = Column(String, nullable=True)  # 岗位职务
    
    # 线索信息
    source = Column(String, nullable=True)  # 线索来源
    content = Column(Text, nullable=False)  # 线索内容
    attachments = Column(JSON, nullable=True)  # 附件信息
    
    # 元数据
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = Column(String, nullable=True)  # 创建人
    updated_by = Column(String, nullable=True)  # 更新人


class DuckDBSubject(DuckDBBase):
    """被反映人资料表 - DuckDB版本"""
    __tablename__ = "case_subjects"

    subject_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    project_id = Column(String, nullable=False, index=True)
    
    # 基本信息
    name = Column(String, nullable=False, index=True)
    gender = Column(String, nullable=True)
    birth_date = Column(String, nullable=True)
    nationality = Column(String, nullable=True)
    native_place = Column(String, nullable=True)
    birth_place = Column(String, nullable=True)
    id_number = Column(String, nullable=True, index=True)
    
    # 工作信息
    work_start_date = Column(String, nullable=True)
    education = Column(String, nullable=True)
    major = Column(String, nullable=True)
    degree = Column(String, nullable=True)
    professional_title = Column(String, nullable=True)
    current_position = Column(String, nullable=True)
    work_unit = Column(String, nullable=True)
    
    # 简历信息
    resume = Column(Text, nullable=True)
    
    # 联系信息
    phone = Column(String, nullable=True)
    address = Column(String, nullable=True)
    
    # 其他信息
    political_status = Column(String, nullable=True)
    marital_status = Column(String, nullable=True)
    notes = Column(Text, nullable=True)
    
    # 元数据
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = Column(String, nullable=True)
    updated_by = Column(String, nullable=True)


class DuckDBRelatedPerson(DuckDBBase):
    """相关人员资料表 - DuckDB版本"""
    __tablename__ = "case_related_persons"

    person_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    project_id = Column(String, nullable=False, index=True)
    
    # 基本信息
    name = Column(String, nullable=False, index=True)
    id_number = Column(String, nullable=True, index=True)
    role = Column(String, nullable=False)  # 嫌疑人、证人、被害人、其他相关方
    gender = Column(String, nullable=True)
    birthday = Column(String, nullable=True)
    phone = Column(String, nullable=True)
    address = Column(String, nullable=True)
    notes = Column(Text, nullable=True)
    
    # 元数据
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class DuckDBAsset(DuckDBBase):
    """财产信息表 - DuckDB版本"""
    __tablename__ = "case_assets"

    asset_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    project_id = Column(String, nullable=False, index=True)
    
    # 基本信息
    name = Column(String, nullable=False)
    type = Column(String, nullable=False, index=True)  # 房产、车辆、银行账户等
    owner = Column(String, nullable=False, index=True)
    value = Column(Float, nullable=True)
    status = Column(String, nullable=True)  # 已查封、已冻结等
    location = Column(String, nullable=True)
    identification_number = Column(String, nullable=True)  # 房产证号、车牌号等
    acquisition_date = Column(String, nullable=True)
    acquisition_method = Column(String, nullable=True)
    
    # 自定义字段
    custom_field1 = Column(String, nullable=True)
    custom_field2 = Column(String, nullable=True)
    custom_field3 = Column(Text, nullable=True)
    
    # 详细信息
    detail = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)
    
    # 元数据
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class DuckDBRelatedUnit(DuckDBBase):
    """相关单位情况表 - DuckDB版本"""
    __tablename__ = "case_related_units"

    unit_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    project_id = Column(String, nullable=False, index=True)
    
    # 基本信息
    name = Column(String, nullable=False, index=True)
    legal_representative = Column(String, nullable=True)
    registered_capital = Column(Float, nullable=True)
    establishment_date = Column(String, nullable=True)
    business_scope = Column(Text, nullable=True)
    registered_address = Column(String, nullable=True)
    contact_info = Column(String, nullable=True)
    relationship_description = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)
    
    # 元数据
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class DuckDBRelationship(DuckDBBase):
    """人物关系表 - DuckDB版本"""
    __tablename__ = "case_relationships"

    relationship_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    project_id = Column(String, nullable=False, index=True)
    
    # 关系双方
    person_a = Column(String, nullable=False, index=True)
    person_b = Column(String, nullable=False, index=True)
    relationship_type = Column(String, nullable=False)  # 夫妻、朋友、同事等
    status = Column(String, nullable=True)  # 已确认、疑似、待核实
    description = Column(Text, nullable=True)
    evidence = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)
    
    # 元数据
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


# 为提高查询性能创建组合索引
def create_duckdb_indexes(engine):
    """创建DuckDB特定的索引"""
    indexes = [
        # 账户查询索引
        Index('idx_accounts_project_person', DuckDBAccount.project_id, DuckDBAccount.person_name),
        Index('idx_accounts_bank_number', DuckDBAccount.bank_name, DuckDBAccount.account_number),
        
        # 交易查询索引
        Index('idx_transactions_account_date', DuckDBTransaction.account_id, DuckDBTransaction.transaction_datetime),
        Index('idx_transactions_project_date', DuckDBTransaction.project_id, DuckDBTransaction.transaction_datetime),
        Index('idx_transactions_amount', DuckDBTransaction.transaction_amount),
        
        # 原始交易索引
        Index('idx_raw_transactions_account', DuckDBRawTransaction.account_id, DuckDBRawTransaction.import_timestamp),
        Index('idx_raw_transactions_project', DuckDBRawTransaction.project_id, DuckDBRawTransaction.import_timestamp),
        
        # 模板查询索引
        Index('idx_templates_bank', DuckDBParsingTemplate.bank_name, DuckDBParsingTemplate.is_default),
    ]
    
    # 创建索引
    for idx in indexes:
        try:
            idx.create(engine, checkfirst=True)
            print(f"✅ 创建索引: {idx.name}")
        except Exception as e:
            print(f"⚠️ 索引创建失败 {idx.name}: {e}")


def get_duckdb_table_mappings():
    """获取DuckDB表的映射关系，用于数据迁移"""
    return {
        'banks': DuckDBBank,
        'projects': DuckDBProject,
        'parsing_templates': DuckDBParsingTemplate,
        'accounts': DuckDBAccount,
        'raw_transactions': DuckDBRawTransaction,
        'transactions': DuckDBTransaction,
        'case_clues': DuckDBClue,
        'case_subjects': DuckDBSubject,
        'case_related_persons': DuckDBRelatedPerson,
        'case_assets': DuckDBAsset,
        'case_related_units': DuckDBRelatedUnit,
        'case_relationships': DuckDBRelationship,
    }


# 为了兼容性，提供简化的别名
Base = DuckDBBase
Bank = DuckDBBank
Project = DuckDBProject
ParsingTemplate = DuckDBParsingTemplate
Account = DuckDBAccount
RawTransaction = DuckDBRawTransaction
Transaction = DuckDBTransaction
Clue = DuckDBClue 
Subject = DuckDBSubject
RelatedPerson = DuckDBRelatedPerson
Asset = DuckDBAsset
RelatedUnit = DuckDBRelatedUnit
Relationship = DuckDBRelationship 