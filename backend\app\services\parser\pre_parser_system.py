"""
预解析系统 - 分析文件并选择最佳Enhanced解析器
"""
import os
import logging
from typing import Dict, List, Any, Optional
import pandas as pd

from .feature_extractor import BankFileFeatureExtractor
from .confidence_evaluator import ConfidenceEvaluator
from .icbc_format3_standard import ICBCFormat3StandardParser
from .icbc_format4_enhanced import ICBCFormat4EnhancedParser
from .icbc_format1_enhanced import ICBCFormat1EnhancedParser

logger = logging.getLogger(__name__)

class PreParserSystem:
    """
    预解析系统 - 分析文件特征并选择最佳Enhanced解析器
    """
    
    def __init__(self):
        """初始化预解析系统"""
        self.feature_extractor = BankFileFeatureExtractor()
        self.confidence_evaluator = ConfidenceEvaluator()
        
        # 注册Enhanced解析器
        self.enhanced_parsers = [
            {
                "name": "ICBCFormat3StandardParser",
                "class": ICBCFormat3StandardParser,
                "description": "工商银行格式3标准解析器",
                "supported_extensions": [".xls", ".xlsx"],
                "bank_name": "中国工商银行"
            },
            {
                "name": "ICBCFormat4EnhancedParser", 
                "class": ICBCFormat4EnhancedParser,
                "description": "工商银行格式4增强解析器",
                "supported_extensions": [".xlsx"],
                "bank_name": "中国工商银行"
            },
            {
                "name": "ICBCFormat1EnhancedParser",
                "class": ICBCFormat1EnhancedParser,
                "description": "工商银行格式1增强解析器",
                "supported_extensions": [".xls", ".xlsx"],
                "bank_name": "中国工商银行"
            }
        ]
    
    def select_best_parser(self, file_path: str) -> Dict[str, Any]:
        """
        为文件选择最佳Enhanced解析器
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 包含最佳解析器信息的结果
        """
        result = {
            "file_path": file_path,
            "file_analysis": {},
            "parser_scores": [],
            "best_parser": None,
            "fallback_parsers": []
        }
        
        try:
            # 提取文件特征
            logger.info(f"分析文件: {os.path.basename(file_path)}")
            features = self.feature_extractor.extract_features(file_path)
            result["file_analysis"] = features
            
            detected_bank = features.get("detected_bank", "未知")
            file_ext = os.path.splitext(file_path.lower())[1]
            
            logger.info(f"检测到银行: {detected_bank}, 文件类型: {file_ext}")
            
            # 评估每个Enhanced解析器的适用性
            parser_scores = []
            
            for parser_info in self.enhanced_parsers:
                score = self._evaluate_parser_compatibility(parser_info, features, file_ext)
                parser_scores.append({
                    "name": parser_info["name"],
                    "class": parser_info["class"],
                    "confidence": score,
                    "description": parser_info["description"]
                })
                
                logger.info(f"解析器 {parser_info['name']} 评分: {score:.2f}")
            
            # 按评分排序
            parser_scores.sort(key=lambda x: x["confidence"], reverse=True)
            result["parser_scores"] = parser_scores
            
            # 选择最佳解析器
            if parser_scores and parser_scores[0]["confidence"] > 0.5:
                result["best_parser"] = parser_scores[0]
                result["fallback_parsers"] = parser_scores[1:3]  # 保留前3个作为备选
                
                logger.info(f"选择解析器: {parser_scores[0]['name']} (置信度: {parser_scores[0]['confidence']:.2f})")
            else:
                logger.warning("未找到合适的解析器，置信度过低")
            
            return result
            
        except Exception as e:
            logger.error(f"预解析系统出错: {str(e)}")
            result["error"] = str(e)
            return result
    
    def _evaluate_parser_compatibility(self, parser_info: Dict[str, Any], 
                                     features: Dict[str, Any], file_ext: str) -> float:
        """
        评估解析器与文件的兼容性
        
        Args:
            parser_info: 解析器信息
            features: 文件特征
            file_ext: 文件扩展名
            
        Returns:
            float: 兼容性评分 (0-1)
        """
        score = 0.0
        
        try:
            # 检查文件扩展名支持
            if file_ext in parser_info.get("supported_extensions", []):
                score += 0.3
            
            # 检查银行匹配
            detected_bank = features.get("detected_bank", "")
            parser_bank = parser_info.get("bank_name", "")
            if detected_bank == parser_bank:
                score += 0.4
            elif "工商" in detected_bank and "工商" in parser_bank:
                score += 0.3
            
            # 根据解析器特点调整评分
            parser_name = parser_info["name"]
            
            if parser_name == "ICBCFormat3StandardParser":
                # 格式3适用于标准个人账户格式，通常是.xls
                if file_ext == ".xls":
                    score += 0.2
                if features.get("sheet_count", 0) <= 2:
                    score += 0.1
            
            elif parser_name == "ICBCFormat4EnhancedParser":
                # 格式4适用于企业多账户格式，通常是.xlsx
                if file_ext == ".xlsx":
                    score += 0.2
                if features.get("sheet_count", 0) >= 2:
                    score += 0.1
            
            elif parser_name == "ICBCFormat1EnhancedParser":
                # 格式1作为通用备选，支持多种格式
                score += 0.1
            
            # 其他特征匹配...
            
        except Exception as e:
            logger.error(f"评估解析器兼容性时出错: {str(e)}")
        
        return min(score, 1.0)  # 确保评分不超过1.0
    
    def get_supported_parsers(self) -> List[Dict[str, Any]]:
        """
        获取所有支持的Enhanced解析器信息
        
        Returns:
            List[Dict]: Enhanced解析器列表
        """
        return self.enhanced_parsers.copy()
    
    def test_parser_on_file(self, file_path: str, parser_class) -> Dict[str, Any]:
        """
        测试指定解析器在文件上的效果
        
        Args:
            file_path: 文件路径
            parser_class: 解析器类
            
        Returns:
            Dict: 测试结果
        """
        result = {
            "parser_class": parser_class.__name__,
            "success": False,
            "error": None,
            "preview": None
        }
        
        try:
            # 创建解析器实例并尝试解析
            parser = parser_class(file_path)
            parse_result = parser.parse()
            
            if parse_result and parse_result.get('success'):
                result["success"] = True
                # 提取预览信息
                result["preview"] = {
                    "total_accounts": len(parse_result.get('accounts', [])),
                    "total_transactions": parse_result.get('summary', {}).get('total_transactions', 0)
                }
            else:
                result["error"] = "解析返回失败"
                
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"测试解析器 {parser_class.__name__} 时出错: {str(e)}")
        
        return result 