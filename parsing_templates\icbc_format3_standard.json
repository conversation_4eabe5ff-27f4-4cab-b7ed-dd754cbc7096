{"templateId": "icbc_standard_format3", "templateName": "工商银行格式3标准解析器", "bankName": "中国工商银行", "description": "专门处理工商银行3.xls文件的标准个人账户格式，单工作表带账户信息头部的交易流水", "version": "1.0.0", "fileType": "XLS", "confidence": 95, "parserType": "standard", "parserClass": "ICBCFormat3StandardParser", "supportedFormats": [".xls"], "features": ["标准个人账户解析", "账户信息头部识别", "单工作表处理", "标准时间格式处理", "借贷标志标准化", "交易摘要智能提取"], "identificationRules": {"fileExtension": ".xls", "fileSizeRange": {"min": 50000, "max": 500000}, "headerKeywords": ["交易日期", "交易时间", "借贷标志", "发生额", "余额", "摘要"], "accountInfoIndicators": ["账户名", "账号", "卡号", "开户行"]}, "dataStructure": {"multipleSheets": false, "singleSheet": true, "hasAccountHeader": true, "headerRowsCount": 3, "dataStartRow": 4, "accountInfoRows": "1-2"}, "fieldMapping": {"account_name": "B1", "account_number": "A", "currency": "B", "card_number": "C", "transaction_date": "D", "transaction_time": "E", "dr_cr_flag": "F", "transaction_amount": "G", "balance_amount": "H", "remark1": "I", "counterparty_account": "J", "transaction_location": "K", "transaction_branch": "L", "teller_id": "M", "transaction_code": "N", "service_interface": "O", "transaction_summary": "P", "cash_flag": "Q", "terminal_id": "R", "transaction_place": "S", "counterparty_name": "T", "counterparty_bank": "U"}, "processingRules": {"accountInfoExtraction": {"accountNameCell": "B1", "accountNumberCell": "B2", "cardNumberCell": "D2", "extractionPattern": {"name": "[\\u4e00-\\u9fa5]{2,10}", "account": "\\d{16,20}", "card": "\\d{16,19}"}}, "headerDetection": {"keywordRow": 3, "mandatoryKeywords": ["交易日期", "发生额", "余额"], "optionalKeywords": ["摘要", "对方户名"]}, "amountProcessing": {"drCrMapping": {"借": "支", "贷": "收", "支出": "支", "收入": "收"}, "amountFormat": "decimal", "negativeForDebit": true}, "timeFormatStandardization": {"dateFormat": "YYYY-MM-DD", "timeFormat": "HH:MM:SS", "combinedFormat": "YYYY-MM-DD HH:MM:SS", "fallbackTime": "00:00:00"}, "remarkProcessing": {"primaryRemark": "remark1", "secondaryRemark": "remark2", "concatenateIfBoth": true, "separator": " | "}}, "validationRules": {"requiredFields": ["transaction_date", "transaction_amount", "balance_amount", "account_number"], "dataTypeValidation": {"transaction_amount": "number", "balance_amount": "number", "transaction_date": "date", "account_number": "string"}, "rangeValidation": {"transaction_amount": {"min": -********.99, "max": ********.99}, "balance_amount": {"min": 0, "max": ********.99}}}, "outputFormat": {"standardFields17": true, "currency": "CNY", "timezone": "Asia/Shanghai", "encoding": "UTF-8"}, "metadata": {"author": "银行流水分析工具", "created": "2025-01-XX", "lastModified": "2025-01-XX", "tags": ["工商银行", "标准解析", "个人账户", "单工作表", "账户头部"]}}