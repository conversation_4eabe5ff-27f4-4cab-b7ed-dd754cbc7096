#!/usr/bin/env python3
"""
优化版置信度评估器 v2.0
解决性能问题的同时保留4维度评估功能
"""

import logging
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import os

logger = logging.getLogger(__name__)

class ConfidenceEvaluatorV2:
    """
    优化版4维度置信度评估器
    
    改进：
    1. 使用轻量级样本分析，避免完整解析
    2. 保留4维度评分功能
    3. 相对比较和归一化，避免多个100%
    4. 性能优化，快速响应
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        # 🔧 修复：使用正确的4维度定义
        self.dimensions = {
            'cardholder_recognition': {'weight': 0.25, 'name': '持卡人姓名识别'},
            'time_format_accuracy': {'weight': 0.25, 'name': '时间格式准确性'},
            'account_recognition': {'weight': 0.25, 'name': '账号或卡号识别'},
            'amount_parsing': {'weight': 0.25, 'name': '金额解析能力'}
        }
    
    def evaluate_multiple_parsers(self, parsers_info: List[Dict], file_path: str) -> List[Dict]:
        """
        评估多个解析器，进行相对比较和归一化
        
        Args:
            parsers_info: 解析器信息列表
            file_path: 文件路径
            
        Returns:
            评估结果列表，按置信度排序
        """
        try:
            logger.info(f"🔍 开始多解析器4维度评估: {os.path.basename(file_path)}")
            
            results = []
            
            # 1. 为每个解析器计算原始评分
            for parser_info in parsers_info:
                try:
                    evaluation = self.quick_evaluate_parser(
                        parser_info['instance'], 
                        file_path, 
                        parser_info
                    )
                    evaluation['parser_info'] = parser_info
                    results.append(evaluation)
                except Exception as e:
                    logger.warning(f"解析器 {parser_info.get('id', 'unknown')} 评估失败: {e}")
                    continue
            
            # 2. 相对比较和归一化
            if len(results) > 1:
                results = self._normalize_scores(results)
            
            # 3. 按置信度排序
            results.sort(key=lambda x: x.get('confidence', 0), reverse=True)
            
            logger.info(f"✅ 多解析器评估完成，共 {len(results)} 个有效结果")
            return results
            
        except Exception as e:
            logger.error(f"多解析器评估失败: {e}")
            return []
    
    def quick_evaluate_parser(self, parser_instance, file_path: str, parser_info: Dict = None) -> Dict:
        """
        快速4维度评估单个解析器
        
        使用轻量级分析，避免完整解析
        """
        try:
            if parser_info is None:
                parser_info = {}
            
            parser_id = parser_info.get('id', getattr(parser_instance, 'name', 'unknown'))
            logger.info(f"📊 评估解析器: {parser_id}")
            
            # 1. 先使用解析器自带的置信度计算作为基础
            base_confidence = 0.0
            if hasattr(parser_instance, 'calculate_confidence'):
                try:
                    base_confidence = parser_instance.calculate_confidence(file_path)
                    logger.info(f"解析器自带置信度: {base_confidence}")
                except Exception as e:
                    logger.warning(f"解析器自带置信度计算失败: {e}")
                    base_confidence = 40.0  # 默认分数
            else:
                base_confidence = 40.0  # 默认分数
            
            # 2. 提取轻量级样本数据（可选，用于4维度详情）
            sample_data = self._extract_lightweight_sample(parser_instance, file_path)
            
            # 3. 计算正确的4维度评分
            dimensions_scores = {}
            
            # 🔧 修复：使用正确的4维度评估
            # 维度1: 持卡人姓名识别 (25分)
            cardholder_score = self._evaluate_cardholder_recognition(sample_data, parser_instance, file_path)
            dimensions_scores['cardholder_recognition'] = {
                'score': cardholder_score,
                'percentage': cardholder_score,
                'description': f"持卡人姓名识别: {cardholder_score:.1f}%",
                'details': f"姓名字段识别和提取能力评估"
            }
            
            # 维度2: 时间格式准确性 (25分)
            time_score = self._evaluate_time_format_accuracy(sample_data, parser_instance, file_path)
            dimensions_scores['time_format_accuracy'] = {
                'score': time_score,
                'percentage': time_score,
                'description': f"时间格式准确性: {time_score:.1f}%",
                'details': f"日期时间解析准确度评估"
            }
            
            # 维度3: 账号或卡号识别 (25分)
            account_score = self._evaluate_account_recognition(sample_data, parser_instance, file_path)
            dimensions_scores['account_recognition'] = {
                'score': account_score,
                'percentage': account_score,
                'description': f"账号识别: {account_score:.1f}%",
                'details': f"账号和卡号字段提取能力评估"
            }
            
            # 维度4: 金额解析能力 (25分)
            amount_score = self._evaluate_amount_parsing(sample_data, parser_instance, file_path)
            dimensions_scores['amount_parsing'] = {
                'score': amount_score,
                'percentage': amount_score,
                'description': f"金额解析: {amount_score:.1f}%",
                'details': f"交易金额和余额解析准确性评估"
            }
            
            # 4. 计算4维度总分（等权重平均）
            total_score = (cardholder_score + time_score + account_score + amount_score)
            total_score = max(0.0, min(100.0, total_score))
            
            logger.info(f"📈 解析器 {parser_id} 4维度评分完成: {total_score:.1f}%")
            
            return {
                'confidence': total_score,
                'confidence_percentage': total_score,
                'total_score': total_score,
                'match_reason': f"4维度智能评估 (总分:{total_score:.1f}/100)",
                'details': dimensions_scores,
                'core_metrics': dimensions_scores,
                'evaluation_breakdown': {
                    'cardholder_recognition': dimensions_scores['cardholder_recognition']['score'],
                    'time_format_accuracy': dimensions_scores['time_format_accuracy']['score'],
                    'account_recognition': dimensions_scores['account_recognition']['score'],
                    'amount_parsing': dimensions_scores['amount_parsing']['score']
                },
                'sample_analysis': {
                    'sample_size': len(sample_data.get('transactions', [])),
                    'accounts_found': len(sample_data.get('accounts', [])),
                    'analysis_method': 'lightweight_sample'
                },
                'analysis_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"解析器评估失败: {e}")
            return {
                'confidence': 0,
                'confidence_percentage': 0,
                'total_score': 0,
                'match_reason': f"评估失败: {str(e)}",
                'details': {},
                'core_metrics': {},
                'evaluation_breakdown': {},
                'sample_analysis': {},
                'analysis_time': datetime.now().isoformat()
            }
    
    def _extract_lightweight_sample(self, parser_instance, file_path: str) -> Dict:
        """
        🔧 修复：真实解析样本获取，基于实际解析结果进行4维度评估
        1) 直接运行解析器 parse() 获取真实解析结果
        2) 如果解析失败或结果为空，则4维度评分应该为0
        3) 只有真实解析出数据才能获得相应评分
        """
        plugin_name = getattr(parser_instance, 'plugin_id', 'unknown')
        logger.info(f"🔍 开始真实解析样本提取 - 插件: {plugin_name}, 文件: {file_path}")

        try:
            # 🔧 修复：只使用真实的parse()方法，不回退到extract_sample
            if hasattr(parser_instance, 'parse'):
                try:
                    logger.info(f"🔍 使用真实parse()方法进行4维度评估 - 插件: {plugin_name}")
                    parse_result = parser_instance.parse(file_path)
                    accounts = parse_result.get('accounts', []) or []
                    transactions = parse_result.get('transactions', []) or []
                    logger.info(f"🔍 真实parse()结果 - 账户数: {len(accounts)}, 交易数: {len(transactions)}")

                    # 🔧 关键修复：如果真实解析没有数据，直接返回空结果，不再回退
                    if not accounts and not transactions:
                        logger.warning(f"❌ 真实解析无数据 - 插件: {plugin_name}, 4维度评分将为0")
                        return {'accounts': [], 'transactions': [], 'source': 'parse_failed'}

                    first_tx = None
                    for t in transactions:
                        if isinstance(t, dict):
                            first_tx = t
                            break

                    result = {
                        'accounts': accounts[:1] if accounts else [],
                        'transactions': [first_tx] if first_tx else [],
                        'source': 'parse_success',
                        'metadata': {
                            'total_accounts': len(accounts),
                            'total_transactions': len(transactions)
                        }
                    }
                    logger.info(f"✅ 真实parse()样本提取成功 - 插件: {plugin_name}, 返回账户: {len(result['accounts'])}, 交易: {len(result['transactions'])}")
                    return result
                except Exception as e:
                    logger.warning(f"❌ 真实parse()方法失败 - 插件: {plugin_name}, 错误: {e}")
                    return {'accounts': [], 'transactions': [], 'source': 'parse_error'}

            # 🔧 修复：如果插件没有parse方法，直接返回空结果
            logger.warning(f"❌ 插件无parse方法 - 插件: {plugin_name}, 4维度评分将为0")
            return {'accounts': [], 'transactions': [], 'source': 'no_parse_method'}
        except Exception as e:
            logger.warning(f"❌ 样本提取完全失败 - 插件: {plugin_name}, 错误: {e}")
            return {'accounts': [], 'transactions': []}
    
    # 🔧 修复：实现正确的4维度评估方法
    
    def _evaluate_cardholder_recognition(self, sample_data: Dict, parser_instance, file_path: str) -> float:
        """🔧 修复：评估持卡人姓名识别能力 (25分) - 基于真实解析结果"""
        plugin_name = getattr(parser_instance, 'plugin_id', 'unknown')
        logger.info(f"🔍 开始持卡人姓名识别评估 - 插件: {plugin_name}")

        try:
            # 🔧 修复：只使用真实解析的样本数据，不再回退到其他方法
            accounts = (sample_data or {}).get('accounts') or []
            logger.info(f"🔍 真实解析的账户数: {len(accounts)} - 插件: {plugin_name}")

            if not accounts:
                logger.warning(f"❌ 真实解析无账户数据，姓名识别评分: 0 - 插件: {plugin_name}")
                return 0.0

            first_account = accounts[0]
            logger.info(f"🔍 第一个账户数据: {first_account} - 插件: {plugin_name}")

            cardholder_name = first_account.get('cardholder_name') or first_account.get('holder_name') or first_account.get('person_name')
            logger.info(f"🔍 提取的持卡人姓名: '{cardholder_name}' - 插件: {plugin_name}")

            if not cardholder_name:
                logger.warning(f"❌ 持卡人姓名为空，姓名识别评分: 0 - 插件: {plugin_name}")
                return 0.0

            name_str = str(cardholder_name).strip()
            name_len = len(name_str)
            # 🎯 标准：不为空且长度>=2且<6 → 25分
            score = 25.0 if 2 <= name_len < 6 else 0.0
            logger.info(f"✅ 持卡人姓名识别评估完成 - 插件: {plugin_name}, 姓名: '{name_str}', 长度: {name_len}, 评分: {score}")
            return score
        except Exception as e:
            logger.warning(f"❌ 持卡人姓名识别评估异常 - 插件: {plugin_name}, 错误: {e}")
            return 0.0
    
    def _evaluate_time_format_accuracy(self, sample_data: Dict, parser_instance, file_path: str) -> float:
        """🔧 修复：评估时间格式准确性 (25分) - 基于真实解析结果"""
        plugin_name = getattr(parser_instance, 'plugin_id', 'unknown')
        try:
            # 🔧 修复：只使用真实解析的样本数据
            txs = (sample_data or {}).get('transactions') or []
            logger.info(f"🔍 真实解析的交易数: {len(txs)} - 插件: {plugin_name}")

            if not txs:
                logger.warning(f"❌ 真实解析无交易数据，时间格式评分: 0 - 插件: {plugin_name}")
                return 0.0

            first_transaction = txs[0]
            # 兼容 datetime 优先
            date_str = str(first_transaction.get('transaction_datetime') or first_transaction.get('transaction_date') or '').strip()
            logger.info(f"🔍 提取的时间字符串: '{date_str}' - 插件: {plugin_name}")

            if not date_str:
                logger.warning(f"❌ 时间字符串为空，时间格式评分: 0 - 插件: {plugin_name}")
                return 0.0

            score = 25.0 if self._is_valid_date_format(date_str.split(' ')[0]) else 0.0
            logger.info(f"✅ 时间格式评估完成 - 插件: {plugin_name}, 时间: '{date_str}', 评分: {score}")
            return score
        except Exception as e:
            logger.warning(f"❌ 时间格式准确性评估异常 - 插件: {plugin_name}, 错误: {e}")
            return 0.0
    
    def _is_valid_date_format(self, date_str: str) -> bool:
        """检查日期格式是否有效"""
        try:
            import re
            from datetime import datetime
            
            # 常见日期格式模式
            date_patterns = [
                r'^\d{4}[-/]\d{1,2}[-/]\d{1,2}$',  # 2023-01-01 或 2023/1/1
                r'^\d{4}年\d{1,2}月\d{1,2}日$',    # 2023年1月1日
                r'^\d{2}[-/]\d{1,2}[-/]\d{2,4}$',  # 01-01-23 或 01/01/2023
                r'^\d{8}$',                        # 20230101
                r'^\d{4}\.\d{1,2}\.\d{1,2}$'       # 2023.1.1
            ]
            
            # 检查是否匹配任何一种模式
            for pattern in date_patterns:
                if re.match(pattern, date_str):
                    return True
            
            # 尝试解析为日期
            try:
                # 尝试几种常见格式
                datetime.strptime(date_str, '%Y-%m-%d')
                return True
            except:
                try:
                    datetime.strptime(date_str, '%Y/%m/%d')
                    return True
                except:
                    pass
            
            return False
            
        except Exception:
            return False
    
    def _evaluate_account_recognition(self, sample_data: Dict, parser_instance, file_path: str) -> float:
        """🔧 修复：评估账号或卡号识别能力 (25分) - 基于真实解析结果"""
        plugin_name = getattr(parser_instance, 'plugin_id', 'unknown')
        try:
            # 🔧 修复：只使用真实解析的样本数据
            accounts = (sample_data or {}).get('accounts') or []
            logger.info(f"🔍 真实解析的账户数: {len(accounts)} - 插件: {plugin_name}")

            if not accounts:
                logger.warning(f"❌ 真实解析无账户数据，账号识别评分: 0 - 插件: {plugin_name}")
                return 0.0

            first_account = accounts[0]
            account_number = first_account.get('account_number')
            if not account_number:
                account_number = first_account.get('card_number')

            logger.info(f"🔍 提取的账号: '{account_number}' - 插件: {plugin_name}")
            score = 25.0 if self._is_valid_account_number(account_number) else 0.0
            logger.info(f"✅ 账号识别评估完成 - 插件: {plugin_name}, 账号: '{account_number}', 评分: {score}")
            return score
        except Exception as e:
            logger.warning(f"❌ 账号识别评估异常 - 插件: {plugin_name}, 错误: {e}")
            return 0.0
    
    def _is_valid_account_number(self, number) -> bool:
        """检查账号/卡号是否有效：纯阿拉伯数字且长度>=8"""
        try:
            if not number:
                return False  # 空值无效
            
            number_str = str(number).strip()
            if not number_str:
                return False  # 空字符串无效
            
            # 检查是否为纯数字
            if not number_str.isdigit():
                return False  # 包含非数字字符无效
            
            # 检查长度
            if len(number_str) < 8:
                return False  # 长度不足无效
            
            return True  # 满足所有条件
            
        except Exception:
            return False
    
    def _evaluate_amount_parsing(self, sample_data: Dict, parser_instance, file_path: str) -> float:
        """🔧 修复：评估金额解析能力 (25分) - 基于真实解析结果"""
        plugin_name = getattr(parser_instance, 'plugin_id', 'unknown')
        try:
            # 🔧 修复：只使用真实解析的样本数据
            transactions = (sample_data or {}).get('transactions') or []
            logger.info(f"🔍 真实解析的交易数: {len(transactions)} - 插件: {plugin_name}")

            if not transactions:
                logger.warning(f"❌ 真实解析无交易数据，金额解析评分: 0 - 插件: {plugin_name}")
                return 0.0  # 没有交易数据：0分

            # 检查第一条交易的金额字段
            first_transaction = transactions[0]
            # 兼容不同字段命名
            transaction_amount = first_transaction.get('transaction_amount')
            if transaction_amount in (None, ""):
                transaction_amount = first_transaction.get('amount')
            balance = first_transaction.get('balance')
            if balance in (None, ""):
                balance = first_transaction.get('balance_amount')

            logger.info(f"🔍 提取的金额字段 - 交易金额: '{transaction_amount}', 余额: '{balance}' - 插件: {plugin_name}")

            # 🎯 标准：交易额、账户余额两个字段均能转换为数字，且不能有任何中文英文文本
            amount_valid = self._is_valid_amount(transaction_amount)
            balance_valid = self._is_valid_amount(balance)

            # 两个字段均有效 → 25分，否则0分
            if amount_valid and balance_valid:
                score = 25.0  # 满分
                logger.info(f"✅ 金额解析评估完成 - 插件: {plugin_name}, 交易金额有效: {amount_valid}, 余额有效: {balance_valid}, 评分: {score}")
                return score
            else:
                score = 0.0   # 不满足条件：0分
                logger.warning(f"❌ 金额解析评估完成 - 插件: {plugin_name}, 交易金额有效: {amount_valid}, 余额有效: {balance_valid}, 评分: {score}")
                return score

        except Exception as e:
            logger.warning(f"❌ 金额解析评估异常 - 插件: {plugin_name}, 错误: {e}")
            return 0.0
    
    def _is_valid_amount(self, amount) -> bool:
        """检查金额是否有效：能转换为数字且不含中文英文"""
        try:
            if amount is None:
                return False  # None值无效
            
            amount_str = str(amount).strip()
            if not amount_str:
                return False  # 空字符串无效
            
            # 检查是否包含中文或英文字符
            import re
            if re.search(r'[a-zA-Z\u4e00-\u9fff]', amount_str):
                return False  # 包含中文或英文无效
            
            # 尝试转换为数字
            try:
                float(amount_str)
                return True  # 能转换为数字且不含文字
            except ValueError:
                return False  # 无法转换为数字
                
        except Exception:
            return False
    
    def _normalize_scores(self, results: List[Dict]) -> List[Dict]:
        """
        对多个解析器的评分进行归一化，避免都是100%的情况
        """
        try:
            if len(results) <= 1:
                return results
            
            # 获取所有分数
            scores = [r.get('confidence', 0) for r in results]
            max_score = max(scores)
            min_score = min(scores)
            
            # 如果分数差异太小，进行重新分配
            if max_score - min_score < 15:
                logger.info("🔧 分数差异过小，进行归一化调整")
                
                # 按原始分数排序，重新分配分数
                sorted_results = sorted(results, key=lambda x: x.get('confidence', 0), reverse=True)
                
                # 分配分数：最高90%，其他按20%递减
                for i, result in enumerate(sorted_results):
                    if i == 0:
                        new_score = 90.0  # 最高分
                    elif i == 1:
                        new_score = 70.0  # 第二名
                    else:
                        new_score = max(20.0, 50.0 - (i-2) * 15)  # 其他递减
                    
                    # 更新分数
                    result['confidence'] = new_score
                    result['confidence_percentage'] = new_score
                    result['total_score'] = new_score
                    result['match_reason'] += f" (归一化调整: {new_score:.1f}%)"
                
                return sorted_results
            
            return results
            
        except Exception as e:
            logger.warning(f"分数归一化失败: {e}")
            return results
