@echo off
chcp 65001 > nul 2>&1
echo ========================================
echo Bank Flow Analysis System - Diagnosis Tool
echo 银行流水分析系统 - 诊断脚本 v2.1
echo ========================================

:: 1. 环境检查
echo [1] 环境检查
echo ------------------------------------------
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Python 可用
) else (
    echo Python 不可用
)

node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Node.js 可用
) else (
    echo Node.js 不可用
)

npm --version >nul 2>&1
if %errorlevel% equ 0 (
    echo npm 可用
) else (
    echo npm 不可用
)

:: 2. 文件结构检查
echo.
echo [2] 文件结构检查
echo ------------------------------------------
if exist "backend\app\main.py" (
    echo 后端主文件存在
) else (
    echo 后端主文件缺失
)

if exist "frontend\bankflow-client\package.json" (
    echo 前端配置文件存在
) else (
    echo 前端配置文件缺失
)

if exist "backend\requirements.txt" (
    echo 后端依赖文件存在
) else (
    echo 后端依赖文件缺失
)

:: 3. 端口占用检查
echo.
echo [3] 端口占用检查
echo ------------------------------------------
netstat -ano | findstr ":8000" | findstr "LISTENING" >nul 2>&1
if %errorlevel% equ 0 (
    echo 端口8000被占用
) else (
    echo 端口8000可用
)

netstat -ano | findstr ":3000" | findstr "LISTENING" >nul 2>&1
if %errorlevel% equ 0 (
    echo 端口3000被占用
) else (
    echo 端口3000可用
)

:: 4. 进程检查
echo.
echo [4] 相关进程检查
echo ------------------------------------------
tasklist | findstr "python" >nul 2>&1
if %errorlevel% equ 0 (
    echo 发现Python进程
) else (
    echo 无Python进程
)

tasklist | findstr "node" >nul 2>&1
if %errorlevel% equ 0 (
    echo 发现Node进程
) else (
    echo 无Node进程
)

:: 5. 依赖检查（简单）
echo.
echo [5] 依赖包检查
echo ------------------------------------------
pushd backend 2>nul
if exist "%cd%" (
    pip show fastapi >nul 2>&1
    if %errorlevel% equ 0 (
        echo FastAPI 已安装
    ) else (
        echo FastAPI 未安装
    )
    
    pip show uvicorn >nul 2>&1
    if %errorlevel% equ 0 (
        echo Uvicorn 已安装
    ) else (
        echo Uvicorn 未安装
    )
)
popd

pushd frontend\bankflow-client 2>nul
if exist "%cd%" (
    if exist "node_modules" (
        echo 前端依赖已安装
    ) else (
        echo 前端依赖未安装，需要运行 npm install
    )
)
popd

echo.
echo ========================================
echo 诊断完成！请根据上述结果解决问题
echo ========================================
pause 