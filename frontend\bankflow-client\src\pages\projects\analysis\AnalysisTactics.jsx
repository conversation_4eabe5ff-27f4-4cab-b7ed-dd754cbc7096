import React, { useState } from 'react';
import { Card, Steps, Form, Input, Button, Select, Divider, List, Typography, Tag, Space, Modal, message } from 'antd';
import { PlusOutlined, DeleteOutlined, SaveOutlined, PlayCircleOutlined, CheckCircleOutlined } from '@ant-design/icons';

const { Step } = Steps;
const { Option } = Select;
const { Title, Text, Paragraph } = Typography;

const AnalysisTactics = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [form] = Form.useForm();
  const [tactics, setTactics] = useState([
    {
      id: '1',
      name: '大额交易分析',
      description: '查找并分析大额交易',
      type: 'amount',
      criteria: {
        threshold: 50000,
        timespan: 30,
      },
      status: 'ready',
    },
    {
      id: '2',
      name: '频繁交易账户分析',
      description: '查找交易频率异常的账户',
      type: 'frequency',
      criteria: {
        frequency: 10,
        timespan: 7,
      },
      status: 'complete',
    },
    {
      id: '3',
      name: '资金往来关系分析',
      description: '分析主要账户间的资金往来关系',
      type: 'relation',
      criteria: {
        minTransactions: 3,
        minAmount: 10000,
      },
      status: 'running',
    },
  ]);
  const [editingTactic, setEditingTactic] = useState(null);
  const [isModalVisible, setIsModalVisible] = useState(false);

  // 渲染状态标签
  const renderStatusTag = (status) => {
    if (status === 'ready') {
      return <Tag color="blue">就绪</Tag>;
    } else if (status === 'running') {
      return <Tag color="processing">运行中</Tag>;
    } else if (status === 'complete') {
      return <Tag icon={<CheckCircleOutlined />} color="success">完成</Tag>;
    }
    return <Tag color="default">{status}</Tag>;
  };

  // 处理添加分析策略
  const handleAddTactic = () => {
    setEditingTactic(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 处理编辑分析策略
  const handleEditTactic = (tactic) => {
    setEditingTactic(tactic);
    form.setFieldsValue({
      name: tactic.name,
      description: tactic.description,
      type: tactic.type,
      ...tactic.criteria,
    });
    setIsModalVisible(true);
  };

  // 处理删除分析策略
  const handleDeleteTactic = (id) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个分析策略吗？',
      onOk() {
        setTactics(tactics.filter(tactic => tactic.id !== id));
        message.success('策略已删除');
      },
    });
  };

  // 处理运行分析策略
  const handleRunTactic = (tactic) => {
    setCurrentStep(1);
    // 可以在这里设置表单初始值
  };

  // 处理开始分析
  const handleStartAnalysis = () => {
    message.loading('正在执行分析...', 2.5)
      .then(() => message.success('分析完成，报告已生成'));
  };

  // 处理模态框确认
  const handleModalOk = () => {
    form.validateFields()
      .then(values => {
        const { name, description, type, ...criteria } = values;
        
        if (editingTactic === null) {
          // 新增
          const newTactic = {
            id: Date.now().toString(),
            name,
            description,
            type,
            criteria,
            status: 'ready',
          };
          setTactics([...tactics, newTactic]);
          message.success('分析策略已添加');
        } else {
          // 编辑
          setTactics(tactics.map(tactic => 
            tactic.id === editingTactic.id ? { 
              ...tactic, 
              name,
              description,
              type,
              criteria,
            } : tactic
          ));
          message.success('分析策略已更新');
        }
        setIsModalVisible(false);
      })
      .catch(info => {
        console.log('表单验证失败:', info);
      });
  };

  // 处理模态框取消
  const handleModalCancel = () => {
    setIsModalVisible(false);
  };

  // 步骤内容
  const steps = [
    {
      title: '选择分析策略',
      content: (
        <div>
          <Paragraph>选择要运行的分析策略，或创建新的分析策略。</Paragraph>
          <List
            itemLayout="horizontal"
            dataSource={tactics}
            renderItem={item => (
              <List.Item
                actions={[
                  <Button
                    type="text"
                    icon={<PlayCircleOutlined />}
                    onClick={() => handleRunTactic(item)}
                    disabled={item.status === 'running'}
                  >
                    运行
                  </Button>,
                  <Button
                    type="text"
                    icon={<SaveOutlined />}
                    onClick={() => handleEditTactic(item)}
                  >
                    编辑
                  </Button>,
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handleDeleteTactic(item.id)}
                  >
                    删除
                  </Button>,
                ]}
              >
                <List.Item.Meta
                  title={
                    <Space>
                      {item.name}
                      {renderStatusTag(item.status)}
                    </Space>
                  }
                  description={item.description}
                />
              </List.Item>
            )}
          />
          <Divider />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddTactic}
          >
            创建新策略
          </Button>
        </div>
      ),
    },
    {
      title: '配置分析参数',
      content: (
        <div>
          <Paragraph>配置分析参数，设定分析的条件和范围。</Paragraph>
          <Form
            layout="vertical"
            form={form}
            initialValues={{
              dateRange: 30,
              accountScope: 'all',
              includeDetails: true,
            }}
          >
            <Form.Item name="dateRange" label="分析时间范围(天)">
              <Select>
                <Option value={7}>最近7天</Option>
                <Option value={30}>最近30天</Option>
                <Option value={90}>最近3个月</Option>
                <Option value={180}>最近6个月</Option>
                <Option value={365}>最近1年</Option>
              </Select>
            </Form.Item>
            <Form.Item name="accountScope" label="账户范围">
              <Select>
                <Option value="all">所有账户</Option>
                <Option value="main">主要账户</Option>
                <Option value="selected">选定账户</Option>
              </Select>
            </Form.Item>
            <Form.Item name="includeDetails" valuePropName="checked" label="包含详细记录">
              <Select>
                <Option value={true}>是</Option>
                <Option value={false}>否</Option>
              </Select>
            </Form.Item>
          </Form>
          <Divider />
          <Space>
            <Button onClick={() => setCurrentStep(currentStep - 1)}>
              上一步
            </Button>
            <Button type="primary" onClick={() => setCurrentStep(currentStep + 1)}>
              下一步
            </Button>
          </Space>
        </div>
      ),
    },
    {
      title: '确认并执行',
      content: (
        <div>
          <Title level={4}>分析任务确认</Title>
          <Paragraph>
            您选择了以下分析策略进行执行，点击"开始分析"按钮开始执行分析任务。分析结果将在完成后生成报告。
          </Paragraph>
          <div style={{ background: '#f5f5f5', padding: 16, marginBottom: 16, borderRadius: 4 }}>
            <Text strong>策略名称：</Text> <Text>大额交易分析</Text><br />
            <Text strong>分析范围：</Text> <Text>最近30天</Text><br />
            <Text strong>账户范围：</Text> <Text>所有账户</Text><br />
            <Text strong>包含详细记录：</Text> <Text>是</Text>
          </div>
          <Paragraph>
            <Text type="warning">注意：分析过程可能需要几分钟时间，请耐心等待。</Text>
          </Paragraph>
          <Space>
            <Button onClick={() => setCurrentStep(currentStep - 1)}>
              上一步
            </Button>
            <Button type="primary" onClick={handleStartAnalysis}>
              开始分析
            </Button>
          </Space>
        </div>
      ),
    },
  ];

  return (
    <div>
      <Card title="银行流水分析策略">
        <Steps current={currentStep} onChange={setCurrentStep}>
          {steps.map(item => (
            <Step key={item.title} title={item.title} />
          ))}
        </Steps>
        <div style={{ marginTop: 24, minHeight: 200 }}>
          {steps[currentStep].content}
        </div>
      </Card>

      <Modal
        title={editingTactic === null ? "添加分析策略" : "编辑分析策略"}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="策略名称"
            rules={[{ required: true, message: '请输入策略名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="description"
            label="策略描述"
          >
            <Input.TextArea rows={2} />
          </Form.Item>
          <Form.Item
            name="type"
            label="策略类型"
            rules={[{ required: true, message: '请选择策略类型' }]}
          >
            <Select>
              <Option value="amount">金额分析</Option>
              <Option value="frequency">频率分析</Option>
              <Option value="relation">关系分析</Option>
              <Option value="pattern">模式分析</Option>
              <Option value="custom">自定义分析</Option>
            </Select>
          </Form.Item>
          
          <Divider>策略参数</Divider>
          
          <Form.Item
            name="threshold"
            label="金额阈值"
          >
            <Input type="number" addonBefore="￥" />
          </Form.Item>
          <Form.Item
            name="frequency"
            label="频率阈值"
          >
            <Input type="number" addonAfter="次/周期" />
          </Form.Item>
          <Form.Item
            name="timespan"
            label="时间周期"
          >
            <Input type="number" addonAfter="天" />
          </Form.Item>
          <Form.Item
            name="minTransactions"
            label="最小交易次数"
          >
            <Input type="number" addonAfter="次" />
          </Form.Item>
          <Form.Item
            name="minAmount"
            label="最小交易金额"
          >
            <Input type="number" addonBefore="￥" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AnalysisTactics; 