{"name": "bocm_format1_plugin", "version": "1.0.0", "description": "交通银行Format1解析器插件，支持交通银行标准Excel格式的银行流水解析", "author": "银行流水系统团队", "license": "MIT", "homepage": "https://github.com/your-org/bank-parser", "supported_formats": ["Excel (.xlsx)", "Excel (.xls)"], "supported_banks": ["交通银行"], "dependencies": ["pandas>=1.3.0", "openpyxl>=3.0.0", "xlrd>=2.0.0"], "entry_point": "plugin.Plugin", "confidence_threshold": 0.7, "keywords": ["交通银行", "银行流水", "解析器", "插件", "Format1"], "format_features": {"multi_sheet": true, "time_format": "custom_6_5_digit", "amount_format": "debit_credit_flag", "header_info": true, "transaction_sheet": "all", "special_time_handling": "early_morning_single_digit", "transaction_direction": "borrow_lend_flag"}, "changelog": {"1.0.0": "初始版本，支持交通银行标准Excel格式解析，包含特殊时间格式处理（6位数/5位数）、借贷方向处理、字段动态提取"}}