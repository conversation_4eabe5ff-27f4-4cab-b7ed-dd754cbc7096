"""
工商银行格式1解析器插件 - 生产级版本
专门处理1.xlsx文件的多工作表个人账户结构：
- 每个工作表代表一个持卡人
- 持卡人姓名+身份证号作为账户分隔符
- 支持同一持卡人多个账户的解析
"""

import pandas as pd
import logging
from typing import Dict, List, Any, Tuple
from datetime import datetime
import re
import os
import time
import traceback

# 修复导入路径
try:
    from ...core.plugin_interface import BasePlugin
except ImportError:
    # 如果相对导入失败，创建一个简单的基础类
    class BasePlugin:
        def __init__(self):
            self.name = "icbc_format1_plugin"
            self.version = "2.0.0"
            self.description = "工商银行Format1解析器插件 - 增强版"
            self.bank_name = "中国工商银行"

logger = logging.getLogger(__name__)

class Plugin(BasePlugin):
    """工商银行格式1解析器插件 - 集成ICBCFormat1EnhancedParser逻辑"""
    
    def __init__(self, file_path: str = None):
        super().__init__()
        self.name = "icbc_format1_plugin"
        self.version = "2.0.0"
        self.description = "工商银行Format1解析器插件 - 增强版"
        self.bank_name = "中国工商银行"
        self.format_type = "xlsx"
        self.file_path = file_path
        
        # 解析结果
        self.accounts = []
        self.transactions = []
        
        # 状态管理
        self.start_time = time.time()
        self.error_count = 0
        self.last_error = None
        
        # 支持的文件扩展名
        self.supported_extensions = ['.xlsx', '.xls']
        
        logger.info(f"✅ {self.name} v{self.version} 初始化完成")
    
    def calculate_confidence(self, file_path: str = None) -> float:
        """
        静态计算置信度，基于文件内容分析，避免解析结果污染
        """
        try:
            target_file = file_path or self.file_path
            if not target_file:
                return 0.0
            
            logger.info(f"🔍 计算文件置信度: {os.path.basename(target_file)}")
            
            # 🔧 关键修复：只使用样本数据进行评估，不调用实际解析
            sample_data = self.extract_sample(target_file, limit=5)
            accounts = sample_data.get('accounts', [])
            transactions = sample_data.get('transactions', [])
            
            if not accounts:
                logger.warning("未找到账户信息，置信度为0")
                return 0.0
            
            # 🔧 基于样本数据的静态置信度评估
            base_confidence = self._calculate_simple_confidence_score(accounts, transactions)
            
            # 🔧 文件特征检查加分（Format1特定特征）
            feature_bonus = 0.0
            try:
                # 检查多工作表特征
                with pd.ExcelFile(target_file) as xls:
                    if len(xls.sheet_names) > 1:
                        feature_bonus += 10.0  # 多工作表格式
                    
                    # 检查工作表名称模式
                    has_person_sheets = any(
                        len(sheet_name.strip()) >= 2 and not sheet_name.startswith('Sheet')
                        for sheet_name in xls.sheet_names
                    )
                    if has_person_sheets:
                        feature_bonus += 10.0  # 人名工作表
                        
            except Exception as e:
                logger.warning(f"文件特征检查失败: {e}")
            
            final_confidence = min(100.0, base_confidence + feature_bonus)
            
            logger.info(f"🎯 Format1置信度评估完成: {final_confidence:.1f}% (基于样本数据静态分析)")
            return final_confidence
            
        except Exception as e:
            logger.error(f"Format1置信度计算失败: {e}")
            return 10.0
    
    def validate_file(self, file_path: str) -> bool:
        """
        验证文件是否为工商银行格式
        """
        if not file_path or not os.path.exists(file_path):
            return False
        
        # 检查文件扩展名
        if not any(file_path.lower().endswith(ext) for ext in self.supported_extensions):
            return False
        
        try:
            # 快速检查Excel文件结构
            excel_file = pd.ExcelFile(file_path)
            
            # 检查是否有工作表
            if not excel_file.sheet_names:
                return False
            
            # 读取第一个工作表的前几行
            first_sheet = excel_file.sheet_names[0]
            df = pd.read_excel(file_path, sheet_name=first_sheet, header=None, nrows=10)
            
            # 检查是否包含工商银行特征
            # 1. 查找持卡人信息行（姓名+身份证号）
            for idx in range(len(df)):
                cell_value = str(df.iloc[idx, 0]) if not pd.isna(df.iloc[idx, 0]) else ""
                if re.match(r'^[\u4e00-\u9fa5]{2,4}\d{18}$', cell_value.strip()):
                    logger.info(f"✅ 检测到工商银行格式特征: 持卡人信息")
                    return True
            
            # 2. 查找表头特征
            for idx in range(len(df)):
                row_text = ' '.join([str(cell) for cell in df.iloc[idx] if not pd.isna(cell)])
                if "账号" in row_text and "交易日期" in row_text:
                    logger.info(f"✅ 检测到工商银行格式特征: 表头")
                    return True
            
            return False
            
        except Exception as e:
            logger.warning(f"文件验证失败: {e}")
            return False
    
    def parse(self, file_path: str = None) -> Dict[str, Any]:
        """
        执行解析 - 使用ICBCFormat1EnhancedParser的逻辑
        """
        try:
            # 使用传入的文件路径或实例的文件路径
            if file_path:
                self.file_path = file_path
            elif not self.file_path:
                raise ValueError("未设置文件路径")
            
            logger.info(f"🔍 开始解析工商银行Format1文件: {os.path.basename(self.file_path)}")
            
            # 验证文件
            if not self.validate_file(self.file_path):
                raise ValueError("文件验证失败")
            
            # 重置状态
            self.accounts = []
            self.transactions = []
            
            # 获取所有工作表名称
            excel_file = pd.ExcelFile(self.file_path)
            sheet_names = excel_file.sheet_names
            
            logger.info(f"发现工作表: {sheet_names}")
            
            # 处理每个工作表
            for sheet_name in sheet_names:
                logger.info(f"处理工作表: {sheet_name}")
                self._process_sheet(sheet_name)
            
            # 构建解析结果
            result = self._build_result()
            
            logger.info(f"✅ 解析完成: 共 {len(self.accounts)} 个账户, {len(self.transactions)} 条交易")
            return result
            
        except Exception as e:
            logger.error(f"文件解析失败: {str(e)}")
            self.handle_error(e)
            return {
                'success': False,
                'error': str(e),
                'accounts': [],
                'transactions_by_account': {},
                'summary': {'total_accounts': 0, 'total_transactions': 0}
            }
    
    def extract_sample(self, file_path: str = None, limit: int = 10) -> Dict[str, Any]:
        """
        提取样本数据用于快速置信度评估
        """
        try:
            target_file = file_path or self.file_path
            if not target_file:
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
            
            # 快速读取：只读取第一个工作表的前50行
            excel_file = pd.ExcelFile(target_file)
            first_sheet = excel_file.sheet_names[0]
            df = pd.read_excel(target_file, sheet_name=first_sheet, header=None, nrows=50)
            
            logger.info(f"快速评估模式：读取工作表 {first_sheet} 前50行")
            
            # 简化边界检测：只检查前30行
            boundaries = []
            for idx in range(min(30, len(df))):
                cell_value = df.iloc[idx, 0] if not pd.isna(df.iloc[idx, 0]) else ""
                holder_name, holder_id = self._extract_account_info_from_cell(cell_value)
                
                if holder_name and holder_id:
                    boundaries.append((idx, len(df), holder_name, holder_id))
                    logger.info(f"快速检测到账户: {holder_name}")
                    break  # 只找第一个账户即可
            
            if not boundaries:
                logger.warning("快速评估：未找到账户信息")
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
            
            # 使用第一个找到的账户
            start_idx, end_idx, holder_name, holder_id = boundaries[0]
            
            # 快速处理样本数据
            sample_transactions = []
            data_start = start_idx + 2  # 跳过姓名行和表头行
            
            for idx in range(data_start, min(data_start + limit * 2, len(df))):
                if len(sample_transactions) >= limit:
                    break
                    
                if idx >= len(df):
                    break
                
                row = df.iloc[idx]
                if row.isna().all():
                    continue
                
                # 快速验证：检查第一列是否像账号
                first_cell = self._clean_field(row.iloc[0])
                if not re.match(r'^\d{16,25}', first_cell.replace(' ', '').replace('\t', '')):
                    continue
                
                try:
                    # 基本字段提取
                    account_number = first_cell.replace(' ', '').replace('\t', '')
                    transaction_date = self._clean_field(row.iloc[3])  # D列：交易日期
                    amount_raw = self._clean_field(row.iloc[6])  # G列：发生额
                    dr_cr_flag = self._clean_field(row.iloc[5])  # F列：借贷标志
                    
                    if account_number and transaction_date and amount_raw:
                        transaction = {
                            'cardholder_name': holder_name,
                            'account_number': account_number,
                            'transaction_date': transaction_date,
                            'transaction_amount': self._parse_amount(amount_raw, dr_cr_flag),
                            'dr_cr_flag': dr_cr_flag,
                            'card_number': self._clean_field(row.iloc[2]) if len(row) > 2 else "",
                            'currency': self._clean_field(row.iloc[1]) if len(row) > 1 else "CNY"
                        }
                        sample_transactions.append(transaction)
                        
                except Exception as e:
                    logger.debug(f"跳过第{idx+1}行: {str(e)}")
                    continue
            
            # 构建样本账户信息
            sample_account = {
                'account_number': sample_transactions[0]['account_number'] if sample_transactions else "",
                'cardholder_name': holder_name,
                'card_number': sample_transactions[0]['card_number'] if sample_transactions else "",
                'bank_name': '中国工商银行',
                'account_type': '个人账户'
            }
            
            logger.info(f"快速评估完成：提取{len(sample_transactions)}条样本交易")
            
            return {
                'accounts': [sample_account] if sample_transactions else [],
                'transactions': sample_transactions[:limit],
                'metadata': {
                    'sample_size': len(sample_transactions),
                    'sheet_count': len(excel_file.sheet_names),
                    'evaluation_mode': 'quick_sample',
                    'holder_name': holder_name,
                    'holder_id': holder_id
                }
            }
            
        except Exception as e:
            logger.error(f"快速样本数据提取失败: {str(e)}")
            return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
    
    # ===== 核心解析方法 =====
    
    def _clean_field(self, value) -> str:
        """清理字段值"""
        if value is None or pd.isna(value):
            return ""
        return str(value).strip().replace('\t', '').replace('\n', '').replace('\r', '')
    
    def _clean_amount_string(self, value) -> str:
        """清理金额字符串"""
        if value is None:
            return ""
        
        clean_value = str(value).replace(" ", "").replace(",", "").replace("，", "").replace("\t", "").replace("\n", "").replace("\r", "").strip()
        clean_value = clean_value.replace("¥", "").replace("$", "").replace("€", "").replace("￥", "")
        
        return clean_value
    
    def _parse_amount(self, amount_str: str, dr_cr_flag: str = "") -> float:
        """解析金额"""
        if not amount_str:
            return 0.0
        
        clean_amount = self._clean_amount_string(amount_str)
        
        try:
            amount = float(clean_amount)
            
            if dr_cr_flag == "借":
                return -abs(amount)
            elif dr_cr_flag == "贷":
                return abs(amount)
            else:
                return amount
        except (ValueError, TypeError):
            logger.warning(f"无法解析金额: {amount_str}")
            return 0.0
    
    def _extract_account_info_from_cell(self, cell_value: str) -> Tuple[str, str]:
        """从单元格值中提取账户信息（持卡人姓名+身份证号）"""
        if not cell_value:
            return "", ""
        
        cell_str = str(cell_value).strip()
        
        # 匹配格式：中文姓名+18位身份证号
        match = re.match(r'^([\u4e00-\u9fa5]{2,4})(\d{18})$', cell_str)
        if match:
            return match.group(1), match.group(2)
        
        # 尝试其他可能的格式
        if len(cell_str) >= 20:
            id_match = re.search(r'(\d{18})', cell_str)
            if id_match:
                id_number = id_match.group(1)
                name_part = cell_str[:id_match.start()].strip()
                if re.match(r'^[\u4e00-\u9fa5]{2,4}$', name_part):
                    return name_part, id_number
        
        # 🔧 增强匹配：处理更宽松的格式，但仍要求相对严格
        # 只有当字符串看起来像"姓名+身份证"格式时才匹配
        if len(cell_str) >= 5 and re.search(r'[\u4e00-\u9fa5]{2,4}', cell_str):
            # 尝试从任何位置提取姓名和身份证
            name_match = re.search(r'[\u4e00-\u9fa5]{2,4}', cell_str)
            id_match = re.search(r'\d{15,18}', cell_str)  # 15或18位数字
            
            if name_match and id_match:
                return name_match.group(0), id_match.group(0)
        
        return "", ""
    
    def _find_account_boundaries(self, df: pd.DataFrame) -> List[Tuple[int, int, str, str]]:
        """查找账户边界"""
        boundaries = []
        
        for idx in range(len(df)):
            cell_value = df.iloc[idx, 0] if not pd.isna(df.iloc[idx, 0]) else ""
            
            holder_name, holder_id = self._extract_account_info_from_cell(cell_value)
            
            if holder_name and holder_id:
                start_idx = idx
                end_idx = len(df)
                
                for next_idx in range(idx + 1, len(df)):
                    next_cell = df.iloc[next_idx, 0] if not pd.isna(df.iloc[next_idx, 0]) else ""
                    next_name, next_id = self._extract_account_info_from_cell(next_cell)
                    
                    if next_name and next_id:
                        end_idx = next_idx
                        break
                
                boundaries.append((start_idx, end_idx, holder_name, holder_id))
                logger.info(f"找到账户边界: {holder_name}({holder_id}) 行{start_idx}-{end_idx}")
        
        return boundaries
    
    def _standardize_time_format(self, date_str: str, time_str: str = "") -> str:
        """标准化时间格式为 YYYY-MM-DD HH:MM:SS"""
        if not date_str:
            return ""
        
        try:
            date_clean = self._clean_field(date_str)
            if not date_clean:
                return ""
            
            date_obj = pd.to_datetime(date_clean).date()
            date_formatted = date_obj.strftime("%Y-%m-%d")
            
            if time_str:
                time_clean = self._clean_field(time_str)
                if '.' in time_clean:
                    time_clean = time_clean.replace('.', ':')
                
                time_parts = time_clean.split(':')
                if len(time_parts) == 3:
                    try:
                        hour = int(time_parts[0])
                        minute = int(time_parts[1])
                        second = int(time_parts[2])
                        time_formatted = f"{hour:02d}:{minute:02d}:{second:02d}"
                        return f"{date_formatted} {time_formatted}"
                    except ValueError:
                        pass
            
            return f"{date_formatted} 00:00:00"
            
        except Exception as e:
            logger.warning(f"时间格式标准化失败: {date_str} {time_str}, 错误: {str(e)}")
            return date_str
    
    def _process_transaction_data(self, df: pd.DataFrame, start_row: int, 
                                account_name: str, account_number: str, card_number: str) -> None:
        """处理交易数据"""
        i = start_row
        while i < len(df):
            row = df.iloc[i]
            
            if row.isna().all():
                i += 1
                continue
            
            first_cell = self._clean_field(row.iloc[0])
            
            # 检查是否是新的持卡人信息行
            if re.match(r'^[\u4e00-\u9fa5]{2,4}\d{18}$', first_cell):
                account_name = re.match(r'^([\u4e00-\u9fa5]{2,4})\d{18}$', first_cell).group(1)
                logger.info(f"发现新持卡人: {account_name}")
                i += 1
                continue
            
            # 检查是否是表头行
            if first_cell == "账号" or "交易日期" in first_cell:
                logger.info(f"跳过表头行: 第{i+1}行")
                i += 1
                continue
            
            # 检查是否是数据行
            if not re.match(r'^\d{16,20}$', first_cell.replace(' ', '').replace('\t', '')):
                i += 1
                continue
            
            try:
                # 正确的字段映射
                current_account = first_cell.replace(' ', '').replace('\t', '')
                currency = self._clean_field(row.iloc[1])
                current_card = self._clean_field(row.iloc[2])
                transaction_date_raw = self._clean_field(row.iloc[3])
                transaction_time = self._clean_field(row.iloc[4])
                dr_cr_flag_raw = self._clean_field(row.iloc[5])
                amount_raw = self._clean_field(row.iloc[6])
                balance_raw = self._clean_field(row.iloc[7])
                
                # 数据有效性检查
                if not transaction_date_raw or not amount_raw or not dr_cr_flag_raw:
                    i += 1
                    continue
                
                # 确保不会误解析时间格式为金额
                if re.match(r'^\d{2}\.\d{2}\.\d{2}$', amount_raw):
                    logger.error(f"❌ 第{i+1}行错误：发生额字段包含时间格式 {amount_raw}")
                    i += 1
                    continue
                
                if re.match(r'^\d{2}\.\d{2}\.\d{2}$', balance_raw):
                    logger.error(f"❌ 第{i+1}行错误：余额字段包含时间格式 {balance_raw}")
                    i += 1
                    continue
                
                # 提取数据
                transaction_date = str(transaction_date_raw).lstrip() if transaction_date_raw else ""
                
                if not current_card or current_card in ['', 'nan', 'None']:
                    current_card = ""
                
                dr_cr_flag = "收" if dr_cr_flag_raw in ["贷", "收入"] else "支"
                
                # 安全的金额解析
                try:
                    amount = self._parse_amount(amount_raw, dr_cr_flag_raw)
                except Exception as e:
                    logger.warning(f"第{i+1}行金额解析失败: amount_raw='{amount_raw}', error={e}")
                    i += 1
                    continue
                
                try:
                    balance = self._parse_amount(balance_raw) if balance_raw and balance_raw not in ['', 'nan', 'None'] else 0.0
                except Exception as e:
                    logger.warning(f"第{i+1}行余额解析失败: balance_raw='{balance_raw}', error={e}")
                    balance = 0.0
                
                # 标准化时间
                datetime_str = self._standardize_time_format(transaction_date, transaction_time)
                date_part = datetime_str.split(' ')[0] if ' ' in datetime_str else datetime_str
                time_part = datetime_str.split(' ')[1] if ' ' in datetime_str else "00:00:00"
                
                # 获取其他字段
                remark1 = self._clean_field(row.iloc[8]) if len(row) > 8 else ""
                counterparty_account = self._clean_field(row.iloc[9]) if len(row) > 9 else ""
                transaction_area = self._clean_field(row.iloc[10]) if len(row) > 10 else ""
                transaction_branch = self._clean_field(row.iloc[11]) if len(row) > 11 else ""
                cashier_no = self._clean_field(row.iloc[12]) if len(row) > 12 else ""
                transaction_code = self._clean_field(row.iloc[13]) if len(row) > 13 else ""
                service_interface = self._clean_field(row.iloc[14]) if len(row) > 14 else ""
                
                # 构建交易方式描述
                transaction_method = remark1 if remark1 else "其他"
                if service_interface:
                    transaction_method += f"({service_interface})"
                
                # 构建交易记录
                transaction = {
                    'sequence_number': len(self.transactions) + 1,
                    'holder_name': account_name,
                    'cardholder_name': account_name,
                    'bank_name': '中国工商银行',
                    'account_number': current_account,
                    'card_number': current_card,
                    'transaction_date': date_part,
                    'transaction_time': time_part,
                    'transaction_method': transaction_method,
                    'transaction_amount': amount,
                    'balance_amount': balance,
                    'dr_cr_flag': dr_cr_flag,
                    'counterparty_name': "",
                    'counterparty_account': counterparty_account,
                    'counterparty_bank': "",
                    'remark1': remark1,
                    'remark2': "",
                    'remark3': "",
                    'currency': currency or 'CNY'
                }
                
                self.transactions.append(transaction)
                logger.debug(f"✅ 成功解析第{i+1}行交易: {account_name} - {amount} - {transaction_method}")
                
            except Exception as e:
                logger.warning(f"处理第{i+1}行交易记录失败: {str(e)}")
            
            i += 1
    
    def _process_sheet(self, sheet_name: str) -> None:
        """处理单个工作表"""
        try:
            df = pd.read_excel(self.file_path, sheet_name=sheet_name, header=None)
            
            boundaries = self._find_account_boundaries(df)
            
            if not boundaries:
                logger.warning(f"工作表 {sheet_name} 中未找到账户信息")
                return
            
            for i, (start_idx, end_idx, holder_name, holder_id) in enumerate(boundaries):
                logger.info(f"处理账户区段: {holder_name} ({start_idx}-{end_idx})")
                self._process_transaction_data(df, start_idx, holder_name, "", "")
                
        except Exception as e:
            logger.error(f"处理工作表 {sheet_name} 时发生错误: {str(e)}")
    
    def _calculate_simple_confidence_score(self, accounts: List[Dict], transactions: List[Dict]) -> float:
        """简单置信度评估"""
        if not accounts:
            return 0.0
        
        sample_accounts = accounts[:5]
        
        # 1. 金额验证 (25分)
        amount_valid = False
        for account in sample_accounts:
            account_transactions = account.get('transactions', [])
            if not account_transactions:
                # 如果账户没有transactions字段，从transactions列表中查找
                account_transactions = [t for t in transactions if t.get('account_number') == account.get('account_number')]
            
            if account_transactions:
                for trans in account_transactions[:3]:
                    amount = trans.get('transaction_amount')
                    if amount is not None:
                        try:
                            float(amount)
                            amount_valid = True
                            break
                        except:
                            pass
            if amount_valid:
                break
        
        amount_score = 25 if amount_valid else 0
        
        # 2. 持卡人姓名验证 (25分)
        name_valid = False
        for account in sample_accounts:
            name = account.get('cardholder_name', '')
            if name and re.match(r'^[\u4e00-\u9fff]{2,18}$', name):
                name_valid = True
                break
        
        name_score = 25 if name_valid else 0
        
        # 3. 账号验证 (25分)
        account_valid = False
        for account in sample_accounts:
            account_num = account.get('account_number', '')
            if account_num and re.match(r'^\d{10,25}$', str(account_num)):
                account_valid = True
                break
        
        account_score = 25 if account_valid else 0
        
        # 4. 卡号验证 (25分)
        card_valid = False
        for account in sample_accounts:
            card_num = account.get('card_number', '')
            if card_num and re.match(r'^\d{13,19}$', str(card_num)):
                card_valid = True
                break
        
        card_score = 25 if card_valid else 0
        
        total_confidence = amount_score + name_score + account_score + card_score
        # 返回0-100范围的置信度（与原始解析器保持一致）
        return total_confidence
    
    def _build_result(self) -> Dict[str, Any]:
        """构建解析结果"""
        # 按账户分组交易
        transactions_by_account = {}
        accounts = []
        
        # 为每条交易分配序号
        for index, transaction in enumerate(self.transactions, 1):
            transaction['sequence_number'] = index
            
            account_number = transaction['account_number']
            
            if account_number not in transactions_by_account:
                transactions_by_account[account_number] = []
                
                account_data = {
                    'account_number': account_number,
                    'card_number': transaction['card_number'],
                    'cardholder_name': transaction['cardholder_name'],
                    'bank_name': '中国工商银行',
                    'account_type': '个人账户',
                    
                    'transactions_count': 0,
                    'total_inflow': 0.0,
                    'total_outflow': 0.0,
                    'date_range': "",
                    
                    'account_id': account_number,
                    'account_name': transaction['cardholder_name'],
                    'currency': transaction['currency'],
                    'is_primary': len(accounts) == 0,
                    
                    'transactions': []
                }
                accounts.append(account_data)
            
            transactions_by_account[account_number].append(transaction)
        
        # 计算统计信息
        for account in accounts:
            account_transactions = transactions_by_account[account['account_number']]
            account['transactions_count'] = len(account_transactions)
            account['transactions'] = account_transactions

            inflow = sum(t['transaction_amount'] for t in account_transactions if t['transaction_amount'] > 0)
            outflow = sum(abs(t['transaction_amount']) for t in account_transactions if t['transaction_amount'] < 0)

            account['total_inflow'] = inflow
            account['total_outflow'] = outflow

            dates = [t['transaction_date'] for t in account_transactions if t['transaction_date']]
            if dates:
                account['date_range'] = f"{min(dates)} 至 {max(dates)}"

            # 🔧 修复：计算账户余额 - 从最后一个时间段的交易记录获取余额
            account_balance = 0.0
            if account_transactions:
                # 按交易日期和时间排序，获取最新的交易记录
                # 对于同一时间的交易，取原始顺序的最后一条（余额最终状态）
                def get_datetime_key(transaction):
                    date = transaction.get('transaction_date', '')
                    time = transaction.get('transaction_time', '')
                    # 添加原始索引确保同一时间的交易按原始顺序排列
                    original_index = account_transactions.index(transaction)
                    return f"{date} {time}", original_index

                sorted_transactions = sorted(account_transactions, key=get_datetime_key, reverse=True)
                latest_transaction = sorted_transactions[0]  # 最新的交易

                # 从最新交易的balance_amount字段获取账户余额
                if 'balance_amount' in latest_transaction and latest_transaction['balance_amount'] is not None:
                    try:
                        account_balance = float(latest_transaction['balance_amount'])
                        datetime_key, _ = get_datetime_key(latest_transaction)
                        logger.info(f"账户 {account['account_number']} 余额: ¥{account_balance:,.2f} (来自最新交易: {datetime_key})")
                    except (ValueError, TypeError):
                        logger.warning(f"账户 {account['account_number']} 余额转换失败: {latest_transaction['balance_amount']}")
                        account_balance = 0.0
                else:
                    logger.warning(f"账户 {account['account_number']} 最新交易缺少余额信息")

            account['account_balance'] = account_balance
        
        # 计算置信度
        confidence_score = self._calculate_simple_confidence_score(accounts, self.transactions)
        
        # 构建最终结果
        return {
            'success': True,
            'file_info': {
                'name': os.path.basename(self.file_path),
                'format': 'xlsx'
            },
            'summary': {
                'total_accounts': len(accounts),
                'total_transactions': len(self.transactions),
                'date_range': self._calculate_overall_date_range()
            },
            'accounts': accounts,
            'transactions': self.transactions,
            'transactions_by_account': transactions_by_account,
            'confidence_score': confidence_score,
            'metadata': {
                'parser_version': self.version,
                'parsing_time': datetime.now().isoformat(),
                'confidence_score': confidence_score
            }
        }
    
    def _calculate_overall_date_range(self) -> str:
        """计算整体时间范围"""
        if not self.transactions:
            return ""
        
        dates = [t['transaction_date'] for t in self.transactions if t['transaction_date']]
        if dates:
            return f"{min(dates)} 至 {max(dates)}"
        
        return ""
    
    def handle_error(self, error: Exception) -> None:
        """增强的错误处理"""
        self.error_count += 1
        self.last_error = str(error)
        
        if isinstance(error, FileNotFoundError):
            logger.error(f"文件不存在错误: {error}")
        elif isinstance(error, PermissionError):
            logger.error(f"文件权限错误: {error}")
        elif isinstance(error, pd.errors.EmptyDataError):
            logger.error(f"文件数据为空: {error}")
        elif isinstance(error, (ValueError, TypeError)):
            logger.error(f"数据格式错误: {error}")
        else:
            logger.error(f"未知错误: {error}")
            logger.error(f"错误堆栈:\\n{traceback.format_exc()}")
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        uptime = time.time() - self.start_time
        
        return {
            'status': 'healthy' if self.error_count < 5 else 'degraded',
            'uptime_seconds': uptime,
            'error_count': self.error_count,
            'last_error': self.last_error,
            'memory_usage': 'normal',
            'performance': 'good' if uptime < 300 else 'slow'
        }
    
    def get_info(self) -> Dict[str, Any]:
        """获取插件信息"""
        return {
            'name': self.name,
            'version': self.version,
            'description': self.description,
            'bank_name': self.bank_name,
            'format_type': self.format_type,
            'supported_extensions': self.supported_extensions,
            'health_status': self.get_health_status()
        } 