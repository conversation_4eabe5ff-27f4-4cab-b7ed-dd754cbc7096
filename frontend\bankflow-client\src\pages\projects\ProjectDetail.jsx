import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Card, Descriptions, Button, Tabs, Table, Space, Spin, 
  Empty, Typography, Tag, Statistic, Row, Col
} from 'antd';
import { 
  ArrowLeftOutlined, UserOutlined, BankOutlined, 
  FileOutlined
} from '@ant-design/icons';
import CustomExportOutlined from '../../components/icons/CustomExportOutlined';

const { TabPane } = Tabs;
const { Title, Text } = Typography;

/**
 * 项目详情页面组件
 * 
 * @returns {JSX.Element} 项目详情页面
 */
const ProjectDetail = () => {
  const { projectId } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [project, setProject] = useState(null);
  const [accounts, setAccounts] = useState([]);
  
  useEffect(() => {
    // 模拟加载项目详情
    setLoading(true);
    setTimeout(() => {
      setProject({
        project_id: projectId,
        project_name: '案件调查2024-001',
        person_name: '张三',
        is_active: true,
        created_at: '2024-05-01T10:00:00',
        updated_at: '2024-05-02T15:30:00',
        description: '关于某公司资金流向的调查'
      });
      
      setAccounts([
        {
          account_id: 'acc1',
          person_name: '张三',
          bank_name: '中国工商银行',
          account_name: '张三',
          card_number: '6222021234567890123',
          transaction_count: 32
        },
        {
          account_id: 'acc2',
          person_name: '李四',
          bank_name: '中国农业银行',
          account_name: '李四',
          card_number: '6228481234567890123',
          transaction_count: 18
        }
      ]);
      
      setLoading(false);
    }, 1000);
    
    // 实际应该调用API获取数据
    // Promise.all([
    //   getProject(projectId),
    //   getProjectAccounts(projectId)
    // ]).then(([projectData, accountsData]) => {
    //   setProject(projectData);
    //   setAccounts(accountsData);
    //   setLoading(false);
    // });
  }, [projectId]);
  
  // 账户表格列定义
  const accountColumns = [
    {
      title: '账户名称',
      dataIndex: 'account_name',
      key: 'account_name'
    },
    {
      title: '关联人员',
      dataIndex: 'person_name',
      key: 'person_name'
    },
    {
      title: '银行',
      dataIndex: 'bank_name',
      key: 'bank_name'
    },
    {
      title: '卡号/账号',
      dataIndex: 'card_number',
      key: 'card_number'
    },
    {
      title: '交易记录数',
      dataIndex: 'transaction_count',
      key: 'transaction_count'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button type="link">查看交易详情</Button>
      )
    }
  ];
  
  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p>加载项目数据...</p>
      </div>
    );
  }
  
  if (!project) {
    return (
      <Empty
        description="找不到项目信息"
        style={{ margin: '50px 0' }}
      >
        <Button type="primary" onClick={() => navigate('/projects')}>
          返回项目列表
        </Button>
      </Empty>
    );
  }
  
  return (
    <div>
      <Button
        icon={<ArrowLeftOutlined />}
        style={{ marginBottom: 16 }}
        onClick={() => navigate('/projects')}
      >
        返回项目列表
      </Button>
      
      <Card>
        <Row gutter={[16, 16]}>
          <Col span={18}>
            <Title level={3}>
              {project.project_name}
              {project.is_active && <Tag color="green" style={{ marginLeft: 8 }}>活动</Tag>}
            </Title>
            <Descriptions column={2}>
              <Descriptions.Item label="关联人员">
                <Space>
                  <UserOutlined />
                  {project.person_name}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(project.created_at).toLocaleString('zh-CN')}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {new Date(project.updated_at).toLocaleString('zh-CN')}
              </Descriptions.Item>
              <Descriptions.Item label="项目描述">
                {project.description || '暂无描述'}
              </Descriptions.Item>
            </Descriptions>
          </Col>
          
          <Col span={6}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button 
                type="primary" 
                icon={<FileOutlined />}
                block
                onClick={() => navigate('/import', { state: { projectId } })}
              >
                导入流水数据
              </Button>
                              <Button icon={<CustomExportOutlined />} block disabled>
                导出分析报告
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>
      
      <Card style={{ marginTop: 16 }}>
        <Tabs defaultActiveKey="accounts">
          <TabPane tab="账户信息" key="accounts">
            <div style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <Statistic 
                    title="账户总数" 
                    value={accounts.length} 
                    prefix={<UserOutlined />} 
                  />
                </Col>
                <Col span={8}>
                  <Statistic 
                    title="交易记录总数" 
                    value={accounts.reduce((sum, acc) => sum + acc.transaction_count, 0)} 
                    prefix={<FileOutlined />} 
                  />
                </Col>
                <Col span={8}>
                  <Statistic 
                    title="银行种类" 
                    value={new Set(accounts.map(acc => acc.bank_name)).size} 
                    prefix={<BankOutlined />} 
                  />
                </Col>
              </Row>
            </div>
            
            <Table 
              columns={accountColumns} 
              dataSource={accounts} 
              rowKey="account_id"
              pagination={false}
            />
          </TabPane>
          <TabPane tab="交易分析" key="analytics" disabled>
            <Empty description="此功能即将推出" />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default ProjectDetail; 