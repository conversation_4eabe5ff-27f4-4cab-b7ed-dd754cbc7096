"""
银行流水文件特征提取器
用于分析Excel文件的结构和内容特征，为解析器选择提供依据
"""
import os
import logging
import pandas as pd
import openpyxl
import xlrd
from typing import Dict, List, Any, Optional, Tuple, Set
import re
from collections import Counter

logger = logging.getLogger(__name__)

class BankFileFeatureExtractor:
    """
    银行流水文件特征提取器
    分析Excel文件的结构和内容特征，为解析器选择提供依据
    """
    
    def __init__(self):
        """初始化特征提取器"""
        # 银行关键词 - 目前只支持工商银行，未来将扩展到其他银行
        self.bank_keywords = {
            "中国工商银行": ["工商银行", "ICBC", "中国工商银行", "工行"],
            # 预留：未来将添加其他银行的关键词定义
            # "中国农业银行": ["农业银行", "ABC", "中国农业银行", "农行"],
            # "中国建设银行": ["建设银行", "CCB", "中国建设银行", "建行"],
            # ... 其他银行将在开发时添加
            
            # 通用解析器关键词 - 用于识别用户手工调整的通用格式
            "通用解析器": ["通用格式", "标准格式", "手工调整", "GENERIC_PARSER", "UNIVERSAL"]
        }
        
        # 常见账户类型关键词
        self.account_type_keywords = {
            "个人账户": ["个人", "储蓄", "活期", "信用卡", "借记卡", "长城", "牡丹"],
            "对公账户": ["对公", "企业", "公司", "商户", "基本户", "一般户", "专用户"]
        }
        
        # 交易数据特征关键词
        self.transaction_keywords = [
            "交易日期", "交易时间", "交易金额", "借方金额", "贷方金额",
            "余额", "对方户名", "对方账号", "摘要", "交易类型", "借贷标志",
            "入账", "支出", "收入", "汇款", "转账", "取款", "存款", "交易账号"
        ]
        
    def extract_features(self, file_path: str) -> Dict[str, Any]:
        """
        提取文件特征
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict[str, Any]: 特征字典
        """
        file_ext = os.path.splitext(file_path)[1].lower()
        features = {
            "file_path": file_path,
            "file_name": os.path.basename(file_path),
            "file_extension": file_ext,
            "sheet_count": 0,
            "sheets": [],
            "detected_bank": None,
            "account_type": None,
            "transaction_data_found": False,
            "transaction_headers": [],
            "special_markers": [],
            "sheet_analysis": []
        }
        
        try:
            # 分析Excel文件结构
            if file_ext in ['.xlsx', '.xls']:
                if file_ext == '.xlsx':
                    workbook = openpyxl.load_workbook(file_path, read_only=True, data_only=True)
                    features["sheet_count"] = len(workbook.sheetnames)
                    features["sheets"] = workbook.sheetnames
                    
                    # 分析每个工作表
                    for sheet_name in workbook.sheetnames:
                        sheet_features = self._analyze_sheet_openpyxl(workbook, sheet_name)
                        features["sheet_analysis"].append(sheet_features)
                        
                elif file_ext == '.xls':
                    workbook = xlrd.open_workbook(file_path)
                    features["sheet_count"] = workbook.nsheets
                    features["sheets"] = workbook.sheet_names()
                    
                    # 分析每个工作表
                    for sheet_idx in range(workbook.nsheets):
                        sheet = workbook.sheet_by_index(sheet_idx)
                        sheet_features = self._analyze_sheet_xlrd(sheet)
                        features["sheet_analysis"].append(sheet_features)
            
            # 汇总特征分析
            features = self._summarize_features(features)
            
            return features
        except Exception as e:
            logger.error(f"提取文件特征失败: {str(e)}")
            return features
    
    def _analyze_sheet_openpyxl(self, workbook: openpyxl.Workbook, sheet_name: str) -> Dict[str, Any]:
        """
        分析openpyxl工作表特征
        
        Args:
            workbook: openpyxl工作簿
            sheet_name: 工作表名称
            
        Returns:
            Dict[str, Any]: 工作表特征
        """
        sheet = workbook[sheet_name]
        sheet_features = {
            "sheet_name": sheet_name,
            "row_count": sheet.max_row,
            "column_count": sheet.max_column,
            "headers": [],
            "keywords": [],
            "potential_accounts": [],
            "bank_keywords": [],
            "account_type_keywords": [],
            "transaction_pattern_score": 0
        }
        
        # 扫描前30行寻找特征
        for row_idx in range(1, min(30, sheet.max_row + 1)):
            row_text = ' '.join(str(cell.value or '') for cell in sheet[row_idx])
            
            # 提取关键词
            sheet_features["keywords"].extend(self._extract_keywords(row_text))
            
            # 提取银行关键词
            for bank, keywords in self.bank_keywords.items():
                if any(kw in row_text for kw in keywords):
                    sheet_features["bank_keywords"].append(bank)
            
            # 提取账户类型关键词
            for acc_type, keywords in self.account_type_keywords.items():
                if any(kw in row_text for kw in keywords):
                    sheet_features["account_type_keywords"].append(acc_type)
            
            # 寻找可能的账户信息
            account_patterns = [
                r'账[号户]\s*[：:]\s*(\d+)',
                r'卡号[：:]\s*(\d+)',
                r'账[号户][:,：]\s*[*＊]+(\d{4})',  # 匹配尾号模式
                r'([0-9]{4}[- ][0-9]{4}[- ][0-9]{4}[- ][0-9]{4})',  # 信用卡格式
            ]
            for pattern in account_patterns:
                matches = re.findall(pattern, row_text)
                if matches:
                    sheet_features["potential_accounts"].extend(matches)
            
            # 寻找表头
            header_score = 0
            for kw in self.transaction_keywords:
                if kw in row_text:
                    header_score += 1
            
            if header_score >= 3:  # 如果一行包含至少3个交易关键词，可能是表头
                headers = [str(cell.value or '') for cell in sheet[row_idx] if cell.value]
                sheet_features["headers"] = [h for h in headers if len(h.strip()) > 0]
                sheet_features["transaction_pattern_score"] = header_score
        
        return sheet_features
    
    def _analyze_sheet_xlrd(self, sheet: xlrd.sheet.Sheet) -> Dict[str, Any]:
        """
        分析xlrd工作表特征
        
        Args:
            sheet: xlrd工作表
            
        Returns:
            Dict[str, Any]: 工作表特征
        """
        sheet_features = {
            "sheet_name": sheet.name,
            "row_count": sheet.nrows,
            "column_count": sheet.ncols,
            "headers": [],
            "keywords": [],
            "potential_accounts": [],
            "bank_keywords": [],
            "account_type_keywords": [],
            "transaction_pattern_score": 0
        }
        
        # 扫描前30行寻找特征
        for row_idx in range(min(30, sheet.nrows)):
            row_values = sheet.row_values(row_idx)
            row_text = ' '.join(str(value) for value in row_values)
            
            # 提取关键词
            sheet_features["keywords"].extend(self._extract_keywords(row_text))
            
            # 提取银行关键词
            for bank, keywords in self.bank_keywords.items():
                if any(kw in row_text for kw in keywords):
                    sheet_features["bank_keywords"].append(bank)
            
            # 提取账户类型关键词
            for acc_type, keywords in self.account_type_keywords.items():
                if any(kw in row_text for kw in keywords):
                    sheet_features["account_type_keywords"].append(acc_type)
            
            # 寻找可能的账户信息
            account_patterns = [
                r'账[号户]\s*[：:]\s*(\d+)',
                r'卡号[：:]\s*(\d+)',
                r'账[号户][:,：]\s*[*＊]+(\d{4})',  # 匹配尾号模式
                r'([0-9]{4}[- ][0-9]{4}[- ][0-9]{4}[- ][0-9]{4})',  # 信用卡格式
            ]
            for pattern in account_patterns:
                matches = re.findall(pattern, row_text)
                if matches:
                    sheet_features["potential_accounts"].extend(matches)
            
            # 寻找表头
            header_score = 0
            for kw in self.transaction_keywords:
                if kw in row_text:
                    header_score += 1
            
            if header_score >= 3:  # 如果一行包含至少3个交易关键词，可能是表头
                headers = [str(value) for value in row_values if value]
                sheet_features["headers"] = [h for h in headers if len(h.strip()) > 0]
                sheet_features["transaction_pattern_score"] = header_score
        
        return sheet_features
    
    def _extract_keywords(self, text: str) -> List[str]:
        """
        从文本中提取关键词
        
        Args:
            text: 文本
            
        Returns:
            List[str]: 关键词列表
        """
        # 简单实现：分词并提取可能的关键词
        # 实际应用中可以使用更复杂的NLP技术
        keywords = []
        patterns = [
            r'([^\s,，、]{2,}银行)',
            r'(对账单)',
            r'(流水)',
            r'(交易明细)',
            r'(对公账户|个人账户)',
            r'(信用卡|储蓄卡|借记卡)',
            r'(结单周期|账单周期)',
            r'(人民币|外币|美元|欧元)',
            r'(起止日期|开始日期|终止日期)',
            r'(交易日期|交易时间|交易金额|交易账号|摘要)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            keywords.extend(matches)
        
        return keywords
    
    def _summarize_features(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """
        汇总分析提取的特征
        
        Args:
            features: 特征字典
            
        Returns:
            Dict[str, Any]: 更新后的特征字典
        """
        # 合并所有工作表的关键词
        all_keywords = []
        all_bank_keywords = []
        all_account_types = []
        all_headers = []
        transaction_sheets = []
        
        for sheet in features["sheet_analysis"]:
            all_keywords.extend(sheet["keywords"])
            all_bank_keywords.extend(sheet["bank_keywords"])
            all_account_types.extend(sheet["account_type_keywords"])
            
            # 如果工作表可能包含交易数据
            if sheet["transaction_pattern_score"] >= 3:
                transaction_sheets.append(sheet["sheet_name"])
                all_headers.extend(sheet["headers"])
        
        # 检测银行
        if all_bank_keywords:
            bank_counter = Counter(all_bank_keywords)
            features["detected_bank"] = bank_counter.most_common(1)[0][0]
        
        # 检测账户类型
        if all_account_types:
            acc_type_counter = Counter(all_account_types)
            features["account_type"] = acc_type_counter.most_common(1)[0][0]
        
        # 检测是否找到交易数据
        features["transaction_data_found"] = len(transaction_sheets) > 0
        features["transaction_sheets"] = transaction_sheets
        features["transaction_headers"] = list(set(all_headers))
        
        # 提取特殊标记
        special_markers = []
        for kw in all_keywords:
            if kw in ["对账单", "流水", "交易明细", "结单", "账单"]:
                special_markers.append(kw)
        features["special_markers"] = list(set(special_markers))
        
        return features 