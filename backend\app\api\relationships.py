"""
人物关系图谱管理API
"""
import logging
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from fastapi import APIRouter, Depends, HTTPException, status, Query

from ..middleware.auth_middleware import get_current_user, get_user_db
from ..models.duckdb_models import DuckDBRelationship as Relationship

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/")
async def get_relationships(
    project_id: str = Query(..., description="项目ID"),
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> List[Dict[str, Any]]:
    """获取项目的人物关系列表"""
    try:
        logger.info(f"用户 '{current_user}' 获取项目 {project_id} 的人物关系列表")
        
        relationships = db.query(Relationship).filter(
            Relationship.project_id == project_id
        ).order_by(Relationship.created_at.desc()).all()
        
        result = []
        for rel in relationships:
            result.append({
                "relationship_id": rel.relationship_id,
                "project_id": rel.project_id,
                "person_a": rel.person_a,
                "person_b": rel.person_b,
                "relationship_type": rel.relationship_type,
                "status": rel.status,
                "description": rel.description,
                "evidence": rel.evidence,
                "notes": rel.notes,
                "created_at": rel.created_at.isoformat() if rel.created_at else None,
                "updated_at": rel.updated_at.isoformat() if rel.updated_at else None
            })
        
        logger.info(f"用户 '{current_user}' 获取到 {len(result)} 条人物关系")
        return result
        
    except Exception as e:
        logger.error(f"用户 '{current_user}' 获取人物关系列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取人物关系列表失败: {str(e)}"
        )

@router.post("/")
async def create_relationship(
    relationship_data: Dict[str, Any],
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> Dict[str, Any]:
    """创建人物关系"""
    try:
        logger.info(f"用户 '{current_user}' 创建人物关系: {relationship_data.get('relationship_type', '未知')}")
        
        new_relationship = Relationship(
            project_id=relationship_data["project_id"],
            person_a=relationship_data.get("person_a", ""),
            person_b=relationship_data.get("person_b", ""),
            relationship_type=relationship_data.get("relationship_type", ""),
            status=relationship_data.get("status", ""),
            description=relationship_data.get("description", ""),
            evidence=relationship_data.get("evidence", ""),
            notes=relationship_data.get("notes", ""),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        db.add(new_relationship)
        db.commit()
        db.refresh(new_relationship)
        
        logger.info(f"用户 '{current_user}' 成功创建人物关系: {new_relationship.relationship_id}")
        
        return {
            "relationship_id": new_relationship.relationship_id,
            "project_id": new_relationship.project_id,
            "person_a": new_relationship.person_a,
            "person_b": new_relationship.person_b,
            "relationship_type": new_relationship.relationship_type,
            "status": new_relationship.status,
            "description": new_relationship.description,
            "evidence": new_relationship.evidence,
            "notes": new_relationship.notes,
            "created_at": new_relationship.created_at.isoformat() if new_relationship.created_at else None,
            "updated_at": new_relationship.updated_at.isoformat() if new_relationship.updated_at else None
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"用户 '{current_user}' 创建人物关系失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建人物关系失败: {str(e)}"
        )

@router.put("/{relationship_id}")
async def update_relationship(
    relationship_id: str,
    relationship_data: Dict[str, Any],
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> Dict[str, Any]:
    """更新人物关系信息"""
    try:
        logger.info(f"用户 '{current_user}' 更新人物关系，ID: {relationship_id}")
        
        relationship = db.query(Relationship).filter(
            Relationship.relationship_id == relationship_id
        ).first()
        if not relationship:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"人物关系 {relationship_id} 不存在"
            )
        
        # 更新字段
        for field, value in relationship_data.items():
            if field != "relationship_id" and hasattr(relationship, field):
                setattr(relationship, field, value)
        
        relationship.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(relationship)
        
        logger.info(f"用户 '{current_user}' 成功更新人物关系: {relationship.relationship_id}")
        
        return {
            "relationship_id": relationship.relationship_id,
            "project_id": relationship.project_id,
            "person_a": relationship.person_a,
            "person_b": relationship.person_b,
            "relationship_type": relationship.relationship_type,
            "status": relationship.status,
            "description": relationship.description,
            "evidence": relationship.evidence,
            "notes": relationship.notes,
            "created_at": relationship.created_at.isoformat() if relationship.created_at else None,
            "updated_at": relationship.updated_at.isoformat() if relationship.updated_at else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"用户 '{current_user}' 更新人物关系失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新人物关系失败: {str(e)}"
        )

@router.delete("/{relationship_id}")
async def delete_relationship(
    relationship_id: str,
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> Dict[str, str]:
    """删除人物关系"""
    try:
        logger.info(f"用户 '{current_user}' 删除人物关系，ID: {relationship_id}")
        
        relationship = db.query(Relationship).filter(
            Relationship.relationship_id == relationship_id
        ).first()
        if not relationship:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"人物关系 {relationship_id} 不存在"
            )
        
        db.delete(relationship)
        db.commit()
        
        logger.info(f"用户 '{current_user}' 成功删除人物关系: {relationship.relationship_id}")
        
        return {"message": "人物关系删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"用户 '{current_user}' 删除人物关系失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除人物关系失败: {str(e)}"
        ) 