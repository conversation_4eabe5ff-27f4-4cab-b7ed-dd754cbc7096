import React, { useState, useEffect } from 'react';
import { 
  Table, Card, Button, Space, Modal, Form, Input, Select, 
  message, Tooltip, Tabs, Typography, Collapse, Tag
} from 'antd';
import { 
  PlusOutlined, EditOutlined, DeleteOutlined, 
  ImportOutlined, InfoCircleOutlined, CodeOutlined 
} from '@ant-design/icons';
import { templateApi } from '../../services/api';

const { TabPane } = Tabs;
const { Text } = Typography;
const { Panel } = Collapse;
const { TextArea } = Input;
const { Option } = Select;

/**
 * 模板设置页面组件
 * 
 * @returns {JSX.Element} 模板设置页面
 */
const TemplateSettings = () => {
  const [loading, setLoading] = useState(true);
  const [templates, setTemplates] = useState([]);
  const [bankList, setBankList] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [currentTemplate, setCurrentTemplate] = useState(null);
  const [activeTab, setActiveTab] = useState('list');

  useEffect(() => {
    // 模拟数据加载
    setTimeout(() => {
      setBankList([
        { bank_id: '1', bank_name: '中国工商银行' },
        { bank_id: '2', bank_name: '中国农业银行' },
        { bank_id: '3', bank_name: '中国银行' },
        { bank_id: '4', bank_name: '中国建设银行' },
        { bank_id: '5', bank_name: '交通银行' }
      ]);
      
      setTemplates([
        {
          template_id: 'template1',
          template_name: '工商银行个人对账单',
          bank_name: '中国工商银行',
          template_type: '个人账户',
          created_at: '2024-05-01T10:00:00',
          sheet_index: 0,
          header_row: 3,
          data_start_row: 4,
          footer_row: -1
        },
        {
          template_id: 'template2',
          template_name: '工商银行企业对账单',
          bank_name: '中国工商银行',
          template_type: '企业账户',
          created_at: '2024-05-01T11:00:00',
          sheet_index: 0,
          header_row: 5,
          data_start_row: 6,
          footer_row: -1
        },
        {
          template_id: 'template3',
          template_name: '农业银行个人账单',
          bank_name: '中国农业银行',
          template_type: '个人账户',
          created_at: '2024-05-02T14:00:00',
          sheet_index: 0,
          header_row: 2,
          data_start_row: 3,
          footer_row: -1
        }
      ]);
      
      setLoading(false);
    }, 1000);
    
    // 实际代码应该调用API
    // Promise.all([
    //   templateApi.getTemplates(),
    //   bankApi.getBanks()
    // ]).then(([templatesData, banksData]) => {
    //   setTemplates(templatesData);
    //   setBankList(banksData);
    //   setLoading(false);
    // });
  }, []);

  const showModal = (template = null) => {
    setCurrentTemplate(template);
    setIsModalVisible(true);
    
    if (template) {
      form.setFieldsValue({
        template_name: template.template_name,
        bank_name: template.bank_name,
        template_type: template.template_type,
        sheet_index: template.sheet_index,
        header_row: template.header_row,
        data_start_row: template.data_start_row,
        footer_row: template.footer_row,
        mapping_config: JSON.stringify(template.mapping_config || {}, null, 2)
      });
    } else {
      form.resetFields();
      form.setFieldsValue({
        sheet_index: 0,
        header_row: 0,
        data_start_row: 1,
        footer_row: -1,
        mapping_config: JSON.stringify({
          date: { column: "交易日期", format: "YYYY-MM-DD" },
          amount: { column: "金额", is_debit: true },
          balance: { column: "余额" },
          description: { column: "摘要" },
          counterparty: { column: "对方户名" },
          counterparty_account: { column: "对方账号" }
        }, null, 2)
      });
    }
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      try {
        values.mapping_config = JSON.parse(values.mapping_config);
      } catch (error) {
        message.error('映射配置JSON格式错误');
        return;
      }
      
      if (currentTemplate) {
        // 编辑现有模板
        // 实际代码应该调用API
        // await templateApi.updateTemplate(currentTemplate.template_id, values);
        setTemplates(templates.map(t => 
          t.template_id === currentTemplate.template_id 
            ? { ...t, ...values } 
            : t
        ));
        message.success('模板更新成功');
      } else {
        // 创建新模板
        // 实际代码应该调用API
        // const newTemplate = await templateApi.createTemplate(values);
        const newTemplate = {
          template_id: Date.now().toString(),
          ...values,
          created_at: new Date().toISOString()
        };
        setTemplates([...templates, newTemplate]);
        message.success('模板创建成功');
      }
      
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('表单错误:', error);
    }
  };

  const handleDelete = (templateId) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除此模板吗？此操作不可恢复。',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        // 实际代码应该调用API
        // await templateApi.deleteTemplate(templateId);
        setTemplates(templates.filter(t => t.template_id !== templateId));
        message.success('模板已删除');
      }
    });
  };

  const handleImportTemplates = async () => {
    setLoading(true);
    
    try {
      // 实际代码应该调用API
      // await templateApi.importTemplates();
      // const updatedTemplates = await templateApi.getTemplates();
      // setTemplates(updatedTemplates);
      
      setTimeout(() => {
        message.success('成功导入 2 个新模板');
        
        // 模拟新增模板
        setTemplates([
          ...templates,
          {
            template_id: 'new_template1',
            template_name: '建设银行对公账单',
            bank_name: '中国建设银行',
            template_type: '企业账户',
            created_at: new Date().toISOString(),
            sheet_index: 0,
            header_row: 4,
            data_start_row: 5,
            footer_row: -1
          },
          {
            template_id: 'new_template2',
            template_name: '中国银行个人账单',
            bank_name: '中国银行',
            template_type: '个人账户',
            created_at: new Date().toISOString(),
            sheet_index: 0,
            header_row: 3,
            data_start_row: 4,
            footer_row: -1
          }
        ]);
        
        setLoading(false);
      }, 2000);
    } catch (error) {
      message.error('导入模板失败');
      setLoading(false);
    }
  };

  const columns = [
    {
      title: '模板名称',
      dataIndex: 'template_name',
      key: 'template_name',
      sorter: (a, b) => a.template_name.localeCompare(b.template_name)
    },
    {
      title: '银行',
      dataIndex: 'bank_name',
      key: 'bank_name',
      filters: [...new Set(templates.map(t => t.bank_name))].map(bank => ({ text: bank, value: bank })),
      onFilter: (value, record) => record.bank_name === value
    },
    {
      title: '类型',
      dataIndex: 'template_type',
      key: 'template_type',
      render: (type) => {
        let color = type === '个人账户' ? 'blue' : 'orange';
        return (
          <Tag color={color}>{type}</Tag>
        );
      },
      filters: [
        { text: '个人账户', value: '个人账户' },
        { text: '企业账户', value: '企业账户' }
      ],
      onFilter: (value, record) => record.template_type === value
    },
    {
      title: '表格设置',
      key: 'sheet_config',
      render: (_, record) => (
        <Tooltip title={
          <div>
            <p>工作表索引: {record.sheet_index}</p>
            <p>表头行: {record.header_row}</p>
            <p>数据开始行: {record.data_start_row}</p>
            <p>结束行: {record.footer_row === -1 ? '自动' : record.footer_row}</p>
          </div>
        }>
          <Button icon={<InfoCircleOutlined />} type="text">表格设置</Button>
        </Tooltip>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: text => new Date(text).toLocaleString('zh-CN'),
      sorter: (a, b) => new Date(a.created_at) - new Date(b.created_at),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="编辑">
            <Button 
              icon={<EditOutlined />} 
              onClick={() => showModal(record)} 
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button 
              danger 
              icon={<DeleteOutlined />} 
              onClick={() => handleDelete(record.template_id)} 
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const helpContent = (
    <Collapse>
      <Panel header="模板设置说明" key="1">
        <p>模板用于解析不同银行的Excel对账单格式。每个银行可以配置多个模板，适用于不同类型的账户（个人/企业）。</p>
        <p>主要配置项：</p>
        <ul>
          <li><Text strong>工作表索引</Text>：Excel中包含数据的工作表（从0开始）</li>
          <li><Text strong>表头行</Text>：包含列名的行号（从0开始）</li>
          <li><Text strong>数据开始行</Text>：实际数据开始的行号</li>
          <li><Text strong>结束行</Text>：数据结束的行号（-1表示自动检测）</li>
        </ul>
      </Panel>
      <Panel header="映射配置说明" key="2">
        <p>映射配置用JSON格式定义，将银行流水中的列映射到标准字段：</p>
        <ul>
          <li><Text code>date</Text>：交易日期</li>
          <li><Text code>amount</Text>：交易金额</li>
          <li><Text code>balance</Text>：账户余额</li>
          <li><Text code>description</Text>：交易描述/摘要</li>
          <li><Text code>counterparty</Text>：对方户名</li>
          <li><Text code>counterparty_account</Text>：对方账号</li>
        </ul>
        <Text type="secondary">格式示例：</Text>
        <pre style={{ background: '#f5f5f5', padding: '10px' }}>
{`{
  "date": { "column": "交易日期", "format": "YYYY-MM-DD" },
  "amount": { "column": "金额", "is_debit": true },
  "balance": { "column": "余额" },
  "description": { "column": "摘要" },
  "counterparty": { "column": "对方户名" },
  "counterparty_account": { "column": "对方账号" }
}`}
        </pre>
      </Panel>
    </Collapse>
  );

  return (
    <div>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="模板列表" key="list">
          <Card 
            title="解析模板管理" 
            extra={
              <Space>
                <Button 
                  icon={<ImportOutlined />} 
                  onClick={handleImportTemplates}
                  loading={loading}
                >
                  导入模板
                </Button>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />} 
                  onClick={() => showModal()}
                >
                  新建模板
                </Button>
              </Space>
            }
          >
            <Table 
              columns={columns} 
              dataSource={templates} 
              rowKey="template_id" 
              loading={loading}
            />
          </Card>
        </TabPane>
        <TabPane tab="使用说明" key="help">
          <Card title="模板设置使用说明">
            {helpContent}
          </Card>
        </TabPane>
      </Tabs>

      <Modal
        title={currentTemplate ? "编辑模板" : "新建模板"}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        okText="保存"
        cancelText="取消"
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="template_name"
            label="模板名称"
            rules={[{ required: true, message: '请输入模板名称' }]}
          >
            <Input placeholder="请输入模板名称" />
          </Form.Item>
          
          <Form.Item
            name="bank_name"
            label="所属银行"
            rules={[{ required: true, message: '请选择银行' }]}
          >
            <Select placeholder="请选择银行">
              {bankList.map(bank => (
                <Option key={bank.bank_id} value={bank.bank_name}>{bank.bank_name}</Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="template_type"
            label="账户类型"
            rules={[{ required: true, message: '请选择账户类型' }]}
          >
            <Select placeholder="请选择账户类型">
              <Option value="个人账户">个人账户</Option>
              <Option value="企业账户">企业账户</Option>
            </Select>
          </Form.Item>
          
          <Collapse style={{ marginBottom: 16 }}>
            <Panel header="Excel表格设置" key="excel_settings">
              <Form.Item
                name="sheet_index"
                label="工作表索引"
                tooltip="Excel中包含数据的工作表索引，从0开始计数"
                rules={[{ required: true, message: '请输入工作表索引' }]}
              >
                <Input type="number" min={0} placeholder="0" />
              </Form.Item>
              
              <Form.Item
                name="header_row"
                label="表头行"
                tooltip="包含列名的行号，从0开始计数"
                rules={[{ required: true, message: '请输入表头行' }]}
              >
                <Input type="number" min={0} placeholder="0" />
              </Form.Item>
              
              <Form.Item
                name="data_start_row"
                label="数据开始行"
                tooltip="实际数据开始的行号，从0开始计数"
                rules={[{ required: true, message: '请输入数据开始行' }]}
              >
                <Input type="number" min={0} placeholder="1" />
              </Form.Item>
              
              <Form.Item
                name="footer_row"
                label="结束行"
                tooltip="数据结束的行号，-1表示自动检测"
                rules={[{ required: true, message: '请输入结束行' }]}
              >
                <Input type="number" placeholder="-1" />
              </Form.Item>
            </Panel>
          </Collapse>
          
          <Form.Item
            name="mapping_config"
            label="映射配置"
            tooltip="配置Excel列与标准字段的映射关系"
            rules={[{ required: true, message: '请输入映射配置' }]}
          >
            <TextArea 
              rows={10} 
              placeholder="映射配置JSON" 
              style={{ fontFamily: 'monospace' }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TemplateSettings; 