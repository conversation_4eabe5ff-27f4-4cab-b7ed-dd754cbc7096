"""
银行流水解析器置信度评估系统
基于4个核心指标的快速评估：
1. 持卡人姓名识别 (25分)
2. 时间格式准确性 (25分) 
3. 账号或卡号识别 (25分)
4. 金额解析能力 (25分)
"""
import re
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class ConfidenceEvaluator:
    """置信度评估器 - 基于4个核心指标的快速评估系统"""
    
    def __init__(self):
        self.core_metrics = {
            'cardholder_name_score': {
                'weight': 25,
                'description': '持卡人姓名识别'
            },
            'time_format_score': {
                'weight': 25,
                'description': '时间格式准确性'
            },
            'account_number_score': {
                'weight': 25,
                'description': '账号或卡号识别'
            },
            'amount_parsing_score': {
                'weight': 25,
                'description': '金额解析能力'
            }
        }
    
    def quick_evaluate_parser(self, parser_class, file_path: str, parser_info: Dict) -> Dict[str, Any]:
        """
        快速评估解析器的4个核心指标
        
        Args:
            parser_class: 解析器类或实例
            file_path: 文件路径
            parser_info: 解析器信息
            
        Returns:
            Dict: 包含4个核心指标的详细评估结果
        """
        try:
            logger.info(f"开始4维度快速评估: {file_path}")
            
            # 🔧 修复：优先使用轻量级extract_sample方法，符合预解析规范
            sample_data = None

            # 1. 优先尝试extract_sample方法（轻量级预解析）
            if hasattr(parser_class, 'extract_sample'):
                try:
                    logger.info("🚀 优先使用extract_sample进行轻量级预解析(limit=3)")
                    fallback = parser_class.extract_sample(file_path, limit=3)
                    # 归一到评估所需的最小结构
                    sample_data = {
                        'accounts': (fallback.get('accounts') or [])[:1],
                        'transactions': (fallback.get('transactions') or [])[:3]
                    } if isinstance(fallback, dict) else None

                    if sample_data and (sample_data.get('accounts') or sample_data.get('transactions')):
                        logger.info(f"✅ extract_sample成功获取样本数据: {len(sample_data.get('accounts', []))}个账户, {len(sample_data.get('transactions', []))}条交易")
                    else:
                        logger.info("⚠️ extract_sample返回空数据，将尝试完整解析")
                        sample_data = None
                except Exception as e:
                    logger.warning(f"extract_sample失败: {e}，将尝试完整解析")
                    sample_data = None

            # 2. 只有在extract_sample失败或无数据时才回退到完整解析
            if not sample_data:
                logger.info("🔄 回退到完整解析方法")
                sample_data = self._extract_sample_data_for_evaluation(parser_class, file_path)

            if not sample_data:
                logger.warning("无法提取样本数据，返回空评估结果")
                return {
                    'core_metrics': {},
                    'evaluation_breakdown': {},
                    'sample_analysis': {},
                    'total_score': 0,
                    'summary': '无法提取样本数据进行评估'
                }
            
            # 🌟 执行4个核心指标的评估
            evaluation_results = self._evaluate_core_metrics(sample_data)
        
            # 计算总分
            total_score = 0
            metrics_summary = {}
            
            for metric_name, metric_config in self.core_metrics.items():
                if metric_name in evaluation_results:
                    metric_result = evaluation_results[metric_name]
                    score = metric_result.get('score', 0)
                    total_score += score
                    
                    metrics_summary[metric_name] = {
                        'score': score,
                        'max_score': metric_config['weight'],
                        'description': metric_config['description'],
                        'details': metric_result.get('details', ''),
                        'percentage': round((score / metric_config['weight']) * 100, 1)
                    }
            
            # 构建完整的返回结果
            evaluation_summary = {
                'core_metrics': metrics_summary,
                'evaluation_breakdown': {
                    'total_score': round(total_score, 1),
                    'max_possible_score': 100,
                    'overall_percentage': round(total_score, 1),
                    'account_count': len(sample_data.get('accounts', [])),
                    'transaction_count': len(sample_data.get('transactions', []))
                },
                'sample_analysis': {
                    'sample_accounts': sample_data.get('accounts', [])[:2],  # 前2个账户作为样本
                    'sample_transactions': sample_data.get('transactions', [])[:3],  # 前3条交易作为样本
                    'data_extraction_success': True
                },
                'total_score': round(total_score, 1),
                'summary': self._generate_evaluation_summary(total_score, metrics_summary)
            }
            
            logger.info(f"4维度评估完成，总分: {total_score:.1f}/100")
            return evaluation_summary
            
        except Exception as e:
            logger.error(f"4维度快速评估失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            
            return {
                'core_metrics': {},
                'evaluation_breakdown': {'total_score': 0, 'error': str(e)},
                'sample_analysis': {'data_extraction_success': False, 'error': str(e)},
                'total_score': 0,
                'summary': f'评估失败: {str(e)}'
            }
    
    def _extract_sample_data(self, parser_instance) -> Optional[Dict]:
        """🔧 修复：基于真实解析能力进行评估，而不是样本提取"""
        try:
            # 🔧 关键修复：使用真实的parse方法进行评估
            logger.info("使用真实parse方法进行置信度评估")

            if hasattr(parser_instance, 'parse'):
                # 使用解析器的真实解析能力
                parse_result = parser_instance.parse()

                if isinstance(parse_result, dict):
                    accounts = parse_result.get('accounts', [])
                    transactions = parse_result.get('transactions', [])
                    success = parse_result.get('success', False)

                    # 🔧 修复：处理不同的交易数据结构
                    # 如果transactions为空，尝试从transactions_by_account获取
                    if not transactions:
                        transactions_by_account = parse_result.get('transactions_by_account', {})
                        if transactions_by_account:
                            # 合并所有账户的交易数据
                            all_transactions = []
                            for account_transactions in transactions_by_account.values():
                                if isinstance(account_transactions, list):
                                    all_transactions.extend(account_transactions)
                            transactions = all_transactions
                            logger.info(f"从transactions_by_account获取到{len(transactions)}条交易")

                    # 只有真实解析成功且有数据才认为有效
                    if success and (len(accounts) > 0 or len(transactions) > 0):
                        # 限制样本大小以提高评估效率
                        sample_transactions = transactions[:10] if transactions else []

                        sample_data = {
                            'accounts': accounts,
                            'transactions': sample_transactions,
                            'metadata': {
                                'total_accounts_found': len(accounts),
                                'total_transactions_found': len(transactions),
                                'sample_size': len(sample_transactions),
                                'parsing_successful': True,
                                'evaluation_mode': 'real_parse'
                            }
                        }

                        logger.info(f"真实解析成功: {len(accounts)}个账户, {len(transactions)}条交易 (样本:{len(sample_transactions)}条)")
                        return sample_data
                    else:
                        logger.warning(f"真实解析失败或无数据: success={success}, accounts={len(accounts)}, transactions={len(transactions)}")
                        return None
                else:
                    logger.warning(f"parse方法返回异常格式: {type(parse_result)}")
                    return None
            else:
                logger.warning("解析器没有parse方法，无法进行真实评估")
                return None

        except Exception as e:
            logger.error(f"提取样本数据失败: {str(e)}")
            return None
    
    def _extract_sample_data_for_evaluation(self, parser_class, file_path: str) -> Optional[Dict]:
        """
        提取样本数据用于4维度评估
        �� 修复：正确处理插件实例的文件路径设置，防止缓存数据污染
        """
        try:
            logger.info(f"尝试提取样本数据: {file_path}")
            
            # 🔧 关键修复：创建全新的解析器实例，避免状态污染
            if hasattr(parser_class, 'parse'):
                # 🔧 确保每次都是干净的实例
                if hasattr(parser_class, '__init__'):
                    # 重新初始化解析器实例，清除可能的状态残留
                    parser_class.__init__(file_path)
                else:
                    # 设置文件路径到插件实例
                    parser_class.file_path = file_path
                    logger.info(f"设置插件实例文件路径: {file_path}")
                
                # 🔧 关键修复：使用真实的parse方法进行评估（兼容两种签名）
                logger.info("使用真实parse方法进行置信度评估（兼容签名）")
                try:
                    # 优先尝试带文件路径
                    parse_result = parser_class.parse(file_path)
                except TypeError as te:
                    logger.info(f"parse(file_path)签名不兼容，尝试无参调用: {te}")
                    # 兼容无参签名，但先确保file_path已设置
                    if hasattr(parser_class, 'file_path'):
                        parser_class.file_path = file_path
                    parse_result = parser_class.parse()
                except Exception as e:
                    logger.error(f"parse方法调用失败: {e}")
                    return None

                if isinstance(parse_result, dict):
                    accounts = parse_result.get('accounts', [])
                    transactions = parse_result.get('transactions', [])
                    success = parse_result.get('success', False)

                    # 🔧 修复：处理不同的交易数据结构
                    # 如果transactions为空，尝试从transactions_by_account获取
                    if not transactions:
                        transactions_by_account = parse_result.get('transactions_by_account', {})
                        if transactions_by_account:
                            # 合并所有账户的交易数据
                            all_transactions = []
                            for account_transactions in transactions_by_account.values():
                                if isinstance(account_transactions, list):
                                    all_transactions.extend(account_transactions)
                            transactions = all_transactions
                            logger.info(f"从transactions_by_account获取到{len(transactions)}条交易")

                    # 只有真实解析成功且有数据才认为有效
                    if success and (len(accounts) > 0 or len(transactions) > 0):
                        # 限制样本大小以提高评估效率
                        sample_transactions = transactions[:10] if transactions else []

                        sample_data = {
                            'accounts': accounts,
                            'transactions': sample_transactions,
                            'metadata': {
                                'total_accounts_found': len(accounts),
                                'total_transactions_found': len(transactions),
                                'sample_size': len(sample_transactions),
                                'parsing_successful': True,
                                'evaluation_mode': 'real_parse'
                            }
                        }

                        logger.info(f"真实解析成功: {len(accounts)}个账户, {len(transactions)}条交易 (样本:{len(sample_transactions)}条)")
                        return sample_data
                    else:
                        logger.warning(f"真实解析失败或无数据: success={success}, accounts={len(accounts)}, transactions={len(transactions)}")
                        return None
                else:
                    logger.warning(f"parse方法返回异常格式: {type(parse_result)}")
                    return None
            else:
                logger.warning("解析器没有parse方法，无法进行真实评估")
                return None

        except Exception as e:
            logger.error(f"提取样本数据失败: {str(e)}")
            return None

        except Exception as e:
            logger.error(f"提取样本数据失败: {str(e)}")
            return None
    
    def _validate_sample_data(self, sample_data: Dict, file_path: str) -> bool:
        """
        验证样本数据的有效性，防止使用错误的缓存数据
        
        Args:
            sample_data: 样本数据
            file_path: 当前文件路径
            
        Returns:
            bool: 样本数据是否有效
        """
        try:
            # 基础验证
            if not sample_data or not isinstance(sample_data, dict):
                logger.warning("样本数据为空或格式错误")
                return False
            
            accounts = sample_data.get('accounts', [])
            transactions = sample_data.get('transactions', [])
            
            # 🔧 关键验证：检查是否有实际的解析结果
            if not accounts and not transactions:
                logger.warning("样本数据中没有账户和交易信息")
                return False
            
            # 🔧 验证数据结构完整性
            if accounts:
                first_account = accounts[0]
                required_account_fields = ['account_number', 'holder_name']
                missing_fields = [field for field in required_account_fields 
                                if not first_account.get(field)]
                if missing_fields:
                    logger.warning(f"账户数据缺少必要字段: {missing_fields}")
            
            if transactions:
                first_transaction = transactions[0]
                required_transaction_fields = ['transaction_date', 'transaction_amount']
                missing_fields = [field for field in required_transaction_fields 
                                if not first_transaction.get(field)]
                if missing_fields:
                    logger.warning(f"交易数据缺少必要字段: {missing_fields}")
            
            logger.info(f"样本数据验证通过: {len(accounts)}个账户, {len(transactions)}条交易")
            return True
            
        except Exception as e:
            logger.error(f"样本数据验证失败: {e}")
            return False
    
    def _evaluate_core_metrics(self, sample_data: Dict) -> Dict[str, Dict]:
        """评估4个核心指标"""
        accounts = sample_data.get('accounts', [])
        transactions = sample_data.get('transactions', [])
        
        evaluation_results = {}
        
        # 1. 持卡人姓名识别 (25分)
        evaluation_results['cardholder_name_score'] = self._evaluate_holder_name(transactions, accounts)
        
        # 2. 时间格式准确性 (25分)
        evaluation_results['time_format_score'] = self._evaluate_date_format(transactions)
        
        # 3. 账号或卡号识别 (25分)
        evaluation_results['account_number_score'] = self._evaluate_account_identification(transactions, accounts)
        
        # 4. 金额解析能力 (25分)
        evaluation_results['amount_parsing_score'] = self._evaluate_amount_parsing(transactions)
        
        return evaluation_results
    
    def _evaluate_holder_name(self, transactions: List[Dict], accounts: List[Dict]) -> Dict:
        """评估持卡人姓名识别 (25分)"""
        valid_names = 0
        total_names = 0
        details = []
        
        # 检查账户数据中的持卡人姓名 - 支持多种字段名
        for account in accounts:
            # 尝试多种可能的字段名
            cardholder_name = (account.get('cardholder_name', '') or 
                             account.get('holder_name', '') or 
                             account.get('account_name', '') or 
                             account.get('enterprise_name', '')).strip()
            
            if cardholder_name:
                total_names += 1
                
                if self._is_valid_chinese_name(cardholder_name):
                    valid_names += 1
                    details.append(f"有效姓名: {cardholder_name}")
                else:
                    details.append(f"无效姓名: {cardholder_name}")
        
        # 检查交易数据中的持卡人姓名 - 支持多种字段名
        for transaction in transactions[:5]:
            # 尝试多种可能的字段名
            cardholder_name = (transaction.get('cardholder_name', '') or 
                             transaction.get('holder_name', '') or 
                             transaction.get('enterprise_name', '') or 
                             transaction.get('account_name', '')).strip()
            
            if cardholder_name:
                total_names += 1
                
                if self._is_valid_chinese_name(cardholder_name):
                    valid_names += 1
                    details.append(f"有效交易姓名: {cardholder_name}")
                else:
                    details.append(f"无效交易姓名: {cardholder_name}")
        
        if total_names == 0:
            return {'score': 0, 'details': '未找到持卡人姓名字段', 'valid_count': 0, 'total_count': 0}
        
        # 计算得分：有效姓名比例 * 25分
        score = (valid_names / total_names) * 25
        
        return {
            'score': round(score, 1),
            'details': f'有效姓名 {valid_names}/{total_names}',
            'valid_count': valid_names,
            'total_count': total_names,
            'sample_details': details[:3]
        }
    
    def _evaluate_date_format(self, transactions: List[Dict]) -> Dict:
        """评估时间格式准确性 (25分)"""
        valid_dates = 0
        total_dates = 0
        details = []
        
        # 定义可能的时间字段名（按优先级排序）
        time_field_names = [
            'transaction_datetime',  # 完整时间戳
            'transaction_date',      # 只有日期
            'date',                  # 通用日期字段
            'trading_date',          # 交易日期
            'settle_date'            # 结算日期
        ]
        
        for transaction in transactions:
            date_field = None
            
            # 按优先级尝试不同的时间字段名
            for field_name in time_field_names:
                if field_name in transaction and transaction[field_name]:
                    date_field = str(transaction[field_name]).strip()
                    break
            
            if date_field:
                total_dates += 1
                
                if self._is_valid_date_format(date_field):
                    valid_dates += 1
                    details.append(f"有效日期: {date_field}")
                else:
                    details.append(f"无效日期: {date_field}")
        
        if total_dates == 0:
            return {'score': 0, 'details': '未找到日期字段', 'valid_count': 0, 'total_count': 0}
        
        # 计算得分：有效日期比例 * 25分
        score = (valid_dates / total_dates) * 25
        
        return {
            'score': round(score, 1),
            'details': f'有效日期 {valid_dates}/{total_dates}',
            'valid_count': valid_dates,
            'total_count': total_dates,
            'sample_details': details[:3]
        }
    
    def _evaluate_account_identification(self, transactions: List[Dict], accounts: List[Dict]) -> Dict:
        """评估账号或卡号识别 (25分)"""
        valid_accounts = 0
        total_accounts = 0
        details = []
        
        # 检查账户数据中的账号/卡号 - 优化逻辑以达到100%
        for account in accounts:
            account_number = account.get('account_number', '').strip()
            card_number = account.get('card_number', '').strip()
            
            # 只有当账号和卡号都为空时才跳过，否则只要有一个有效就算通过
            if not account_number and not card_number:
                continue  # 跳过完全没有账号信息的账户
            
            total_accounts += 1
            
            # 只要账号或卡号任一有效就算通过
            account_valid = self._is_valid_account_number(account_number) if account_number else False
            card_valid = self._is_valid_account_number(card_number) if card_number else False
            
            if account_valid or card_valid:
                valid_accounts += 1
                valid_number = account_number if account_valid else card_number
                details.append(f"有效账号: {valid_number}")
            else:
                # 只有当两个都无效时才记录为无效
                details.append(f"无效账号: {account_number or card_number}")
        
        # 检查交易数据中的账号 - 应用同样的宽容逻辑
        for transaction in transactions[:5]:
            account_number = transaction.get('account_number', '').strip()
            card_number = transaction.get('card_number', '').strip()
            
            # 跳过完全没有账号信息的交易
            if not account_number and not card_number:
                continue
            
            total_accounts += 1
            
            # 只要账号或卡号任一有效就算通过
            account_valid = self._is_valid_account_number(account_number) if account_number else False
            card_valid = self._is_valid_account_number(card_number) if card_number else False
            
            if account_valid or card_valid:
                valid_accounts += 1
                valid_number = account_number if account_valid else card_number
                details.append(f"有效交易账号: {valid_number}")
            else:
                # 只有当两个都无效时才记录为无效
                details.append(f"无效交易账号: {account_number or card_number}")
        
        if total_accounts == 0:
            return {'score': 0, 'details': '未找到账号字段', 'valid_count': 0, 'total_count': 0}
        
        # 计算得分
        score = (valid_accounts / total_accounts) * 25
        
        return {
            'score': round(score, 1),
            'details': f'有效账号 {valid_accounts}/{total_accounts}',
            'valid_count': valid_accounts,
            'total_count': total_accounts,
            'sample_details': details[:3]
        }
    
    def _evaluate_amount_parsing(self, transactions: List[Dict]) -> Dict:
        """评估金额解析能力 (25分)"""
        valid_amounts = 0
        total_amounts = 0
        details = []
        
        # 定义可能的金额字段名（按优先级排序）
        amount_field_names = [
            'transaction_amount',  # 标准字段名
            'amount',              # 通用金额字段名
            'trade_amount',        # 其他可能的字段名
            'money',               # 通用字段名
            'value'                # 备选字段名
        ]
        
        for transaction in transactions:
            amount = None
            
            # 按优先级尝试不同的金额字段名
            for field_name in amount_field_names:
                if field_name in transaction and transaction[field_name] is not None:
                    amount = transaction[field_name]
                    break
            
            if amount is not None:
                total_amounts += 1
                
                if self._is_valid_amount(amount):
                    valid_amounts += 1
                    details.append(f"有效金额: {amount}")
                else:
                    details.append(f"无效金额: {amount}")
        
        if total_amounts == 0:
            return {'score': 0, 'details': '未找到金额字段', 'valid_count': 0, 'total_count': 0}
        
        # 计算得分
        score = (valid_amounts / total_amounts) * 25
        
        return {
            'score': round(score, 1),
            'details': f'有效金额 {valid_amounts}/{total_amounts}',
            'valid_count': valid_amounts,
            'total_count': total_amounts,
            'sample_details': details[:3]
        }
    
    def _is_valid_chinese_name(self, name: str) -> bool:
        """检查是否为有效的中文姓名 (2-20个中文字符)"""
        if not name or not isinstance(name, str):
            return False
        
        # 移除空格和特殊字符
        clean_name = re.sub(r'[^\u4e00-\u9fff]', '', name)
        return 2 <= len(clean_name) <= 20
    
    def _is_valid_date_format(self, date_str: str) -> bool:
        """检查是否为有效的日期格式 (XXXX-XX-XX格式)"""
        if not date_str or not isinstance(date_str, str):
            return False
        
        # 检查基本格式 YYYY-MM-DD 或 YYYY/MM/DD
        date_pattern = r'^\d{4}[-/]\d{1,2}[-/]\d{1,2}'
        if re.match(date_pattern, date_str):
            return True
        
        # 检查其他常见格式
        other_patterns = [
            r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}',  # YYYY-MM-DD HH:MM:SS
            r'^\d{4}/\d{2}/\d{2}',  # YYYY/MM/DD
            r'^\d{8}$'  # YYYYMMDD
        ]
        
        return any(re.match(pattern, date_str) for pattern in other_patterns)
    
    def _is_valid_account_number(self, account_str: str) -> bool:
        """检查是否为有效的账号 (10-20个数字)"""
        if not account_str or not isinstance(account_str, str):
            return False
        
        # 移除空格和特殊字符，只保留数字
        clean_account = re.sub(r'[^\d]', '', account_str)
        return 10 <= len(clean_account) <= 20
    
    def _is_valid_amount(self, amount) -> bool:
        """检查是否为有效的金额 (能解析出有效数字，包括0)"""
        try:
            if amount is None:
                return False
        
            # 转换为浮点数
            if isinstance(amount, str):
                # 移除货币符号和逗号
                clean_amount = re.sub(r'[^\d.-]', '', amount.replace(',', ''))
                if not clean_amount:
                    return False
                amount_float = float(clean_amount)
            else:
                amount_float = float(amount)
            
            # 检查是否为有效数字，包括0值
            # 只要不是NaN就算有效（包括0值）
            return not (amount_float != amount_float)  # 排除NaN，但允许0值
            
        except (ValueError, TypeError):
            return False
    
    def _generate_match_reason(self, evaluation_results: Dict, sample_data: Dict) -> str:
        """生成匹配原因说明"""
        reasons = []
        
        # 添加各项指标得分
        for metric_key, metric_result in evaluation_results.items():
            metric_name = self.core_metrics[metric_key]['description']
            score = metric_result['score']
            reasons.append(f"{metric_name}: {score}/25分")
        
        # 添加样本数据统计
        accounts_count = sample_data.get('metadata', {}).get('total_accounts_found', 0)
        transactions_count = sample_data.get('metadata', {}).get('total_transactions_found', 0)
        
        if accounts_count > 0 and transactions_count > 0:
            reasons.append(f"解析出{accounts_count}个账户，{transactions_count}条交易")
        
        # 添加评估模式说明
        reasons.append("基于前10条样本数据评估")
        
        return '; '.join(reasons)
    
    def _create_failed_result(self, error_message: str) -> Dict[str, Any]:
        """创建失败结果"""
        return {
            'confidence': 0,
            'confidence_percentage': 0,
            'match_reason': error_message,
            'evaluation_details': {
                metric: {'score': 0, 'details': error_message}
                for metric in self.core_metrics.keys()
            },
            'sample_data': {},
            'evaluation_mode': 'failed',
            'analysis_time': datetime.now().isoformat()
        }

    def _generate_evaluation_summary(self, total_score: float, metrics_summary: Dict) -> str:
        """生成评估摘要"""
        if total_score >= 90:
            level = "优秀"
        elif total_score >= 80:
            level = "良好"
        elif total_score >= 70:
            level = "一般"
        elif total_score >= 50:
            level = "较差"
        else:
            level = "很差"
        
        # 找出得分最高和最低的指标
        best_metric = max(metrics_summary.items(), key=lambda x: x[1]['score'], default=None)
        worst_metric = min(metrics_summary.items(), key=lambda x: x[1]['score'], default=None)
        
        summary_parts = [f"综合评级：{level}（{total_score:.1f}/100分）"]
        
        if best_metric:
            summary_parts.append(f"最佳指标：{best_metric[1]['description']}（{best_metric[1]['score']:.1f}分）")
        
        if worst_metric and worst_metric[1]['score'] < 20:
            summary_parts.append(f"待改善：{worst_metric[1]['description']}（{worst_metric[1]['score']:.1f}分）")
        
        return " | ".join(summary_parts)