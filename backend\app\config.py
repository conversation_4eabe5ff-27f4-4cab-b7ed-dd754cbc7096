"""
应用程序配置管理模块
集中管理所有环境变量和配置项
"""
import os
from datetime import timedelta
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_env(key: str, default: str = None) -> str:
    """安全获取环境变量"""
    value = os.getenv(key, default)
    if value is None:
        logger.warning(f"环境变量 {key} 未设置，使用默认值")
    return value

# 🎯 统一数据库配置（2024年重构）
# 所有数据库文件统一使用 .duckdb 扩展名
DATABASE_URL = get_env("DATABASE_URL", "duckdb:///./data/bankflow.duckdb")

# JWT配置
SECRET_KEY = get_env("SECRET_KEY", "your-secret-key-change-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = int(get_env("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

# 文件上传配置
UPLOAD_DIR = get_env("UPLOAD_DIR", "./uploads")
MAX_FILE_SIZE = int(get_env("MAX_FILE_SIZE", str(100 * 1024 * 1024)))  # 100MB

# CORS配置（统一标准端口）
CORS_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000"
]

# 开发模式配置
DEBUG = get_env("DEBUG", "False").lower() == "true"
RELOAD = DEBUG

# 服务器配置（标准端口，不得修改）
SERVER_HOST = get_env("SERVER_HOST", "127.0.0.1")
SERVER_PORT = int(get_env("SERVER_PORT", "8000"))

# 确保目录存在
os.makedirs(UPLOAD_DIR, exist_ok=True)
os.makedirs("./data", exist_ok=True)

# 配置摘要（用于调试）
def get_config_summary():
    """获取配置摘要"""
    return {
        "DATABASE_URL": DATABASE_URL,
        "UPLOAD_DIR": UPLOAD_DIR,
        "MAX_FILE_SIZE": MAX_FILE_SIZE,
        "DEBUG": DEBUG,
        "SERVER_HOST": SERVER_HOST,
        "SERVER_PORT": SERVER_PORT,
        "CORS_ORIGINS": CORS_ORIGINS
    }

# 启动时显示配置
if DEBUG:
    logger.info("🔧 当前配置摘要:")
    for key, value in get_config_summary().items():
        if "SECRET" not in key:
            logger.info(f"  {key}: {value}") 