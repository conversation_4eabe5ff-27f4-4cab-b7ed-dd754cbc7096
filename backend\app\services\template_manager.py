"""
模板管理服务
负责加载、存储和管理解析模板
"""
import os
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from sqlalchemy.orm import Session
from ..models.duckdb_models import ParsingTemplate, Bank

logger = logging.getLogger(__name__)

class TemplateManager:
    """
    解析模板管理器，负责加载和管理解析模板
    """
    
    def __init__(self, templates_dir: str = None):
        """
        初始化模板管理器
        
        Args:
            templates_dir: 模板文件所在的目录，默认为当前目录下的parsing_templates子目录
        """
        if templates_dir is None:
            # 默认模板目录在项目根目录或backend目录下的parsing_templates
            base_dir = os.path.abspath(os.path.dirname(__file__))
            possible_dirs = [
                os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(base_dir))), "parsing_templates"),
                os.path.join(os.path.dirname(os.path.dirname(base_dir)), "parsing_templates")
            ]
            
            for dir_path in possible_dirs:
                if os.path.exists(dir_path) and os.path.isdir(dir_path):
                    templates_dir = dir_path
                    break
        
        self.templates_dir = templates_dir
        self.templates_cache = {}  # 缓存加载的模板
    
    def load_template_from_file(self, file_path: str) -> Dict[str, Any]:
        """
        从文件加载模板配置
        
        Args:
            file_path: 模板文件路径
            
        Returns:
            Dict[str, Any]: 模板配置
            
        Raises:
            FileNotFoundError: 文件不存在时抛出
            json.JSONDecodeError: JSON解析错误时抛出
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"模板文件不存在: {file_path}")
            
        with open(file_path, 'r', encoding='utf-8') as f:
            template_config = json.load(f)
            
        return template_config
    
    def load_template_by_id(self, template_id: str) -> Optional[Dict[str, Any]]:
        """
        根据模板ID加载模板
        
        Args:
            template_id: 模板ID
            
        Returns:
            Optional[Dict[str, Any]]: 找到则返回模板配置，否则返回None
        """
        # 检查缓存
        if template_id in self.templates_cache:
            return self.templates_cache[template_id]
            
        # 在模板目录中查找
        if self.templates_dir and os.path.exists(self.templates_dir):
            for filename in os.listdir(self.templates_dir):
                if filename.endswith(('.json')):
                    try:
                        file_path = os.path.join(self.templates_dir, filename)
                        template = self.load_template_from_file(file_path)
                        
                        if template.get("templateId") == template_id:
                            # 缓存并返回
                            self.templates_cache[template_id] = template
                            return template
                    except Exception as e:
                        logger.error(f"加载模板文件 {filename} 时出错: {str(e)}")
        
        return None
    
    def load_templates_for_bank(self, bank_name: str) -> List[Dict[str, Any]]:
        """
        加载指定银行的所有模板
        
        Args:
            bank_name: 银行名称
            
        Returns:
            List[Dict[str, Any]]: 银行对应的模板列表
        """
        templates = []
        
        # 在模板目录中查找
        if self.templates_dir and os.path.exists(self.templates_dir):
            for filename in os.listdir(self.templates_dir):
                if filename.endswith(('.json')):
                    try:
                        file_path = os.path.join(self.templates_dir, filename)
                        template = self.load_template_from_file(file_path)
                        
                        if template.get("bankName") == bank_name:
                            templates.append(template)
                            # 同时缓存
                            self.templates_cache[template.get("templateId")] = template
                    except Exception as e:
                        logger.error(f"加载模板文件 {filename} 时出错: {str(e)}")
        
        return templates
    
    def save_template_to_db(self, db: Session, template_config: Dict[str, Any]) -> ParsingTemplate:
        """
        保存模板到数据库
        
        Args:
            db: 数据库会话
            template_config: 模板配置
            
        Returns:
            ParsingTemplate: 保存后的模板记录
            
        Raises:
            ValueError: 模板配置无效时抛出
        """
        # 验证必要字段
        required_fields = ["templateId", "bankName", "fileType"]
        for field in required_fields:
            if field not in template_config:
                raise ValueError(f"模板缺少必要字段: {field}")
        
        # 检查银行是否存在
        bank = db.query(Bank).filter(Bank.bank_name == template_config["bankName"]).first()
        if not bank:
            raise ValueError(f"银行 '{template_config['bankName']}' 不存在")
        
        # 检查是否已存在该模板
        existing_template = db.query(ParsingTemplate).filter(
            ParsingTemplate.template_id == template_config["templateId"]
        ).first()
        
        if existing_template:
            # 更新现有模板
            existing_template.account_type = template_config.get("accountType")
            existing_template.file_type = template_config["fileType"]
            existing_template.template_config = template_config
            existing_template.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(existing_template)
            return existing_template
        else:
            # 创建新模板
            new_template = ParsingTemplate(
                template_id=template_config["templateId"],
                bank_name=template_config["bankName"],
                account_type=template_config.get("accountType"),
                file_type=template_config["fileType"],
                template_config=template_config,
                is_default=template_config.get("isDefault", False)
            )
            db.add(new_template)
            db.commit()
            db.refresh(new_template)
            return new_template
    
    def import_templates_from_dir(self, db: Session) -> int:
        """
        从模板目录导入所有模板到数据库
        
        Args:
            db: 数据库会话
            
        Returns:
            int: 成功导入的模板数量
        """
        imported_count = 0
        
        if not self.templates_dir or not os.path.exists(self.templates_dir):
            logger.error(f"模板目录不存在: {self.templates_dir}")
            return imported_count
            
        for filename in os.listdir(self.templates_dir):
            if filename.endswith(('.json')):
                try:
                    file_path = os.path.join(self.templates_dir, filename)
                    template = self.load_template_from_file(file_path)
                    
                    self.save_template_to_db(db, template)
                    imported_count += 1
                    logger.info(f"成功导入模板: {template.get('templateId')} ({filename})")
                except Exception as e:
                    logger.error(f"导入模板 {filename} 时出错: {str(e)}")
        
        return imported_count 