"""
创建通用解析器模板Excel文件
"""
import pandas as pd
import os
from datetime import datetime

def create_universal_template():
    """创建通用解析器模板Excel文件"""
    
    # 定义模板数据结构（A-R列）
    headers = [
        '序号',      # A列 - 必填
        '持卡人',    # B列 - 必填  
        '银行名称',  # C列 - 必填
        '账号',      # D列 - 必填
        '卡号',      # E列 - 可选
        '交易日期',  # F列 - 必填
        '交易时间',  # G列 - 可选
        '交易方式',  # H列 - 可选
        '交易金额',  # I列 - 必填
        '账户余额',  # J列 - 必填
        '借贷标志',  # K列 - 可选
        '对方户名',  # L列 - 可选
        '对方账号',  # M列 - 可选
        '对方开户行', # N列 - 可选
        '备注1',     # O列 - 可选
        '备注2',     # P列 - 可选
        '备注3',     # Q列 - 可选
        '币种'       # R列 - 可选
    ]
    
    # 创建示例数据（3行示例）
    sample_data = [
        # 第一条示例记录
        [
            1,                    # 序号
            '张三',               # 持卡人
            '中国工商银行',       # 银行名称
            '6222021234567890',   # 账号
            '6222021234567890',   # 卡号
            '2024-01-15',         # 交易日期
            '14:30:25',           # 交易时间
            '网银转账',           # 交易方式
            -500.00,              # 交易金额（负数表示支出）
            9500.00,              # 账户余额
            '支',                 # 借贷标志
            '李四',               # 对方户名
            '6228481234567890',   # 对方账号
            '中国农业银行',       # 对方开户行
            '转账备注',           # 备注1
            '业务类型',           # 备注2
            '附加信息',           # 备注3
            'CNY'                 # 币种
        ],
        # 第二条示例记录
        [
            2,
            '张三',
            '中国工商银行', 
            '6222021234567890',
            '6222021234567890',
            '2024-01-16',
            '09:15:30',
            'ATM取款',
            -200.00,
            9300.00,
            '支',
            '',
            '',
            '',
            'ATM取款',
            '自助设备',
            '',
            'CNY'
        ],
        # 第三条示例记录
        [
            3,
            '张三',
            '中国工商银行',
            '6222021234567890', 
            '6222021234567890',
            '2024-01-17',
            '16:45:12',
            '工资入账',
            8000.00,              # 正数表示收入
            17300.00,
            '收',
            '某某公司',
            '1234567890123456',
            '中国建设银行',
            '2024年1月工资',
            '代发工资',
            '月度薪资',
            'CNY'
        ]
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(sample_data, columns=headers)
    
    # 确保目录存在
    template_dir = "f:/流水清洗/backend/data/templates"
    os.makedirs(template_dir, exist_ok=True)
    
    # 创建模板文件路径
    template_file = os.path.join(template_dir, "通用解析器模板.xlsx")
    
    # 创建Excel文件
    with pd.ExcelWriter(template_file, engine='openpyxl') as writer:
        # 写入数据
        df.to_excel(writer, sheet_name='通用格式模板', index=False)
        
        # 获取工作表对象进行格式化
        worksheet = writer.sheets['通用格式模板']
        
        # 设置列宽
        column_widths = {
            'A': 8,   # 序号
            'B': 12,  # 持卡人
            'C': 15,  # 银行名称
            'D': 20,  # 账号
            'E': 20,  # 卡号
            'F': 12,  # 交易日期
            'G': 12,  # 交易时间
            'H': 12,  # 交易方式
            'I': 12,  # 交易金额
            'J': 12,  # 账户余额
            'K': 8,   # 借贷标志
            'L': 15,  # 对方户名
            'M': 20,  # 对方账号
            'N': 15,  # 对方开户行
            'O': 15,  # 备注1
            'P': 15,  # 备注2
            'Q': 15,  # 备注3
            'R': 8    # 币种
        }
        
        for col, width in column_widths.items():
            worksheet.column_dimensions[col].width = width
        
        # 设置表头样式
        from openpyxl.styles import Font, Alignment, PatternFill
        
        # 表头字体和背景
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        # 应用表头样式
        for col in range(1, len(headers) + 1):
            cell = worksheet.cell(row=1, column=col)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
        
        # 数据行居中对齐
        data_alignment = Alignment(horizontal="center", vertical="center")
        for row in range(2, len(sample_data) + 2):
            for col in range(1, len(headers) + 1):
                cell = worksheet.cell(row=row, column=col)
                cell.alignment = data_alignment
    
    print(f"通用解析器模板已创建: {template_file}")
    return template_file

if __name__ == "__main__":
    create_universal_template()