"""
相关人员资料管理API
"""
import logging
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from fastapi import APIRouter, Depends, HTTPException, status, Query

from ..middleware.auth_middleware import get_current_user, get_user_db
from ..models.duckdb_models import DuckD<PERSON>elated<PERSON>erson as RelatedPerson
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter()

class RelatedPersonCreate(BaseModel):
    """创建相关人员的请求模式"""
    project_id: str
    name: str
    id_number: Optional[str] = None
    role: str  # 嫌疑人、证人、被害人、其他相关方
    gender: Optional[str] = None
    birthday: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    notes: Optional[str] = None

class RelatedPersonUpdate(BaseModel):
    """更新相关人员的请求模式"""
    name: Optional[str] = None
    id_number: Optional[str] = None
    role: Optional[str] = None
    gender: Optional[str] = None
    birthday: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    notes: Optional[str] = None

class RelatedPersonResponse(BaseModel):
    """相关人员响应模式"""
    person_id: str
    project_id: str
    name: str
    id_number: Optional[str] = None
    role: str
    gender: Optional[str] = None
    birthday: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

@router.get("/")
async def get_related_persons(
    project_id: str = Query(..., description="项目ID"),
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> List[Dict[str, Any]]:
    """获取项目的相关人员列表"""
    try:
        logger.info(f"用户 '{current_user}' 获取项目 {project_id} 的相关人员列表")
        
        persons = db.query(RelatedPerson).filter(
            RelatedPerson.project_id == project_id
        ).order_by(RelatedPerson.created_at.desc()).all()
        
        result = []
        for person in persons:
            result.append({
                "person_id": person.person_id,
                "project_id": person.project_id,
                "name": person.name,
                "id_number": person.id_number,
                "role": person.role,
                "gender": person.gender,
                "birthday": person.birthday,
                "phone": person.phone,
                "address": person.address,
                "notes": person.notes,
                "created_at": person.created_at.isoformat() if person.created_at else None,
                "updated_at": person.updated_at.isoformat() if person.updated_at else None
            })
        
        logger.info(f"用户 '{current_user}' 获取到 {len(result)} 条相关人员")
        return result
        
    except Exception as e:
        logger.error(f"用户 '{current_user}' 获取相关人员列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取相关人员列表失败: {str(e)}"
        )

@router.post("/")
async def create_related_person(
    person_data: Dict[str, Any],
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> Dict[str, Any]:
    """创建相关人员"""
    try:
        logger.info(f"用户 '{current_user}' 创建相关人员: {person_data.get('name', '未知')}")
        
        new_person = RelatedPerson(
            project_id=person_data["project_id"],
            name=person_data.get("name", ""),
            id_number=person_data.get("id_number", ""),
            role=person_data.get("role", ""),
            gender=person_data.get("gender", ""),
            birthday=person_data.get("birthday", ""),
            phone=person_data.get("phone", ""),
            address=person_data.get("address", ""),
            notes=person_data.get("notes", ""),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        db.add(new_person)
        db.commit()
        db.refresh(new_person)
        
        logger.info(f"用户 '{current_user}' 成功创建相关人员: {new_person.person_id}")
        
        return {
            "person_id": new_person.person_id,
            "project_id": new_person.project_id,
            "name": new_person.name,
            "id_number": new_person.id_number,
            "role": new_person.role,
            "gender": new_person.gender,
            "birthday": new_person.birthday,
            "phone": new_person.phone,
            "address": new_person.address,
            "notes": new_person.notes,
            "created_at": new_person.created_at.isoformat() if new_person.created_at else None,
            "updated_at": new_person.updated_at.isoformat() if new_person.updated_at else None
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"用户 '{current_user}' 创建相关人员失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建相关人员失败: {str(e)}"
        )

@router.put("/{person_id}")
async def update_related_person(
    person_id: str,
    person_data: Dict[str, Any],
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> Dict[str, Any]:
    """更新相关人员信息"""
    try:
        logger.info(f"用户 '{current_user}' 更新相关人员，ID: {person_id}")
        
        person = db.query(RelatedPerson).filter(RelatedPerson.person_id == person_id).first()
        if not person:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"相关人员 {person_id} 不存在"
            )
        
        # 更新字段
        for field, value in person_data.items():
            if field != "person_id" and hasattr(person, field):
                setattr(person, field, value)
        
        person.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(person)
        
        logger.info(f"用户 '{current_user}' 成功更新相关人员: {person.person_id}")
        
        return {
            "person_id": person.person_id,
            "project_id": person.project_id,
            "name": person.name,
            "id_number": person.id_number,
            "role": person.role,
            "gender": person.gender,
            "birthday": person.birthday,
            "phone": person.phone,
            "address": person.address,
            "notes": person.notes,
            "created_at": person.created_at.isoformat() if person.created_at else None,
            "updated_at": person.updated_at.isoformat() if person.updated_at else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"用户 '{current_user}' 更新相关人员失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新相关人员失败: {str(e)}"
        )

@router.delete("/{person_id}")
async def delete_related_person(
    person_id: str,
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> Dict[str, str]:
    """删除相关人员"""
    try:
        logger.info(f"用户 '{current_user}' 删除相关人员，ID: {person_id}")
        
        person = db.query(RelatedPerson).filter(RelatedPerson.person_id == person_id).first()
        if not person:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"相关人员 {person_id} 不存在"
            )
        
        db.delete(person)
        db.commit()
        
        logger.info(f"用户 '{current_user}' 成功删除相关人员: {person.person_id}")
        
        return {"message": "相关人员删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"用户 '{current_user}' 删除相关人员失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除相关人员失败: {str(e)}"
        ) 