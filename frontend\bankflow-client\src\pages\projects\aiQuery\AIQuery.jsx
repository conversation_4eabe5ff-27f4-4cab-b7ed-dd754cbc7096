import React, { useState } from 'react';
import { Card, Input, Button, Typography, Space, List, Tag, Divider, Spin, Empty, Tabs } from 'antd';
import { SearchOutlined, BulbOutlined, FileTextOutlined, BankOutlined, UserOutlined, TeamOutlined } from '@ant-design/icons';

const { Search } = Input;
const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

const AIQuery = () => {
  const [query, setQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [searchHistory, setSearchHistory] = useState([]);

  // 处理搜索
  const handleSearch = (value) => {
    if (!value.trim()) return;
    
    setQuery(value);
    setLoading(true);
    
    // 保存搜索历史
    if (!searchHistory.includes(value)) {
      setSearchHistory([value, ...searchHistory].slice(0, 10));
    }
    
    // 模拟搜索结果
    setTimeout(() => {
      const mockResults = generateMockResults(value);
      setSearchResults(mockResults);
      setLoading(false);
    }, 1000);
  };

  // 生成模拟搜索结果
  const generateMockResults = (searchQuery) => {
    const baseResults = [
      {
        id: '1',
        title: `与"${searchQuery}"相关的银行流水记录`,
        content: `发现多条与"${searchQuery}"相关的银行流水记录，总计15笔交易，金额合计¥245,600。`,
        type: 'bankflow',
        date: '2023-01-15',
        relevance: 0.95,
        tags: ['银行流水', '高关联度'],
      },
      {
        id: '2',
        title: `关于"${searchQuery}"的谈话笔录`,
        content: `在谈话笔录中找到与"${searchQuery}"相关的内容，主要在李某的谈话中提及。`,
        type: 'transcript',
        date: '2023-01-18',
        relevance: 0.87,
        tags: ['谈话笔录', '中等关联度'],
      },
      {
        id: '3',
        title: `案件调查中的"${searchQuery}"线索`,
        content: `案件调查过程中发现与"${searchQuery}"相关的线索3条，可能与涉案资金流向有关。`,
        type: 'clue',
        date: '2023-01-20',
        relevance: 0.82,
        tags: ['案件线索', '中等关联度'],
      },
      {
        id: '4',
        title: `与"${searchQuery}"相关的人员信息`,
        content: `系统中有2名人员与"${searchQuery}"相关，包括张某和李某。`,
        type: 'person',
        date: '2023-01-25',
        relevance: 0.75,
        tags: ['人员信息', '中等关联度'],
      },
      {
        id: '5',
        title: `关于"${searchQuery}"的分析报告`,
        content: `系统生成了与"${searchQuery}"相关的分析报告1份，包含资金流向和交易模式分析。`,
        type: 'report',
        date: '2023-02-01',
        relevance: 0.93,
        tags: ['分析报告', '高关联度'],
      },
      {
        id: '6',
        title: `${searchQuery}的账户异常记录`,
        content: `发现与"${searchQuery}"相关的账户存在3次异常交易记录，金额共计¥156,800。`,
        type: 'bankflow',
        date: '2023-02-05',
        relevance: 0.88,
        tags: ['异常交易', '高关联度'],
      },
    ];
    
    // 根据搜索关键词进行简单的排序和过滤
    return baseResults.sort((a, b) => b.relevance - a.relevance);
  };

  // 渲染搜索结果项
  const renderResultItem = (item) => {
    let icon;
    let color;
    
    switch (item.type) {
      case 'bankflow':
        icon = <BankOutlined />;
        color = 'blue';
        break;
      case 'transcript':
        icon = <FileTextOutlined />;
        color = 'green';
        break;
      case 'clue':
        icon = <BulbOutlined />;
        color = 'orange';
        break;
      case 'person':
        icon = <UserOutlined />;
        color = 'purple';
        break;
      case 'report':
        icon = <FileTextOutlined />;
        color = 'red';
        break;
      default:
        icon = <FileTextOutlined />;
        color = 'default';
    }
    
    return (
      <List.Item
        actions={[
          <a key="view" onClick={() => console.log('查看详情', item.id)}>查看详情</a>,
        ]}
      >
        <List.Item.Meta
          avatar={<div style={{ fontSize: 24, color: `var(--ant-color-${color})` }}>{icon}</div>}
          title={
            <Space>
              <Text strong>{item.title}</Text>
              <Tag color={color}>
                {item.type === 'bankflow' ? '银行流水' :
                 item.type === 'transcript' ? '谈话笔录' :
                 item.type === 'clue' ? '案件线索' :
                 item.type === 'person' ? '人员信息' :
                 item.type === 'report' ? '分析报告' : item.type}
              </Tag>
            </Space>
          }
          description={
            <>
              <div>{item.content}</div>
              <div style={{ marginTop: 8 }}>
                <Text type="secondary">{item.date}</Text>
                <Divider type="vertical" />
                <Text type="secondary">关联度: {(item.relevance * 100).toFixed(0)}%</Text>
                <Divider type="vertical" />
                {item.tags.map(tag => (
                  <Tag key={tag} color="default">{tag}</Tag>
                ))}
              </div>
            </>
          }
        />
      </List.Item>
    );
  };

  // 过滤结果
  const filteredResults = activeTab === 'all' 
    ? searchResults 
    : searchResults.filter(item => item.type === activeTab);

  return (
    <div>
      <Card title="全局智能查询">
        <div style={{ marginBottom: 24 }}>
          <Title level={4} style={{ marginBottom: 16 }}>AI辅助全局搜索</Title>
          <Paragraph>
            输入关键词，AI将帮助您在银行流水、谈话笔录、案件线索等所有资料中查找相关信息。
          </Paragraph>
          
          <Space direction="vertical" style={{ width: '100%' }}>
            <Search
              placeholder="搜索银行、人名、公司、交易信息等关键词"
              enterButton={<Button type="primary" icon={<SearchOutlined />}>搜索</Button>}
              size="large"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onSearch={handleSearch}
              loading={loading}
            />
            
            {searchHistory.length > 0 && (
              <div style={{ marginTop: 8 }}>
                <Text type="secondary">搜索历史: </Text>
                {searchHistory.map((item, index) => (
                  <Tag 
                    key={index} 
                    style={{ cursor: 'pointer' }} 
                    onClick={() => handleSearch(item)}
                  >
                    {item}
                  </Tag>
                ))}
              </div>
            )}
          </Space>
        </div>
        
        {loading ? (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Spin tip="正在搜索中..." />
          </div>
        ) : searchResults.length > 0 ? (
          <div>
            <Tabs activeKey={activeTab} onChange={setActiveTab}>
              <TabPane tab="全部结果" key="all" />
              <TabPane tab={<span><BankOutlined /> 银行流水</span>} key="bankflow" />
              <TabPane tab={<span><FileTextOutlined /> 谈话笔录</span>} key="transcript" />
              <TabPane tab={<span><BulbOutlined /> 案件线索</span>} key="clue" />
              <TabPane tab={<span><UserOutlined /> 人员信息</span>} key="person" />
              <TabPane tab={<span><FileTextOutlined /> 分析报告</span>} key="report" />
            </Tabs>
            
            <List
              itemLayout="vertical"
              size="large"
              pagination={{ pageSize: 5 }}
              dataSource={filteredResults}
              renderItem={renderResultItem}
            />
          </div>
        ) : query ? (
          <Empty description="未找到匹配的结果，请尝试其他关键词" />
        ) : (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <TeamOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
            <p style={{ marginTop: 16 }}>输入关键词开始搜索</p>
          </div>
        )}
      </Card>
    </div>
  );
};

export default AIQuery; 