"""
API模型定义
"""
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
from datetime import datetime

# 交易模型
class Transaction(BaseModel):
    transaction_datetime: str
    transaction_amount: float
    balance: Optional[float] = None
    remarks: Optional[str] = None
    counterparty_account_name: Optional[str] = None
    counterparty_account_number: Optional[str] = None
    dr_cr_flag: Optional[str] = None
    
    class Config:
        orm_mode = True

# 账户模型
class Account(BaseModel):
    account_number: str
    account_name: Optional[str] = None
    account_type: Optional[str] = None
    bank_name: Optional[str] = None
    
    class Config:
        orm_mode = True

# 解析响应
class ParsingResponse(BaseModel):
    success: bool
    bank_name: str
    file_name: str
    accounts: List[Dict[str, Any]]
    transactions_by_account: Dict[str, List[Dict[str, Any]]]
    metadata: Dict[str, Any]
    
    class Config:
        orm_mode = True

# 账户统计信息
class AccountStats(BaseModel):
    total_accounts: int
    valid_accounts: int
    invalid_accounts: int
    
    class Config:
        orm_mode = True

# 交易统计信息
class TransactionStats(BaseModel):
    total_accounts: int
    total_transactions: int
    accounts_with_too_few_transactions: Optional[int] = 0
    missing_required_fields: Optional[int] = 0
    date_format_issues: Optional[int] = 0
    amount_format_issues: Optional[int] = 0
    
    class Config:
        orm_mode = True

# 验证摘要
class ValidationSummary(BaseModel):
    is_valid: bool
    account_stats: AccountStats
    transaction_stats: TransactionStats
    
    class Config:
        orm_mode = True

# 一致性摘要
class ConsistencySummary(BaseModel):
    is_consistent: bool
    issues_count: int
    
    class Config:
        orm_mode = True

# 自动解析响应
class AutoParsingResponse(BaseModel):
    success: bool
    file_name: str
    bank_name: Optional[str] = None
    auto_detected_bank: Optional[str] = None
    selected_parser: Optional[str] = None
    confidence_score: Optional[float] = 0
    accounts: Optional[List[Dict[str, Any]]] = None
    transactions_by_account: Optional[Dict[str, List[Dict[str, Any]]]] = None
    metadata: Optional[Dict[str, Any]] = None
    validation_summary: Optional[ValidationSummary] = None
    consistency_summary: Optional[ConsistencySummary] = None
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    
    class Config:
        orm_mode = True 