# 银行解析器验证清单

## 🎯 验证目标
确保银行解析器完全按照用户要求工作，所有功能正常，数据准确无误。

## 📋 强制验证流程

### 阶段1：服务启动验证
- [ ] **后端服务启动**：确认8000端口正常运行
- [ ] **前端服务启动**：确认3000端口正常访问
- [ ] **服务连接正常**：前后端通信无异常
- [ ] **解析器加载**：目标银行解析器正确加载

### 阶段2：文件上传验证
- [ ] **文件上传成功**：测试文件正确上传
- [ ] **银行选择正确**：选择对应的银行
- [ ] **模板匹配正确**：自动选择正确的解析模板
- [ ] **解析启动成功**：解析过程正常启动

### 阶段3：解析结果验证
- [ ] **解析状态成功**：显示"解析完成"
- [ ] **账户数量正确**：与预期的账户数量一致
- [ ] **交易数量正确**：与预期的交易数量一致
- [ ] **无错误信息**：没有解析错误或警告

## 🔍 详细验证项目

### 1. 账户汇总表验证

#### 1.1 字段映射验证
- [ ] **卡号字段**：显示原表的"客户账号"
- [ ] **账号字段**：显示原表的"账户账号"
- [ ] **持卡人字段**：正确显示持卡人姓名
- [ ] **银行名称**：正确显示银行名称

#### 1.2 统计数据验证
- [ ] **收入总额**：不为¥0.00，显示实际收入金额
- [ ] **支出总额**：不为¥0.00，显示实际支出金额
- [ ] **时间范围**：不为"未知"，显示实际时间范围
- [ ] **交易笔数**：与实际交易数量一致

#### 1.3 账户独立性验证
- [ ] **工作表独立**：每个工作表对应一个独立账户
- [ ] **相同卡号独立**：相同卡号的不同工作表不合并
- [ ] **账户ID唯一**：每个账户都有唯一标识
- [ ] **数据不重复**：没有重复的账户记录

### 2. 交易明细验证

#### 2.1 明细访问验证
- [ ] **明细弹窗打开**：点击"查看交易"正常打开
- [ ] **账户信息正确**：弹窗中的账户信息与汇总一致
- [ ] **交易数量正确**：显示的交易数量与汇总一致
- [ ] **分页功能正常**：分页控件正常工作

#### 2.2 字段完整性验证
- [ ] **序号**：正确的序号编排（1, 2, 3...）
- [ ] **持卡人姓名**：正确显示持卡人姓名
- [ ] **银行名称**：正确显示银行名称
- [ ] **账号**：显示账户账号（不是客户账号）
- [ ] **卡号**：显示客户账号（不是账户账号）
- [ ] **交易日期**：正确的日期格式
- [ ] **交易时间**：正确的时间格式
- [ ] **交易方式**：正确的交易类型
- [ ] **交易金额**：正确的金额显示
- [ ] **交易余额**：正确的余额计算
- [ ] **收支符号**：显示"收入"或"支出"，不是"未知"
- [ ] **对方户名**：正确显示对方户名
- [ ] **对方账号**：正确显示对方账号
- [ ] **对方银行**：正确显示对方银行
- [ ] **备注信息**：正确显示备注内容

#### 2.3 数据准确性验证
- [ ] **金额计算正确**：交易金额与原表一致
- [ ] **余额计算正确**：账户余额计算准确
- [ ] **收支判断正确**：收入支出判断准确
- [ ] **日期时间正确**：日期时间解析准确

### 3. 数据一致性验证

#### 3.1 汇总与明细一致性
- [ ] **交易笔数一致**：汇总表的笔数与明细实际笔数一致
- [ ] **收入金额一致**：汇总的收入总额与明细计算结果一致
- [ ] **支出金额一致**：汇总的支出总额与明细计算结果一致
- [ ] **时间范围一致**：汇总的时间范围与明细的实际范围一致

#### 3.2 字段映射一致性
- [ ] **卡号映射一致**：汇总表和明细表的卡号字段都显示客户账号
- [ ] **账号映射一致**：汇总表和明细表的账号字段都显示账户账号
- [ ] **其他字段一致**：所有字段在汇总和明细中保持一致

## ⚠️ 常见问题检查

### 问题1：字段映射错误
**症状**：卡号显示账户账号，账号显示客户账号
**检查**：
- [ ] 查看汇总表的卡号和账号字段
- [ ] 查看明细表的卡号和账号字段
- [ ] 确认是否颠倒了字段映射

### 问题2：统计数据异常
**症状**：收入、支出显示¥0.00，时间范围显示"未知"
**检查**：
- [ ] 查看汇总表的统计数据
- [ ] 检查是否正确计算了收支金额
- [ ] 确认时间范围是否正确提取

### 问题3：工作表合并错误
**症状**：相同卡号的不同工作表被合并显示
**检查**：
- [ ] 查看账户数量是否与工作表数量一致
- [ ] 检查相同卡号是否有多个账户记录
- [ ] 确认每个工作表是否独立显示

### 问题4：交易明细缺失
**症状**：明细中某些字段显示为空或"-"
**检查**：
- [ ] 查看明细表的所有字段
- [ ] 检查收支符号是否显示"未知"
- [ ] 确认对方信息是否正确显示

## 🚨 验证失败处理

### 当发现验证失败时
1. **立即停止测试**
2. **记录具体问题**
3. **分析问题原因**
4. **修复代码问题**
5. **重启服务**
6. **重新完整验证**

### 验证失败的常见原因
- 字段映射逻辑错误
- 统计计算逻辑错误
- 工作表处理逻辑错误
- 数据解析逻辑错误

## ✅ 验证通过标准

### 必须100%通过的项目
1. **字段映射100%正确**
2. **统计数据100%准确**
3. **账户独立性100%保持**
4. **交易明细100%完整**
5. **数据一致性100%保证**

### 验证通过的标志
- [ ] 所有验证项目都打勾通过
- [ ] 没有任何错误或异常
- [ ] 用户确认功能符合要求
- [ ] 数据显示完全正确

## 📝 验证记录模板

```
验证日期：[日期]
验证人员：[姓名]
银行名称：[银行]
测试文件：[文件名]

验证结果：
□ 通过 □ 失败

问题记录：
1. [问题描述]
2. [问题描述]

修复措施：
1. [修复方案]
2. [修复方案]

最终状态：
□ 验证通过，可以使用
□ 需要继续修复
```

## 🎯 验证效率提升

### 快速验证技巧
1. **准备标准测试文件**：每个银行准备标准测试用例
2. **使用验证脚本**：自动化部分验证过程
3. **建立验证模板**：标准化验证流程
4. **记录常见问题**：快速定位和解决问题

### 验证工具
- 浏览器开发者工具：检查网络请求和响应
- 后端日志：查看解析过程和错误信息
- 数据库工具：直接查看存储的数据
- Excel工具：对比原始数据和解析结果

---

**重要提醒：这个验证清单是强制性的，每次开发或修改银行解析器后都必须完整执行。只有通过所有验证项目，才能认为解析器开发完成。**
