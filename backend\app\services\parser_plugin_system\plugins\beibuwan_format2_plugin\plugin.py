"""
北部湾银行Format2解析器插件
支持黄宪文2.xlsx格式的银行流水解析
"""

import pandas as pd
import time
import os
import traceback
from datetime import datetime
from typing import Dict, Any, List, Optional
import logging
import json
from pathlib import Path
import re

# 导入基础插件接口
try:
    from ...core.plugin_interface import BasePlugin
except ImportError:
    # 如果在测试环境中，创建一个简单的基础类
    class BasePlugin:
        def __init__(self):
            self.name = "default_plugin"
            self.version = "1.0.0"
            self.description = "默认解析器插件"
            self.bank_name = "通用银行"
            self.format_type = "standard"

logger = logging.getLogger(__name__)

class Plugin(BasePlugin):
    """北部湾银行Format2解析器插件"""
    
    def __init__(self, file_path: str = None):
        super().__init__()
        self.name = "beibuwan_format2_plugin"
        self.version = "1.0.0"
        self.description = "北部湾银行Format2解析器插件"
        self.bank_name = "北部湾银行"
        self.format_type = "format2"
        self.start_time = time.time()
        self.error_count = 0
        self.file_path = file_path
        
        # 加载配置
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载插件配置"""
        try:
            config_path = Path(__file__).parent / "config.json"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"无法加载配置文件: {e}")
        
        # 默认配置
        return {
            "parsing_config": {
                "target_sheet": "Sheet1",
                "header_row": 4,
                "debit_credit_format": True,
                "time_field_empty": True,
                "extract_header_info": True,
                "header_info_rows": [0, 1, 2, 3]
            },
            "field_mapping": {
                "date_field": "日期",
                "debit_field": "借方发生额",
                "credit_field": "贷方发生额",
                "balance_field": "余额",
                "account_name_field": "账户名称",
                "counterparty_account_field": "交易对手账号",
                "counterparty_name_field": "交易对手名称",
                "reference_field": "凭证号",
                "summary_field": "摘要",
                "channel_field": "代理行"
            }
        }
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取插件元信息"""
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "bank_name": self.bank_name,
            "format_type": self.format_type,
            "supported_formats": ["Excel (.xlsx)"],
            "confidence_threshold": 0.8,
            "author": "银行流水系统团队",
            "license": "MIT",
            "format_features": {
                "multi_sheet": False,
                "time_format": "YYYY-MM-DD",
                "amount_format": "debit_credit_columns",
                "header_info": True,
                "transaction_sheet": "Sheet1"
            }
        }
    
    def validate_file(self, file_path: str) -> bool:
        """验证文件是否适用于此解析器"""
        try:
            # 检查文件扩展名
            if not file_path.lower().endswith('.xlsx'):
                return False
            
            # 检查文件是否可读
            if not os.path.exists(file_path):
                return False
            
            # 检查Excel文件结构
            excel_file = pd.ExcelFile(file_path)
            
            # 检查是否有Sheet1
            if "Sheet1" not in excel_file.sheet_names:
                return False
            
            # 检查Sheet1的基本结构
            df = pd.read_excel(file_path, sheet_name="Sheet1", header=4)
            
            # 检查必需列
            required_columns = ["日期", "借方发生额", "贷方发生额", "余额"]
            if not all(col in df.columns for col in required_columns):
                return False
            
            # 检查数据行数
            if len(df) == 0:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"文件验证失败: {e}")
            return False
    
    def calculate_confidence(self, file_path: str) -> float:
        """计算解析置信度"""
        try:
            if not self.validate_file(file_path):
                return 0.0
            
            confidence = 0.0
            
            # 1. 文件扩展名检查 (10%)
            if file_path.lower().endswith('.xlsx'):
                confidence += 0.1
            
            # 2. 工作表结构检查 (20%)
            excel_file = pd.ExcelFile(file_path)
            if "Sheet1" in excel_file.sheet_names:
                confidence += 0.2
            
            # 3. 表头信息检查 (30%)
            df_header = pd.read_excel(file_path, sheet_name="Sheet1", header=None)
            
            # 检查表头关键信息
            header_keywords = ["广西北部湾银行", "对账明细", "账号", "起止日期"]
            header_content = str(df_header.head(5).values).lower()
            keyword_matches = sum(1 for keyword in header_keywords if keyword in header_content)
            confidence += (keyword_matches / len(header_keywords)) * 0.3
            
            # 4. 列名匹配度检查 (20%)
            df = pd.read_excel(file_path, sheet_name="Sheet1", header=4)
            expected_columns = ["日期", "账户名称", "借方发生额", "贷方发生额", "余额", "摘要"]
            matching_columns = sum(1 for col in expected_columns if col in df.columns)
            confidence += (matching_columns / len(expected_columns)) * 0.2
            
            # 5. 借方贷方格式验证 (20%)
            if "借方发生额" in df.columns and "贷方发生额" in df.columns:
                # 检查是否有借方贷方数据
                debit_count = df["借方发生额"].notna().sum()
                credit_count = df["贷方发生额"].notna().sum()
                
                if debit_count > 0 and credit_count > 0:
                    confidence += 0.2
                elif debit_count > 0 or credit_count > 0:
                    confidence += 0.1
            
            return min(confidence, 1.0)
            
        except Exception as e:
            logger.error(f"置信度计算失败: {e}")
            self.error_count += 1
            return 0.0
    
    def extract_sample(self, limit: int = 10) -> Dict[str, Any]:
        """提取样本数据进行快速评估"""
        try:
            logger.info(f"开始提取北部湾银行Format2文件样本数据: {limit}条")
            
            # 1. 读取Excel文件
            file_path = self.file_path
            if not file_path:
                raise ValueError("未设置文件路径")
            excel_file = pd.ExcelFile(file_path)
            
            # 2. 提取表头信息
            header_info = self._extract_header_info(file_path)
            
            # 3. 读取交易明细（从第5行开始，只读取前N条）
            df_transactions = pd.read_excel(file_path, sheet_name="Sheet1", header=4, nrows=limit)
            
            # 4. 提取账户信息
            accounts = self._extract_accounts(header_info, df_transactions)
            
            # 5. 提取交易记录（样本）
            transactions = self._extract_transactions(df_transactions, accounts)
            
            # 6. 构建样本结果
            sample_result = {
                'accounts': accounts,
                'transactions': transactions,
                'metadata': {
                    'total_accounts_found': len(accounts),
                    'total_transactions_found': len(transactions),
                    'sample_size': limit,
                    'parsing_successful': True,
                    'evaluation_mode': 'extract_sample'
                }
            }
            
            logger.info(f"样本数据提取完成: {len(accounts)}个账户, {len(transactions)}条交易")
            return sample_result
            
        except Exception as e:
            logger.error(f"样本数据提取失败: {str(e)}")
            return {
                'accounts': [],
                'transactions': [],
                'metadata': {
                    'total_accounts_found': 0,
                    'total_transactions_found': 0,
                    'sample_size': 0,
                    'parsing_successful': False,
                    'error': str(e)
                }
            }
    
    def parse(self, file_path: str = None) -> Dict[str, Any]:
        """执行解析"""
        try:
            # 使用传入的文件路径或实例的文件路径
            if file_path:
                self.file_path = file_path
            elif not self.file_path:
                raise ValueError("未设置文件路径")
            
            logger.info(f"开始解析北部湾银行Format2文件: {self.file_path}")
            
            # 1. 读取Excel文件
            excel_file = pd.ExcelFile(self.file_path)
            
            # 2. 提取表头信息
            header_info = self._extract_header_info(self.file_path)
            
            # 3. 读取交易明细
            df_transactions = pd.read_excel(self.file_path, sheet_name="Sheet1", header=4)
            
            # 4. 提取账户信息
            accounts = self._extract_accounts(header_info, df_transactions)
            
            # 5. 提取交易记录
            transactions = self._extract_transactions(df_transactions, accounts)
            
            # 6. 数据验证
            self._validate_data_consistency(accounts, transactions)
            
            # 7. 计算置信度
            confidence = self.calculate_confidence(self.file_path)
            
            result = {
                "success": True,
                "message": "解析成功",
                "accounts": accounts,
                "transactions": transactions,
                "parser_type": self.name,
                "confidence_score": confidence,
                "total_accounts": len(accounts),
                "total_transactions": len(transactions),
                "parsing_details": {
                    "source_sheet": "Sheet1",
                    "header_row": 4,
                    "debit_credit_format": True,
                    "time_field_empty": True,
                    "header_info": header_info
                }
            }
            
            logger.info(f"解析完成: {len(accounts)}个账户, {len(transactions)}条交易")
            return result
            
        except Exception as e:
            logger.error(f"解析失败: {e}")
            self.error_count += 1
            return {
                "success": False,
                "message": f"解析失败: {str(e)}",
                "accounts": [],
                "transactions": [],
                "parser_type": self.name,
                "confidence_score": 0.0,
                "error_details": str(e)
            }
    
    def _extract_header_info(self, file_path: str) -> Dict[str, Any]:
        """提取表头信息"""
        header_info = {
            "bank_name": "北部湾银行",
            "account_number": "Unknown",
            "cardholder_name": "Unknown",
            "start_date": "未知",
            "end_date": "未知"
        }
        
        try:
            # 读取前5行表头信息
            df_header = pd.read_excel(file_path, sheet_name="Sheet1", header=None, nrows=5)
            
            # 第1行：广西北部湾银行对账明细
            if len(df_header) > 0:
                first_row = str(df_header.iloc[0, 0])
                if "广西北部湾银行" in first_row:
                    header_info["bank_name"] = "广西北部湾银行"
                elif "北部湾银行" in first_row:
                    header_info["bank_name"] = "北部湾银行"
            
            # 第3行：网点号、起止日期（第11列）
            if len(df_header) > 2:
                third_row = df_header.iloc[2]
                # 检查第11列（索引10）
                if len(third_row) > 10 and pd.notna(third_row.iloc[10]):
                    date_str = str(third_row.iloc[10])
                    # 解析日期范围：2020-07-01至2022-05-16
                    date_match = re.search(r'(\d{4}-\d{2}-\d{2})至(\d{4}-\d{2}-\d{2})', date_str)
                    if date_match:
                        header_info["start_date"] = date_match.group(1)
                        header_info["end_date"] = date_match.group(2)
                        logger.info(f"提取到时间范围: {header_info['start_date']} 至 {header_info['end_date']}")
            
            # 第4行：账号信息（第2列）
            if len(df_header) > 3:
                fourth_row = df_header.iloc[3]
                # 检查第2列（索引1）
                if len(fourth_row) > 1 and pd.notna(fourth_row.iloc[1]):
                    account_str = str(fourth_row.iloc[1]).strip()
                    # 验证是否是账号（数字且长度合适）
                    if account_str.isdigit() and len(account_str) >= 16:
                        header_info["account_number"] = account_str
                        logger.info(f"提取到账号: {header_info['account_number']}")
            
            # 从数据行中提取持卡人姓名（第6行开始的"账户名称"列）
            try:
                # 读取数据部分，从第5行开始（header=4表示第5行是表头）
                df_data = pd.read_excel(file_path, sheet_name="Sheet1", header=4, nrows=5)
                if "账户名称" in df_data.columns:
                    account_names = df_data["账户名称"].dropna().unique()
                    if len(account_names) > 0:
                        header_info["cardholder_name"] = str(account_names[0])
                        logger.info(f"提取到持卡人姓名: {header_info['cardholder_name']}")
            except Exception as e:
                logger.warning(f"从数据行提取持卡人姓名失败: {e}")
            
            logger.info(f"最终提取的表头信息: {header_info}")
            
        except Exception as e:
            logger.error(f"提取表头信息失败: {e}")
        
        return header_info
    
    def _extract_accounts(self, header_info: Dict[str, Any], df_transactions: pd.DataFrame) -> List[Dict[str, Any]]:
        """提取账户信息"""
        accounts = []
        
        try:
            # 从表头信息获取基本信息
            account_number = header_info.get("account_number", "Unknown")
            bank_name = header_info.get("bank_name", self.bank_name)
            start_date = header_info.get("start_date", "未知")
            end_date = header_info.get("end_date", "未知")
            
            # 从表头信息获取持卡人姓名，如果没有则从交易明细获取
            cardholder_name = header_info.get("cardholder_name", "Unknown")
            if cardholder_name == "Unknown" and "账户名称" in df_transactions.columns:
                account_names = df_transactions["账户名称"].dropna().unique()
                if len(account_names) > 0:
                    cardholder_name = str(account_names[0])
            
            # 计算账户统计信息
            total_inflow = 0.0
            total_outflow = 0.0
            transaction_count = 0
            
            field_mapping = self.config["field_mapping"]
            
            # 计算借方发生额（支出）
            if field_mapping["debit_field"] in df_transactions.columns:
                debit_amounts = pd.to_numeric(df_transactions[field_mapping["debit_field"]], errors='coerce').fillna(0)
                total_outflow = float(debit_amounts.sum())
                
            # 计算贷方发生额（收入）
            if field_mapping["credit_field"] in df_transactions.columns:
                credit_amounts = pd.to_numeric(df_transactions[field_mapping["credit_field"]], errors='coerce').fillna(0)
                total_inflow = float(credit_amounts.sum())
            
            # 计算有效交易数
            valid_transactions = df_transactions[
                (pd.to_numeric(df_transactions[field_mapping["debit_field"]], errors='coerce') > 0) |
                (pd.to_numeric(df_transactions[field_mapping["credit_field"]], errors='coerce') > 0)
            ]
            transaction_count = len(valid_transactions)
            
            # 构建时间范围字符串
            if start_date != "未知" and end_date != "未知":
                date_range = f"{start_date} 至 {end_date}"
            else:
                date_range = "未知"
            
            account = {
                "person_name": cardholder_name,
                "bank_name": bank_name,
                "account_name": f"{cardholder_name}的账户",
                "account_number": account_number,
                "card_number": account_number,  # 黄宪文2.xlsx没有单独的卡号，使用账号作为卡号
                "total_inflow": total_inflow,
                "total_outflow": total_outflow,
                "net_flow": total_inflow - total_outflow,
                "transactions_count": transaction_count,
                "currency": "CNY",
                "date_range": date_range,  # 添加时间范围
                "start_date": start_date,  # 添加开始日期
                "end_date": end_date       # 添加结束日期
            }
            
            accounts.append(account)
            logger.info(f"提取到账户信息: 持卡人={cardholder_name}, 账号={account_number}, 时间范围={date_range}, 交易数={transaction_count}")
            
        except Exception as e:
            logger.error(f"提取账户信息失败: {e}")
        
        return accounts
    
    def _extract_transactions(self, df: pd.DataFrame, accounts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """提取交易记录"""
        transactions = []
        
        try:
            # 获取账户信息
            account_info = accounts[0] if accounts else {}
            cardholder_name = account_info.get("person_name", "Unknown")
            account_number = account_info.get("account_number", "Unknown")
            
            field_mapping = self.config["field_mapping"]
            
            for index, row in df.iterrows():
                # 跳过无效行
                if pd.isna(row.get(field_mapping["date_field"])):
                    continue
                
                # 处理交易日期
                transaction_date = self._format_date(row[field_mapping["date_field"]])
                
                # 处理借方贷方发生额
                debit_amount = pd.to_numeric(row.get(field_mapping["debit_field"], 0), errors='coerce')
                credit_amount = pd.to_numeric(row.get(field_mapping["credit_field"], 0), errors='coerce')
                
                if pd.isna(debit_amount):
                    debit_amount = 0.0
                if pd.isna(credit_amount):
                    credit_amount = 0.0
                
                # 确定交易类型和金额
                if debit_amount > 0:
                    # 借方发生额 = 支出
                    transaction_amount = float(debit_amount)
                    dr_cr_flag = "支"
                    transaction_type = "支出"
                elif credit_amount > 0:
                    # 贷方发生额 = 收入
                    transaction_amount = float(credit_amount)
                    dr_cr_flag = "收"
                    transaction_type = "收入"
                else:
                    # 无效交易
                    continue
                
                # 处理余额
                balance = pd.to_numeric(row.get(field_mapping["balance_field"], 0), errors='coerce')
                if pd.isna(balance):
                    balance = 0.0
                
                # 处理摘要
                summary = str(row.get(field_mapping["summary_field"], "")).strip()
                if summary.lower() in ["nan", "none", ""]:
                    summary = ""
                
                # 生成序号（从1开始）
                sequence_number = len(transactions) + 1
                
                transaction = {
                    "sequence_number": sequence_number,  # 添加序号
                    "cardholder_name": cardholder_name,
                    "bank_name": account_info.get("bank_name", self.bank_name),  # 添加银行名称
                    "account_number": account_number,
                    "card_number": account_number,  # 黄宪文2.xlsx没有单独的卡号，使用账号作为卡号
                    "transaction_date": transaction_date,
                    "transaction_time": "00:00:00",  # Format2没有时间信息
                    "transaction_amount": transaction_amount,
                    "balance": float(balance),
                    "dr_cr_flag": dr_cr_flag,
                    "transaction_type": transaction_type,
                    "counterparty_name": str(row.get(field_mapping["counterparty_name_field"], "")).strip() or "",
                    "counterparty_account": str(row.get(field_mapping["counterparty_account_field"], "")).strip() or "",
                    "summary": summary,
                    "remark1": summary,  # 备注1使用摘要
                    "remark2": "",  # 备注2留空
                    "channel": str(row.get(field_mapping["channel_field"], "")).strip() or "",
                    "reference_number": str(row.get(field_mapping["reference_field"], "")).strip() or "",
                    "currency": "CNY",
                    "original_debit": float(debit_amount),
                    "original_credit": float(credit_amount)
                }
                
                transactions.append(transaction)
                
        except Exception as e:
            logger.error(f"提取交易记录失败: {e}")
        
        return transactions
    
    def _format_date(self, date_value) -> str:
        """格式化日期"""
        try:
            if pd.isna(date_value):
                return "1900-01-01"
            
            if isinstance(date_value, str):
                # 字符串格式
                if "-" in date_value:
                    # 已经是YYYY-MM-DD格式
                    return date_value.split()[0]  # 去掉可能的时间部分
                else:
                    # 尝试解析其他格式
                    try:
                        date_obj = pd.to_datetime(date_value)
                        return date_obj.strftime("%Y-%m-%d")
                    except:
                        return "1900-01-01"
            
            elif hasattr(date_value, 'strftime'):
                # datetime对象
                return date_value.strftime("%Y-%m-%d")
            
            else:
                # 其他格式，尝试转换
                try:
                    date_obj = pd.to_datetime(date_value)
                    return date_obj.strftime("%Y-%m-%d")
                except:
                    return "1900-01-01"
                    
        except Exception as e:
            logger.warning(f"日期格式化失败: {e}")
            return "1900-01-01"
    
    def _validate_data_consistency(self, accounts: List[Dict], transactions: List[Dict]) -> None:
        """验证数据一致性"""
        try:
            if not accounts or not transactions:
                return
            
            account = accounts[0]
            
            # 验证交易笔数
            assert len(transactions) == account["transactions_count"], \
                f"交易笔数不一致: 账户{account['transactions_count']} vs 交易{len(transactions)}"
            
            # 验证金额统计
            actual_inflow = sum(t["transaction_amount"] for t in transactions if t["dr_cr_flag"] == "收")
            actual_outflow = sum(t["transaction_amount"] for t in transactions if t["dr_cr_flag"] == "支")
            
            tolerance = 0.01
            assert abs(actual_inflow - account["total_inflow"]) <= tolerance, \
                f"收入金额不一致: 账户{account['total_inflow']} vs 交易{actual_inflow}"
            
            assert abs(actual_outflow - account["total_outflow"]) <= tolerance, \
                f"支出金额不一致: 账户{account['total_outflow']} vs 交易{actual_outflow}"
            
            logger.info("数据一致性验证通过")
            
        except AssertionError as e:
            logger.error(f"数据一致性验证失败: {e}")
            raise
        except Exception as e:
            logger.warning(f"数据一致性验证异常: {e}")
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取插件健康状态"""
        uptime = time.time() - self.start_time
        
        return {
            "healthy": self.error_count < 10,
            "last_check": time.time(),
            "memory_usage": "normal",
            "error_count": self.error_count,
            "uptime": uptime,
            "status_details": {
                "error_rate": self.error_count / max(1, uptime / 3600),
                "performance": "good" if uptime > 0 else "unknown",
                "supported_format": "北部湾银行Format2 (.xlsx)",
                "features": ["借方贷方处理", "表头信息提取", "日期格式处理", "空时间字段处理"]
            }
        } 