# 上海浦东发展银行Format1解析器插件

## 📋 插件概述

上海浦东发展银行Format1解析器插件专门用于解析浦发银行的流水文件，支持XLS和XLSX两种格式，能够自动适应不同格式间的字段名差异。

## 🎯 支持特性

### 文件格式支持
- ✅ XLS格式（Excel 2003及以下）
- ✅ XLSX格式（Excel 2007及以上）

### 字段映射规则

根据浦发银行流水的特点，本插件实现了以下字段映射：

| 浦发银行字段 | 映射目标 | 说明 |
|-------------|---------|------|
| 账户中文名、户名 | 持卡人姓名 | 支持两种字段名变体 |
| 卡号 | (空值) | 浦发银行流水中卡号字段通常为空 |
| 账号 | 账号 | 直接映射 |
| 借贷标志 | 收支标识 | 0=支出，1=收入 |
| 摘要代码、摘要 | 交易方式 | 支持两种字段名变体 |
| 附言 | 备注1 | 直接映射 |
| 对方行名 | 对方银行 | 直接映射 |

### 智能识别能力
- 🔍 自动检测表头行位置
- 📊 多工作表智能评分选择最佳数据表
- 🔧 容错处理：支持字段名轻微差异
- 📈 置信度计算：基于关键字段匹配度评估文件适用性

## 🚀 使用方法

### 通过插件管理器使用

插件会自动注册到系统中，通过解析器API调用时会自动选择：

```python
# 系统会根据文件内容自动选择最适合的解析器
result = parser_service.parse_file("浦发银行流水.xlsx")
```

### 直接使用插件

```python
from app.services.parser_plugin_system.plugins.spdb_format1_plugin import Plugin

parser = Plugin()
result = parser.parse("浦发银行流水.xlsx")
```

## 📈 置信度评估

插件通过以下方式计算解析置信度：

1. **关键字段检测**（40%权重）
   - 账户中文名/户名
   - 账号
   - 借贷标志
   - 摘要代码/摘要
   - 附言
   - 对方行名

2. **必需字段组合**（60%权重）
   - 持卡人姓名字段存在：+30%
   - 账号字段存在：+30%
   - 借贷标志字段存在：+20%
   - 交易方式字段存在：+10%

## 🔧 配置选项

插件配置文件 `config.json` 包含以下可调节参数：

```json
{
  "confidence_threshold": 0.8,
  "timeout": 60,
  "memory_limit": "512MB",
  "retry_count": 3,
  "debug": false
}
```

## 📊 输出格式

### 账户信息
```json
{
  "holder_name": "张三",
  "bank_name": "上海浦东发展银行",
  "account_number": "*****************",
  "card_number": "",
  "total_inflow": 50000.00,
  "total_outflow": 30000.00,
  "transaction_count": 150,
  "account_balance": 20000.00,
  "date_range": "2023-01-01 至 2023-12-31"
}
```

### 交易记录
```json
{
  "sequence_number": 1,
  "holder_name": "张三",
  "bank_name": "上海浦东发展银行",
  "account_number": "*****************",
  "card_number": "",
  "transaction_date": "2023-01-15",
  "transaction_time": "14:30:00",
  "transaction_method": "网上银行转账",
  "transaction_amount": 1000.00,
  "account_balance": 15000.00,
  "dr_cr_flag": "支出",
  "counterpart_name": "李四",
  "counterpart_account": "*****************",
  "counterpart_bank": "中国工商银行",
  "remark1": "转账备注",
  "remark2": ""
}
```

## 🐛 故障排除

### 常见问题

1. **置信度过低**
   - 检查文件是否为浦发银行流水
   - 确认包含必需的关键字段
   - 验证文件格式是否为XLS/XLSX

2. **解析失败**
   - 检查文件是否损坏
   - 确认文件编码格式
   - 查看日志文件获取详细错误信息

3. **字段映射错误**
   - 检查列名是否与预期一致
   - 可能需要手工调整表头行位置

### 调试模式

启用调试模式以获取详细的解析日志：

```json
{
  "debug": true
}
```

## 📝 版本历史

- **v1.0.0** (2024-01-xx)
  - 初始版本
  - 支持XLS和XLSX格式
  - 实现浦发银行特有的字段映射规则
  - 支持借贷标志的特殊映射（0=支出，1=收入）

## 👥 开发团队

银行流水分析系统开发团队

## 📄 许可证

本插件遵循项目整体许可证协议。


