{"name": "bankflow-client", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^5.2.6", "ajv": "^8.11.0", "antd": "^5.12.8", "axios": "1.4.0", "http-proxy-middleware": "^3.0.5", "moment": "^2.30.1", "react": "18.2.0", "react-dom": "18.2.0", "react-router-dom": "6.8.0", "react-scripts": "5.0.1", "web-vitals": "^5.0.1"}, "scripts": {"start": "set PORT=3000 && react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}