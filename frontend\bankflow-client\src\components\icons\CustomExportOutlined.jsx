import React from 'react';

/**
 * 修复了fill-rule警告的自定义ExportOutlined图标
 * 用于替代@ant-design/icons中的ExportOutlined
 */
const CustomExportOutlined = ({ style, className, onClick }) => (
  <svg
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 1024 1024"
    style={style}
    className={className}
    onClick={onClick}
  >
    <path d="M888 384H768V128c0-17.7-14.3-32-32-32H288c-17.7 0-32 14.3-32 32v256H136c-17.7 0-32 14.3-32 32v448c0 17.7 14.3 32 32 32h752c17.7 0 32-14.3 32-32V416c0-17.7-14.3-32-32-32zM320 160h384v224H320V160zm544 704H160V448h704v416z"/>
    <path d="M396 632c0-4.4 3.6-8 8-8h216c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8H404c-4.4 0-8-3.6-8-8v-48z"/>
  </svg>
);

export default CustomExportOutlined; 