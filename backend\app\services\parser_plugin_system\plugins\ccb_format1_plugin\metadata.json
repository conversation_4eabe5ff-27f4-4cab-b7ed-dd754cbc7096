{"name": "ccb_format1_plugin", "version": "1.0.0", "description": "建设银行Format1解析器插件，支持建设银行标准Excel格式的银行流水解析", "author": "银行流水系统团队", "license": "MIT", "homepage": "https://github.com/your-org/bank-parser", "supported_formats": ["Excel (.xlsx)", "Excel (.xls)"], "supported_banks": ["中国建设银行"], "dependencies": ["pandas>=1.3.0", "openpyxl>=3.0.0", "xlrd>=2.0.0"], "entry_point": "plugin.Plugin", "confidence_threshold": 0.7, "keywords": ["建设银行", "银行流水", "解析器", "插件", "Format1"], "format_features": {"multi_sheet": true, "time_format": "YYYY-MM-DD HH:MM:SS", "amount_format": "debit_credit_flag", "header_info": true, "transaction_sheet": "all"}, "changelog": {"1.0.0": "初始版本，支持建设银行标准Excel格式解析，包含借贷方向处理、多表结构支持、字段动态提取"}}