"""
银行管理API
"""
import uuid
from datetime import datetime
from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel

router = APIRouter(
    prefix="/banks",
    tags=["banks"]
)

class BankResponse(BaseModel):
    """银行响应模式"""
    bank_id: str
    bank_name: str
    bank_code: str
    description: str = None

# 静态银行列表
BANKS_DATA = [
    {
        "bank_id": "icbc",
        "bank_name": "中国工商银行",
        "bank_code": "ICBC",
        "description": "中国工商银行股份有限公司"
    },
    {
        "bank_id": "abc",
        "bank_name": "中国农业银行",
        "bank_code": "ABC",
        "description": "中国农业银行股份有限公司"
    },
    {
        "bank_id": "boc",
        "bank_name": "中国银行",
        "bank_code": "BOC",
        "description": "中国银行股份有限公司"
    },
    {
        "bank_id": "ccb",
        "bank_name": "中国建设银行",
        "bank_code": "CCB",
        "description": "中国建设银行股份有限公司"
    },
    {
        "bank_id": "bocom",
        "bank_name": "交通银行",
        "bank_code": "BOCOM",
        "description": "交通银行股份有限公司"
    },
    {
        "bank_id": "psbc",
        "bank_name": "中国邮政储蓄银行",
        "bank_code": "PSBC",
        "description": "中国邮政储蓄银行股份有限公司"
    },
    {
        "bank_id": "cmb",
        "bank_name": "招商银行",
        "bank_code": "CMB",
        "description": "招商银行股份有限公司"
    },
    {
        "bank_id": "spdb",
        "bank_name": "上海浦东发展银行",
        "bank_code": "SPDB",
        "description": "上海浦东发展银行股份有限公司"
    },
    {
        "bank_id": "citic",
        "bank_name": "中信银行",
        "bank_code": "CITIC",
        "description": "中信银行股份有限公司"
    },
    {
        "bank_id": "ceb",
        "bank_name": "中国光大银行",
        "bank_code": "CEB",
        "description": "中国光大银行股份有限公司"
    },
    {
        "bank_id": "cmbc",
        "bank_name": "中国民生银行",
        "bank_code": "CMBC",
        "description": "中国民生银行股份有限公司"
    },
    {
        "bank_id": "cib",
        "bank_name": "兴业银行",
        "bank_code": "CIB",
        "description": "兴业银行股份有限公司"
    },
    {
        "bank_id": "pingan",
        "bank_name": "平安银行",
        "bank_code": "PINGAN",
        "description": "平安银行股份有限公司"
    },
    {
        "bank_id": "beibuwan",
        "bank_name": "北部湾银行",
        "bank_code": "BEIBUWAN",
        "description": "广西北部湾银行股份有限公司"
    }
]

@router.get("/", response_model=List[BankResponse])
async def get_banks() -> List[BankResponse]:
    """
    获取所有银行列表
    """
    try:
        banks = [BankResponse(**bank) for bank in BANKS_DATA]
        return banks
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取银行列表失败: {str(e)}"
        )

@router.get("/{bank_id}", response_model=BankResponse)
async def get_bank(bank_id: str) -> BankResponse:
    """
    获取指定银行详情
    """
    try:
        bank_data = next((bank for bank in BANKS_DATA if bank["bank_id"] == bank_id), None)
        if not bank_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"银行 {bank_id} 不存在"
            )
        
        return BankResponse(**bank_data)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取银行详情失败: {str(e)}"
        ) 