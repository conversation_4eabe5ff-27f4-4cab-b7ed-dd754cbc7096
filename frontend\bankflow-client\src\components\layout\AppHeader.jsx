import React, { useState, useEffect } from 'react';
import { Layout, Typography, Breadcrumb, Select, Space, Button, Dropdown, message } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  HomeOutlined, 
  ProjectOutlined, 
  UserOutlined, 
  SettingOutlined,
  LogoutOutlined,
  BankOutlined,
  FileTextOutlined,
  BarChartOutlined,
  CommentOutlined,
  RobotOutlined
} from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';
import { buildApiUrl, API_ENDPOINTS } from '../../config/api';

const { Header } = Layout;
const { Title } = Typography;
const { Option } = Select;

/**
 * 应用头部组件
 * 包含标题、面包屑导航、项目切换器等
 * 
 * @returns {JSX.Element} 头部组件
 */
const AppHeader = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { logout, getUsername } = useAuth();
  const [projects, setProjects] = useState([]);
  const [currentProject, setCurrentProject] = useState(null);
  const [loading, setLoading] = useState(false);

  // 获取当前项目ID
  const projectId = location.pathname.split('/projects/')[1]?.split('/')[0];
  const isInProjectDetail = location.pathname.includes('/projects/') && projectId;

  // 加载项目列表
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        const response = await fetch(buildApiUrl(API_ENDPOINTS.projects));
        if (response.ok) {
          const data = await response.json();
          setProjects(data);
          
          // 设置当前项目
          if (projectId) {
            const current = data.find(p => p.project_id === projectId);
            setCurrentProject(current);
          }
        }
      } catch (error) {
        console.error('获取项目列表失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, [projectId]);

  // 生成面包屑导航
  const generateBreadcrumbs = () => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs = [
      {
        title: <><HomeOutlined /> 首页</>,
        onClick: () => navigate('/projects')
      }
    ];

    if (pathSegments[0] === 'projects') {
      if (pathSegments.length === 1) {
        // 项目列表页
        breadcrumbs.push({
          title: <><ProjectOutlined /> 项目管理</>,
        });
      } else if (pathSegments.length >= 2) {
        // 项目详情页
        breadcrumbs.push(
          {
            title: <><ProjectOutlined /> 项目管理</>,
            onClick: () => navigate('/projects')
          },
          {
            title: currentProject ? currentProject.project_name : `项目 ${projectId}`,
            onClick: () => navigate(`/projects/${projectId}`)
          }
        );

        // 添加子模块面包屑
        if (pathSegments.length >= 3) {
          const module = pathSegments[2];
          const subModule = pathSegments[3];

          const moduleMap = {
            'case-brief': { icon: <FileTextOutlined />, name: '案情简要' },
            'bankStatements': { icon: <BankOutlined />, name: '银行流水' },
            'analysis': { icon: <BarChartOutlined />, name: '银行流水分析' },
            'transcripts': { icon: <CommentOutlined />, name: '谈话笔录' },
            'aiQuery': { icon: <RobotOutlined />, name: '全局智能查询' }
          };

          const subModuleMap = {
            'clues': '问题线索',
            'subject': '被反映人资料',
            'related-persons': '相关人员资料',
            'related-units': '相关单位情况',
            'assets': '财产信息',
            'relationship': '人物关系图谱',
            'import': '银行流水导入',
            'clean': '数据清洗',
            'parsers': '解析器管理',
            'query': '查询整理',
            'export': '导出模块',
            'tactics': '常用战法分析',
            'ai': 'AI智能分析'
          };

          if (moduleMap[module]) {
            breadcrumbs.push({
              title: <>{moduleMap[module].icon} {moduleMap[module].name}</>,
              onClick: () => navigate(`/projects/${projectId}/${module}`)
            });

            if (subModule && subModuleMap[subModule]) {
              breadcrumbs.push({
                title: subModuleMap[subModule]
              });
            }
          }
        }
      }
    } else if (pathSegments[0] === 'settings') {
      breadcrumbs.push({
        title: <><SettingOutlined /> 系统设置</>,
      });
    }

    return breadcrumbs;
  };

  // 项目切换处理
  const handleProjectChange = (selectedProjectId) => {
    if (selectedProjectId === 'new') {
      navigate('/projects');
    } else {
      navigate(`/projects/${selectedProjectId}`);
    }
  };

  /**
   * 处理用户菜单点击
   */
  const handleUserMenuClick = ({ key }) => {
    switch (key) {
      case 'settings':
        navigate('/settings');
        break;
      case 'logout':
        handleLogout();
        break;
      default:
        break;
    }
  };

  /**
   * 处理退出登录
   */
  const handleLogout = () => {
    try {
      logout();
      message.success('已成功退出登录');
      navigate('/login');
    } catch (error) {
      console.error('退出登录失败:', error);
      message.error('退出登录失败');
    }
  };

  // 用户菜单项 - 简化版本，只保留系统设置和退出登录
  const userMenuItems = [
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置'
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录'
    }
  ];

  return (
    <Header style={{ 
      background: '#fff', 
      padding: '0 24px', 
      borderBottom: '1px solid #f0f0f0',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between'
    }}>
      <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
        <Title level={1} className="system-title" style={{ margin: 0, marginRight: 24, color: '#262626', fontSize: '32px', fontWeight: '600' }}>
          柳州供电局银行流水分析系统
        </Title>
        
        <Breadcrumb 
          style={{ flex: 1 }}
          items={generateBreadcrumbs().map((item, index) => ({
            key: index,
            title: (
              <span 
                onClick={item.onClick} 
                style={{ cursor: item.onClick ? 'pointer' : 'default' }}
              >
                {item.title}
              </span>
            )
          }))}
        />
      </div>

      <Space size="large">
        {/* 项目切换器 */}
        {isInProjectDetail && (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span style={{ marginRight: 8, color: '#666' }}>当前项目:</span>
            <Select
              value={projectId}
              style={{ minWidth: 200 }}
              onChange={handleProjectChange}
              loading={loading}
              placeholder="选择项目"
            >
              {projects.map(project => (
                <Option key={project.project_id} value={project.project_id}>
                  {project.project_name}
                </Option>
              ))}
              <Option value="new">
                <ProjectOutlined /> 返回项目列表
              </Option>
            </Select>
          </div>
        )}

        {/* 用户菜单 */}
        <Dropdown 
          menu={{ 
            items: userMenuItems,
            onClick: handleUserMenuClick
          }}
          placement="bottomRight"
        >
          <Button type="text" icon={<UserOutlined />}>
            {getUsername() || '用户'}
          </Button>
        </Dropdown>
      </Space>
    </Header>
  );
};

export default AppHeader; 