# 银行流水分析工具开发规则文件 (Rules3)
# 创建时间: 2024年 基于历史开发经验总结

## 🚨 核心强制规则 (违反=项目失败)

### RULE 1: 端到端验证强制要求 (无例外)
- 禁止声称"问题解决"，除非完成以下验证：
  1. 后端API测试通过
  2. 前端实际操作测试通过，包括测试前端UI交互  
  3. 用户完整业务流程验证通过
  4. 前端显示数据与后端数据库数据100%一致性验证
- 禁止以"技术限制"为理由跳过前端实际操作测试
- 必须使用所有可用工具进行完整验证
- 如果Playwright工具暂时连不上不可用要明确做出说明
- 如违反端到端测试规则，必须立即停止并重新执行

### RULE 2: 状态报告诚实化
- 禁止："问题完全解决"
- 改为："后端API修复完成，前端集成待验证"
- 禁止："系统正常运行" 
- 改为："部分功能修复，整体功能需用户验证"

### RULE 3: 测试证据强制提供
每次声称修复时必须提供：
1. 具体测试了哪些场景
2. 具体没有测试哪些场景  
3. 用户需要验证的具体步骤
4. 可能仍存在的问题点

### RULE 4: 前后端数据一致性原则
- 前端显示 = 后端数据 (100%一致)
- 不能只测试流程能走通
- 必须验证数据的数值准确性
- 如果不一致则作为BUG继续修复

## 🔧 代码修改控制规则

### RULE 5: 最小化修改原则
- 严格控制代码修改范围
- 在多次修改某个任务未完成时不能任意扩大修改范围
- 没有用户授权，禁止修改无问题的代码
- 只修改必要的代码来解决具体问题

### RULE 6: 数据库同步修改
- 所有前端输入字段的代码修改，都要同步修改后端数据库
- 确保前端数据与后端数据的完全同步

## 🌐 系统配置规则

### RULE 7: 端口管理规则
- 当后端8000端口被占用时，首先要解决占用的问题
- 禁止擅自修改端口为8001或其他
- 使用 netstat -ano | findstr :8000 查找占用进程
- 使用 taskkill /PID <进程ID> /F 杀死占用进程

### RULE 8: 用户数据隔离强制
- 必须使用get_user_db函数
- 所有API端点必须包含用户身份验证
- 数据库查询必须包含用户ID过滤
- 定期验证用户数据隔离完整性

## 🗣️ 交互规范

### RULE 9: Interactive Feedback强制使用
- 每当想要询问问题时，总是调用 MCP `interactive_feedback`
- 每当即将完成用户请求时，调用 MCP `interactive_feedback` 而不是简单地结束过程
- 持续调用 MCP 直到用户的反馈为空，然后结束请求

### RULE 10: 语言和注释规范
- ALWAYS RESPOND IN 中文
- 使用JSDoc注释
- thinking模式的思考过程和反馈都使用中文

## 🧪 测试工具使用规则

### RULE 11: 测试工具规范
- 如果前端验证时需调用Playwright的MCP服务但没有调用成功，请暂停工作并告诉用户
- 可以提出改用其他测试办法的建议，但未经用户允许不要擅自转用其他办法
- 必须使用现有的测试脚本进行验证

## 📋 历史问题防范

### RULE 12: 重大问题防范
基于历史经验，必须防范以下问题：
1. 数据库连接混乱 -> 强制使用用户数据隔离
2. 解析器置信度计算错误 -> 详细单元测试和边界值测试
3. 前端按钮无响应 -> 添加错误处理和加载状态
4. 持卡人姓名提取失败 -> 正确设置DataFrame name属性

### RULE 13: 开发效率规则
- 充分利用现有的测试脚本
- 遇到问题时先查看历史问题记录
- 保持代码修改的最小化原则
- 不要在没有备份的情况下进行大范围重构

## 🎯 成功标准

### RULE 14: 任务完成检查清单
每次任务完成时，确保能回答：
1. 后端API是否测试通过？
2. 前端实际操作是否测试通过？
3. 前端显示的数据是否与数据库数据100%一致？
4. 用户完整业务流程是否验证通过？
5. 是否有可能的遗留问题？

## 铁律提醒
- 永远不要说"问题完全解决"，除非通过了完整的端到端测试
- 前端显示数据 = 后端数据库数据（必须100%一致）
- 端口被占用时解决占用问题，不要改端口
- 遇到问题时使用interactive_feedback与用户沟通
- 端到端验证是最重要的规则，违反此规则将导致项目失败

## 历史血教训
1. 多次出现"后端修复完成"但前端仍有问题的情况
2. 用户数据隔离机制失效导致数据混乱
3. 置信度计算逻辑错误导致结果不准确
4. 随意更改端口导致系统配置混乱
5. 跳过端到端测试导致问题重复出现

---
文档版本: v1.0
基于项目: 银行流水分析工具 (纪委专用)
维护: 每次重大问题解决后都应更新此规则文件 