"""
问题线索管理API
"""
import logging
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_
from fastapi import APIRouter, Depends, HTTPException, status, Query

from ..middleware.auth_middleware import get_current_user, get_user_db
from ..models.duckdb_models import DuckDBClue as Clue
from ..schemas.clue import (
    ClueCreate, 
    ClueUpdate, 
    ClueResponse, 
    ClueListResponse
)

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/")
async def get_clues(
    project_id: str = Query(..., description="项目ID"),
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> List[Dict[str, Any]]:
    """获取项目的问题线索列表"""
    try:
        logger.info(f"用户 '{current_user}' 获取项目 {project_id} 的问题线索列表")
        
        clues = db.query(Clue).filter(Clue.project_id == project_id).all()
        
        result = []
        for clue in clues:
            result.append({
                "clue_id": clue.clue_id,
                "project_id": clue.project_id,
                "clue_number": clue.clue_number,
                "subject_name": clue.subject_name,
                "subject_position": clue.subject_position,
                "source": clue.source,
                "content": clue.content,
                "attachments": clue.attachments or [],
                "created_at": clue.created_at.isoformat() if clue.created_at else None,
                "updated_at": clue.updated_at.isoformat() if clue.updated_at else None,
                "created_by": clue.created_by,
                "updated_by": clue.updated_by
            })
        
        logger.info(f"用户 '{current_user}' 获取到 {len(result)} 条问题线索")
        return result
        
    except Exception as e:
        logger.error(f"用户 '{current_user}' 获取问题线索列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取问题线索列表失败: {str(e)}"
        )

@router.post("/")
async def create_clue(
    clue_data: Dict[str, Any],
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> Dict[str, Any]:
    """创建问题线索"""
    try:
        logger.info(f"用户 '{current_user}' 创建问题线索: {clue_data.get('clue_number', '未知')}")
        
        # 创建新的问题线索
        clue = Clue(
            project_id=clue_data["project_id"],
            clue_number=clue_data.get("clue_number", ""),
            subject_name=clue_data.get("subject_name", ""),
            subject_position=clue_data.get("subject_position", ""),
            source=clue_data.get("source", ""),
            content=clue_data.get("content", ""),
            attachments=clue_data.get("attachments", []),
            created_by=clue_data.get("created_by", current_user),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        db.add(clue)
        db.commit()
        db.refresh(clue)
        
        logger.info(f"用户 '{current_user}' 成功创建问题线索，ID: {clue.clue_id}")
        
        return {
            "clue_id": clue.clue_id,
            "project_id": clue.project_id,
            "clue_number": clue.clue_number,
            "subject_name": clue.subject_name,
            "subject_position": clue.subject_position,
            "source": clue.source,
            "content": clue.content,
            "attachments": clue.attachments or [],
            "created_at": clue.created_at.isoformat() if clue.created_at else None,
            "updated_at": clue.updated_at.isoformat() if clue.updated_at else None,
            "created_by": clue.created_by,
            "updated_by": clue.updated_by
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"用户 '{current_user}' 创建问题线索失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建问题线索失败: {str(e)}"
        )

@router.put("/{clue_id}")
async def update_clue(
    clue_id: str,
    clue_data: Dict[str, Any],
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> Dict[str, Any]:
    """更新问题线索"""
    try:
        logger.info(f"用户 '{current_user}' 更新问题线索，ID: {clue_id}")
        
        clue = db.query(Clue).filter(Clue.clue_id == clue_id).first()
        if not clue:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"问题线索 {clue_id} 不存在"
            )
        
        # 更新字段
        for field, value in clue_data.items():
            if field != "clue_id" and hasattr(clue, field):
                setattr(clue, field, value)
        
        clue.updated_by = clue_data.get("updated_by", current_user)
        clue.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(clue)
        
        logger.info(f"用户 '{current_user}' 成功更新问题线索: {clue.clue_id}")
        
        return {
            "clue_id": clue.clue_id,
            "project_id": clue.project_id,
            "clue_number": clue.clue_number,
            "subject_name": clue.subject_name,
            "subject_position": clue.subject_position,
            "source": clue.source,
            "content": clue.content,
            "attachments": clue.attachments or [],
            "created_at": clue.created_at.isoformat() if clue.created_at else None,
            "updated_at": clue.updated_at.isoformat() if clue.updated_at else None,
            "created_by": clue.created_by,
            "updated_by": clue.updated_by
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"用户 '{current_user}' 更新问题线索失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新问题线索失败: {str(e)}"
        )

@router.delete("/{clue_id}")
async def delete_clue(
    clue_id: str,
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> Dict[str, str]:
    """删除问题线索"""
    try:
        logger.info(f"用户 '{current_user}' 删除问题线索，ID: {clue_id}")
        
        clue = db.query(Clue).filter(Clue.clue_id == clue_id).first()
        if not clue:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"问题线索 {clue_id} 不存在"
            )
        
        db.delete(clue)
        db.commit()
        
        logger.info(f"用户 '{current_user}' 成功删除问题线索: {clue.clue_id}")
        
        return {"message": "问题线索删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"用户 '{current_user}' 删除问题线索失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除问题线索失败: {str(e)}"
        ) 