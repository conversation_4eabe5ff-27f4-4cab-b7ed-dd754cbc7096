"""
统一配置管理中心（简化版）
集中管理所有应用配置，确保配置的一致性和可维护性
"""
import os
import logging
from pathlib import Path
from typing import List, Optional
from datetime import timedelta

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Settings:
    """
    应用程序设置类（简化版）
    不依赖pydantic，使用原生Python实现
    """
    
    def __init__(self):
        # 🎯 应用基础配置
        self.app_name = "银行流水分析工具"
        self.app_version = "2.0.0"
        self.app_description = "纪委银行流水分析工具后端API"
        self.debug = self._get_bool_env("DEBUG", False)
        
        # 🌐 服务器配置（标准端口，不得修改）
        self.server_host = self._get_env("SERVER_HOST", "127.0.0.1")
        self.server_port = self._get_int_env("SERVER_PORT", 8000)
        
        # 🔐 安全配置
        self.secret_key = self._get_env("SECRET_KEY", "your-secret-key-change-in-production")
        self.algorithm = "HS256"
        self.access_token_expire_minutes = self._get_int_env("ACCESS_TOKEN_EXPIRE_MINUTES", 30)
        
        # 🗄️ 数据库配置（统一标准）
        self.database_url = self._get_env("DATABASE_URL", "duckdb://data/bankflow.duckdb")
        self.database_pool_size = 5
        self.database_echo = self._get_bool_env("DATABASE_ECHO", False)
        
        # 📁 文件和目录配置
        self.upload_dir = self._get_env("UPLOAD_DIR", "./uploads")
        self.data_dir = self._get_env("DATA_DIR", "./data")
        self.templates_dir = self._get_env("TEMPLATES_DIR", "./parsing_templates")
        self.max_file_size = self._get_int_env("MAX_FILE_SIZE", 100 * 1024 * 1024)  # 100MB
        
        # 🌍 CORS配置（支持多端口）
        self.cors_origins = [
            "http://localhost:3000",
            "http://127.0.0.1:3000",
            "http://localhost:3001",  # 🔧 添加3001端口支持
            "http://127.0.0.1:3001"
        ]
        
        # 📊 日志配置
        self.log_level = self._get_env("LOG_LEVEL", "INFO")
        self.log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        
        # ⚡ 性能配置
        self.api_timeout = self._get_int_env("API_TIMEOUT", 30)
        self.parse_timeout = self._get_int_env("PARSE_TIMEOUT", 300)  # 5分钟
        self.max_concurrent_uploads = self._get_int_env("MAX_CONCURRENT_UPLOADS", 3)
        
        # 🔧 功能开关
        self.enable_docs = self._get_bool_env("ENABLE_DOCS", True)
        self.enable_metrics = self._get_bool_env("ENABLE_METRICS", True)
        self.enable_health_check = self._get_bool_env("ENABLE_HEALTH_CHECK", True)
        
        # 初始化验证
        self._validate_settings()
    
    def _get_env(self, key: str, default: str = None) -> str:
        """安全获取环境变量"""
        return os.getenv(key, default)
    
    def _get_int_env(self, key: str, default: int) -> int:
        """获取整数环境变量"""
        try:
            return int(os.getenv(key, str(default)))
        except ValueError:
            logger.warning(f"环境变量 {key} 不是有效整数，使用默认值: {default}")
            return default
    
    def _get_bool_env(self, key: str, default: bool) -> bool:
        """获取布尔环境变量"""
        value = os.getenv(key, "").lower()
        if value in ("true", "1", "yes", "on"):
            return True
        elif value in ("false", "0", "no", "off"):
            return False
        else:
            return default
    
    def _validate_settings(self):
        """验证配置设置"""
        warnings = []
        
        # 验证服务器端口
        if self.server_port != 8000:
            warnings.append(f"使用非标准端口: {self.server_port}，建议使用8000")
        if not (1024 <= self.server_port <= 65535):
            warnings.append(f"端口号超出有效范围: {self.server_port}")
        
        # 验证数据库URL
        if not self.database_url.startswith('duckdb://'):
            warnings.append(f"数据库URL不是DuckDB格式: {self.database_url}")
        
        # 验证文件大小
        max_allowed = 500 * 1024 * 1024  # 500MB
        if self.max_file_size > max_allowed:
            warnings.append(f"文件大小限制过大: {self.max_file_size}, 最大推荐: {max_allowed}")
        
        # 显示警告
        for warning in warnings:
            logger.warning(f"⚠️ {warning}")
    
    def create_directories(self):
        """创建必要的目录"""
        directories = [
            self.upload_dir,
            self.data_dir,
            self.templates_dir
        ]
        
        for directory in directories:
            path = Path(directory)
            if not path.exists():
                path.mkdir(parents=True, exist_ok=True)
                logger.info(f"创建目录: {path.absolute()}")
    
    def get_database_path(self) -> str:
        """获取数据库文件路径"""
        if self.database_url.startswith('duckdb://'):
            return self.database_url.replace('duckdb://', '')
        return self.database_url
    
    def is_production(self) -> bool:
        """判断是否为生产环境"""
        return not self.debug and self.secret_key != "your-secret-key-change-in-production"
    
    def get_cors_settings(self) -> dict:
        """获取CORS设置"""
        return {
            "allow_origins": self.cors_origins,
            "allow_credentials": True,
            "allow_methods": ["*"],
            "allow_headers": ["*"],
        }
    
    def get_uvicorn_settings(self) -> dict:
        """获取Uvicorn服务器设置"""
        return {
            "host": self.server_host,
            "port": self.server_port,
            "reload": self.debug,
            "log_level": self.log_level.lower(),
            "access_log": self.debug
        }

# 创建全局设置实例
settings = Settings()

# 启动时验证和初始化
def initialize_settings():
    """初始化设置"""
    logger.info(f"🚀 初始化 {settings.app_name} v{settings.app_version}")
    
    # 创建必要目录
    settings.create_directories()
    
    # 显示关键配置
    logger.info(f"🌐 服务器: {settings.server_host}:{settings.server_port}")
    logger.info(f"🗄️ 数据库: {settings.get_database_path()}")
    logger.info(f"📁 上传目录: {Path(settings.upload_dir).absolute()}")
    logger.info(f"🔧 调试模式: {settings.debug}")
    
    return settings

# 导出设置
__all__ = ["settings", "initialize_settings", "Settings"] 