import React, { useState } from 'react';
import { Card, Form, Button, Select, Checkbox, Table, Space, Radio, Divider, Alert, message } from 'antd';
import { DownloadOutlined, FileExcelOutlined, FilePdfOutlined } from '@ant-design/icons';
import { useParams } from 'react-router-dom';

const { Group } = Checkbox;
const { Option } = Select;

const BankStatementsExport = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [fileFormat, setFileFormat] = useState('excel');
  const [fileReady, setFileReady] = useState(false);
  const [selectedAccounts, setSelectedAccounts] = useState([]);

  // 导出选项列表
  const exportOptions = [
    { label: '基本信息', value: 'basicInfo' },
    { label: '交易明细', value: 'transactions' },
    { label: '余额变动', value: 'balanceChanges' },
    { label: '交易对手信息', value: 'counterparties' },
    { label: '跨行交易', value: 'crossBankTransactions' },
    { label: '现金交易', value: 'cashTransactions' },
    { label: '可疑交易', value: 'suspiciousTransactions' },
  ];

  // 账户列表数据
  const accountsList = [
    {
      key: '1',
      accountName: '张三',
      accountNumber: '6222021234567890123',
      bank: '工商银行',
      recordCount: 156,
    },
    {
      key: '2',
      accountName: '张三',
      accountNumber: '6227001234567890123',
      bank: '建设银行',
      recordCount: 87,
    },
    {
      key: '3',
      accountName: '李四',
      accountNumber: '6228481234567890456',
      bank: '农业银行',
      recordCount: 43,
    },
  ];

  // 表格列定义
  const columns = [
    {
      title: '账户名称',
      dataIndex: 'accountName',
      key: 'accountName',
    },
    {
      title: '账号',
      dataIndex: 'accountNumber',
      key: 'accountNumber',
      render: (text) => `${text.substring(0, 4)}****${text.substring(text.length - 4)}`,
    },
    {
      title: '银行',
      dataIndex: 'bank',
      key: 'bank',
    },
    {
      title: '记录数量',
      dataIndex: 'recordCount',
      key: 'recordCount',
    },
  ];

  // 处理导出格式改变
  const handleFormatChange = (e) => {
    setFileFormat(e.target.value);
  };

  // 处理表格选择
  const rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(`选中的行键: ${selectedRowKeys}`, '选中的行:', selectedRows);
      setSelectedAccounts(selectedRows);
    },
  };

  // 处理导出表单提交
  const handleExport = (values) => {
    if (selectedAccounts.length === 0) {
      message.error('请至少选择一个账户');
      return;
    }

    setLoading(true);
    console.log('导出参数:', values);
    console.log('选中的账户:', selectedAccounts);
    
    // 模拟异步导出
    setTimeout(() => {
      setLoading(false);
      setFileReady(true);
      message.success('文件已准备好，可以下载');
    }, 1500);
  };

  // 处理下载
  const handleDownload = () => {
    message.success(`已开始下载 ${fileFormat === 'excel' ? 'Excel' : fileFormat === 'pdf' ? 'PDF' : '文本'} 文件`);
  };

  return (
    <div>
      <Card title="银行流水导出">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleExport}
          initialValues={{
            exportFormat: 'excel',
            exportOptions: ['basicInfo', 'transactions', 'balanceChanges'],
          }}
        >
          <Alert
            message="导出说明"
            description="选择需要导出的银行账户、时间范围和导出内容，系统将生成相应的报表文件供下载。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <Divider orientation="left">选择账户</Divider>
          <Table 
            rowSelection={{
              type: 'checkbox',
              ...rowSelection,
            }}
            columns={columns} 
            dataSource={accountsList} 
            pagination={false}
            size="small"
          />
          
          <Divider orientation="left">导出选项</Divider>
          <Form.Item 
            name="dateRange" 
            label="交易日期范围"
            rules={[{ required: true, message: '请选择交易日期范围' }]}
          >
            <Space>
              <Form.Item name="startYear" style={{ marginBottom: 0 }}>
                <Select 
                  placeholder="开始年份" 
                  style={{ width: 120 }}
                >
                  {Array.from({ length: 20 }, (_, i) => {
                    const year = new Date().getFullYear() - i;
                    return (
                      <Option key={year} value={year}>
                        {year}年
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
              <span style={{ color: '#8c8c8c' }}>至</span>
              <Form.Item name="endYear" style={{ marginBottom: 0 }}>
                <Select 
                  placeholder="结束年份" 
                  style={{ width: 120 }}
                >
                  {Array.from({ length: 20 }, (_, i) => {
                    const year = new Date().getFullYear() - i;
                    return (
                      <Option key={year} value={year}>
                        {year}年
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Space>
          </Form.Item>
          
          <Form.Item 
            name="exportOptions" 
            label="导出内容"
            rules={[{ required: true, message: '请选择至少一项导出内容' }]}
          >
            <Group options={exportOptions} />
          </Form.Item>
          
          <Form.Item name="exportFormat" label="导出格式">
            <Radio.Group onChange={handleFormatChange} value={fileFormat}>
              <Radio value="excel">
                <Space>
                  <FileExcelOutlined />
                  Excel
                </Space>
              </Radio>
              <Radio value="pdf">
                <Space>
                  <FilePdfOutlined />
                  PDF
                </Space>
              </Radio>
              <Radio value="txt">
                <Space>
                  <DownloadOutlined />
                  文本文件
                </Space>
              </Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item name="includeCharts" valuePropName="checked">
            <Checkbox>包含图表</Checkbox>
          </Form.Item>
          
          <Form.Item name="includeAnalysis" valuePropName="checked">
            <Checkbox>包含分析报告</Checkbox>
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={loading}
              >
                生成导出文件
              </Button>
              
              {fileReady && (
                <Button 
                  type="default" 
                  icon={<DownloadOutlined />} 
                  onClick={handleDownload}
                >
                  下载文件
                </Button>
              )}
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default BankStatementsExport; 