@echo off
echo ========================================
echo 重启银行流水分析系统服务
echo ========================================

echo [1/4] 停止现有进程...
taskkill /f /im python.exe /t 2>nul
taskkill /f /im node.exe /t 2>nul
timeout /t 3 /nobreak >nul

echo [2/4] 等待端口释放...
timeout /t 2 /nobreak >nul

echo [3/4] 启动后端服务...
cd /d "%~dp0"
start "后端服务" cmd /k "cd backend && python -m uvicorn app.main:app --host 127.0.0.1 --port 8000 --reload"

echo [4/4] 等待后端启动，然后启动前端...
timeout /t 5 /nobreak >nul

start "前端服务" cmd /k "cd frontend\bankflow-client && npm start"

echo ========================================
echo 服务重启完成！
echo 后端: http://127.0.0.1:8000
echo 前端: http://localhost:3000
echo ========================================
pause 