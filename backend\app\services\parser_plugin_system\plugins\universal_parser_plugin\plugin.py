"""
通用解析器插件 - 支持标准模板格式的银行流水解析
包含银行名称标准化功能，将不规范名称转换为系统标准名称
"""

import pandas as pd
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import re
import uuid
import os
import time
import traceback

# 修复导入路径
try:
    from ...core.plugin_interface import BasePlugin
except ImportError:
    # 如果相对导入失败，创建一个简单的基础类
    class BasePlugin:
        def __init__(self):
            self.name = "universal_parser_plugin"
            self.version = "1.0.0"
            self.description = "通用解析器插件"
            self.bank_name = "通用解析器"

logger = logging.getLogger(__name__)

class Plugin(BasePlugin):
    """通用解析器插件 - 支持标准模板格式解析"""
    
    def __init__(self, file_path: str = None):
        super().__init__()
        self.name = "universal_parser_plugin"
        self.version = "1.0.0"
        self.description = "通用解析器插件，支持用户按标准模板整理的银行流水文件解析"
        self.bank_name = "通用解析器"
        self.format_type = "xlsx"
        self.file_path = file_path
        
        # 解析结果
        self.accounts = []
        self.transactions = []
        self.transactions_by_account = {}
        
        # 状态管理
        self.start_time = time.time()
        self.error_count = 0
        
        # 标准字段映射（A-R列）
        self.standard_columns = {
            'A': '序号',
            'B': '持卡人', 
            'C': '银行名称',
            'D': '账号',
            'E': '卡号',
            'F': '交易日期',
            'G': '交易时间',
            'H': '交易方式',
            'I': '交易金额',
            'J': '账户余额',
            'K': '借贷标志',
            'L': '对方户名',
            'M': '对方账号',
            'N': '对方开户行',
            'O': '备注1',
            'P': '备注2',
            'Q': '备注3',
            'R': '币种'
        }
        
        # 必填字段
        self.required_fields = ['序号', '持卡人', '银行名称', '账号', '交易日期', '交易金额', '账户余额']
        
        # 🔧 银行名称标准化映射表
        self.bank_name_mapping = {
            # 工商银行的各种写法
            '工商': '中国工商银行',
            '工行': '中国工商银行',
            '工商银行': '中国工商银行',
            'ICBC': '中国工商银行',
            'icbc': '中国工商银行',
            '中国工商': '中国工商银行',
            
            # 建设银行的各种写法
            '建行': '中国建设银行',
            '建设': '中国建设银行',
            '建设银行': '中国建设银行',
            'CCB': '中国建设银行',
            'ccb': '中国建设银行',
            '中国建设': '中国建设银行',
            
            # 农业银行的各种写法
            '农行': '中国农业银行',
            '农业': '中国农业银行',
            '农业银行': '中国农业银行',
            'ABC': '中国农业银行',
            'abc': '中国农业银行',
            '中国农业': '中国农业银行',
            
            # 中国银行的各种写法
            '中行': '中国银行',
            'BOC': '中国银行',
            'boc': '中国银行',
            
            # 交通银行的各种写法
            '交行': '交通银行',
            '交通': '交通银行',
            'BOCOM': '交通银行',
            'bocom': '交通银行',
            
            # 招商银行的各种写法
            '招行': '招商银行',
            '招商': '招商银行',
            'CMB': '招商银行',
            'cmb': '招商银行',
            
            # 邮政储蓄银行的各种写法
            '邮政': '中国邮政储蓄银行',
            '邮储': '中国邮政储蓄银行',
            '邮政银行': '中国邮政储蓄银行',
            '邮储银行': '中国邮政储蓄银行',
            'PSBC': '中国邮政储蓄银行',
            'psbc': '中国邮政储蓄银行',
            
            # 浦发银行的各种写法
            '浦发': '上海浦东发展银行',
            '浦东发展': '上海浦东发展银行',
            'SPDB': '上海浦东发展银行',
            'spdb': '上海浦东发展银行',
            
            # 中信银行的各种写法
            '中信': '中信银行',
            'CITIC': '中信银行',
            'citic': '中信银行',
            
            # 光大银行的各种写法
            '光大': '中国光大银行',
            'CEB': '中国光大银行',
            'ceb': '中国光大银行',
            
            # 民生银行的各种写法
            '民生': '中国民生银行',
            'CMBC': '中国民生银行',
            'cmbc': '中国民生银行',
            
            # 兴业银行的各种写法
            '兴业': '兴业银行',
            'CIB': '兴业银行',
            'cib': '兴业银行',
            
            # 平安银行的各种写法
            '平安': '平安银行',
            'PINGAN': '平安银行',
            'pingan': '平安银行',
            
            # 北部湾银行的各种写法
            '北部湾': '北部湾银行',
            'BEIBUWAN': '北部湾银行',
            'beibuwan': '北部湾银行'
        }
    
    def standardize_bank_name(self, original_name: str) -> str:
        """
        标准化银行名称
        
        Args:
            original_name: 原始银行名称
            
        Returns:
            str: 标准化后的银行名称
        """
        if not original_name or pd.isna(original_name):
            return '未知银行'
        
        # 转换为字符串并去除空格
        name = str(original_name).strip()
        
        # 直接匹配标准名称
        if name in self.bank_name_mapping.values():
            return name
        
        # 查找映射表中的匹配
        for key, standard_name in self.bank_name_mapping.items():
            if key in name or name in key:
                logger.info(f"银行名称标准化: '{original_name}' -> '{standard_name}'")
                return standard_name
        
        # 如果没有找到匹配，返回原名称
        logger.warning(f"未找到银行名称 '{original_name}' 的标准化映射，保持原名称")
        return name    
    def parse(self, file_path: str = None) -> Dict[str, Any]:
        """
        主解析方法 - 解析通用格式的银行流水文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 解析结果
        """
        try:
            if file_path:
                self.file_path = file_path
            
            if not self.file_path:
                return {
                    'success': False,
                    'error': '未指定文件路径'
                }
            
            logger.info(f"开始使用通用解析器插件解析文件: {self.file_path}")
            self.start_time = time.time()
            
            # 1. 加载文件
            self._load_workbook()
            
            # 2. 检测和验证表头
            if not self._validate_headers():
                return {
                    'success': False,
                    'error': '文件不符合通用格式标准，请使用通用模板整理数据'
                }
            
            # 3. 解析交易数据
            self._parse_transactions()
            
            # 4. 提取账户信息
            self._extract_accounts()
            
            # 5. 按账户组织交易数据
            self._organize_transactions_by_account()
            
            # 6. 构建返回结果
            return self._build_result()
            
        except Exception as e:
            logger.error(f"通用解析器插件解析失败: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': f'解析失败: {str(e)}',
                'accounts': [],
                'transactions': []
            }
    
    def _load_workbook(self):
        """加载Excel工作簿"""
        try:
            if self.file_path.endswith('.xlsx'):
                self.workbook = pd.read_excel(self.file_path, engine='openpyxl', sheet_name=None)
            else:
                self.workbook = pd.read_excel(self.file_path, engine='xlrd', sheet_name=None)
            
            logger.info(f"成功加载工作簿，包含 {len(self.workbook)} 个工作表")
            
        except Exception as e:
            logger.error(f"加载工作簿失败: {str(e)}")
            raise
    
    def _validate_headers(self) -> bool:
        """验证表头是否符合通用格式标准"""
        try:
            # 获取第一个工作表
            first_sheet_name = list(self.workbook.keys())[0]
            df = self.workbook[first_sheet_name]

            if df.empty:
                logger.error("工作表为空")
                return False

            # 🔧 修复：使用DataFrame的列名作为表头，而不是第一行数据
            headers = list(df.columns)
            logger.info(f"检测到的表头: {headers}")

            # 检查必填字段是否存在
            missing_fields = []
            for required_field in self.required_fields:
                if required_field not in headers:
                    missing_fields.append(required_field)

            if missing_fields:
                logger.error(f"缺少必填字段: {missing_fields}")
                return False

            logger.info("表头验证通过")
            return True

        except Exception as e:
            logger.error(f"表头验证失败: {str(e)}")
            return False
    
    def _parse_transactions(self):
        """解析交易数据"""
        try:
            self.transactions = []

            # 处理第一个工作表（通用格式通常只有一个工作表）
            first_sheet_name = list(self.workbook.keys())[0]
            df = self.workbook[first_sheet_name]

            # 🔧 修复：使用DataFrame的列名作为表头
            headers = list(df.columns)

            # 🔧 修复：从第一行开始解析数据（因为列名已经是表头了）
            for row_idx in range(len(df)):
                row_data = df.iloc[row_idx]
                transaction = self._build_transaction_record(row_data, headers, row_idx)
                
                if transaction:
                    self.transactions.append(transaction)
            
            logger.info(f"成功解析 {len(self.transactions)} 条交易记录")
            
        except Exception as e:
            logger.error(f"解析交易数据失败: {str(e)}")
            raise
    
    def _build_transaction_record(self, row_data: pd.Series, headers: List[str], row_idx: int) -> Optional[Dict[str, Any]]:
        """构建单条交易记录"""
        try:
            transaction = {}
            
            # 遍历每一列，根据表头映射数据
            for col_idx, header in enumerate(headers):
                if col_idx < len(row_data):
                    value = row_data.iloc[col_idx]
                    
                    # 处理空值
                    if pd.isna(value) or value == '':
                        value = None
                    else:
                        value = str(value).strip()
                    
                    # 根据字段名映射到标准字段
                    if header == '序号':
                        transaction['sequence_number'] = self._safe_int(value)
                    elif header == '持卡人':
                        transaction['holder_name'] = value
                    elif header == '银行名称':
                        # 🔧 关键修改：使用银行名称标准化
                        transaction['bank_name'] = self.standardize_bank_name(value)
                    elif header == '账号':
                        transaction['account_number'] = value
                    elif header == '卡号':
                        transaction['card_number'] = value
                    elif header == '交易日期':
                        transaction['transaction_date'] = self._parse_date(value)
                    elif header == '交易时间':
                        transaction['transaction_time'] = self._parse_time(value)
                    elif header == '交易方式':
                        transaction['transaction_method'] = value
                    elif header == '交易金额':
                        transaction['transaction_amount'] = self._safe_decimal(value)
                    elif header == '账户余额':
                        transaction['balance_amount'] = self._safe_decimal(value)
                    elif header == '借贷标志':
                        transaction['dr_cr_flag'] = value
                    elif header == '对方户名':
                        transaction['counterparty_name'] = value
                    elif header == '对方账号':
                        transaction['counterparty_account'] = value
                    elif header == '对方开户行':
                        transaction['counterparty_bank'] = value
                    elif header == '备注1':
                        transaction['remark1'] = value
                    elif header == '备注2':
                        transaction['remark2'] = value
                    elif header == '备注3':
                        transaction['remark3'] = value
                    elif header == '币种':
                        transaction['currency'] = value or 'CNY'
            
            # 验证必填字段
            required_check = ['holder_name', 'account_number', 'transaction_date', 'transaction_amount', 'balance_amount']
            for field in required_check:
                if not transaction.get(field):
                    logger.warning(f"第{row_idx+2}行缺少必填字段: {field}")
                    return None
            
            # 添加系统字段
            transaction['transaction_id'] = str(uuid.uuid4())
            transaction['raw_transaction_id'] = f"universal_{row_idx}"
            transaction['person_name'] = transaction['holder_name']
            transaction['account_name'] = transaction['holder_name']
            transaction['transaction_type'] = self._determine_transaction_type(transaction['transaction_amount'])
            transaction['counterparty_name'] = transaction.get('counterparty_name', '')
            transaction['counterparty_account'] = transaction.get('counterparty_account', '')
            transaction['purpose'] = transaction.get('remark1', '')
            transaction['channel'] = transaction.get('transaction_method', '通用格式')
            transaction['reference_number'] = transaction.get('remark2', '')
            transaction['currency'] = transaction.get('currency', 'CNY')
            transaction['created_at'] = datetime.now()
            transaction['updated_at'] = datetime.now()
            
            return transaction
            
        except Exception as e:
            logger.error(f"构建交易记录失败 (行{row_idx+2}): {str(e)}")
            return None    
    def _extract_accounts(self):
        """从交易数据中提取账户信息"""
        try:
            self.accounts = []

            # 🔧 修复：按持卡人+账户号组合分组，确保不同持卡人的账户被正确识别
            account_groups = {}
            for transaction in self.transactions:
                holder_name = transaction.get('holder_name', '')
                account_number = transaction.get('account_number', '')
                # 使用持卡人+账户号作为唯一标识
                account_key = f"{holder_name}_{account_number}"
                if account_key:
                    if account_key not in account_groups:
                        account_groups[account_key] = []
                    account_groups[account_key].append(transaction)
            
            # 为每个账户创建账户记录
            for account_key, transactions in account_groups.items():
                first_transaction = transactions[0]

                account = {
                    'account_id': str(uuid.uuid4()),
                    'person_name': first_transaction.get('holder_name', ''),
                    'bank_name': first_transaction.get('bank_name', '通用格式'),
                    'account_name': first_transaction.get('holder_name', ''),
                    'account_number': first_transaction.get('account_number', ''),
                    'card_number': first_transaction.get('card_number', ''),
                    # 🔧 添加前端需要的字段映射
                    'holder_name': first_transaction.get('holder_name', ''),
                    'cardholder_name': first_transaction.get('holder_name', ''),
                    'import_file_source': self.file_path,
                    'creation_timestamp': datetime.now().strftime('%Y%m%d_%H%M%S'),
                    'updated_at': datetime.now()
                }
                
                # 🔧 修复：添加前端需要的账户统计信息
                account['transactions_count'] = len(transactions)

                # 计算收入和支出总额
                total_inflow = sum(t.get('transaction_amount', 0) for t in transactions if t.get('transaction_amount', 0) > 0)
                total_outflow = sum(abs(t.get('transaction_amount', 0)) for t in transactions if t.get('transaction_amount', 0) < 0)
                account['total_inflow'] = total_inflow
                account['total_outflow'] = total_outflow

                # 计算时间范围（字符串格式）
                dates = [t.get('transaction_date', '') for t in transactions if t.get('transaction_date')]
                if dates:
                    start_date = min(dates)
                    end_date = max(dates)
                    if start_date == end_date:
                        account['date_range'] = start_date
                    else:
                        account['date_range'] = f"{start_date} 至 {end_date}"
                else:
                    account['date_range'] = "未知"
                
                self.accounts.append(account)
            
            logger.info(f"成功提取 {len(self.accounts)} 个账户")
            
        except Exception as e:
            logger.error(f"提取账户信息失败: {str(e)}")
            raise
    
    def _organize_transactions_by_account(self):
        """按账户组织交易数据"""
        try:
            self.transactions_by_account = {}

            for transaction in self.transactions:
                holder_name = transaction.get('holder_name', '')
                account_number = transaction.get('account_number', '')
                # 🔧 修复：使用持卡人+账户号作为唯一标识，与账户提取逻辑保持一致
                account_key = f"{holder_name}_{account_number}"
                if account_key:
                    if account_key not in self.transactions_by_account:
                        self.transactions_by_account[account_key] = []
                    self.transactions_by_account[account_key].append(transaction)

            logger.info(f"按账户组织交易数据完成，共 {len(self.transactions_by_account)} 个账户")

        except Exception as e:
            logger.error(f"组织交易数据失败: {str(e)}")
            raise
    
    def _build_result(self) -> Dict[str, Any]:
        """构建返回结果"""
        confidence_score = self._calculate_confidence_score()
        
        return {
            'success': True,
            'accounts': self.accounts,
            'transactions': self.transactions,
            'transactions_by_account': self.transactions_by_account,
            'confidence_score': confidence_score,
            'parser_type': 'UniversalParser',
            'bank_name': '通用解析器',
            'file_path': self.file_path,
            'summary': {
                'total_accounts': len(self.accounts),
                'total_transactions': len(self.transactions),
                'confidence': confidence_score,
                'processing_time': time.time() - self.start_time
            }
        }
    
    def _calculate_confidence_score(self) -> float:
        """
        计算置信度得分
        通用解析器对符合格式的文件给予高置信度
        
        Returns:
            float: 置信度得分 (0-100)
        """
        try:
            # 基础得分
            score = 0.0
            
            # 1. 数据完整性检查 (40分)
            if self.accounts and self.transactions:
                score += 40.0
                
                # 账户数据完整性
                account_completeness = sum(1 for account in self.accounts if account.get('account_number') and account.get('person_name')) / len(self.accounts)
                score += account_completeness * 10.0
                
                # 交易数据完整性
                transaction_completeness = sum(1 for t in self.transactions if t.get('transaction_date') and t.get('transaction_amount')) / len(self.transactions)
                score += transaction_completeness * 10.0
            
            # 2. 格式标准化程度 (30分)
            # 通用格式本身就是标准化的，给满分
            score += 30.0
            
            # 3. 数据质量 (20分)
            if self.transactions:
                # 检查日期格式
                valid_dates = sum(1 for t in self.transactions if t.get('transaction_date'))
                date_ratio = valid_dates / len(self.transactions)
                score += date_ratio * 10.0
                
                # 检查金额格式
                valid_amounts = sum(1 for t in self.transactions if isinstance(t.get('transaction_amount'), (int, float)))
                amount_ratio = valid_amounts / len(self.transactions)
                score += amount_ratio * 10.0
            
            # 4. 特殊加分 (10分)
            # 通用格式识别准确，给满分
            score += 10.0
            
            # 确保分数在0-100之间
            confidence = max(0.0, min(100.0, score))
            
            logger.info(f"通用解析器插件置信度得分: {confidence:.1f}")
            return confidence
            
        except Exception as e:
            logger.error(f"计算置信度失败: {str(e)}")
            return 50.0  # 默认中等置信度
    
    @staticmethod
    def calculate_confidence(file_path: str) -> float:
        """
        静态方法：计算文件的置信度（用于快速评估）
        
        Args:
            file_path: 文件路径
            
        Returns:
            float: 置信度得分 (0-100)
        """
        try:
            # 快速检查文件内容
            try:
                if file_path.endswith('.xlsx'):
                    df = pd.read_excel(file_path, engine='openpyxl', nrows=10)
                else:
                    df = pd.read_excel(file_path, engine='xlrd', nrows=10)
                
                if df.empty:
                    return 0.0
                
                # 获取表头
                headers = df.iloc[0].fillna('').astype(str).tolist()
                
                # 检查是否包含通用格式标准字段
                universal_headers = ['序号', '持卡人', '银行名称', '账号', '交易日期', '交易金额', '账户余额']
                
                # 计算字段匹配度
                header_matches = 0
                for expected_header in universal_headers:
                    if expected_header in headers:
                        header_matches += 1
                
                # 🔧 修复：通用解析器只对真正的通用格式文件给高分
                # 如果字段匹配度低，说明不是通用格式，应该返回0分
                if header_matches < 5:  # 7个字段中少于5个匹配，说明不是通用格式
                    logger.info(f"通用解析器插件置信度评估: 字段匹配{header_matches}/{len(universal_headers)}, 不是通用格式，得分0")
                    return 0.0

                # 基础得分计算（只有高匹配度才给分）
                header_score = (header_matches / len(universal_headers)) * 70.0

                # 数据行检查
                data_score = 0
                if len(df) > 1:  # 有数据行
                    data_score = 20.0

                # 文件名指示得分
                filename_score = 0
                import os
                filename = os.path.basename(file_path).lower()
                universal_indicators = ['通用', '标准', 'universal', 'generic', 'template']
                for indicator in universal_indicators:
                    if indicator in filename:
                        filename_score = 10
                        break

                total_score = header_score + data_score + filename_score

                # 设置最低和最高分（只有真正匹配的才给分）
                if header_matches >= 6:  # 7个字段中至少有6个匹配
                    total_score = max(total_score, 85.0)  # 高匹配度给85分
                elif header_matches >= 5:  # 至少5个字段匹配
                    total_score = max(total_score, 60.0)  # 中等匹配度给60分
                else:
                    # 少于5个字段匹配，不是通用格式
                    total_score = 0.0
                
                logger.info(f"通用解析器插件置信度评估: 字段匹配{header_matches}/{len(universal_headers)}, 得分{total_score:.1f}")
                return min(100.0, total_score)
            
            except Exception as e:
                logger.warning(f"通用解析器插件置信度计算中的文件读取错误: {str(e)}")
                return 5.0  # 文件读取失败，给最低参与度
            
        except Exception as e:
            logger.error(f"通用解析器插件置信度计算失败: {str(e)}")
            return 5.0  # 计算失败，给最低参与度    
    def extract_sample(self, file_path: str = None, limit: int = 10) -> Dict[str, Any]:
        """
        提取样本数据用于快速置信度评估
        通用解析器版本 - 支持4维度评估
        
        Args:
            file_path: 文件路径
            limit: 样本数量限制
            
        Returns:
            Dict: 包含样本账户和交易的字典
        """
        try:
            target_file = file_path or self.file_path
            if not target_file:
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
            
            logger.info(f"通用解析器插件开始提取样本数据，限制条数: {limit}")
            
            # 🔧 快速样本提取模式（避免完整解析）
            sample_accounts = []
            sample_transactions = []
            
            try:
                # 快速读取Excel文件前几行
                if target_file.endswith('.xlsx'):
                    df = pd.read_excel(target_file, engine='openpyxl', nrows=limit * 2)
                else:
                    df = pd.read_excel(target_file, engine='xlrd', nrows=limit * 2)
                
                if df.empty:
                    return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
                
                headers = list(df.columns)
                logger.info(f"通用解析器检测到表头: {headers}")
                
                # 验证必要字段存在
                required_fields = ['持卡人', '账号', '交易金额', '账户余额']
                missing_fields = [field for field in required_fields if field not in headers]

                if missing_fields:
                    logger.warning(f"通用解析器缺少必要字段: {missing_fields}")
                    # 🔧 检测是否为民生银行格式，如果是则返回低置信度
                    cmbc_indicators = ['户名', '交易方式', '摘要', '收入', '支出']
                    cmbc_score = sum(1 for indicator in cmbc_indicators if any(indicator in str(col) for col in headers))

                    if cmbc_score >= 2:  # 如果检测到民生银行特征
                        logger.info(f"检测到民生银行格式特征，通用解析器不适用")
                        return {
                            'accounts': [],
                            'transactions': [],
                            'success': False,
                            'metadata': {
                                'sample_size': 0,
                                'missing_fields': missing_fields,
                                'plugin_name': self.name,
                                'confidence': 5,  # 很低的置信度
                                'reason': '检测到民生银行格式，通用解析器不适用',
                                'dimension_scores': {
                                    'customer_name': 0,
                                    'transaction_method': 0,
                                    'remark1': 0,
                                    'income_expense': 0
                                }
                            }
                        }

                    return {
                        'accounts': [],
                        'transactions': [],
                        'success': False,
                        'metadata': {
                            'sample_size': 0,
                            'missing_fields': missing_fields,
                            'plugin_name': self.name,
                            'confidence': 10,  # 低置信度
                            'dimension_scores': {
                                'customer_name': 0,
                                'transaction_method': 0,
                                'remark1': 0,
                                'income_expense': 0
                            }
                        }
                    }
                
                # 提取样本账户信息
                first_holder_name = ""
                first_account_number = ""
                first_bank_name = "通用银行"
                
                if len(df) > 0:
                    first_row = df.iloc[0]
                    first_holder_name = str(first_row.get('持卡人', '通用测试用户')).strip()
                    first_account_number = str(first_row.get('账号', '****************')).strip()
                    first_bank_name = self.standardize_bank_name(str(first_row.get('银行名称', '通用银行')))
                
                if first_holder_name and first_account_number:
                    sample_account = {
                        'cardholder_name': first_holder_name,  # 🔧 4维度姓名识别需要
                        'holder_name': first_holder_name,
                        'account_number': first_account_number,
                        'card_number': str(first_row.get('卡号', '')).strip(),
                        'bank_name': first_bank_name,
                        'account_type': '个人账户' if len(first_holder_name) <= 4 else '企业账户'
                    }
                    sample_accounts.append(sample_account)
                
                # 提取样本交易数据
                transaction_count = 0
                for idx, row in df.iterrows():
                    if transaction_count >= limit:
                        break
                    
                    try:
                        # 提取关键字段
                        holder_name = str(row.get('持卡人', '')).strip()
                        account_number = str(row.get('账号', '')).strip()
                        transaction_date = str(row.get('交易日期', '')).strip()
                        amount_raw = row.get('交易金额', 0)
                        balance_raw = row.get('账户余额', 0)
                        
                        # 基本验证
                        if not holder_name or not account_number or not transaction_date:
                            continue
                        
                        # 解析金额和余额
                        try:
                            amount = float(amount_raw) if amount_raw else 0.0
                            balance = float(balance_raw) if balance_raw else 0.0
                        except (ValueError, TypeError):
                            continue
                        
                        # 构建样本交易
                        transaction = {
                            'cardholder_name': holder_name,  # 🔧 4维度姓名识别需要
                            'holder_name': holder_name,
                            'account_number': account_number,
                            'transaction_date': self._parse_date(transaction_date) or transaction_date,
                            'transaction_amount': amount,
                            'balance': balance,  # 🔧 4维度金额解析需要
                            'dr_cr_flag': '收' if amount >= 0 else '支',
                            'currency': str(row.get('币种', 'CNY')).strip(),
                            'transaction_method': str(row.get('交易方式', '通用交易')).strip(),
                            'bank_name': first_bank_name
                        }
                        
                        sample_transactions.append(transaction)
                        transaction_count += 1
                        
                    except Exception as e:
                        logger.debug(f"跳过第{idx+1}行: {str(e)}")
                        continue
                
                # 🔧 计算4维度评分和置信度
                dimension_scores = self._evaluate_universal_4_dimensions(headers, sample_accounts, sample_transactions)
                confidence = sum(dimension_scores.values()) / len(dimension_scores) if dimension_scores else 0

                return {
                    'accounts': sample_accounts,
                    'transactions': sample_transactions[:limit],
                    'success': True,
                    'metadata': {
                        'sample_size': len(sample_transactions),
                        'evaluation_mode': 'extract_sample',
                        'plugin_name': self.name,
                        'confidence': confidence,
                        'bank_name': first_bank_name,
                        'headers_detected': headers,
                        'dimension_scores': dimension_scores
                    }
                }
                
            except Exception as e:
                logger.error(f"通用解析器快速样本提取失败: {str(e)}")
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0, 'error': str(e)}}
            
        except Exception as e:
            logger.error(f"通用解析器extract_sample方法失败: {str(e)}")
            return {
                'accounts': [],
                'transactions': [],
                'success': False,
                'metadata': {
                    'sample_size': 0,
                    'error': str(e),
                    'plugin_name': self.name,
                    'confidence': 0,
                    'dimension_scores': {
                        'customer_name': 0,
                        'transaction_method': 0,
                        'remark1': 0,
                        'income_expense': 0
                    }
                }
            }

    def _evaluate_universal_4_dimensions(self, headers: List[str], accounts: List[Dict], transactions: List[Dict]) -> Dict[str, float]:
        """
        通用解析器4维度评估方法
        检查是否符合通用解析器的标准格式要求
        """
        try:
            scores = {
                'customer_name': 0.0,
                'transaction_method': 0.0,
                'remark1': 0.0,
                'income_expense': 0.0
            }

            # 通用解析器要求的标准字段
            required_fields = {
                'customer_name': ['持卡人'],
                'transaction_method': ['交易方式'],
                'remark1': ['备注', '摘要'],
                'income_expense': ['收支标志', '借贷标志']
            }

            # 检查每个维度的字段是否存在
            for dimension, field_names in required_fields.items():
                for field_name in field_names:
                    if field_name in headers:
                        scores[dimension] = 100.0
                        logger.info(f"✅ 通用解析器{dimension}字段匹配: {field_name}")
                        break

            # 如果检测到民生银行特征，降低评分
            cmbc_indicators = ['户名', '交易方式', '摘要', '收入', '支出']
            cmbc_score = sum(1 for indicator in cmbc_indicators if any(indicator in str(col) for col in headers))

            if cmbc_score >= 2:
                # 检测到民生银行格式，大幅降低评分
                for key in scores:
                    scores[key] = max(0, scores[key] - 80)  # 降低80分
                logger.info(f"检测到民生银行格式特征，通用解析器评分降低")

            logger.info(f"通用解析器4维度评分: {scores}")
            return scores

        except Exception as e:
            logger.error(f"通用解析器4维度评估失败: {str(e)}")
            return {
                'customer_name': 0,
                'transaction_method': 0,
                'remark1': 0,
                'income_expense': 0
            }

    def _safe_int(self, value) -> Optional[int]:
        """安全转换为整数"""
        if value is None or value == '':
            return None
        try:
            return int(float(str(value)))
        except (ValueError, TypeError):
            return None
    
    def _safe_decimal(self, value) -> Optional[float]:
        """安全转换为浮点数"""
        if value is None or value == '':
            return None
        try:
            # 处理中文数字符号
            str_value = str(value).replace(',', '').replace('，', '')
            return float(str_value)
        except (ValueError, TypeError):
            return None
    
    def _parse_date(self, value) -> Optional[str]:
        """解析日期"""
        if value is None or value == '':
            return None
        
        try:
            # 尝试多种日期格式
            date_formats = [
                '%Y-%m-%d',
                '%Y/%m/%d',
                '%Y年%m月%d日',
                '%m/%d/%Y',
                '%d/%m/%Y'
            ]
            
            str_value = str(value).strip()
            
            for fmt in date_formats:
                try:
                    parsed_date = datetime.strptime(str_value, fmt)
                    return parsed_date.strftime('%Y-%m-%d')
                except ValueError:
                    continue
            
            # 如果都失败了，尝试pandas的日期解析
            parsed_date = pd.to_datetime(str_value)
            return parsed_date.strftime('%Y-%m-%d')
            
        except Exception:
            logger.warning(f"无法解析日期: {value}")
            return None
    
    def _parse_time(self, value) -> Optional[str]:
        """解析时间"""
        if value is None or value == '':
            return None
        
        try:
            str_value = str(value).strip()
            
            # 尝试多种时间格式
            time_formats = [
                '%H:%M:%S',
                '%H:%M',
                '%H时%M分%S秒',
                '%H时%M分'
            ]
            
            for fmt in time_formats:
                try:
                    parsed_time = datetime.strptime(str_value, fmt)
                    return parsed_time.strftime('%H:%M:%S')
                except ValueError:
                    continue
            
            return str_value  # 如果解析失败，返回原值
            
        except Exception:
            logger.warning(f"无法解析时间: {value}")
            return None
    
    def _determine_transaction_type(self, amount) -> str:
        """根据金额判断交易类型"""
        try:
            if amount is None:
                return '未知'
            
            amount_value = float(amount)
            if amount_value > 0:
                return '收入'
            elif amount_value < 0:
                return '支出'
            else:
                return '无变动'
                
        except (ValueError, TypeError):
            return '未知'
    
    def get_info(self) -> Dict[str, Any]:
        """获取插件信息"""
        return {
            'name': self.name,
            'version': self.version,
            'description': self.description,
            'bank_name': self.bank_name,
            'format_type': self.format_type,
            'supported_formats': ['通用标准格式', '手工整理格式', '标准模板格式'],
            'features': [
                '支持18个标准字段（A-R列）',
                '银行名称自动标准化',
                '多账户支持',
                '数据完整性验证',
                '高置信度评估'
            ]
        }
    
    def validate_file(self, file_path: str) -> Dict[str, Any]:
        """验证文件是否符合通用格式"""
        try:
            confidence = self.calculate_confidence(file_path)
            
            return {
                'valid': confidence > 10,
                'confidence': confidence,
                'message': f'置信度: {confidence:.1f}%',
                'recommendations': [
                    '确保文件包含必填字段：序号、持卡人、银行名称、账号、交易日期、交易金额、账户余额',
                    '使用标准的通用模板格式',
                    '确保数据格式正确（日期、金额等）'
                ]
            }
            
        except Exception as e:
            return {
                'valid': False,
                'confidence': 0,
                'message': f'验证失败: {str(e)}',
                'recommendations': ['请检查文件格式是否正确']
            }