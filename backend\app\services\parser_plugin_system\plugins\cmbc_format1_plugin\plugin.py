"""
民生银行Format1解析器插件
支持民生银行多子表结构Excel格式的银行流水解析
基于原有成功的解析逻辑，添加4维度预解析功能
"""

import pandas as pd
import time
import os
import traceback
from datetime import datetime
from typing import Dict, Any, List, Optional
import logging
import json
from pathlib import Path
import re

# 导入基础插件接口
try:
    from ...core.plugin_interface import BasePlugin
except ImportError:
    # 如果在测试环境中，创建一个简单的基础类
    class BasePlugin:
        def __init__(self):
            self.name = "cmbc_format1_plugin"
            self.version = "1.0.0"
            self.description = "民生银行Format1解析器插件"
            self.bank_name = "中国民生银行"

logger = logging.getLogger(__name__)

class Plugin(BasePlugin):
    """民生银行Format1解析器插件"""

    def __init__(self, file_path: str = None):
        super().__init__()
        self.name = "cmbc_format1_plugin"
        self.version = "1.0.0"
        self.description = "民生银行Format1解析器插件，支持民生银行多子表结构Excel格式的银行流水解析"
        self.bank_name = "中国民生银行"
        self.format_type = "format1"
        self.start_time = time.time()
        self.error_count = 0
        self.file_path = file_path

        # 加载配置
        self.config = self._load_config()

        # 解析结果
        self.accounts = []
        self.transactions = []

        # 民生银行特定的字段映射
        self.field_mapping = {
            'date_field': '交易日期',
            'time_field': '交易时间',
            'amount_field': '交易金额',
            'balance_field': '账户余额',
            'summary_field': '摘要',
            'remark_field': '备注',
            'cardholder_field': '客户名称',
            'card_field': '卡号',
            'direction_field': '借贷方向',
            'account_field': '账号',
            'opposite_account_field': '对方账号',
            'opposite_name_field': '对方户名'
        }

        # 🔧 修复：添加民生银行特征关键词
        self.bank_keywords = [
            '民生银行', '中国民生银行', 'CMBC',
            '客户名称', '交易金额', '账户余额', '交易日期', '交易时间',
            '借贷标志', '交易摘要', '对方户名', '对方账号'
        ]

        # 支持的文件扩展名（用于快速校验，与北部湾插件保持一致）
        self.supported_formats = ['.xlsx', '.xls']

        logger.info(f"✅ {self.name} v{self.version} 初始化完成")

    def _load_config(self) -> Dict[str, Any]:
        """加载插件配置"""
        try:
            config_path = Path(__file__).parent / "config.json"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"加载配置失败: {e}")

        # 默认配置
        return {
            "enabled": True,
            "confidence_threshold": 0.7,
            "plugin_settings": {
                "max_file_size": "100MB",
                "supported_extensions": [".xlsx", ".xls"]
            }
        }

    def get_metadata(self) -> Dict[str, Any]:
        """获取插件元信息"""
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "supported_formats": ["中国民生银行Excel格式"],
            "confidence_threshold": self.config.get("confidence_threshold", 0.7),
            "bank_name": self.bank_name,
            "format_type": self.format_type,
            "field_mapping": self.field_mapping
        }

    def calculate_confidence(self, file_path: str = None) -> float:
        """
        计算解析器对文件的置信度
        """
        try:
            target_file = file_path or self.file_path
            if not target_file:
                return 0.0

            # 检查文件扩展名
            if not any(target_file.lower().endswith(fmt) for fmt in self.supported_formats):
                return 0.0

            logger.info(f"🔍 计算民生银行文件置信度: {os.path.basename(target_file)}")

            # 🔧 关键修复：只使用样本数据进行评估，不调用实际解析
            sample_data = self.extract_sample(target_file, limit=5)
            accounts = sample_data.get('accounts', [])
            transactions = sample_data.get('transactions', [])

            if not accounts:
                logger.warning("未找到账户信息，置信度为0")
                return 0.0

            # 🔧 修复：使用更精确的民生银行特征检测
            confidence = 0.0

            # 读取Excel文件检查内容
            excel_file = pd.ExcelFile(target_file)

            # 检查是否包含民生银行核心特征
            cmbc_features_found = 0
            total_features_checked = 0

            # 检查文件内容
            for sheet_name in excel_file.sheet_names[:3]:  # 只检查前3个工作表
                try:
                    df = pd.read_excel(target_file, sheet_name=sheet_name, nrows=20)
                    content_str = ' '.join([str(cell) for row in df.values for cell in row if pd.notna(cell)])

                    # 🔧 核心特征检测：民生银行必须包含的字段组合
                    core_features = [
                        '客户名称',  # 民生银行特有字段
                        '交易金额',  # 基础字段
                        '账户余额',  # 基础字段
                        '交易日期',  # 基础字段
                        '借贷标志'   # 民生银行特有字段
                    ]

                    for feature in core_features:
                        total_features_checked += 1
                        if feature in content_str:
                            cmbc_features_found += 1
                            logger.info(f"✅ 检测到民生银行特征: {feature}")

                    # 如果找到足够的特征，跳出循环
                    if cmbc_features_found >= 3:
                        break

                except Exception:
                    continue

            # 🔧 基于特征匹配度计算置信度
            if cmbc_features_found >= 4:  # 5个特征中有4个或以上
                confidence = 100.0  # 完美匹配
            elif cmbc_features_found >= 3:  # 5个特征中有3个
                confidence = 85.0   # 高匹配度
            elif cmbc_features_found >= 2:  # 5个特征中有2个
                confidence = 60.0   # 中等匹配度
            elif cmbc_features_found >= 1:  # 5个特征中有1个
                confidence = 30.0   # 低匹配度
            else:
                confidence = 0.0    # 不匹配

            final_confidence = confidence
            logger.info(f"民生银行解析器置信度: {final_confidence}%")
            return final_confidence

        except Exception as e:
            logger.error(f"民生银行置信度计算失败: {e}")
            return 0.0

    def validate_file(self, file_path: str) -> bool:
        """验证文件是否可以被此解析器处理"""
        try:
            if not os.path.exists(file_path):
                return False

            # 检查文件扩展名
            if not any(file_path.lower().endswith(fmt) for fmt in self.supported_formats):
                return False

            # 尝试读取Excel文件
            excel_file = pd.ExcelFile(file_path)
            if len(excel_file.sheet_names) == 0:
                return False

            return True

        except Exception as e:
            logger.warning(f"文件验证失败: {e}")
            return False

    def extract_sample(self, file_path: str = None, limit: int = 10) -> Dict[str, Any]:
        """
        提取样本数据用于4维度预解析评估 - 民生银行Format1版本
        专门针对民生银行Excel格式进行优化的4维度评估

        Args:
            file_path: 文件路径
            limit: 样本数量限制

        Returns:
            Dict: 包含样本账户和交易的字典，用于4维度评估
        """
        try:
            target_file = file_path or self.file_path
            if not target_file:
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}

            logger.info(f"🔍 民生银行4维度预解析开始，限制条数: {limit}")

            # 🔧 使用轻量级解析获取样本数据
            excel_file = pd.ExcelFile(target_file)

            # 1. 快速识别目标工作表
            target_sheet = self._find_valid_sheet_lightweight(excel_file)
            if not target_sheet:
                logger.warning("未找到有效的数据工作表")
                return {
                    'accounts': [],
                    'transactions': [],
                    'success': False,
                    'metadata': {
                        'sample_size': 0,
                        'plugin_name': self.name,
                        'confidence': 0,
                        'dimension_scores': {
                            'customer_name': 0,
                            'transaction_method': 0,
                            'remark1': 0,
                            'income_expense': 0
                        }
                    }
                }

            # 2. 读取样本数据进行4维度分析
            df_sample = pd.read_excel(target_file, sheet_name=target_sheet, header=None, nrows=100)
            data_start_row = self._find_data_start_row_lightweight(df_sample)

            # 首次按识别的起始行读取
            df = pd.read_excel(target_file, sheet_name=target_sheet, header=data_start_row, nrows=limit + 50)

            # 🔧 兜底：如果关键字段缺失，尝试向下偏移表头行再读，避免将标题行当作表头
            def has_key_columns(_df: pd.DataFrame) -> bool:
                # 统一列名为字符串进行包含判断
                cols = {str(c).strip() for c in _df.columns}
                name_keys = ['户名', '客户名称', '客户姓名', '姓名', '持卡人', '账户名', '交易对方名称']
                acct_keys = ['账号', '卡号', '客户账号', '账户', '银行卡号', '查询卡号']
                amount_keys = ['交易金额', '发生额', '金额', '借方发生额', '贷方发生额']
                balance_keys = ['账户余额', '交易余额', '余额', '交易后余额', '当前余额']

                has_name = any(k for k in name_keys if any(k in c for c in cols))
                has_acct = any(k for k in acct_keys if any(k in c for c in cols))
                has_amt = any(k for k in amount_keys if any(k in c for c in cols))
                has_bal = any(k for k in balance_keys if any(k in c for c in cols))
                return (has_amt or has_bal)

            if not has_key_columns(df):
                # 优先小范围下移尝试
                for shift in range(1, 21):  # 向下尝试20行
                    try:
                        df_try = pd.read_excel(target_file, sheet_name=target_sheet, header=data_start_row + shift, nrows=limit + 80)
                        if has_key_columns(df_try):
                            logger.info(f"✅ 表头修正成功：将数据起始行由 {data_start_row} 调整为 {data_start_row + shift}")
                            df = df_try
                            break
                    except Exception as _e:
                        logger.debug(f"表头修正第{shift}次失败: {_e}")

                # 若仍失败，回退到无表头模式重新扫描表头
                if not has_key_columns(df):
                    try:
                        df_headless = pd.read_excel(target_file, sheet_name=target_sheet, header=None, nrows=200)
                        new_start = self._find_data_start_row_lightweight(df_headless)
                        df_try2 = pd.read_excel(target_file, sheet_name=target_sheet, header=new_start, nrows=limit + 100)
                        if has_key_columns(df_try2):
                            logger.info(f"✅ 二次扫描定位表头成功：起始行 {new_start}")
                            df = df_try2
                    except Exception as _e2:
                        logger.debug(f"二次无表头扫描失败: {_e2}")

            # 3. 提取样本数据
            sample_accounts, sample_transactions = self._extract_lightweight_sample(df, limit)

            # 🔧 4维度字段验证和评分 - 针对民生银行格式优化
            dimension_scores = self._evaluate_cmbc_4_dimensions(df, sample_accounts, sample_transactions)

            # 计算总体置信度 - 民生银行专用评分逻辑
            total_confidence = self._calculate_cmbc_confidence(dimension_scores, df)

            logger.info(f"民生银行4维度预解析完成: {len(sample_accounts)}个账户, {len(sample_transactions)}条交易, 置信度: {total_confidence}%")
            logger.info(f"4维度评分: {dimension_scores}")

            return {
                'accounts': sample_accounts,
                'transactions': sample_transactions,
                'success': True,
                'metadata': {
                    'sample_size': len(sample_transactions),
                    'plugin_name': self.name,
                    'confidence': total_confidence,
                    'parser_version': 'v4.0-4dimension_support',
                    'dimension_scores': dimension_scores
                }
            }

        except Exception as e:
            logger.error(f"民生银行extract_sample方法失败: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'accounts': [],
                'transactions': [],
                'success': False,
                'error': str(e),
                'metadata': {
                    'sample_size': 0,
                    'plugin_name': self.name,
                    'error_details': str(e)
                }
            }

    def _evaluate_cmbc_4_dimensions(self, df: pd.DataFrame, accounts: List[Dict], transactions: List[Dict]) -> Dict[str, float]:
        """
        民生银行专用4维度评估方法
        针对民生银行Excel格式进行精确的字段匹配和评分
        """
        try:
            scores = {
                'customer_name': 0.0,
                'transaction_method': 0.0,
                'remark1': 0.0,
                'income_expense': 0.0
            }

            if df.empty:
                return scores

            # 获取列名（转换为小写便于匹配）
            columns = [str(col).lower().strip() for col in df.columns if pd.notna(col)]
            logger.info(f"民生银行4维度评估 - 检测到列名: {columns}")

            # 1. 客户姓名维度 - 民生银行通常有"户名"、"客户姓名"等字段
            customer_name_keywords = ['户名', '客户姓名', '姓名', '持卡人', '账户名']
            customer_score = 0
            for keyword in customer_name_keywords:
                if any(keyword in col for col in columns):
                    customer_score = 100
                    logger.info(f"✅ 客户姓名字段匹配成功: {keyword}")
                    break
            scores['customer_name'] = customer_score

            # 2. 交易方式维度 - 民生银行通常有"交易方式"、"渠道"等字段
            method_keywords = ['交易方式', '渠道', '交易渠道', '方式']
            method_score = 0
            for keyword in method_keywords:
                if any(keyword in col for col in columns):
                    method_score = 100
                    logger.info(f"✅ 交易方式字段匹配成功: {keyword}")
                    break
            scores['transaction_method'] = method_score

            # 3. 备注1维度 - 民生银行通常有"摘要"、"备注"、"用途"等字段
            remark_keywords = ['摘要', '备注', '用途', '交易摘要', '说明']
            remark_score = 0
            for keyword in remark_keywords:
                if any(keyword in col for col in columns):
                    remark_score = 100
                    logger.info(f"✅ 备注字段匹配成功: {keyword}")
                    break
            scores['remark1'] = remark_score

            # 4. 收支维度 - 民生银行通常有"收入"/"支出"或"借方"/"贷方"字段
            income_expense_keywords = ['收入', '支出', '借方', '贷方', '收支标志', '借贷标志']
            income_expense_score = 0
            for keyword in income_expense_keywords:
                if any(keyword in col for col in columns):
                    income_expense_score = 100
                    logger.info(f"✅ 收支字段匹配成功: {keyword}")
                    break
            scores['income_expense'] = income_expense_score

            logger.info(f"民生银行4维度评分结果: {scores}")
            return scores

        except Exception as e:
            logger.error(f"民生银行4维度评估失败: {str(e)}")
            return {
                'customer_name': 0,
                'transaction_method': 0,
                'remark1': 0,
                'income_expense': 0
            }

    def _calculate_cmbc_confidence(self, dimension_scores: Dict[str, float], df: pd.DataFrame) -> float:
        """
        计算民生银行解析器的置信度
        基于4维度评分和数据质量进行综合评估
        """
        try:
            # 基础4维度平均分
            base_score = sum(dimension_scores.values()) / len(dimension_scores) if dimension_scores else 0

            # 民生银行特征加分项
            bonus_score = 0

            if not df.empty:
                columns = [str(col).lower().strip() for col in df.columns if pd.notna(col)]

                # 如果包含民生银行特有字段组合，给予加分
                cmbc_specific_patterns = [
                    ['户名', '交易方式', '摘要'],  # 民生银行典型字段组合
                    ['客户姓名', '渠道', '用途'],
                    ['持卡人', '交易渠道', '交易摘要']
                ]

                for pattern in cmbc_specific_patterns:
                    if all(any(keyword in col for col in columns) for keyword in pattern):
                        bonus_score = 10  # 额外加10分
                        logger.info(f"✅ 检测到民生银行特征字段组合，加分: {pattern}")
                        break

            # 最终置信度 = 基础分 + 加分，但不超过100
            final_confidence = min(100, base_score + bonus_score)

            logger.info(f"民生银行置信度计算: 基础分={base_score}, 加分={bonus_score}, 最终={final_confidence}")
            return final_confidence

        except Exception as e:
            logger.error(f"民生银行置信度计算失败: {str(e)}")
            return 0.0

    def _evaluate_4_dimensions(self, accounts: List[Dict], transactions: List[Dict]) -> Dict[str, float]:
        """
        评估4维度字段质量
        参考交通银行的成功模式
        """
        scores = {
            'cardholder_name': 0.0,    # 姓名维度
            'transaction_date': 0.0,   # 时间维度
            'account_number': 0.0,     # 账号维度
            'transaction_amount': 0.0  # 金额维度
        }

        try:
            # 1. 姓名维度评估
            if accounts:
                valid_names = 0
                for account in accounts:
                    name = account.get('cardholder_name', '')
                    if name and name != '客户账号' and len(name) >= 2:
                        valid_names += 1
                scores['cardholder_name'] = (valid_names / len(accounts)) * 100

            # 2. 账号维度评估
            if accounts:
                valid_accounts = 0
                for account in accounts:
                    account_num = account.get('account_number', '')
                    if account_num and len(account_num) >= 10:
                        valid_accounts += 1
                scores['account_number'] = (valid_accounts / len(accounts)) * 100

            # 3. 时间维度评估
            if transactions:
                valid_dates = 0
                for transaction in transactions:
                    date = transaction.get('transaction_date', '')
                    if date and len(str(date)) >= 8:
                        valid_dates += 1
                scores['transaction_date'] = (valid_dates / len(transactions)) * 100

            # 4. 金额维度评估
            if transactions:
                valid_amounts = 0
                for transaction in transactions:
                    amount = transaction.get('transaction_amount', 0)
                    if amount and amount != 0:
                        valid_amounts += 1
                scores['transaction_amount'] = (valid_amounts / len(transactions)) * 100

        except Exception as e:
            logger.error(f"4维度评估失败: {e}")

        return scores

    def validate_file(self, file_path: str) -> bool:
        """
        验证文件是否为有效的民生银行流水文件
        """
        try:
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return False

            # 检查文件扩展名
            if not file_path.lower().endswith(('.xlsx', '.xls')):
                logger.error(f"不支持的文件格式: {file_path}")
                return False

            # 尝试读取Excel文件
            try:
                excel_file = pd.ExcelFile(file_path)
                if not excel_file.sheet_names:
                    logger.error("Excel文件没有工作表")
                    return False
            except Exception as e:
                logger.error(f"无法读取Excel文件: {e}")
                return False

            logger.info(f"文件验证通过: {file_path}")
            return True

        except Exception as e:
            logger.error(f"文件验证失败: {e}")
            return False

    def parse(self, file_path: str = None) -> Dict[str, Any]:
        """
        解析民生银行流水文件 - 基于原有成功逻辑的完整实现
        """
        try:
            # 使用传入的文件路径或实例的文件路径
            if file_path:
                self.file_path = file_path
            elif not self.file_path:
                raise ValueError("未设置文件路径")

            logger.info(f"🔍 开始解析民生银行Format1文件: {os.path.basename(self.file_path)}")

            # 验证文件
            if not self.validate_file(self.file_path):
                raise ValueError("文件验证失败")

            # 重置状态
            self.accounts = []
            self.transactions = []

            # 使用原有的民生银行解析逻辑
            result = self._parse_cmbc_format1()

            logger.info(f"✅ 民生银行解析完成: 共 {len(self.accounts)} 个账户, {len(self.transactions)} 条交易")
            return result

        except Exception as e:
            logger.error(f"民生银行解析失败: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': str(e),
                'accounts': [],
                'transactions': [],
                'metadata': {
                    'plugin_name': self.name,
                    'error_details': str(e)
                }
            }

    def _parse_cmbc_format1(self) -> Dict[str, Any]:
        """
        民生银行Format1解析核心逻辑
        基于原有成功的解析方法
        """
        try:
            # 读取Excel文件
            excel_file = pd.ExcelFile(self.file_path)
            sheet_names = excel_file.sheet_names

            logger.info(f"发现工作表: {sheet_names}")

            # 处理每个工作表
            for sheet_name in sheet_names:
                logger.info(f"处理工作表: {sheet_name}")
                self._process_sheet(sheet_name)

            # 构建解析结果
            return self._build_result()

        except Exception as e:
            logger.error(f"民生银行核心解析失败: {str(e)}")
            raise

    def _process_sheet(self, sheet_name: str):
        """处理单个工作表"""
        try:
            # 读取工作表数据
            df = pd.read_excel(self.file_path, sheet_name=sheet_name, header=None)
            if df.empty:
                logger.warning(f"工作表 {sheet_name} 为空")
                return

            # 查找账户信息和数据起始位置
            account_info = self._find_account_info(df, sheet_name)
            if not account_info:
                logger.warning(f"工作表 {sheet_name} 未找到账户信息")
                return

            # 提取交易数据
            transactions = self._extract_transactions(df, account_info)

            # 添加到结果中
            if account_info not in self.accounts:
                self.accounts.append(account_info)

            self.transactions.extend(transactions)

            logger.info(f"工作表 {sheet_name} 处理完成: {len(transactions)} 条交易")

        except Exception as e:
            logger.error(f"处理工作表 {sheet_name} 失败: {str(e)}")

    def _find_account_info(self, df: pd.DataFrame, sheet_name: str) -> Dict[str, Any]:
        """查找账户信息 - 修复版本，正确提取客户姓名和唯一卡号"""
        try:
            # 提取客户姓名（B2单元格，第1行第1列）
            customer_name = "未知"
            if len(df) > 1 and len(df.columns) > 1:
                if pd.notna(df.iloc[1, 1]):
                    customer_name = str(df.iloc[1, 1])
                    logger.info(f"从B2单元格提取到客户姓名: {customer_name}")

            # 查找账号信息（在前20行中查找）
            account_number = "未知"
            for idx in range(min(20, len(df))):
                row = df.iloc[idx]
                row_str = ' '.join([str(cell) for cell in row if pd.notna(cell)])

                # 查找账号信息
                account_match = re.search(r'(\d{10,25})', row_str)
                if account_match:
                    account_number = account_match.group(1)
                    logger.info(f"提取到账号: {account_number}")
                    break

            # 🔧 关键修复：提取唯一卡号
            card_number = self._extract_unique_card_number(df, sheet_name, account_number)

            return {
                'cardholder_name': customer_name,
                'holder_name': customer_name,
                'person_name': customer_name,
                'account_number': account_number,
                'card_number': card_number,
                'bank_name': '中国民生银行',
                'account_name': f"{customer_name}的账户",
                'currency': 'CNY',
                'account_type': '个人账户',
                'sheet_name': sheet_name
            }

        except Exception as e:
            logger.error(f"查找账户信息失败: {str(e)}")
            return {
                'cardholder_name': '未知',
                'holder_name': '未知',
                'person_name': '未知',
                'account_number': '未知',
                'card_number': '未知',
                'bank_name': '中国民生银行',
                'account_name': '未知的账户',
                'currency': 'CNY',
                'account_type': '个人账户',
                'sheet_name': sheet_name
            }

    def _extract_unique_card_number(self, df: pd.DataFrame, sheet_name: str, account_number: str) -> str:
        """提取唯一卡号，确保每个工作表有独立的卡号"""
        print(f"🔧🔧🔧 调试：_extract_unique_card_number被调用，工作表：{sheet_name}, 账号：{account_number}")
        logger.info(f"🔧🔧🔧 调试：_extract_unique_card_number被调用，工作表：{sheet_name}, 账号：{account_number}")
        try:
            # 🔧 关键修复：从"客户账号"行提取卡号（位置[2,1]：第3行B列）
            if len(df) > 2 and len(df.columns) > 1:
                customer_account_value = df.iloc[2, 1]  # 客户账号位置（第3行B列）
                if pd.notna(customer_account_value):
                    customer_account_str = str(customer_account_value).strip()
                    logger.info(f"🔧 调试：从客户账号行([2,1])提取数据: {customer_account_str}")
                    
                    # 查找所有16-19位数字作为卡号候选
                    card_numbers = re.findall(r'\d{16,19}', customer_account_str)
                    if card_numbers:
                        # 如果有多个卡号，选择第一个作为主卡号
                        card_number = card_numbers[0]
                        logger.info(f"从客户账号提取到卡号: {card_number}")
                        return card_number

            # 尝试在前10行查找"账户账号"关键词及其后面的数字
            for idx in range(min(10, len(df))):
                for col_idx in range(len(df.columns) - 1):  # 确保有下一列
                    cell_value = df.iloc[idx, col_idx]
                    if pd.notna(cell_value) and '账户账号' in str(cell_value):
                        next_cell = df.iloc[idx, col_idx + 1]
                        if pd.notna(next_cell):
                            next_str = str(next_cell).strip()
                            card_match = re.search(r'\d{16,19}', next_str)
                            if card_match:
                                card_number = card_match.group()
                                logger.info(f"从'账户账号'标签后提取到卡号: {card_number}")
                                return card_number

            # 尝试从工作表名称中提取数字作为卡号
            sheet_digits = re.findall(r'\d{8,}', str(sheet_name))
            if sheet_digits:
                # 取最长的数字串作为卡号
                longest_digits = max(sheet_digits, key=len)
                logger.info(f"从工作表名称提取到卡号: {longest_digits}")
                return longest_digits

            # 最后兜底：使用工作表名称+账号的哈希值生成唯一卡号
            import hashlib
            unique_str = f"{sheet_name}_{account_number}"
            hash_value = hashlib.md5(unique_str.encode()).hexdigest()[:16]
            # 转换为16位数字
            card_number = ''.join([str(ord(c) % 10) for c in hash_value])
            logger.info(f"生成唯一卡号: {card_number} (基于工作表: {sheet_name})")
            return card_number

        except Exception as e:
            logger.error(f"提取卡号失败: {e}")
            # 返回基于工作表名称的唯一标识
            return f"CARD_{abs(hash(sheet_name)) % ****************:016d}"

    def _extract_transactions(self, df: pd.DataFrame, account_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取交易数据 - 修复版本，正确解析民生银行格式"""
        transactions = []

        try:
            # 查找表头行 - 根据分析，表头在第4行（索引3）
            header_row = None
            for idx in range(min(30, len(df))):
                row = df.iloc[idx]
                row_str = ' '.join([str(cell) for cell in row if pd.notna(cell)])

                # 民生银行表头包含：交易时间、交易金额等
                if '交易时间' in row_str and '交易金额' in row_str:
                    header_row = idx
                    logger.info(f"找到表头行: 第{idx}行, 内容: {row_str}")
                    break

            # 如果没找到，尝试使用固定位置（第4行，索引3）
            if header_row is None and len(df) > 4:
                row_str = ' '.join([str(cell) for cell in df.iloc[4] if pd.notna(cell)])
                if '交易' in row_str:  # 更宽松的条件
                    header_row = 4
                    logger.info(f"使用固定位置表头行: 第{header_row}行, 内容: {row_str}")

            if header_row is None:
                logger.warning("未找到表头行")
                return transactions

            # 获取表头信息，确定列位置
            headers = df.iloc[header_row].tolist()

            # 查找关键列的索引
            transaction_type_col = None  # 交易类型列
            time_col = None
            amount_col = None
            balance_col = None
            counterpart_name_col = None
            counterpart_account_col = None
            counterpart_bank_col = None  # 对方银行列
            debit_credit_col = None
            summary_col = None  # 交易摘要列

            for i, header in enumerate(headers):
                if pd.notna(header):
                    header_str = str(header)
                    if '交易类型' in header_str:
                        transaction_type_col = i
                    elif '交易时间' in header_str:
                        time_col = i
                    elif '交易金额' in header_str:
                        amount_col = i
                    elif '交易余额' in header_str:
                        balance_col = i
                    elif '交易对方名称' in header_str:
                        counterpart_name_col = i
                    elif '交易对方账号' in header_str and '开户行' not in header_str:
                        counterpart_account_col = i
                    elif '交易对方账号开户行' in header_str:
                        counterpart_bank_col = i
                    elif '借贷标志' in header_str:
                        debit_credit_col = i
                    elif '交易摘要' in header_str:
                        summary_col = i

            logger.info(f"列位置 - 类型:{transaction_type_col}, 时间:{time_col}, 金额:{amount_col}, 余额:{balance_col}, 借贷:{debit_credit_col}, 摘要:{summary_col}, 对方银行:{counterpart_bank_col}")

            # 从表头行下一行开始处理数据
            data_start = header_row + 1

            for idx in range(data_start, len(df)):
                row = df.iloc[idx]
                if row.isna().all():
                    continue

                try:
                    # 检查是否为有效的交易行
                    if time_col is not None and pd.isna(row.iloc[time_col]):
                        continue
                    if amount_col is not None and pd.isna(row.iloc[amount_col]):
                        continue

                    # 解析交易时间
                    time_str = str(row.iloc[time_col]) if time_col is not None else ''
                    transaction_datetime = None
                    if time_str and time_str != 'nan':
                        if '/' in time_str:
                            # 处理 "2014/01/28 22:14:00" 格式
                            transaction_datetime = pd.to_datetime(time_str, format='%Y/%m/%d %H:%M:%S')
                        else:
                            transaction_datetime = pd.to_datetime(time_str)

                    if transaction_datetime is None:
                        continue

                    # 解析交易金额
                    amount_str = str(row.iloc[amount_col]).replace(',', '') if amount_col is not None else '0'
                    transaction_amount = float(amount_str)

                    # 解析余额
                    balance = None
                    if balance_col is not None and pd.notna(row.iloc[balance_col]):
                        balance_str = str(row.iloc[balance_col]).replace(',', '')
                        balance = float(balance_str)

                    # 解析交易类型
                    transaction_type = None
                    if transaction_type_col is not None and pd.notna(row.iloc[transaction_type_col]):
                        transaction_type = str(row.iloc[transaction_type_col])

                    # 解析借贷标志
                    debit_credit = None
                    if debit_credit_col is not None and pd.notna(row.iloc[debit_credit_col]):
                        debit_credit = str(row.iloc[debit_credit_col])

                    # 解析交易摘要
                    summary = None
                    if summary_col is not None and pd.notna(row.iloc[summary_col]):
                        summary = str(row.iloc[summary_col])

                    # 解析对方信息
                    counterpart_name = None
                    if counterpart_name_col is not None and pd.notna(row.iloc[counterpart_name_col]):
                        counterpart_name = str(row.iloc[counterpart_name_col])

                    counterpart_account = None
                    if counterpart_account_col is not None and pd.notna(row.iloc[counterpart_account_col]):
                        counterpart_account = str(row.iloc[counterpart_account_col])

                    # 解析对方银行
                    counterpart_bank = None
                    if counterpart_bank_col is not None and pd.notna(row.iloc[counterpart_bank_col]):
                        counterpart_bank = str(row.iloc[counterpart_bank_col])

                    # 确定收支方向
                    direction = '收' if debit_credit == '进' else '支'

                    transaction = {
                        'cardholder_name': account_info['cardholder_name'],
                        'holder_name': account_info['holder_name'],
                        'person_name': account_info['person_name'],
                        'account_number': account_info['account_number'],
                        'card_number': account_info['card_number'],
                        'transaction_date': transaction_datetime.strftime('%Y-%m-%d'),
                        'transaction_time': transaction_datetime.strftime('%H:%M:%S'),
                        'transaction_datetime': transaction_datetime.strftime('%Y-%m-%d %H:%M:%S'),
                        'transaction_amount': transaction_amount,
                        'balance': balance,
                        'balance_amount': balance,
                        'dr_cr_flag': direction,
                        'direction': direction,
                        'debit_credit_flag': debit_credit,
                        'currency': 'CNY',
                        'transaction_method': debit_credit or '',  # 修复：交易方式=借贷标志
                        'bank_name': '中国民生银行',
                        'summary': summary or '',
                        'remark1': summary or '',  # 修复：备注1=交易摘要
                        'remark2': debit_credit or '',
                        'remark3': '',
                        'counterparty_name': counterpart_name,
                        'counterparty_account': counterpart_account,
                        'counterparty_bank': counterpart_bank,  # 新增：对方银行
                        'sequence_number': len(transactions) + 1
                    }
                    transactions.append(transaction)

                except Exception as e:
                    logger.debug(f"跳过第{idx+1}行: {str(e)}")
                    continue

            logger.info(f"提取到 {len(transactions)} 条交易记录")
            return transactions

        except Exception as e:
            logger.error(f"提取交易数据失败: {str(e)}")
            return transactions

    def _extract_date(self, row: pd.Series) -> str:
        """提取交易日期"""
        try:
            for cell in row:
                if pd.notna(cell):
                    cell_str = str(cell).strip()
                    # 民生银行日期格式：2014/01/28 22:14:00
                    if re.match(r'\d{4}[-/]\d{1,2}[-/]\d{1,2}', cell_str):
                        # 提取日期部分（去掉时间）
                        date_part = cell_str.split(' ')[0]
                        return date_part.replace('/', '-')
                    elif re.match(r'\d{8}', cell_str):
                        return f"{cell_str[:4]}-{cell_str[4:6]}-{cell_str[6:8]}"
            return None
        except:
            return None

    def _extract_amount(self, row: pd.Series) -> float:
        """提取交易金额"""
        try:
            for cell in row:
                if pd.notna(cell):
                    cell_str = str(cell).strip()
                    # 民生银行金额格式：1.00, 197000.00等
                    # 移除逗号和其他非数字字符，但保留小数点和负号
                    cleaned_str = re.sub(r'[^\d.-]', '', cell_str)
                    if re.match(r'^-?\d+\.?\d*$', cleaned_str) and cleaned_str not in ['', '.', '-']:
                        amount = float(cleaned_str)
                        # 过滤掉明显不是金额的数字（如年份等）
                        if abs(amount) > 0.01 and abs(amount) < 999999999:
                            return amount
            return None
        except:
            return None

    def _extract_time(self, row: pd.Series) -> str:
        """提取交易时间"""
        try:
            for cell in row:
                if pd.notna(cell):
                    cell_str = str(cell).strip()
                    # 民生银行时间格式：2014/01/28 22:14:00
                    if ' ' in cell_str and ':' in cell_str:
                        time_part = cell_str.split(' ')[1] if len(cell_str.split(' ')) > 1 else '00:00:00'
                        return time_part
            return '00:00:00'
        except:
            return '00:00:00'

    def _extract_balance(self, row: pd.Series) -> float:
        """提取交易余额"""
        try:
            # 民生银行余额在第5列（索引4）
            row_values = [str(cell) if pd.notna(cell) else '' for cell in row]
            if len(row_values) > 4:
                balance_str = row_values[4].strip()
                cleaned_str = re.sub(r'[^\d.-]', '', balance_str)
                if re.match(r'^-?\d+\.?\d*$', cleaned_str) and cleaned_str not in ['', '.', '-']:
                    return float(cleaned_str)
            return 0.0
        except:
            return 0.0

    def _build_result(self) -> Dict[str, Any]:
        """构建解析结果 - 修复版本，正确计算收入支出总额和时间范围"""
        try:
            # 按账户组织交易数据
            transactions_by_account = {}
            
            # 🔍 调试：统计不同卡号的交易数量
            card_number_stats = {}

            # 计算统计数据
            total_income = 0.0
            total_expense = 0.0
            min_date = None
            max_date = None

            for transaction in self.transactions:
                # 🔧 关键修复：使用账号+卡号组合键，确保不同卡号分别统计
                account_key = f"{transaction.get('account_number','')}|{transaction.get('card_number','')}"
                if account_key not in transactions_by_account:
                    transactions_by_account[account_key] = []
                transactions_by_account[account_key].append(transaction)
                
                # 🔍 调试：统计每个卡号的交易数量
                card_num = transaction.get('card_number', '')
                if card_num not in card_number_stats:
                    card_number_stats[card_num] = 0
                card_number_stats[card_num] += 1

                # 计算收入支出 - 修复：使用正确的字段名
                amount = transaction.get('transaction_amount', 0)
                direction = transaction.get('direction', '')
                debit_credit = transaction.get('debit_credit_flag', '')

                # 使用direction字段判断收支
                if direction == '收':
                    total_income += amount
                elif direction == '支':
                    total_expense += amount
                # 备用：使用debit_credit_flag字段判断
                elif debit_credit == '进':
                    total_income += amount
                elif debit_credit == '出':
                    total_expense += amount

                # 更新时间范围 - 修复：直接使用字符串日期比较
                transaction_date = transaction.get('transaction_date', '')
                if transaction_date:
                    try:
                        if min_date is None or transaction_date < min_date:
                            min_date = transaction_date
                        if max_date is None or transaction_date > max_date:
                            max_date = transaction_date
                    except:
                        pass

            # 🔍 调试：打印统计信息
            print(f"🔍 [CMBC Debug] 卡号交易统计:")
            for card_num, count in card_number_stats.items():
                print(f"   卡号 {card_num}: {count} 笔交易")
            
            print(f"🔍 [CMBC Debug] transactions_by_account 统计:")
            for key, trans_list in transactions_by_account.items():
                print(f"   键 {key}: {len(trans_list)} 笔交易")

            # 更新账户信息，添加统计数据
            for account in self.accounts:
                # 🔧 关键修复：使用相同的组合键来查找对应的交易记录
                account_key = f"{account.get('account_number','')}|{account.get('card_number','')}"
                account_transactions = transactions_by_account.get(account_key, [])

                # 🔍 调试：打印每个账户的统计计算过程
                print(f"🔍 [CMBC Debug] 账户 {account.get('holder_name', '')} - {account.get('account_number', '')} - {account.get('card_number', '')}")
                print(f"   查找键: {account_key}")
                print(f"   找到交易: {len(account_transactions)} 笔")

                # 计算该账户的统计数据 - 修复：参考工商银行解析器的正确实现
                account['transactions_count'] = len(account_transactions)
                account['transactions'] = account_transactions

                # 计算收支总额 - 参考工商银行解析器实现
                inflow = sum(t['transaction_amount'] for t in account_transactions if t.get('direction') == '收')
                outflow = sum(t['transaction_amount'] for t in account_transactions if t.get('direction') == '支')

                account['total_inflow'] = inflow
                account['total_outflow'] = outflow
                account['total_income'] = inflow  # 兼容字段
                account['total_expense'] = outflow  # 兼容字段
                account['net_amount'] = inflow - outflow

                # 计算时间范围 - 参考工商银行解析器实现
                dates = [t['transaction_date'] for t in account_transactions if t['transaction_date']]
                if dates:
                    account['date_range'] = f"{min(dates)} 至 {max(dates)}"
                    account['start_date'] = min(dates)
                    account['end_date'] = max(dates)
                else:
                    account['date_range'] = "未知"
                    account['start_date'] = None
                    account['end_date'] = None

            return {
                'success': True,
                'accounts': self.accounts,
                'transactions': self.transactions,
                'transactions_by_account': transactions_by_account,
                'summary': {
                    'total_accounts': len(self.accounts),
                    'total_transactions': len(self.transactions),
                    'total_income': total_income,
                    'total_expense': total_expense,
                    'net_amount': total_income - total_expense,
                    'date_range': f"{min_date} 至 {max_date}" if min_date and max_date else "未知",
                    'plugin_name': self.name,
                    'parser_version': self.version
                },
                'metadata': {
                    'plugin_name': self.name,
                    'parser_version': self.version,
                    'parse_time': time.time() - self.start_time,
                    'total_income': total_income,
                    'total_expense': total_expense,
                    'net_amount': total_income - total_expense
                }
            }

        except Exception as e:
            logger.error(f"构建结果失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'accounts': [],
                'transactions': [],
                'metadata': {
                    'plugin_name': self.name,
                    'error_details': str(e)
                }
            }

    def _find_valid_sheet_lightweight(self, excel_file) -> str:
        """轻量级工作表识别，只检查前几行"""
        target_sheet = None
        max_match_score = 0

        # 民生银行关键字段
        key_fields = ['客户名称', '账号', '交易金额', '账户余额', '借贷标志']

        for sheet_name in excel_file.sheet_names:
            try:
                # 只读取前10行进行快速检查
                df_test = pd.read_excel(excel_file, sheet_name=sheet_name, header=None, nrows=10)
                match_score = 0

                # 计算特征匹配度
                for row_idx in range(min(10, len(df_test))):
                    row_str = ' '.join([str(x) for x in df_test.iloc[row_idx].values if pd.notna(x)])
                    for field in key_fields:
                        if field in row_str:
                            match_score += 1

                if match_score > max_match_score:
                    max_match_score = match_score
                    target_sheet = sheet_name

            except Exception as e:
                logger.debug(f"检查工作表 {sheet_name} 失败: {e}")

        return target_sheet

    def _find_data_start_row_lightweight(self, df_sample) -> int:
        """
        轻量级数据起始行识别 - 民生银行专用
        识别包含表头字段的行作为数据起始行
        """
        try:
            # 民生银行可能的表头字段组合
            header_patterns = [
                ['户名', '账号'],  # 民生银行常用字段
                ['客户名称', '账号'],
                ['持卡人', '账号'],
                ['姓名', '账号'],
                ['户名', '卡号'],
                ['交易日期', '户名'],  # 有些格式以交易日期开头
                ['日期', '户名'],
                ['交易时间', '户名']
            ]

            for row_idx in range(min(60, len(df_sample))):  # 扩大搜索范围
                row_vals = [str(x) for x in df_sample.iloc[row_idx].values if pd.notna(x)]
                row_str = ' '.join(row_vals)
                logger.debug(f"检查第{row_idx}行: {row_str[:120]}...")

                # 更严格的表头判定：至少满足“姓名/户名 + 账号/卡号 + (交易金额或余额)”
                name_fields = ['户名', '客户名称', '持卡人', '姓名']
                acct_fields = ['账号', '卡号']
                amount_fields = ['交易金额', '金额']
                balance_fields = ['账户余额', '余额', '交易后余额']
                remark_fields = ['交易摘要', '摘要']
                credit_debit_fields = ['借贷标志', '收支']
                date_fields = ['交易日期', '日期']

                def contains_any(words):
                    return any(w in row_str for w in words)

                name_ok = contains_any(name_fields)
                acct_ok = contains_any(acct_fields)
                amount_ok = contains_any(amount_fields)
                balance_ok = contains_any(balance_fields)
                date_ok = contains_any(date_fields)
                extra_ok = contains_any(remark_fields) or contains_any(credit_debit_fields)

                if (name_ok and acct_ok and (amount_ok or balance_ok)) or (date_ok and name_ok and (amount_ok or balance_ok)):
                    logger.info(f"✅ 找到民生银行数据起始行: 第{row_idx}行, 关键字段命中 -> 姓名:{name_ok}, 账号:{acct_ok}, 金额:{amount_ok}, 余额:{balance_ok}, 日期:{date_ok}")
                    return row_idx

                # 兼容旧的弱匹配：当关键字段数量>=4也视为表头
                key_fields = ['户名', '客户名称', '账号', '卡号', '交易金额', '账户余额', '交易摘要', '借贷标志', '交易日期', '日期']
                field_count = sum(1 for field in key_fields if field in row_str)
                if field_count >= 4:
                    logger.info(f"✅ 找到民生银行数据起始行: 第{row_idx}行, 命中关键字段数={field_count}")
                    return row_idx

            logger.warning("未找到明确的数据起始行，使用默认值0")
            return 0

        except Exception as e:
            logger.error(f"查找数据起始行失败: {str(e)}")
            return 0

    def _extract_lightweight_sample(self, df, limit: int):
        """从DataFrame中快速提取样本数据"""
        sample_accounts = []
        sample_transactions = []

        try:
            # 快速提取账户信息（从前几行推断）
            unique_accounts = set()
            for _, row in df.head(limit * 2).iterrows():  # 多读一些确保覆盖不同账户
                # 兼容'客户名称'或'户名'字段
                name_val = row.get('客户名称') if '客户名称' in df.columns else row.get('户名')
                acct_val = row.get('账号') if '账号' in df.columns else row.get('卡号')
                if pd.notna(name_val) and pd.notna(acct_val):
                    holder = str(name_val).strip()
                    # 去除账号中的非数字字符，保证账号识别维度能通过
                    import re as _re
                    raw_account = str(acct_val)
                    cleaned_account = _re.sub(r'\D', '', raw_account)
                    if not cleaned_account:
                        continue
                    account_key = f"{holder}_{cleaned_account}"
                    if account_key not in unique_accounts:
                        unique_accounts.add(account_key)
                        sample_accounts.append({
                            'holder_name': holder,
                            'cardholder_name': holder,
                            'person_name': holder,
                            'account_number': cleaned_account,
                            'card_number': cleaned_account,
                            'bank_name': '中国民生银行'
                        })

            # 快速提取交易样本
            for _, row in df.head(limit).iterrows():
                amt_val = row.get('交易金额')
                if pd.notna(amt_val):
                    # 清洗金额、余额为纯数字字符串再转浮点，避免包含逗号导致评估失败
                    import re as _re
                    amount_clean = float(_re.sub(r'[^\d.-]', '', str(amt_val))) if str(amt_val).strip() not in ('', 'nan') else 0.0
                    bal_val = row.get('账户余额')
                    balance_clean = float(_re.sub(r'[^\d.-]', '', str(bal_val))) if pd.notna(bal_val) and str(bal_val).strip() not in ('', 'nan') else 0.0
                    # 兼容列名
                    name_val = row.get('客户名称') if '客户名称' in df.columns else row.get('户名')
                    acct_val = row.get('账号') if '账号' in df.columns else row.get('卡号')
                    acct_clean = _re.sub(r'\D', '', str(acct_val)) if acct_val is not None else ''
                    sample_transactions.append({
                        'cardholder_name': str(name_val or ''),
                        'account_number': acct_clean,
                        'transaction_date': str(row.get('交易日期', '')),
                        'transaction_time': str(row.get('交易时间', '')),
                        'transaction_amount': amount_clean,
                        'balance': balance_clean,
                        'balance_amount': balance_clean,
                        'transaction_type': str(row.get('借贷标志', '')),
                        'description': str(row.get('交易摘要', ''))
                    })

        except Exception as e:
            logger.error(f"轻量级样本提取失败: {e}")

        return sample_accounts, sample_transactions

# 插件实例
plugin = Plugin()
