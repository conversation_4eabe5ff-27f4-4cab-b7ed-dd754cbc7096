"""
清洗规则定义模块
提供数据清洗的规则接口和具体实现
"""
import re
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Tuple, Optional, Union
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class CleanerRule(ABC):
    """
    清洗规则基类
    所有具体清洗规则需继承此类并实现clean方法
    """
    
    def __init__(self, name: str, description: str = "", priority: int = 0):
        """
        初始化清洗规则
        
        Args:
            name: 规则名称
            description: 规则描述
            priority: 规则优先级，数字越大优先级越高
        """
        self.name = name
        self.description = description
        self.priority = priority
        
    @abstractmethod
    def clean(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        实现具体的清洗逻辑
        
        Args:
            raw_data: 原始数据，包含待清洗的字段
            
        Returns:
            Dict[str, Any]: 清洗后的数据
        """
        pass
    
    def should_apply(self, raw_data: Dict[str, Any]) -> bool:
        """
        判断是否应该应用此规则
        
        Args:
            raw_data: 原始数据
            
        Returns:
            bool: 如果应该应用此规则则返回True
        """
        # 默认实现始终返回True，子类可以重写此方法添加条件判断
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将规则转换为字典表示，便于序列化
        
        Returns:
            Dict[str, Any]: 规则的字典表示
        """
        return {
            "name": self.name,
            "description": self.description,
            "priority": self.priority,
            "type": self.__class__.__name__
        }


class DateTimeRule(CleanerRule):
    """
    日期时间清洗规则
    将不同格式的日期时间标准化为ISO格式
    """
    
    def __init__(
        self, 
        date_field: str = "raw_transaction_date",
        time_field: Optional[str] = "raw_transaction_time",
        target_field: str = "transaction_datetime",
        default_time: str = "00:00:00",
        name: str = "标准日期时间清洗",
        description: str = "将原始日期和时间字段标准化为YYYY-MM-DD HH:MM:SS格式",
        priority: int = 10
    ):
        """
        初始化日期时间清洗规则
        
        Args:
            date_field: 原始日期字段名
            time_field: 原始时间字段名，可为None
            target_field: 目标日期时间字段名
            default_time: 当时间为空时使用的默认时间
            name: 规则名称
            description: 规则描述
            priority: 规则优先级
        """
        super().__init__(name, description, priority)
        self.date_field = date_field
        self.time_field = time_field
        self.target_field = target_field
        self.default_time = default_time
        
    def clean(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        清洗日期时间数据
        
        Args:
            raw_data: 原始数据
            
        Returns:
            Dict[str, Any]: 添加了标准化日期时间的数据
        """
        result = raw_data.copy()
        
        # 获取原始日期
        raw_date = raw_data.get(self.date_field)
        if not raw_date:
            logger.warning(f"缺少日期字段 {self.date_field}")
            return result
        
        # 获取原始时间
        raw_time = None
        if self.time_field:
            raw_time = raw_data.get(self.time_field)
        
        # 如果没有时间，使用默认时间
        if not raw_time:
            raw_time = self.default_time
            
        # 标准化日期
        std_date = self._standardize_date(raw_date)
        if not std_date:
            logger.warning(f"无法标准化日期: {raw_date}")
            return result
            
        # 标准化时间
        std_time = self._standardize_time(raw_time)
        if not std_time:
            logger.warning(f"无法标准化时间: {raw_time}，使用默认时间")
            std_time = self.default_time
        
        # 合并日期和时间
        result[self.target_field] = f"{std_date} {std_time}"
        
        return result
    
    def _standardize_date(self, date_str: str) -> Optional[str]:
        """
        将各种格式的日期标准化为YYYY-MM-DD格式
        
        Args:
            date_str: 原始日期字符串
            
        Returns:
            Optional[str]: 标准化的日期字符串，如果无法解析则返回None
        """
        if not date_str or not isinstance(date_str, str):
            return None
            
        date_str = date_str.strip()
        
        # 尝试匹配常见的日期格式
        patterns = [
            # YYYY/MM/DD 或 YYYY-MM-DD
            (r"(\d{4})[/-年](\d{1,2})[/-月](\d{1,2})日?", "%Y-%m-%d"),
            # DD/MM/YYYY 或 DD-MM-YYYY
            (r"(\d{1,2})[/-](\d{1,2})[/-](\d{4})", "%d-%m-%Y"),
            # MM/DD/YYYY 或 MM-DD-YYYY
            (r"(\d{1,2})[/-](\d{1,2})[/-](\d{4})", "%m-%d-%Y"),
            # YY/MM/DD 或 YY-MM-DD
            (r"(\d{2})[/-年](\d{1,2})[/-月](\d{1,2})日?", "%y-%m-%d"),
            # 中文格式：YYYY年MM月DD日
            (r"(\d{4})年(\d{1,2})月(\d{1,2})日", "%Y-%m-%d"),
            # 中文格式：MM月DD日
            (r"(\d{1,2})月(\d{1,2})日", "%m-%d"),
        ]
        
        for pattern, fmt in patterns:
            if fmt == "%m-%d":
                # 处理只有月日的情况，需要添加当前年份
                match = re.search(pattern, date_str)
                if match:
                    month, day = match.groups()
                    current_year = datetime.now().year
                    return f"{current_year}-{int(month):02d}-{int(day):02d}"
            elif fmt == "%d-%m-%Y" or fmt == "%m-%d-%Y":
                # 处理DD/MM/YYYY或MM/DD/YYYY的歧义情况
                match = re.search(pattern, date_str)
                if match:
                    a, b, year = match.groups()
                    # 根据数值大小推断哪个是月份（1-12）
                    a, b = int(a), int(b)
                    if a > 12 and b <= 12:  # a是日，b是月
                        return f"{year}-{b:02d}-{a:02d}"
                    elif a <= 12 and b > 12:  # a是月，b是日
                        return f"{year}-{a:02d}-{b:02d}"
                    else:
                        # 如果无法确定，按照MM-DD-YYYY处理
                        # 这里可能需要根据银行特性调整默认规则
                        return f"{year}-{a:02d}-{b:02d}"
            else:
                match = re.search(pattern, date_str)
                if match:
                    if fmt == "%Y-%m-%d":
                        year, month, day = match.groups()
                        return f"{year}-{int(month):02d}-{int(day):02d}"
                    elif fmt == "%y-%m-%d":
                        year, month, day = match.groups()
                        # 处理两位数年份
                        full_year = 2000 + int(year) if int(year) < 50 else 1900 + int(year)
                        return f"{full_year}-{int(month):02d}-{int(day):02d}"
        
        # 尝试使用datetime直接解析
        try:
            # 尝试直接解析各种格式
            formats_to_try = [
                "%Y/%m/%d", "%Y-%m-%d", "%d/%m/%Y", "%m/%d/%Y",
                "%Y年%m月%d日", "%y/%m/%d", "%y-%m-%d"
            ]
            
            for fmt in formats_to_try:
                try:
                    dt = datetime.strptime(date_str, fmt)
                    return dt.strftime("%Y-%m-%d")
                except ValueError:
                    continue
        except Exception as e:
            logger.debug(f"使用datetime解析失败: {str(e)}")
        
        return None
    
    def _standardize_time(self, time_str: str) -> Optional[str]:
        """
        将各种格式的时间标准化为HH:MM:SS格式
        
        Args:
            time_str: 原始时间字符串
            
        Returns:
            Optional[str]: 标准化的时间字符串，如果无法解析则返回None
        """
        if not time_str or not isinstance(time_str, str):
            return None
            
        time_str = time_str.strip()
        
        # 尝试匹配常见的时间格式
        patterns = [
            # HH:MM:SS 或 HH:MM:SS.sss
            (r"(\d{1,2}):(\d{1,2}):(\d{1,2})(?:\.\d+)?", "%H:%M:%S"),
            # HH:MM
            (r"(\d{1,2}):(\d{1,2})", "%H:%M:00"),
            # 中文格式：HH时MM分SS秒
            (r"(\d{1,2})时(\d{1,2})分(\d{1,2})秒", "%H:%M:%S"),
            # 中文格式：HH时MM分
            (r"(\d{1,2})时(\d{1,2})分", "%H:%M:00"),
        ]
        
        for pattern, fmt in patterns:
            match = re.search(pattern, time_str)
            if match:
                if fmt == "%H:%M:%S":
                    hour, minute, second = match.groups()
                    return f"{int(hour):02d}:{int(minute):02d}:{int(second):02d}"
                elif fmt == "%H:%M:00":
                    hour, minute = match.groups()
                    return f"{int(hour):02d}:{int(minute):02d}:00"
        
        # 尝试使用datetime直接解析
        try:
            # 尝试直接解析各种格式
            formats_to_try = [
                "%H:%M:%S", "%H:%M", "%H时%M分%S秒", "%H时%M分"
            ]
            
            for fmt in formats_to_try:
                try:
                    dt = datetime.strptime(time_str, fmt)
                    return dt.strftime("%H:%M:%S")
                except ValueError:
                    continue
        except Exception as e:
            logger.debug(f"使用datetime解析失败: {str(e)}")
        
        return None


class AmountRule(CleanerRule):
    """
    金额清洗规则
    处理不同格式的金额，并确保统一的正负号表示
    """
    
    def __init__(
        self,
        debit_field: Optional[str] = "raw_amount_debit",  # 借方
        credit_field: Optional[str] = "raw_amount_credit",  # 贷方
        single_field: Optional[str] = "raw_amount_single",  # 单一金额
        sign_field: Optional[str] = "raw_sign_keyword",  # 借贷标志
        target_field: str = "transaction_amount",  # 目标金额字段
        dr_cr_field: str = "dr_cr_flag",  # 目标借贷标志字段
        debit_flag: str = "支",  # 借方标志
        credit_flag: str = "收",  # 贷方标志
        name: str = "标准金额清洗",
        description: str = "统一金额格式并确保正负号一致性（负数表示支出，正数表示收入）",
        priority: int = 20
    ):
        """
        初始化金额清洗规则
        
        Args:
            debit_field: 借方金额字段名，可为None
            credit_field: 贷方金额字段名，可为None
            single_field: 单一金额字段名，可为None
            sign_field: 借贷标志字段名，可为None
            target_field: 目标金额字段名
            dr_cr_field: 目标借贷标志字段名
            debit_flag: 借方（支出）标志
            credit_flag: 贷方（收入）标志
            name: 规则名称
            description: 规则描述
            priority: 规则优先级
        """
        super().__init__(name, description, priority)
        self.debit_field = debit_field
        self.credit_field = credit_field
        self.single_field = single_field
        self.sign_field = sign_field
        self.target_field = target_field
        self.dr_cr_field = dr_cr_field
        self.debit_flag = debit_flag
        self.credit_flag = credit_flag
        
        # 借方关键词（表示支出）
        self.debit_keywords = {
            "借方", "借", "支出", "付款", "取款", "-", "借记", "付出", "DR", "借方发生额", "转出"
        }
        
        # 贷方关键词（表示收入）
        self.credit_keywords = {
            "贷方", "贷", "收入", "收款", "存款", "+", "贷记", "收入", "CR", "贷方发生额", "转入"
        }
        
    def clean(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        清洗金额数据
        
        Args:
            raw_data: 原始数据
            
        Returns:
            Dict[str, Any]: 添加了标准化金额的数据
        """
        result = raw_data.copy()
        
        # 尝试多种金额处理方法
        methods = [
            self._process_separate_columns,
            self._process_single_with_sign,
            self._process_single_native_sign
        ]
        
        for method in methods:
            amount, flag = method(raw_data)
            if amount is not None:
                result[self.target_field] = amount
                result[self.dr_cr_field] = flag
                return result
                
        # 如果所有方法都失败，记录警告并返回原始数据
        logger.warning(f"无法处理金额: {raw_data}")
        return result
    
    def _process_separate_columns(self, raw_data: Dict[str, Any]) -> Tuple[Optional[float], Optional[str]]:
        """
        处理分开的借贷方发生额
        
        Args:
            raw_data: 原始数据
            
        Returns:
            Tuple[Optional[float], Optional[str]]: 金额和借贷标志
        """
        if not self.debit_field or not self.credit_field:
            return None, None
            
        debit_amount_raw = raw_data.get(self.debit_field)
        credit_amount_raw = raw_data.get(self.credit_field)
        
        # 处理借方金额 (支出)
        if (debit_amount_raw is not None and str(debit_amount_raw).strip() and 
            str(debit_amount_raw).strip() != '0' and str(debit_amount_raw).strip() != '0.00'):
            try:
                # 去除金额中的逗号和空格
                clean_amount = str(debit_amount_raw).replace(',', '').replace(' ', '')
                # 处理可能的货币符号
                clean_amount = re.sub(r'[^\d.-]', '', clean_amount)
                amount = -abs(float(clean_amount))  # 借方为负值
                return amount, self.debit_flag
            except ValueError:
                # 无法解析为数字
                logger.debug(f"无法解析借方金额: {debit_amount_raw}")
        
        # 处理贷方金额 (收入)
        if (credit_amount_raw is not None and str(credit_amount_raw).strip() and 
            str(credit_amount_raw).strip() != '0' and str(credit_amount_raw).strip() != '0.00'):
            try:
                # 去除金额中的逗号和空格
                clean_amount = str(credit_amount_raw).replace(',', '').replace(' ', '')
                # 处理可能的货币符号
                clean_amount = re.sub(r'[^\d.-]', '', clean_amount)
                amount = abs(float(clean_amount))  # 贷方为正值
                return amount, self.credit_flag
            except ValueError:
                # 无法解析为数字
                logger.debug(f"无法解析贷方金额: {credit_amount_raw}")
                
        return None, None
    
    def _process_single_with_sign(self, raw_data: Dict[str, Any]) -> Tuple[Optional[float], Optional[str]]:
        """
        处理带有借贷标志的单一金额
        
        Args:
            raw_data: 原始数据
            
        Returns:
            Tuple[Optional[float], Optional[str]]: 金额和借贷标志
        """
        if not self.single_field or not self.sign_field:
            return None, None
            
        single_amount_raw = raw_data.get(self.single_field)
        sign_keyword = raw_data.get(self.sign_field)
        
        if single_amount_raw is None or not str(single_amount_raw).strip():
            return None, None
            
        try:
            # 去除金额中的逗号和空格
            clean_amount = str(single_amount_raw).replace(',', '').replace(' ', '')
            # 处理可能的货币符号
            clean_amount = re.sub(r'[^\d.-]', '', clean_amount)
            amount_value = abs(float(clean_amount))
            
            # 根据借贷标志确定正负
            if sign_keyword:
                sign_keyword_str = str(sign_keyword).strip()
                
                if any(keyword in sign_keyword_str for keyword in self.debit_keywords):
                    return -amount_value, self.debit_flag  # 借方为负值
                elif any(keyword in sign_keyword_str for keyword in self.credit_keywords):
                    return amount_value, self.credit_flag  # 贷方为正值
                    
            # 如果没有明确的标志，检查金额是否本身带有符号
            if '-' in str(single_amount_raw):
                return -amount_value, self.debit_flag
                
        except ValueError:
            # 无法解析为数字
            logger.debug(f"无法解析金额: {single_amount_raw}")
            
        return None, None
    
    def _process_single_native_sign(self, raw_data: Dict[str, Any]) -> Tuple[Optional[float], Optional[str]]:
        """
        处理单一金额字段，根据符号判断
        
        Args:
            raw_data: 原始数据
            
        Returns:
            Tuple[Optional[float], Optional[str]]: 金额和借贷标志
        """
        if not self.single_field:
            return None, None
            
        single_amount_raw = raw_data.get(self.single_field)
        
        if single_amount_raw is None or not str(single_amount_raw).strip():
            return None, None
            
        try:
            # 去除金额中的逗号和空格
            clean_amount = str(single_amount_raw).replace(',', '').replace(' ', '')
            
            # 检查金额是否本身带有符号
            if '-' in clean_amount or '−' in clean_amount:  # 注意包括Unicode减号
                # 处理可能的货币符号
                clean_amount = re.sub(r'[^\d.-]', '', clean_amount)
                amount = -abs(float(clean_amount))  # 确保是负值
                return amount, self.debit_flag
            else:
                # 处理可能的货币符号
                clean_amount = re.sub(r'[^\d.]', '', clean_amount)
                amount = abs(float(clean_amount))  # 确保是正值
                return amount, self.credit_flag
                
        except ValueError:
            # 无法解析为数字
            logger.debug(f"无法解析金额: {single_amount_raw}")
            
        return None, None


class TextRule(CleanerRule):
    """
    文本清洗规则
    处理文本字段，去除特殊字符，统一格式
    """
    
    def __init__(
        self,
        fields_mapping: Dict[str, str],  # 原始字段到目标字段的映射
        name: str = "文本清洗",
        description: str = "清洗文本字段，去除特殊字符，统一格式",
        priority: int = 30
    ):
        """
        初始化文本清洗规则
        
        Args:
            fields_mapping: 原始字段到目标字段的映射
            name: 规则名称
            description: 规则描述
            priority: 规则优先级
        """
        super().__init__(name, description, priority)
        self.fields_mapping = fields_mapping
        
    def clean(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        清洗文本数据
        
        Args:
            raw_data: 原始数据
            
        Returns:
            Dict[str, Any]: 清洗后的数据
        """
        result = raw_data.copy()
        
        for src_field, target_field in self.fields_mapping.items():
            raw_value = raw_data.get(src_field)
            if raw_value is not None and isinstance(raw_value, str):
                # 清洗并保存到目标字段
                cleaned_value = self._clean_text(raw_value)
                result[target_field] = cleaned_value
                
        return result
    
    def _clean_text(self, text: str) -> str:
        """
        清洗文本
        
        Args:
            text: 原始文本
            
        Returns:
            str: 清洗后的文本
        """
        if not text:
            return ""
            
        # 去除首尾空白字符
        text = text.strip()
        
        # 替换多个连续空白字符为单个空格
        text = re.sub(r'\s+', ' ', text)
        
        # 替换全角标点为半角标点
        punctuation_map = {
            '，': ',', '。': '.', '！': '!', '？': '?',
            '：': ':', '；': ';', '（': '(', '）': ')',
            '【': '[', '】': ']', '「': '"', '」': '"',
            '『': ''', '』': ''', '、': ',', '　': ' '
        }
        
        for full, half in punctuation_map.items():
            text = text.replace(full, half)
            
        # 处理重复连续标点符号
        text = re.sub(r'([,.!?:;])\1+', r'\1', text)
        
        return text 