{"templateId": "universal_parser", "templateName": "通用解析器模板", "bankName": "通用解析器", "parserClass": "UniversalParser", "version": "1.0.0", "description": "通用解析器 - 用于解析用户手工调整为标准格式的银行流水文件", "fileType": ["XLS", "XLSX"], "keywords": ["通用格式", "标准格式", "手工调整", "GENERIC_PARSER", "UNIVERSAL"], "multipleAccounts": true, "multipleSheets": false, "accountInfoAtTop": true, "transactionBelowAccount": true, "accountType": "universal", "priority": 100, "downloadTemplateUrl": "/api/templates/universal/download", "standardFields": [{"name": "序号", "key": "sequence_number", "columnIndex": "A", "required": true, "dataType": "integer", "description": "交易序号，从1开始的连续整数"}, {"name": "持卡人", "key": "holder_name", "columnIndex": "B", "required": true, "dataType": "string", "description": "账户持有人姓名"}, {"name": "银行名称", "key": "bank_name", "columnIndex": "C", "required": true, "dataType": "string", "description": "开户银行名称"}, {"name": "账号", "key": "account_number", "columnIndex": "D", "required": true, "dataType": "string", "description": "银行账号"}, {"name": "卡号", "key": "card_number", "columnIndex": "E", "required": false, "dataType": "string", "description": "银行卡号（如有）"}, {"name": "交易日期", "key": "transaction_date", "columnIndex": "F", "required": true, "dataType": "date", "description": "交易发生日期，格式：YYYY-MM-DD"}, {"name": "交易时间", "key": "transaction_time", "columnIndex": "G", "required": false, "dataType": "time", "description": "交易发生时间，格式：HH:MM:SS"}, {"name": "交易方式", "key": "transaction_method", "columnIndex": "H", "required": false, "dataType": "string", "description": "交易方式（如：网银转账、ATM取款等）"}, {"name": "交易金额", "key": "transaction_amount", "columnIndex": "I", "required": true, "dataType": "decimal", "description": "交易金额，正数表示收入，负数表示支出"}, {"name": "账户余额", "key": "balance_amount", "columnIndex": "J", "required": true, "dataType": "decimal", "description": "交易后账户余额"}, {"name": "借贷标志", "key": "dr_cr_flag", "columnIndex": "K", "required": false, "dataType": "string", "description": "借贷标志（借/贷、收/支等）"}, {"name": "对方户名", "key": "counterparty_name", "columnIndex": "L", "required": false, "dataType": "string", "description": "交易对方户名"}, {"name": "对方账号", "key": "counterparty_account", "columnIndex": "M", "required": false, "dataType": "string", "description": "交易对方账号"}, {"name": "对方开户行", "key": "counterparty_bank", "columnIndex": "N", "required": false, "dataType": "string", "description": "交易对方开户行"}, {"name": "备注1", "key": "remark1", "columnIndex": "O", "required": false, "dataType": "string", "description": "交易摘要或备注信息"}, {"name": "备注2", "key": "remark2", "columnIndex": "P", "required": false, "dataType": "string", "description": "额外备注信息"}, {"name": "备注3", "key": "remark3", "columnIndex": "Q", "required": false, "dataType": "string", "description": "扩展备注信息"}, {"name": "币种", "key": "currency", "columnIndex": "R", "required": false, "dataType": "string", "description": "交易币种，默认为CNY"}], "identificationRules": {"headerRow": 1, "dataStartRow": 2, "requiredHeaders": ["序号", "持卡人", "账号", "交易日期", "交易金额", "账户余额"], "identificationMarkers": ["通用格式", "标准格式", "UNIVERSAL", "GENERIC"], "fileNamePatterns": ["通用", "标准", "universal", "generic", "template"]}, "validation": {"minRows": 1, "maxRows": 100000, "requiredColumns": ["序号", "持卡人", "账号", "交易日期", "交易金额", "账户余额"], "dataTypes": {"序号": "integer", "交易日期": "date", "交易金额": "decimal", "账户余额": "decimal"}}, "processingSettings": {"encoding": "utf-8", "fallbackEncoding": "gb18030", "ignoreFileOpenErrors": false, "skipEmptyRows": true, "trimWhitespace": true, "strictValidation": true}, "instructions": {"title": "通用解析器使用说明", "description": "当银行流水格式无法被现有解析器识别时，可使用通用解析器。请按以下步骤操作：", "steps": ["1. 下载通用格式Excel模板", "2. 将您的银行流水数据按照模板的列顺序手工整理", "3. 确保必填字段（序号、持卡人、账号、交易日期、交易金额、账户余额）都有数据", "4. 保存文件并上传解析", "5. 系统将自动识别通用格式并优先使用通用解析器"], "notes": ["• 交易金额：正数表示收入，负数表示支出", "• 日期格式：YYYY-MM-DD（如：2024-01-15）", "• 时间格式：HH:MM:SS（如：14:30:25）", "• 所有金额字段请使用数字格式，不要包含货币符号", "• 建议在文件名中包含'通用'或'universal'关键词以便系统识别"]}}