/**
 * 环境配置管理
 * 统一管理开发和生产环境的配置
 */

/**
 * 检测当前环境
 */
export const isProduction = () => {
  return process.env.NODE_ENV === 'production';
};

export const isDevelopment = () => {
  return process.env.NODE_ENV === 'development';
};

export const isTest = () => {
  return process.env.NODE_ENV === 'test';
};

/**
 * 开发环境功能配置
 */
export const devConfig = {
  // 自动登录功能
  autoLogin: {
    enabled: !isProduction(), // 生产环境强制禁用
    username: process.env.REACT_APP_DEV_USERNAME || '樊迪',
    password: process.env.REACT_APP_DEV_PASSWORD || '123456'
  },
  
  // 开发工具条
  devToolbar: {
    enabled: !isProduction() && process.env.REACT_APP_SHOW_DEV_TOOLS !== 'false',
    position: 'top-right'
  },
  
  // 调试模式
  debug: {
    enabled: !isProduction() && process.env.REACT_APP_DEBUG_MODE !== 'false',
    showConsoleInfo: true,
    showNetworkLogs: true
  },
  
  // API配置
  api: {
    baseUrl: isProduction() 
      ? (process.env.REACT_APP_API_BASE_URL || '/api')
      : (process.env.REACT_APP_API_BASE_URL || 'http://127.0.0.1:8000'),
    timeout: 30000
  }
};

/**
 * 生产环境安全检查
 */
export const productionSecurityCheck = () => {
  if (isProduction()) {
    // 生产环境安全检查清单
    const securityIssues = [];
    
    if (devConfig.autoLogin.enabled) {
      securityIssues.push('自动登录功能在生产环境中被启用');
    }
    
    if (devConfig.devToolbar.enabled) {
      securityIssues.push('开发工具条在生产环境中被启用');
    }
    
    if (devConfig.debug.enabled) {
      securityIssues.push('调试模式在生产环境中被启用');
    }
    
    if (securityIssues.length > 0) {
      console.error('🚨 生产环境安全检查失败:', securityIssues);
      throw new Error('生产环境配置不安全，请检查环境变量设置');
    }
    
    console.log('✅ 生产环境安全检查通过');
  }
};

/**
 * 获取当前环境配置摘要
 */
export const getEnvironmentInfo = () => {
  return {
    environment: process.env.NODE_ENV,
    autoLogin: devConfig.autoLogin.enabled,
    devToolbar: devConfig.devToolbar.enabled,
    debug: devConfig.debug.enabled,
    apiBaseUrl: devConfig.api.baseUrl,
    buildTime: process.env.REACT_APP_BUILD_TIME || new Date().toISOString(),
    version: process.env.REACT_APP_VERSION || '2.0.0'
  };
};

/**
 * 控制台输出环境信息
 */
export const logEnvironmentInfo = () => {
  const info = getEnvironmentInfo();
  const style = isProduction() ? 'color: #ff6b6b; font-weight: bold;' : 'color: #4ecdc4; font-weight: bold;';
  
  console.log(`%c🌍 环境信息 [${info.environment.toUpperCase()}]`, style);
  console.table(info);
  
  if (isProduction()) {
    console.log('%c🔒 生产环境模式：所有开发功能已禁用', 'color: #ff6b6b; font-weight: bold;');
  } else {
    console.log('%c🔧 开发环境模式：开发功能已启用', 'color: #4ecdc4; font-weight: bold;');
  }
};

export default devConfig; 