/**
 * API服务模块 - 统一的API配置和请求管理
 */
import { buildApiUrl } from '../config/api';

/**
 * 获取当前用户
 */
export const getCurrentUser = () => {
  // 用户名映射：前端显示友好 → 后端规范化
    const userMapping = {
      '樊迪': 'fandy',
      '张三': 'zhang<PERSON>',
      '李四': 'lisi',
      'admin': 'admin'
    };
  
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const frontendUser = userInfo.username || 'fandy';
  const backendUser = userMapping[frontendUser] || frontendUser;
  
  console.log('🔍 getCurrentUser:', { 
    frontend: frontendUser, 
    backend: backendUser,
    userInfo: userInfo 
  });
  
  return backendUser;
};

/**
 * 通用API请求方法
 */
export const apiRequest = async (endpoint, options = {}) => {
  const url = buildApiUrl(endpoint);
      const currentUser = getCurrentUser();
  
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      'X-Current-User': currentUser
    },
  };

  const config = { ...defaultOptions, ...options };

  try {
    const response = await fetch(url, config);
    
    if (!response.ok) {
      if (response.status === 401) {
        console.error('认证失败，重定向到登录页面');
      window.location.href = '/login';
        return;
      }
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('API请求错误:', error);
    throw error;
  }
};

/**
 * 项目管理API
 */
export const projectAPI = {
  // 获取项目列表
  getProjects: async () => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl('/api/projects/'), {
      headers: {
        'X-Current-User': currentUser
      }
    });
    if (!response.ok) throw new Error('获取项目列表失败');
    return await response.json();
  },
  
  // 创建项目
  createProject: async (projectData) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl('/api/projects/'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Current-User': currentUser
      },
      body: JSON.stringify(projectData)
    });
    if (!response.ok) throw new Error('创建项目失败');
    return await response.json();
  },

  // 更新项目
  updateProject: async (projectId, projectData) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl(`/api/projects/${projectId}`), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-Current-User': currentUser
      },
      body: JSON.stringify(projectData)
    });
    if (!response.ok) throw new Error('更新项目失败');
    return await response.json();
  },

  // 删除项目
  deleteProject: async (projectId) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl(`/api/projects/${projectId}`), {
      method: 'DELETE',
      headers: {
        'X-Current-User': currentUser
      }
    });
    if (!response.ok) throw new Error('删除项目失败');
    return await response.json();
  }
};

/**
 * 问题线索API
 */
export const clueAPI = {
  getClues: async (projectId) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl(`/api/clues/?project_id=${projectId}`), {
      headers: { 'X-Current-User': currentUser }
    });
    if (!response.ok) throw new Error('获取问题线索失败');
    return await response.json();
  },

  createClue: async (clueData) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl('/api/clues/'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Current-User': currentUser
      },
      body: JSON.stringify(clueData)
    });
    if (!response.ok) throw new Error('创建问题线索失败');
    return await response.json();
  },

  updateClue: async (clueId, clueData) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl(`/api/clues/${clueId}`), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-Current-User': currentUser
      },
      body: JSON.stringify(clueData)
    });
    if (!response.ok) throw new Error('更新问题线索失败');
    return await response.json();
  },

  deleteClue: async (clueId) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl(`/api/clues/${clueId}`), {
      method: 'DELETE',
      headers: { 'X-Current-User': currentUser }
    });
    if (!response.ok) throw new Error('删除问题线索失败');
    return await response.json();
  }
};

/**
 * 被反映人资料API
 */
export const subjectAPI = {
  getSubjects: async (projectId) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl(`/api/subjects/?project_id=${projectId}`), {
      headers: { 'X-Current-User': currentUser }
    });
    if (!response.ok) throw new Error('获取被反映人资料失败');
    return await response.json();
  },

  createSubject: async (subjectData) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl('/api/subjects/'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Current-User': currentUser
      },
      body: JSON.stringify(subjectData)
    });
    if (!response.ok) throw new Error('创建被反映人资料失败');
    return await response.json();
  },

  updateSubject: async (subjectId, subjectData) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl(`/api/subjects/${subjectId}`), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-Current-User': currentUser
      },
      body: JSON.stringify(subjectData)
    });
    if (!response.ok) throw new Error('更新被反映人资料失败');
    return await response.json();
  },

  deleteSubject: async (subjectId) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl(`/api/subjects/${subjectId}`), {
      method: 'DELETE',
      headers: { 'X-Current-User': currentUser }
    });
    if (!response.ok) throw new Error('删除被反映人资料失败');
    return await response.json();
  }
};

/**
 * 财产信息API
 */
export const assetAPI = {
  getAssets: async (projectId) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl(`/api/assets/?project_id=${projectId}`), {
      headers: { 'X-Current-User': currentUser }
    });
    if (!response.ok) throw new Error('获取财产信息失败');
    return await response.json();
  },

  createAsset: async (assetData) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl('/api/assets/'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Current-User': currentUser
      },
      body: JSON.stringify(assetData)
    });
    if (!response.ok) throw new Error('创建财产信息失败');
    return await response.json();
  },

  updateAsset: async (assetId, assetData) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl(`/api/assets/${assetId}`), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-Current-User': currentUser
      },
      body: JSON.stringify(assetData)
    });
    if (!response.ok) throw new Error('更新财产信息失败');
    return await response.json();
  },

  deleteAsset: async (assetId) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl(`/api/assets/${assetId}`), {
      method: 'DELETE',
      headers: { 'X-Current-User': currentUser }
    });
    if (!response.ok) throw new Error('删除财产信息失败');
    return await response.json();
  }
};

/**
 * 相关人员API
 */
export const relatedPersonAPI = {
  getRelatedPersons: async (projectId) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl(`/api/related-persons/?project_id=${projectId}`), {
      headers: { 'X-Current-User': currentUser }
    });
    if (!response.ok) throw new Error('获取相关人员失败');
    return await response.json();
  },

  createRelatedPerson: async (personData) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl('/api/related-persons/'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Current-User': currentUser
      },
      body: JSON.stringify(personData)
    });
    if (!response.ok) throw new Error('创建相关人员失败');
    return await response.json();
  },

  updateRelatedPerson: async (personId, personData) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl(`/api/related-persons/${personId}`), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-Current-User': currentUser
      },
      body: JSON.stringify(personData)
    });
    if (!response.ok) throw new Error('更新相关人员失败');
    return await response.json();
  },

  deleteRelatedPerson: async (personId) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl(`/api/related-persons/${personId}`), {
      method: 'DELETE',
      headers: { 'X-Current-User': currentUser }
    });
    if (!response.ok) throw new Error('删除相关人员失败');
    return await response.json();
  }
};

/**
 * 相关单位API
 */
export const relatedUnitAPI = {
  getRelatedUnits: async (projectId) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl(`/api/related-units/?project_id=${projectId}`), {
      headers: { 'X-Current-User': currentUser }
    });
    if (!response.ok) throw new Error('获取相关单位失败');
    return await response.json();
  },

  createRelatedUnit: async (unitData) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl('/api/related-units/'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Current-User': currentUser
      },
      body: JSON.stringify(unitData)
    });
    if (!response.ok) throw new Error('创建相关单位失败');
    return await response.json();
  },

  updateRelatedUnit: async (unitId, unitData) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl(`/api/related-units/${unitId}`), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-Current-User': currentUser
      },
      body: JSON.stringify(unitData)
    });
    if (!response.ok) throw new Error('更新相关单位失败');
    return await response.json();
  },

  deleteRelatedUnit: async (unitId) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl(`/api/related-units/${unitId}`), {
      method: 'DELETE',
      headers: { 'X-Current-User': currentUser }
    });
    if (!response.ok) throw new Error('删除相关单位失败');
    return await response.json();
  }
};

/**
 * 关系映射API
 */
export const relationshipAPI = {
  getRelationships: async (projectId) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl(`/api/relationships/?project_id=${projectId}`), {
      headers: { 'X-Current-User': currentUser }
    });
    if (!response.ok) throw new Error('获取关系映射失败');
    return await response.json();
  },

  createRelationship: async (relationshipData) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl('/api/relationships/'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Current-User': currentUser
      },
      body: JSON.stringify(relationshipData)
    });
    if (!response.ok) throw new Error('创建关系映射失败');
    return await response.json();
  },

  updateRelationship: async (relationshipId, relationshipData) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl(`/api/relationships/${relationshipId}`), {
      method: 'PUT',
    headers: {
        'Content-Type': 'application/json',
        'X-Current-User': currentUser
      },
      body: JSON.stringify(relationshipData)
    });
    if (!response.ok) throw new Error('更新关系映射失败');
    return await response.json();
  },

  deleteRelationship: async (relationshipId) => {
    const currentUser = getCurrentUser();
    const response = await fetch(buildApiUrl(`/api/relationships/${relationshipId}`), {
      method: 'DELETE',
      headers: { 'X-Current-User': currentUser }
    });
    if (!response.ok) throw new Error('删除关系映射失败');
    return await response.json();
  }
};

/**
 * 模板API
 */
export const templateApi = {
  // 获取所有模板 - 修复API路径
  async getTemplates() {
    return await apiRequest('/api/parser/v2/templates');
  },
  
  // 其他模板操作...
};

/**
 * 银行API
 */
export const bankApi = {
  // 获取银行列表
  getBanks: async () => {
    return await apiRequest('/api/banks/');
  },
  
  // 其他银行操作...
};

// 默认导出
export default {
  apiRequest,
  getCurrentUser,
  projectAPI,
  clueAPI,
  subjectAPI,
  assetAPI,
  relatedPersonAPI,
  relatedUnitAPI,
  relationshipAPI,
  templateApi,
  bankApi
}; 