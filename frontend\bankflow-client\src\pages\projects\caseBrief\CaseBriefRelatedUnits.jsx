import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Card, Table, Button, Space, Modal, Form, Input, Select, message, Upload, Tag, Descriptions, ConfigProvider } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, UploadOutlined, EyeOutlined, CloudDownloadOutlined } from '@ant-design/icons';
import moment from 'moment';
import { relatedUnitAPI } from '../../../services/api';
import zhCN from 'antd/locale/zh_CN';

const { Option } = Select;
const { TextArea } = Input;

/**
 * 相关单位情况管理组件
 * 支持单位信息录入、天眼查数据导入等功能
 */
const CaseBriefRelatedUnits = () => {
  const { projectId } = useParams();
  const [units, setUnits] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editingId, setEditingId] = useState(null);
  const [selectedUnit, setSelectedUnit] = useState(null);

  // 组件挂载时获取数据
  useEffect(() => {
    if (projectId) {
      fetchRelatedUnits();
    }
  }, [projectId]);

  // 获取相关单位列表
  const fetchRelatedUnits = async () => {
    try {
      setLoading(true);
      const response = await relatedUnitAPI.getRelatedUnits(projectId);
      
      const formattedUnits = response.data.map(unit => ({
        id: unit.unit_id,
        unitName: unit.name,
        legalRepresentative: unit.legal_representative,
        registeredCapital: unit.registered_capital,
        establishDate: unit.establishment_date,
        businessScope: unit.business_scope,
        registeredAddress: unit.registered_address,
        contactInfo: unit.contact_info,
        relationshipDescription: unit.relationship_description,
        notes: unit.notes,
        createTime: new Date(unit.created_at).toLocaleString('zh-CN'),
        updateTime: unit.updated_at ? new Date(unit.updated_at).toLocaleString('zh-CN') : null
      }));
      setUnits(formattedUnits);
      message.success('获取相关单位列表成功');
    } catch (error) {
      console.error('获取相关单位列表出错:', error);
      message.error('获取相关单位列表失败，请检查网络连接');
      setUnits([]);
    } finally {
      setLoading(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '单位名称',
      dataIndex: 'unitName',
      key: 'unitName',
      width: 200,
      render: (text) => <span style={{ fontWeight: 'bold' }}>{text}</span>
    },
    {
      title: '法定代表人',
      dataIndex: 'legalRepresentative',
      key: 'legalRepresentative',
      width: 120,
      render: (text) => text || '未填写'
    },
    {
      title: '注册资本(万元)',
      dataIndex: 'registeredCapital',
      key: 'registeredCapital',
      width: 130,
      render: (value) => {
        if (!value) return <Tag color="default">未填写</Tag>;
        return (
          <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
            {value.toLocaleString()}万
          </span>
        );
      },
      sorter: (a, b) => (a.registeredCapital || 0) - (b.registeredCapital || 0),
    },
    {
      title: '成立时间',
      dataIndex: 'establishDate',
      key: 'establishDate',
      width: 120,
      render: (text) => text || '未填写'
    },
    {
      title: '注册地址',
      dataIndex: 'registeredAddress',
      key: 'registeredAddress',
      width: 200,
      ellipsis: true,
      render: (text) => text || '未填写'
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="text" 
            size="small"
            icon={<EyeOutlined />} 
            onClick={() => handleViewDetail(record)}
          />
          <Button 
            type="text" 
            size="small"
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record)}
          />
          <Button 
            type="text" 
            size="small"
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDelete(record.id)}
          />
        </Space>
      ),
    },
  ];

  // 处理查看详情
  const handleViewDetail = (record) => {
    setSelectedUnit(record);
    setIsDetailModalVisible(true);
  };

  // 处理添加或编辑单位
  const handleAddOrEdit = () => {
    setIsModalVisible(true);
    if (editingId === null) {
      form.resetFields();
    }
  };

  // 处理编辑单位
  const handleEdit = (record) => {
    setEditingId(record.id);
    form.setFieldsValue({
      unitName: record.unitName,
      legalRepresentative: record.legalRepresentative,
      registeredCapital: record.registeredCapital,
      establishDate: record.establishDate ? moment(record.establishDate) : null,
      businessScope: record.businessScope,
      registeredAddress: record.registeredAddress,
      contactInfo: record.contactInfo,
      relationshipDescription: record.relationshipDescription,
      notes: record.notes,
    });
    setIsModalVisible(true);
  };

  // 处理删除单位
  const handleDelete = async (id) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个单位信息吗？删除后无法恢复。',
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await relatedUnitAPI.deleteRelatedUnit(id);
          message.success('单位信息已删除');
          // 重新获取数据
          await fetchRelatedUnits();
        } catch (error) {
          console.error('删除单位信息失败:', error);
          message.error('删除失败，请重试');
        }
      },
    });
  };

  // 处理模态框确认
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      
      const unitData = {
        project_id: projectId,
        name: values.unitName,
        legal_representative: values.legalRepresentative || null,
        registered_capital: values.registeredCapital || null,
        establishment_date: values.establishDate ? values.establishDate.format('YYYY-MM-DD') : null,
        business_scope: values.businessScope || null,
        registered_address: values.registeredAddress || null,
        contact_info: values.contactInfo || null,
        relationship_description: values.relationshipDescription || null,
        notes: values.notes || null
      };

      if (editingId === null) {
        // 新增单位
        await relatedUnitAPI.createRelatedUnit(unitData);
        message.success('单位信息已添加');
      } else {
        // 更新单位
        const updateData = { ...unitData };
        delete updateData.project_id; // 更新时不需要project_id
        
        await relatedUnitAPI.updateRelatedUnit(editingId, updateData);
        message.success('单位信息已更新');
      }

      setIsModalVisible(false);
      setEditingId(null);
      form.resetFields();
      // 重新获取数据
      await fetchRelatedUnits();
    } catch (error) {
      console.error('表单验证或提交失败:', error);
      message.error('操作失败，请重试');
    }
  };

  // 处理模态框取消
  const handleCancel = () => {
    setIsModalVisible(false);
    setEditingId(null);
    form.resetFields();
  };

  // 处理天眼查数据导入（预留功能）
  const handleTianYanChaImport = () => {
    message.info('天眼查数据导入功能正在开发中，敬请期待！');
    // TODO: 实现天眼查数据导入功能
    // 1. 上传天眼查导出的Excel或PDF文件
    // 2. 解析文件内容，提取单位信息
    // 3. 自动填充表单或批量导入
  };

  return (
    <div>
      <Card 
        title="相关单位情况" 
        extra={
          <Space>
            <Button 
              icon={<CloudDownloadOutlined />}
              onClick={handleTianYanChaImport}
              disabled={true}
            >
              天眼查导入
            </Button>
            <Button 
              loading={loading}
              onClick={fetchRelatedUnits}
            >
              刷新
            </Button>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={handleAddOrEdit}
            >
              添加单位
            </Button>
          </Space>
        }
      >
        <Table 
          columns={columns} 
          dataSource={units} 
          rowKey="id" 
          loading={loading}
          pagination={{ 
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 添加/编辑单位模态框 */}
      <Modal
        title={editingId === null ? "添加单位信息" : "编辑单位信息"}
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          requiredMark={false}
        >
          <Form.Item
            name="unitName"
            label="单位名称"
            rules={[{ required: true, message: '请输入单位名称' }]}
          >
            <Input placeholder="请输入单位全称" />
          </Form.Item>

          <Form.Item
            name="legalRepresentative"
            label="法定代表人"
          >
            <Input placeholder="请输入法定代表人姓名" />
          </Form.Item>

          <Form.Item
            name="registeredCapital"
            label="注册资本(万元)"
          >
            <Input placeholder="请输入注册资本" />
          </Form.Item>

          <Form.Item
            name="establishDate"
            label="成立时间"
          >
            <Space>
              <Form.Item name="establish_year" style={{ marginBottom: 0 }}>
                <Select 
                  placeholder="选择年份" 
                  style={{ width: 100 }}
                >
                  {Array.from({ length: 50 }, (_, i) => {
                    const year = new Date().getFullYear() - i;
                    return (
                      <Option key={year} value={year}>
                        {year}年
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
              <Form.Item name="establish_month" style={{ marginBottom: 0 }}>
                <Select 
                  placeholder="选择月份" 
                  style={{ width: 80 }}
                >
                  {Array.from({ length: 12 }, (_, i) => {
                    const month = i + 1;
                    return (
                      <Option key={month} value={month}>
                        {month.toString().padStart(2, '0')}月
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Space>
          </Form.Item>

          <Form.Item
            name="businessScope"
            label="经营范围"
          >
            <TextArea 
              rows={4} 
              placeholder="请输入经营范围..."
              showCount
              maxLength={1000}
            />
          </Form.Item>

          <Form.Item
            name="registeredAddress"
            label="注册地址"
          >
            <Input placeholder="请输入注册地址" />
          </Form.Item>

          <Form.Item
            name="contactInfo"
            label="联系方式"
          >
            <Input placeholder="请输入联系电话或邮箱" />
          </Form.Item>

          <Form.Item
            name="relationshipDescription"
            label="关系描述"
          >
            <TextArea 
              rows={3} 
              placeholder="请描述该单位与案件的关系..."
              showCount
              maxLength={500}
            />
          </Form.Item>

          <Form.Item
            name="notes"
            label="备注"
          >
            <TextArea 
              rows={3} 
              placeholder="其他需要说明的信息..."
              showCount
              maxLength={500}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 单位详情查看模态框 */}
      <Modal
        title="单位详细信息"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedUnit && (
          <Descriptions bordered column={2} size="middle">
            <Descriptions.Item label="单位名称" span={2}>
              {selectedUnit.unitName}
            </Descriptions.Item>
            <Descriptions.Item label="法定代表人">
              {selectedUnit.legalRepresentative || '未填写'}
            </Descriptions.Item>
            <Descriptions.Item label="注册资本">
              {selectedUnit.registeredCapital ? `${selectedUnit.registeredCapital}万元` : '未填写'}
            </Descriptions.Item>
            <Descriptions.Item label="成立时间">
              {selectedUnit.establishDate || '未填写'}
            </Descriptions.Item>
            <Descriptions.Item label="联系方式">
              {selectedUnit.contactInfo || '未填写'}
            </Descriptions.Item>
            <Descriptions.Item label="注册地址" span={2}>
              {selectedUnit.registeredAddress || '未填写'}
            </Descriptions.Item>
            <Descriptions.Item label="经营范围" span={2}>
              <div style={{ 
                maxHeight: '100px', 
                overflowY: 'auto',
                whiteSpace: 'pre-wrap'
              }}>
                {selectedUnit.businessScope || '未填写'}
              </div>
            </Descriptions.Item>
            <Descriptions.Item label="关系描述" span={2}>
              <div style={{ 
                maxHeight: '80px', 
                overflowY: 'auto',
                whiteSpace: 'pre-wrap'
              }}>
                {selectedUnit.relationshipDescription || '未填写'}
              </div>
            </Descriptions.Item>
            {selectedUnit.notes && (
              <Descriptions.Item label="备注" span={2}>
                <div style={{ 
                  maxHeight: '80px', 
                  overflowY: 'auto',
                  whiteSpace: 'pre-wrap'
                }}>
                  {selectedUnit.notes}
                </div>
              </Descriptions.Item>
            )}
            <Descriptions.Item label="创建时间">
              {selectedUnit.createTime}
            </Descriptions.Item>
            <Descriptions.Item label="更新时间">
              {selectedUnit.updateTime || '未更新'}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default CaseBriefRelatedUnits; 