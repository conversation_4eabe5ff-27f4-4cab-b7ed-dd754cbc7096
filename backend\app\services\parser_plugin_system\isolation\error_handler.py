"""
错误隔离处理机制

提供插件执行的错误隔离、超时控制、异常捕获等功能
"""

import threading
import time
import signal
import traceback
import functools
import sys
from typing import Dict, Any, Callable, Optional
from concurrent.futures import ThreadPoolExecutor, TimeoutError


class ErrorHandler:
    """错误隔离处理器
    
    提供插件执行的安全环境，确保插件错误不会影响主系统
    """
    
    def __init__(self, max_workers: int = 5):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.active_executions = {}
        self._lock = threading.Lock()
    
    def execute_with_isolation(self, 
                             func: Callable, 
                             *args, 
                             timeout: int = 60,
                             **kwargs) -> Dict[str, Any]:
        """在隔离环境中执行函数
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            timeout: 超时时间(秒)
            **kwargs: 函数关键字参数
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        execution_id = f"{func.__name__}_{threading.current_thread().ident}_{time.time()}"
        
        try:
            with self._lock:
                self.active_executions[execution_id] = {
                    'start_time': time.time(),
                    'function': func.__name__,
                    'status': 'running'
                }
            
            # 包装函数以添加异常处理
            wrapped_func = self._wrap_function_with_error_handling(func)
            
            # 在线程池中执行
            future = self.executor.submit(wrapped_func, *args, **kwargs)
            
            try:
                result = future.result(timeout=timeout)
                
                # 更新执行状态
                with self._lock:
                    if execution_id in self.active_executions:
                        self.active_executions[execution_id]['status'] = 'completed'
                
                return result
                
            except TimeoutError:
                # 超时处理
                future.cancel()
                
                with self._lock:
                    if execution_id in self.active_executions:
                        self.active_executions[execution_id]['status'] = 'timeout'
                
                return {
                    "success": False,
                    "message": f"插件执行超时 ({timeout}秒)",
                    "transactions": [],
                    "accounts": [],
                    "error_type": "timeout"
                }
                
        except Exception as e:
            # 其他异常处理
            with self._lock:
                if execution_id in self.active_executions:
                    self.active_executions[execution_id]['status'] = 'error'
            
            return {
                "success": False,
                "message": f"插件执行异常: {str(e)}",
                "transactions": [],
                "accounts": [],
                "error_type": "exception",
                "error_trace": traceback.format_exc()
            }
        
        finally:
            # 清理执行记录
            with self._lock:
                if execution_id in self.active_executions:
                    self.active_executions[execution_id]['end_time'] = time.time()
    
    def _wrap_function_with_error_handling(self, func: Callable) -> Callable:
        """包装函数以添加错误处理
        
        Args:
            func: 原始函数
            
        Returns:
            Callable: 包装后的函数
        """
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
                
            except MemoryError:
                return {
                    "success": False,
                    "message": "插件内存使用超限",
                    "transactions": [],
                    "accounts": [],
                    "error_type": "memory_error"
                }
                
            except FileNotFoundError:
                return {
                    "success": False,
                    "message": "文件不存在",
                    "transactions": [],
                    "accounts": [],
                    "error_type": "file_not_found"
                }
                
            except PermissionError:
                return {
                    "success": False,
                    "message": "文件访问权限不足",
                    "transactions": [],
                    "accounts": [],
                    "error_type": "permission_error"
                }
                
            except UnicodeDecodeError:
                return {
                    "success": False,
                    "message": "文件编码错误",
                    "transactions": [],
                    "accounts": [],
                    "error_type": "encoding_error"
                }
                
            except ImportError as e:
                return {
                    "success": False,
                    "message": f"插件依赖缺失: {str(e)}",
                    "transactions": [],
                    "accounts": [],
                    "error_type": "import_error"
                }
                
            except Exception as e:
                # 通用异常处理
                error_info = {
                    "success": False,
                    "message": f"插件执行失败: {str(e)}",
                    "transactions": [],
                    "accounts": [],
                    "error_type": "general_error",
                    "error_trace": traceback.format_exc()
                }
                
                # 记录详细错误信息
                self._log_error(func.__name__, e, error_info)
                
                return error_info
        
        return wrapper
    
    def _log_error(self, func_name: str, error: Exception, error_info: Dict[str, Any]):
        """记录错误信息
        
        Args:
            func_name: 函数名
            error: 异常对象
            error_info: 错误信息
        """
        print(f"❌ 插件错误 [{func_name}]: {str(error)}")
        print(f"错误类型: {type(error).__name__}")
        print(f"错误堆栈: {error_info.get('error_trace', 'No trace available')}")
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            stats = {
                'active_executions': len([e for e in self.active_executions.values() if e['status'] == 'running']),
                'total_executions': len(self.active_executions),
                'completed_executions': len([e for e in self.active_executions.values() if e['status'] == 'completed']),
                'failed_executions': len([e for e in self.active_executions.values() if e['status'] in ['error', 'timeout']]),
                'executions': list(self.active_executions.values())[-10:]  # 最近10次执行
            }
        
        return stats
    
    def cleanup_old_executions(self, max_age_hours: int = 24):
        """清理旧的执行记录
        
        Args:
            max_age_hours: 最大保留时间(小时)
        """
        cutoff_time = time.time() - (max_age_hours * 3600)
        
        with self._lock:
            old_executions = [
                exec_id for exec_id, exec_info in self.active_executions.items()
                if exec_info.get('end_time', exec_info['start_time']) < cutoff_time
            ]
            
            for exec_id in old_executions:
                del self.active_executions[exec_id]
        
        print(f"🧹 清理了 {len(old_executions)} 条旧的执行记录")
    
    def kill_hanging_executions(self, max_runtime_minutes: int = 10):
        """终止挂起的执行
        
        Args:
            max_runtime_minutes: 最大运行时间(分钟)
        """
        cutoff_time = time.time() - (max_runtime_minutes * 60)
        
        with self._lock:
            hanging_executions = [
                exec_id for exec_id, exec_info in self.active_executions.items()
                if exec_info['status'] == 'running' and exec_info['start_time'] < cutoff_time
            ]
            
            for exec_id in hanging_executions:
                self.active_executions[exec_id]['status'] = 'terminated'
                print(f"⚠️ 终止挂起的执行: {exec_id}")
    
    def shutdown(self):
        """关闭错误处理器"""
        print("🔒 关闭错误处理器...")
        self.executor.shutdown(wait=True)
        print("✅ 错误处理器已关闭")


class CircuitBreaker:
    """熔断器模式实现
    
    当插件连续失败时自动熔断，避免系统资源浪费
    """
    
    def __init__(self, failure_threshold: int = 5, timeout: int = 60):
        """
        Args:
            failure_threshold: 失败阈值
            timeout: 熔断超时时间(秒)
        """
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'closed'  # closed, open, half-open
        self._lock = threading.Lock()
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """通过熔断器调用函数
        
        Args:
            func: 要调用的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            Any: 函数执行结果
            
        Raises:
            Exception: 当熔断器开启时抛出异常
        """
        with self._lock:
            if self.state == 'open':
                if time.time() - self.last_failure_time > self.timeout:
                    self.state = 'half-open'
                    print(f"🔄 熔断器进入半开状态")
                else:
                    raise Exception("熔断器开启中，拒绝执行")
            
            try:
                result = func(*args, **kwargs)
                
                # 成功时重置计数
                if self.state == 'half-open':
                    self.state = 'closed'
                    self.failure_count = 0
                    print(f"✅ 熔断器恢复正常状态")
                
                return result
                
            except Exception as e:
                self.failure_count += 1
                self.last_failure_time = time.time()
                
                if self.failure_count >= self.failure_threshold:
                    self.state = 'open'
                    print(f"🚨 熔断器开启，连续失败 {self.failure_count} 次")
                
                raise e
    
    def get_state(self) -> Dict[str, Any]:
        """获取熔断器状态
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        with self._lock:
            return {
                'state': self.state,
                'failure_count': self.failure_count,
                'failure_threshold': self.failure_threshold,
                'last_failure_time': self.last_failure_time,
                'timeout': self.timeout
            }
    
    def reset(self):
        """重置熔断器"""
        with self._lock:
            self.state = 'closed'
            self.failure_count = 0
            self.last_failure_time = None
            print(f"🔄 熔断器已重置") 