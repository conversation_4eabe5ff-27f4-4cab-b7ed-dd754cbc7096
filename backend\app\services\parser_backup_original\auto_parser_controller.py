"""
自动解析器选择系统控制器
协调特征提取、解析器选择和结果验证的整体流程
"""
import os
import logging
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session

from .cleaner.data_cleaner import DataCleaner
from .parser.feature_extractor import BankFileFeatureExtractor
from .parser.parser_selector import ParserSelector
from .parser.result_validator import ParsingResultValidator
from .parser.icbc_format1_enhanced import ICBCFormat1EnhancedParser
from .parser.icbc_format3_standard import ICBCFormat3StandardParser
from .parser.icbc_format4_enhanced import ICBCFormat4EnhancedParser
from .parser.universal_parser import UniversalParser
from .parser_service import ParserService

logger = logging.getLogger(__name__)

class AutoParserController:
    """
    自动解析控制器 - 负责自动选择最合适的解析器并执行解析
    """
    
    def __init__(self):
        """初始化控制器"""
        self.feature_extractor = BankFileFeatureExtractor()
        self.parser_selector = ParserSelector()
        self.result_validator = ParsingResultValidator()
        self.parser_service = ParserService()
    
    def auto_parse(self, file_path: str, bank_name: str = None, db: Session = None) -> Dict[str, Any]:
        """
        自动解析银行流水文件
        
        Args:
            file_path: 文件路径
            bank_name: 银行名称，如果为None则尝试自动检测
            db: 数据库会话
            
        Returns:
            Dict[str, Any]: 解析结果和相关信息
        """
        result = {
            "file_path": file_path,
            "bank_name": bank_name,
            "auto_detected_bank": None,
            "selected_parser": None,
            "parsing_successful": False,
            "parsing_result": None,
            "validation_result": None,
            "consistency_check": None,
            "errors": [],
            "warnings": []
        }
        
        try:
            # 步骤1：提取文件特征
            logger.info(f"提取文件特征: {file_path}")
            features = self.feature_extractor.extract_features(file_path)
            
            # 如果未指定银行名称，使用检测到的银行
            if not bank_name and features["detected_bank"]:
                bank_name = features["detected_bank"]
                result["bank_name"] = bank_name
                result["auto_detected_bank"] = bank_name
            
            # 如果仍然没有银行名称，无法继续
            if not bank_name:
                error_msg = "无法确定银行名称，请手动指定"
                logger.error(error_msg)
                result["errors"].append(error_msg)
                return result
            
            # 步骤2：使用新的解析服务
            logger.info(f"使用ParserService解析 {bank_name} 文件")
            parsing_result = self.parser_service.parse_file(file_path, db)
            
            if not parsing_result:
                error_msg = "解析失败，未能提取数据"
                logger.error(error_msg)
                result["errors"].append(error_msg)
                return result
            
            result["parsing_result"] = parsing_result
            result["parsing_successful"] = True
            
            # 步骤3：验证解析结果
            logger.info("验证解析结果")
            validation_result = self.result_validator.validate_result(parsing_result, {})
            result["validation_result"] = validation_result
            
            if not validation_result["is_valid"]:
                result["warnings"].append("解析结果验证未通过，数据可能不完整或有错误")
                result["errors"].extend(validation_result["errors"])
                result["warnings"].extend(validation_result["warnings"])
            
            # 步骤4：检查数据一致性
            logger.info("检查数据一致性")
            consistency_check = self.result_validator.check_data_consistency(parsing_result)
            result["consistency_check"] = consistency_check
            
            if not consistency_check["is_consistent"]:
                result["warnings"].append("数据一致性检查未通过，可能存在数据逻辑问题")
                result["warnings"].extend(consistency_check["issues"])
            
            return result
        except Exception as e:
            logger.exception(f"自动解析过程出错: {str(e)}")
            result["errors"].append(f"解析过程出错: {str(e)}")
            return result
    
    def register_parser_feedback(self, file_path: str, bank_name: str, 
                               parser_class: str, success: bool, feedback: str = None,
                               db: Session = None) -> bool:
        """
        注册解析器反馈，用于改进解析器选择
        
        Args:
            file_path: 文件路径
            bank_name: 银行名称
            parser_class: 解析器类名
            success: 解析是否成功
            feedback: 反馈信息
            db: 数据库会话
            
        Returns:
            bool: 是否成功注册反馈
        """
        try:
            # 提取文件特征
            features = self.feature_extractor.extract_features(file_path)
            
            # 记录反馈
            logger.info(f"解析器反馈: 文件={os.path.basename(file_path)}, 银行={bank_name}, " +
                       f"解析器={parser_class}, 成功={success}, 反馈={feedback}")
            
            return True
        except Exception as e:
            logger.error(f"注册解析器反馈时出错: {str(e)}")
            return False