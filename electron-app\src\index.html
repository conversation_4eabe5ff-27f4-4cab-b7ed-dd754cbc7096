<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>银行流水分析工具 - 桌面版</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            text-align: center;
            max-width: 800px;
        }
        
        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .status {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .button {
            background: rgba(255,255,255,0.2);
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            color: white;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .button:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .api-result {
            background: rgba(0,0,0,0.2);
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
            text-align: left;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #FF9800; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏦 银行流水分析工具</h1>
        <p>专业的桌面版银行流水数据分析解决方案</p>
        
        <div class="status-grid">
            <div class="status">
                <h3>🚀 Electron 状态</h3>
                <p id="app-status">正在初始化...</p>
                <button class="button" onclick="testElectronAPI()">测试Electron API</button>
            </div>
            
            <div class="status">
                <h3>🔧 后端服务状态</h3>
                <p id="backend-status">等待检测...</p>
                <button class="button" onclick="testBackendAPI()">测试后端API</button>
            </div>
        </div>
        
        <div class="status">
            <h3>📊 API测试结果</h3>
            <div id="api-result" class="api-result">等待测试...</div>
        </div>
    </div>
    
    <script>
        function testElectronAPI() {
            const status = document.getElementById('app-status');
            
            if (window.electronAPI) {
                status.textContent = '✅ Electron API 连接成功！';
                status.className = 'success';
            } else {
                status.textContent = '❌ Electron API 未找到，但窗口正常显示！';
                status.className = 'warning';
            }
        }
        
        async function testBackendAPI() {
            const status = document.getElementById('backend-status');
            const result = document.getElementById('api-result');
            
            status.textContent = '🔄 正在测试后端连接...';
            
            try {
                const response = await fetch('http://127.0.0.1:8000/api/versions');
                const data = await response.json();
                
                if (response.ok) {
                    status.textContent = '✅ 后端API连接成功！';
                    status.className = 'success';
                    result.innerHTML = `<span class="success">✅ API连接成功:</span><br>${JSON.stringify(data, null, 2)}`;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                status.textContent = '❌ 后端API连接失败';
                status.className = 'error';
                result.innerHTML = `<span class="error">❌ 连接失败:</span><br>${error.message}`;
            }
        }
        
        window.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                testElectronAPI();
                setTimeout(testBackendAPI, 2000);
            }, 500);
        });
    </script>
</body>
</html> 