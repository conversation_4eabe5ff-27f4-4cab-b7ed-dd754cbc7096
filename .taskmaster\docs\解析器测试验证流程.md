# 解析器测试验证流程

## 📋 概述

本文档定义了银行流水解析器的完整测试验证流程，确保解析器在部署前经过充分测试，避免生产环境问题。

## 🎯 测试目标

### 主要目标
- ✅ 确保解析器功能正确性
- ✅ 验证前后端数据一致性  
- ✅ 保证用户界面正常显示
- ✅ 验证错误处理机制
- ✅ 确保性能满足要求

### 测试覆盖范围
- 🔧 **后端单元测试** - 解析器核心逻辑
- 🌐 **API接口测试** - 前后端数据传递
- 🖥️ **前端集成测试** - 用户界面功能
- 🔄 **端到端测试** - 完整业务流程
- ⚡ **性能测试** - 大文件处理能力

## 🧪 测试流程

### 阶段1：后端单元测试

#### 1.1 解析器基础功能测试

```python
# tests/test_parser_basic.py
import unittest
from your_bank_plugin.plugin import YourBankParser

class TestParserBasic(unittest.TestCase):
    def setUp(self):
        self.parser = YourBankParser()
        self.test_file = "tests/test_data/sample.xls"
    
    def test_plugin_info(self):
        \"\"\"测试插件基本信息\"\"\"
        self.assertIsNotNone(self.parser.plugin_id)
        self.assertIsNotNone(self.parser.plugin_name)
        self.assertIsInstance(self.parser.supported_banks, list)
        self.assertGreater(len(self.parser.supported_banks), 0)
    
    def test_file_validation(self):
        \"\"\"测试文件验证\"\"\"
        # 测试有效文件
        result = self.parser.parse_file(self.test_file)
        self.assertTrue(result['success'])
        
        # 测试无效文件
        result = self.parser.parse_file("nonexistent.xls")
        self.assertFalse(result['success'])
        self.assertGreater(len(result['errors']), 0)
    
    def test_parse_success(self):
        \"\"\"测试解析成功\"\"\"
        result = self.parser.parse_file(self.test_file)
        
        self.assertTrue(result['success'])
        self.assertIn('accounts', result)
        self.assertIn('transactions', result)
        self.assertIn('summary', result)
        self.assertEqual(len(result['errors']), 0)
```

#### 1.2 账户信息字段测试

```python
# tests/test_account_fields.py
class TestAccountFields(unittest.TestCase):
    def setUp(self):
        self.parser = YourBankParser()
        self.result = self.parser.parse_file("tests/test_data/sample.xls")
        self.account = self.result['accounts'][0]
    
    def test_required_fields_exist(self):
        \"\"\"测试必需字段存在\"\"\"
        required_fields = [
            'holder_name', 'account_number', 'total_inflow',
            'total_outflow', 'account_balance', 'transaction_count'
        ]
        
        for field in required_fields:
            with self.subTest(field=field):
                self.assertIn(field, self.account, f"缺少必需字段: {field}")
                self.assertIsNotNone(self.account[field], f"字段 {field} 不能为 None")
    
    def test_field_data_types(self):
        \"\"\"测试字段数据类型\"\"\"
        type_checks = [
            ('holder_name', str),
            ('account_number', str),
            ('bank_name', str),
            ('total_inflow', (int, float)),
            ('total_outflow', (int, float)),
            ('account_balance', (int, float)),
            ('transaction_count', int),
        ]
        
        for field, expected_type in type_checks:
            with self.subTest(field=field):
                self.assertIsInstance(
                    self.account[field], 
                    expected_type, 
                    f"字段 {field} 类型错误，期望 {expected_type}"
                )
    
    def test_compatibility_fields(self):
        \"\"\"测试兼容性字段\"\"\"
        compatibility_checks = [
            ('holder_name', 'cardholder_name'),
            ('total_inflow', 'total_income'),
            ('total_outflow', 'total_expense'),
            ('account_balance', 'balance'),
        ]
        
        for new_field, old_field in compatibility_checks:
            with self.subTest(new_field=new_field, old_field=old_field):
                self.assertIn(old_field, self.account, f"缺少兼容性字段: {old_field}")
                self.assertEqual(
                    self.account[new_field], 
                    self.account[old_field],
                    f"兼容性字段值不一致: {new_field} != {old_field}"
                )
    
    def test_account_balance_calculation(self):
        \"\"\"测试账户余额计算\"\"\"
        transactions = self.result['transactions']
        
        if transactions:
            # 账户余额应该等于最后一条交易的余额
            sorted_transactions = sorted(transactions, key=lambda x: x['transaction_date'])
            last_balance = sorted_transactions[-1]['balance']
            
            self.assertEqual(
                self.account['account_balance'], 
                last_balance,
                "账户余额应该等于最后一条交易的余额"
            )
    
    def test_net_flow_calculation(self):
        \"\"\"测试净流水计算\"\"\"
        expected_net_flow = self.account['total_inflow'] - self.account['total_outflow']
        
        self.assertAlmostEqual(
            self.account['net_flow'], 
            expected_net_flow, 
            places=2,
            msg="净流水计算错误"
        )
```

#### 1.3 交易记录字段测试

```python
# tests/test_transaction_fields.py
class TestTransactionFields(unittest.TestCase):
    def setUp(self):
        self.parser = YourBankParser()
        self.result = self.parser.parse_file("tests/test_data/sample.xls")
        self.transactions = self.result['transactions']
    
    def test_transaction_required_fields(self):
        \"\"\"测试交易记录必需字段\"\"\"
        required_fields = [
            'holder_name', 'bank_name', 'account_number',
            'transaction_date', 'transaction_type', 'amount',
            'balance', 'direction'
        ]
        
        for transaction in self.transactions[:5]:  # 测试前5条
            for field in required_fields:
                with self.subTest(field=field):
                    self.assertIn(field, transaction, f"交易记录缺少必需字段: {field}")
    
    def test_transaction_data_types(self):
        \"\"\"测试交易记录数据类型\"\"\"
        if not self.transactions:
            self.skipTest("没有交易记录")
        
        transaction = self.transactions[0]
        type_checks = [
            ('holder_name', str),
            ('bank_name', str),
            ('account_number', str),
            ('transaction_date', str),
            ('transaction_type', str),
            ('amount', (int, float)),
            ('balance', (int, float)),
            ('direction', str),
        ]
        
        for field, expected_type in type_checks:
            with self.subTest(field=field):
                self.assertIsInstance(
                    transaction[field], 
                    expected_type,
                    f"交易记录字段 {field} 类型错误"
                )
    
    def test_direction_values(self):
        \"\"\"测试收支符号值\"\"\"
        valid_directions = ['收', '支']
        
        for i, transaction in enumerate(self.transactions[:10]):
            with self.subTest(transaction_index=i):
                self.assertIn(
                    transaction['direction'], 
                    valid_directions,
                    f"无效的收支符号: {transaction['direction']}"
                )
    
    def test_date_format(self):
        \"\"\"测试日期格式\"\"\"
        import re
        date_pattern = r'^\d{4}-\d{2}-\d{2}$'
        
        for i, transaction in enumerate(self.transactions[:10]):
            with self.subTest(transaction_index=i):
                date = transaction['transaction_date']
                if date:  # 允许空日期
                    self.assertRegex(
                        date, 
                        date_pattern,
                        f"日期格式错误: {date}，应为 YYYY-MM-DD"
                    )
```

### 阶段2：API接口测试

#### 2.1 API基础功能测试

```python
# tests/test_api_integration.py
import requests
import unittest

class TestAPIIntegration(unittest.TestCase):
    def setUp(self):
        self.api_url = "http://127.0.0.1:8000/api/parser/analyze"
        self.test_file = "tests/test_data/sample.xls"
    
    def test_api_upload_success(self):
        \"\"\"测试API文件上传成功\"\"\"
        with open(self.test_file, 'rb') as f:
            files = {'file': ('sample.xls', f, 'application/vnd.ms-excel')}
            data = {
                'bank_name': '您的银行',
                'template_id': 'your_bank_plugin'
            }
            
            response = requests.post(self.api_url, files=files, data=data)
            
            self.assertEqual(response.status_code, 200)
            result = response.json()
            
            self.assertIn('parse_result', result)
            self.assertIn('accounts', result['parse_result'])
            self.assertIn('transactions', result['parse_result'])
    
    def test_api_field_mapping(self):
        \"\"\"测试API字段映射\"\"\"
        with open(self.test_file, 'rb') as f:
            files = {'file': ('sample.xls', f, 'application/vnd.ms-excel')}
            data = {
                'bank_name': '您的银行',
                'template_id': 'your_bank_plugin'
            }
            
            response = requests.post(self.api_url, files=files, data=data)
            result = response.json()
            
            # 验证账户字段
            account = result['parse_result']['accounts'][0]
            required_fields = [
                'holder_name', 'account_number', 'total_inflow',
                'total_outflow', 'account_balance', 'transaction_count'
            ]
            
            for field in required_fields:
                self.assertIn(field, account, f"API返回缺少字段: {field}")
            
            # 验证账户余额不为0
            self.assertNotEqual(account['account_balance'], 0, "账户余额不应为0")
    
    def test_api_error_handling(self):
        \"\"\"测试API错误处理\"\"\"
        # 测试无效文件
        files = {'file': ('invalid.txt', b'invalid content', 'text/plain')}
        data = {
            'bank_name': '您的银行',
            'template_id': 'your_bank_plugin'
        }
        
        response = requests.post(self.api_url, files=files, data=data)
        
        # 应该返回错误信息，但不应该是500错误
        self.assertNotEqual(response.status_code, 500)
```

### 阶段3：前端集成测试

#### 3.1 Playwright端到端测试

```python
# tests/test_frontend_e2e.py
import pytest
from playwright.sync_api import sync_playwright

class TestFrontendE2E:
    def setup_method(self):
        self.playwright = sync_playwright().start()
        self.browser = self.playwright.chromium.launch(headless=False)
        self.page = self.browser.new_page()
        self.test_file = "tests/test_data/sample.xls"
    
    def teardown_method(self):
        self.browser.close()
        self.playwright.stop()
    
    def test_complete_upload_flow(self):
        \"\"\"测试完整上传流程\"\"\"
        # 1. 导航到上传页面
        self.page.goto("http://localhost:3000/projects/new/bankStatements/import")
        
        # 2. 上传文件
        self.page.click('[data-testid="upload-area"]')
        self.page.set_input_files('[data-testid="file-input"]', self.test_file)
        
        # 验证文件上传成功
        self.page.wait_for_selector('text=已上传 1 个文件')
        
        # 3. 选择银行
        self.page.click('text=下一步')
        self.page.click('[data-testid="bank-select"]')
        self.page.click('text=您的银行')
        
        # 4. 选择解析器
        self.page.click('text=下一步')
        self.page.wait_for_selector('text=智能分析完成')
        
        # 5. 开始解析
        self.page.click('text=开始解析')
        self.page.wait_for_selector('text=解析完成', timeout=30000)
        
        # 6. 验证解析结果
        self._verify_account_display()
        self._verify_transaction_display()
    
    def _verify_account_display(self):
        \"\"\"验证账户信息显示\"\"\"
        # 验证持卡人姓名不为空
        holder_name = self.page.text_content('[data-testid="holder-name"]')
        assert holder_name and holder_name != '未知', f"持卡人姓名显示异常: {holder_name}"
        
        # 验证账号不为空
        account_number = self.page.text_content('[data-testid="account-number"]')
        assert account_number and account_number != '未知', f"账号显示异常: {account_number}"
        
        # 验证金额不为¥0.00
        total_inflow = self.page.text_content('[data-testid="total-inflow"]')
        assert total_inflow != '¥0.00', f"总收入显示异常: {total_inflow}"
        
        total_outflow = self.page.text_content('[data-testid="total-outflow"]')
        assert total_outflow != '¥0.00', f"总支出显示异常: {total_outflow}"
    
    def _verify_transaction_display(self):
        \"\"\"验证交易明细显示\"\"\"
        # 点击查看交易
        self.page.click('text=查看交易')
        
        # 验证账户余额不为¥0.00
        account_balance = self.page.text_content('[data-testid="account-balance"]')
        assert account_balance != '¥0.00', f"账户余额显示异常: {account_balance}"
        
        # 验证交易记录表格存在
        self.page.wait_for_selector('[data-testid="transaction-table"]')
        
        # 验证第一条交易记录
        first_row = self.page.query_selector('[data-testid="transaction-row-1"]')
        assert first_row, "第一条交易记录不存在"
        
        # 验证交易金额不为¥0.00
        amount = first_row.query_selector('[data-testid="transaction-amount"]').text_content()
        assert amount != '¥0.00', f"交易金额显示异常: {amount}"
        
        # 验证交易余额不为¥0.00
        balance = first_row.query_selector('[data-testid="transaction-balance"]').text_content()
        assert balance != '¥0.00', f"交易余额显示异常: {balance}"
```

### 阶段4：性能测试

#### 4.1 大文件处理测试

```python
# tests/test_performance.py
import time
import unittest

class TestPerformance(unittest.TestCase):
    def test_large_file_processing(self):
        \"\"\"测试大文件处理性能\"\"\"
        parser = YourBankParser()
        large_file = "tests/test_data/large_sample.xls"  # 假设有大文件
        
        start_time = time.time()
        result = parser.parse_file(large_file)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        # 验证处理成功
        self.assertTrue(result['success'])
        
        # 验证处理时间（根据实际情况调整）
        self.assertLess(processing_time, 60, f"处理时间过长: {processing_time}秒")
        
        # 验证内存使用（可选）
        # 可以使用 memory_profiler 等工具
    
    def test_concurrent_processing(self):
        \"\"\"测试并发处理\"\"\"
        import threading
        
        parser = YourBankParser()
        test_file = "tests/test_data/sample.xls"
        results = []
        
        def process_file():
            result = parser.parse_file(test_file)
            results.append(result)
        
        # 创建多个线程
        threads = []
        for i in range(3):
            thread = threading.Thread(target=process_file)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证所有处理都成功
        for result in results:
            self.assertTrue(result['success'])
```

## 📋 测试执行清单

### 开发阶段测试清单

- [ ] **单元测试**
  - [ ] 插件基本信息测试
  - [ ] 文件验证测试
  - [ ] 解析成功测试
  - [ ] 账户字段测试
  - [ ] 交易字段测试
  - [ ] 数据类型测试
  - [ ] 兼容性字段测试

- [ ] **数据验证测试**
  - [ ] 账户余额计算测试
  - [ ] 净流水计算测试
  - [ ] 日期格式测试
  - [ ] 收支符号测试

### 集成阶段测试清单

- [ ] **API接口测试**
  - [ ] 文件上传成功测试
  - [ ] 字段映射测试
  - [ ] 错误处理测试
  - [ ] 响应格式测试

- [ ] **前端集成测试**
  - [ ] 完整上传流程测试
  - [ ] 账户信息显示测试
  - [ ] 交易明细显示测试
  - [ ] 用户交互测试

### 部署前测试清单

- [ ] **端到端测试**
  - [ ] 完整业务流程测试
  - [ ] 数据一致性测试
  - [ ] 用户体验测试

- [ ] **性能测试**
  - [ ] 大文件处理测试
  - [ ] 并发处理测试
  - [ ] 内存使用测试

- [ ] **兼容性测试**
  - [ ] 不同浏览器测试
  - [ ] 不同文件格式测试
  - [ ] 边界条件测试

## 🚀 自动化测试

### 测试脚本

```bash
#!/bin/bash
# run_tests.sh - 自动化测试脚本

echo "开始解析器测试..."

# 1. 后端单元测试
echo "运行后端单元测试..."
cd backend
python -m pytest tests/test_parser_basic.py -v
python -m pytest tests/test_account_fields.py -v
python -m pytest tests/test_transaction_fields.py -v

# 2. API接口测试
echo "运行API接口测试..."
python -m pytest tests/test_api_integration.py -v

# 3. 前端集成测试
echo "运行前端集成测试..."
cd ../frontend
npm test

# 4. 端到端测试
echo "运行端到端测试..."
npx playwright test

echo "测试完成！"
```

### CI/CD集成

```yaml
# .github/workflows/parser-test.yml
name: Parser Test

on:
  push:
    paths:
      - 'backend/app/services/parser_plugin_system/plugins/**'
  pull_request:
    paths:
      - 'backend/app/services/parser_plugin_system/plugins/**'

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest
    
    - name: Run unit tests
      run: |
        cd backend
        python -m pytest tests/ -v
    
    - name: Run API tests
      run: |
        cd backend
        python -m pytest tests/test_api_integration.py -v
    
    - name: Set up Node.js
      uses: actions/setup-node@v2
      with:
        node-version: 16
    
    - name: Install frontend dependencies
      run: |
        cd frontend
        npm install
    
    - name: Run frontend tests
      run: |
        cd frontend
        npm test
```

## 📊 测试报告

### 测试结果记录

```python
# 测试结果记录模板
test_results = {
    "parser_name": "您的银行Format1解析器",
    "test_date": "2025-01-04",
    "test_version": "v1.0.0",
    "results": {
        "unit_tests": {
            "total": 25,
            "passed": 25,
            "failed": 0,
            "coverage": "95%"
        },
        "api_tests": {
            "total": 8,
            "passed": 8,
            "failed": 0
        },
        "e2e_tests": {
            "total": 5,
            "passed": 5,
            "failed": 0
        },
        "performance_tests": {
            "large_file_processing": "通过 (45秒)",
            "concurrent_processing": "通过",
            "memory_usage": "正常 (<500MB)"
        }
    },
    "issues": [],
    "recommendations": [
        "建议添加更多边界条件测试",
        "考虑增加错误恢复测试"
    ]
}
```

## 📚 相关文档

- [解析器开发规范](./解析器开发规范.md)
- [字段映射检查清单](./字段映射检查清单.md)
- [解析器修复经验总结](./解析器开发修复经验总结.md)

---

**更新时间**: 2025-01-04  
**版本**: v1.0  
**维护者**: 开发团队