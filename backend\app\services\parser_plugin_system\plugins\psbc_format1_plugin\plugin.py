"""
邮政储蓄银行Format1解析器插件
专门处理邮政储蓄银行标准流水格式：
- 表头包含账户基本信息
- 交易时间需要拆分为日期和时间
- 借贷标志：存=收入，取=支出
- 账号卡号都取值于"折/卡号/存单号"字段
"""

import pandas as pd
import logging
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime
import re
import os
import time
import traceback
import json

# 修复导入路径
try:
    from ...core.plugin_interface import BasePlugin
except ImportError:
    # 如果相对导入失败，创建一个简单的基础类
    class BasePlugin:
        def __init__(self):
            self.name = "psbc_format1_plugin"
            self.version = "1.0.0"
            self.description = "邮政储蓄银行Format1解析器插件"
            self.bank_name = "邮政储蓄银行"

logger = logging.getLogger(__name__)

class Plugin(BasePlugin):
    """邮政储蓄银行格式1解析器插件"""
    
    def __init__(self, file_path: str = None):
        super().__init__()
        self.name = "psbc_format1_plugin"
        self.version = "1.0.0"
        self.description = "邮政储蓄银行Format1解析器插件"
        self.bank_name = "邮政储蓄银行"
        self.format_type = "xls"
        self.file_path = file_path
        
        # 解析结果
        self.accounts = []
        self.transactions = []
        
        # 状态管理
        self.start_time = time.time()
        self.error_count = 0
        self.last_error = None
        
        # 加载配置
        self.config = self._load_config()
        
        logger.info(f"邮政储蓄银行Format1解析器插件初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载插件配置"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), 'config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"加载配置文件失败: {e}")
        
        # 返回默认配置
        return {
            "parsing": {
                "header_rows": 5,
                "data_start_row": 6,
                "date_formats": ["%Y/%m/%d %H:%M:%S", "%Y/%m/%d %H:%M"]
            },
            "field_mapping": {
                "account_number_source": "折/卡号/存单号",
                "card_number_source": "折/卡号/存单号"
            }
        }
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取插件元信息"""
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "bank_name": self.bank_name,
            "supported_formats": ["邮政储蓄银行标准格式"],
            "confidence_threshold": 0.8,
            "created_at": self.start_time
        }
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的格式列表"""
        return ["邮政储蓄银行标准格式", "PSBC Format 1"]
    
    def validate_file(self, file_path: str) -> bool:
        """验证文件是否适用于此解析器"""
        try:
            if not os.path.exists(file_path):
                return False
            
            # 检查文件扩展名
            if not file_path.lower().endswith(('.xls', '.xlsx')):
                return False
            
            # 读取文件前几行检查格式
            df = pd.read_excel(file_path, nrows=10, header=None)
            
            # 检查关键标识
            content = df.to_string()
            keywords = ["根据账号/卡号查明细", "账号/卡号/存单号", "户名", "折/卡号/存单号"]
            
            found_keywords = sum(1 for keyword in keywords if keyword in content)
            
            return found_keywords >= 2
            
        except Exception as e:
            logger.error(f"文件验证失败: {e}")
            return False
    
    def calculate_confidence(self, file_path: str) -> float:
        """计算解析置信度"""
        try:
            if not self.validate_file(file_path):
                return 0.0
            
            confidence = 0.0
            
            # 读取文件内容
            df = pd.read_excel(file_path, nrows=20, header=None)
            content = df.to_string()
            
            # 检查邮政储蓄银行特征
            if "邮政储蓄银行" in content:
                confidence += 0.3
            if "根据账号/卡号查明细" in content:
                confidence += 0.2
            if "折/卡号/存单号" in content:
                confidence += 0.2
            if "借贷标志" in content:
                confidence += 0.1
            if "交易摘要" in content:
                confidence += 0.1
            if "账户余额" in content:
                confidence += 0.1
            
            return min(confidence, 1.0)
            
        except Exception as e:
            logger.error(f"置信度计算失败: {e}")
            return 0.0
    
    def parse(self, file_path: str) -> Dict[str, Any]:
        """执行文件解析"""
        try:
            self.file_path = file_path
            self.accounts = []
            self.transactions = []
            
            logger.info(f"开始解析邮政储蓄银行文件: {file_path}")
            
            # 读取Excel文件
            df = pd.read_excel(file_path, header=None)
            
            # 提取表头信息
            header_info = self._extract_header_info(df)
            
            # 查找数据开始行
            data_start_row = self._find_data_start_row(df)
            
            # 读取交易数据，指定数据类型避免科学计数法
            transaction_df = pd.read_excel(
                file_path,
                header=data_start_row,
                dtype=str  # 将所有列都读取为字符串，避免科学计数法
            )

            # 🔧 修复：确保借贷标志列被正确读取
            logger.info(f"读取到的借贷标志列样本: {transaction_df['借贷标志'].head(10).tolist()}")
            logger.info(f"借贷标志列的唯一值: {transaction_df['借贷标志'].unique()}")
            
            # 解析交易记录
            self._parse_transactions(transaction_df, header_info)

            # 根据交易记录生成账户信息
            self._create_accounts_from_transactions(header_info)
            
            # 计算统计信息
            stats = self._calculate_statistics()
            
            result = {
                "success": True,
                "message": f"成功解析邮政储蓄银行文件，共{len(self.accounts)}个账户，{len(self.transactions)}条交易记录",
                "accounts": self.accounts,
                "transactions": self.transactions,
                "statistics": stats,
                "metadata": {
                    "parser_name": self.name,
                    "parser_version": self.version,
                    "bank_name": self.bank_name,
                    "file_path": file_path,
                    "parse_time": datetime.now().isoformat(),
                    "total_accounts": len(self.accounts),
                    "total_transactions": len(self.transactions)
                }
            }
            
            logger.info(f"邮政储蓄银行文件解析完成: {result['message']}")
            return result
            
        except Exception as e:
            self.error_count += 1
            self.last_error = str(e)
            error_msg = f"邮政储蓄银行文件解析失败: {str(e)}"
            logger.error(f"{error_msg}\n{traceback.format_exc()}")
            
            return {
                "success": False,
                "message": error_msg,
                "accounts": [],
                "transactions": [],
                "error": str(e),
                "metadata": {
                    "parser_name": self.name,
                    "error_count": self.error_count
                }
            }
    
    def _extract_header_info(self, df: pd.DataFrame) -> Dict[str, str]:
        """提取表头信息"""
        header_info = {}
        
        try:
            # 遍历前几行查找关键信息
            for i in range(min(10, len(df))):
                row_data = df.iloc[i].fillna('').astype(str)
                row_text = ' '.join(row_data.values)
                
                # 提取账号/卡号/存单号
                if "账号/卡号/存单号：" in row_text:
                    for j, cell in enumerate(row_data):
                        if "账号/卡号/存单号：" in str(cell) and j + 1 < len(row_data):
                            account_card_number = str(row_data.iloc[j + 1]).strip()
                            if account_card_number and account_card_number != 'nan' and account_card_number != '':
                                header_info["account_number"] = account_card_number
                                header_info["card_number"] = account_card_number  # 根据需求，账号卡号相同
                                logger.info(f"提取到账号: {account_card_number}")
                                break
                
                # 提取户名
                if "户名：" in row_text:  # 更精确的匹配，避免匹配到"对方户名"等
                    for j, cell in enumerate(row_data):
                        if "户名：" in str(cell) and j + 1 < len(row_data):
                            cardholder_name = str(row_data.iloc[j + 1]).strip()
                            if cardholder_name and cardholder_name != 'nan' and cardholder_name != '':
                                header_info["cardholder_name"] = cardholder_name
                                logger.info(f"提取到户名: {cardholder_name}")
                                break  # 找到后立即退出，避免被后续的字段覆盖
                
                # 提取证件号码
                if "证件号码：" in row_text:
                    for j, cell in enumerate(row_data):
                        if "证件号码：" in str(cell) and j + 1 < len(row_data):
                            id_number = str(row_data.iloc[j + 1]).strip()
                            if id_number and id_number != 'nan' and id_number != '':
                                header_info["id_number"] = id_number
                                logger.info(f"提取到证件号码: {id_number}")
                                break

                # 提取开户机构名称
                if "开户机构名称：" in row_text:
                    for j, cell in enumerate(row_data):
                        if "开户机构名称：" in str(cell) and j + 1 < len(row_data):
                            bank_branch = str(row_data.iloc[j + 1]).strip()
                            if bank_branch and bank_branch != 'nan' and bank_branch != '':
                                header_info["bank_branch"] = bank_branch
                                logger.info(f"提取到开户机构: {bank_branch}")
                                break
            
            # 设置默认值
            header_info.setdefault("bank_name", "邮政储蓄银行")
            header_info.setdefault("account_type", "个人账户")
            
            logger.info(f"提取表头信息: {header_info}")
            return header_info
            
        except Exception as e:
            logger.error(f"提取表头信息失败: {e}")
            return {"bank_name": "邮政储蓄银行", "account_type": "个人账户"}
    
    def _find_data_start_row(self, df: pd.DataFrame) -> int:
        """查找数据开始行"""
        try:
            for i in range(len(df)):
                row_data = df.iloc[i].fillna('').astype(str)
                row_text = ' '.join(row_data.values)
                
                # 查找包含列标题的行
                if "序号" in row_text and "折/卡号/存单号" in row_text and "交易时间" in row_text:
                    return i
            
            # 默认返回第6行（索引5）
            return 5
            
        except Exception as e:
            logger.error(f"查找数据开始行失败: {e}")
            return 5

    def _format_account_number(self, account_number: str) -> str:
        """格式化账号，避免科学计数法问题"""
        try:
            # 如果是科学计数法格式，转换为标准数字格式
            if 'e' in str(account_number).lower():
                # 转换为浮点数再转为整数字符串
                return str(int(float(account_number)))
            else:
                # 移除可能的小数点
                return str(account_number).split('.')[0]
        except:
            return str(account_number)

    def _create_accounts_from_transactions(self, header_info: Dict[str, str]):
        """根据交易记录创建账户信息"""
        try:
            # 从交易记录中提取所有不同的账号，并格式化为标准格式
            account_numbers = set()
            for transaction in self.transactions:
                account_number = transaction.get("account_number")
                if account_number and account_number != "未知":
                    # 格式化账号，避免科学计数法问题
                    formatted_account = self._format_account_number(account_number)
                    account_numbers.add(formatted_account)
                    # 同时更新交易记录中的账号格式
                    transaction["account_number"] = formatted_account
                    transaction["card_number"] = formatted_account

            logger.info(f"发现{len(account_numbers)}个不同的账号: {account_numbers}")

            # 为每个账号创建账户信息
            for account_number in account_numbers:
                # 计算该账户的交易统计
                account_transactions = [t for t in self.transactions if t.get("account_number") == account_number]

                logger.info(f"账号 {account_number} 有 {len(account_transactions)} 条交易记录")

                # 🔧 修复：正确计算收入和支出，支出金额应该是正数
                total_inflow = sum(t["amount"] for t in account_transactions if t["income_expense"] == "收入" and t["amount"] > 0)
                total_outflow = sum(abs(t["amount"]) for t in account_transactions if t["income_expense"] == "支出")  # 支出取绝对值

                # 🔧 修复：计算账户最终余额 - 取最后一笔交易的余额
                account_balance = 0.0
                if account_transactions:
                    # 按交易日期和时间排序，取最后一笔交易的余额
                    sorted_transactions = sorted(account_transactions,
                                               key=lambda x: (x.get("transaction_date", ""), x.get("transaction_time", "")))
                    last_transaction = sorted_transactions[-1]
                    account_balance = last_transaction.get("balance", 0.0)
                    logger.info(f"账号 {account_number} 最终余额: {account_balance}")

                # 计算时间范围
                dates = [t["transaction_date"] for t in account_transactions if t["transaction_date"]]
                if dates:
                    start_date = min(dates)
                    end_date = max(dates)
                    if start_date == end_date:
                        date_range = start_date
                    else:
                        date_range = f"{start_date} 至 {end_date}"
                else:
                    date_range = "未知"

                # 格式化账号显示（避免科学计数法）
                # 直接使用字符串形式的账号，不进行数值转换以避免精度丢失
                formatted_account_number = str(account_number).strip()

                account = {
                    "holder_name": header_info.get("cardholder_name", "未知"),
                    "cardholder_name": header_info.get("cardholder_name", "未知"),
                    "account_number": formatted_account_number,
                    "card_number": formatted_account_number,  # 根据需求，卡号与账号相同
                    "bank_name": header_info.get("bank_name", "邮政储蓄银行"),
                    "account_type": header_info.get("account_type", "个人账户"),
                    "id_number": header_info.get("id_number", ""),
                    "bank_branch": header_info.get("bank_branch", ""),
                    "currency": "人民币",
                    "account_status": "正常",
                    "transactions_count": len(account_transactions),  # 前端期望的字段名
                    "total_inflow": total_inflow,  # 前端期望的字段名
                    "total_outflow": total_outflow,  # 前端期望的字段名
                    "account_balance": account_balance,  # 🔧 新增：账户最终余额
                    "date_range": date_range,  # 前端期望的字段名
                    "transactions": account_transactions  # 添加交易记录到账户中
                }

                self.accounts.append(account)
                logger.info(f"创建账户: {formatted_account_number}, 交易数: {len(account_transactions)}, 收入: {total_inflow}, 支出: {total_outflow}, 余额: {account_balance}")

            logger.info(f"创建了{len(self.accounts)}个账户信息")

        except Exception as e:
            logger.error(f"创建账户信息失败: {e}")
            # 如果失败，创建一个默认账户
            # 计算默认账户的统计信息
            total_inflow = sum(t["amount"] for t in self.transactions if t["income_expense"] == "收入" and t["amount"] > 0)
            total_outflow = sum(t["amount"] for t in self.transactions if t["income_expense"] == "支出" and t["amount"] > 0)

            # 计算时间范围
            dates = [t["transaction_date"] for t in self.transactions if t["transaction_date"]]
            if dates:
                start_date = min(dates)
                end_date = max(dates)
                if start_date == end_date:
                    date_range = start_date
                else:
                    date_range = f"{start_date} 至 {end_date}"
            else:
                date_range = "未知"

            default_account = {
                "holder_name": header_info.get("cardholder_name", "未知"),
                "cardholder_name": header_info.get("cardholder_name", "未知"),
                "account_number": header_info.get("account_number", "未知"),
                "card_number": header_info.get("card_number", "未知"),
                "bank_name": header_info.get("bank_name", "邮政储蓄银行"),
                "account_type": header_info.get("account_type", "个人账户"),
                "id_number": header_info.get("id_number", ""),
                "bank_branch": header_info.get("bank_branch", ""),
                "currency": "人民币",
                "account_status": "正常",
                "transactions_count": len(self.transactions),  # 前端期望的字段名
                "total_inflow": total_inflow,  # 前端期望的字段名
                "total_outflow": total_outflow,  # 前端期望的字段名
                "date_range": date_range,  # 前端期望的字段名
                "transactions": self.transactions
            }
            self.accounts.append(default_account)
    
    def _parse_transactions(self, df: pd.DataFrame, header_info: Dict[str, str]):
        """解析交易记录"""
        try:
            if df.empty:
                logger.warning("DataFrame为空")
                return

            # 清理列名
            # 统一清理列名中的全角空格/特殊空白与常见变体，避免匹配失败
            def _normalize_col(c):
                s = str(c).replace('\u3000',' ').strip()
                # 修正常见的列名变体
                s = s.replace('借贷 标志', '借贷标志')
                s = s.replace('对方名称/ 钱包昵称', '对方名称/钱包昵称')
                s = s.replace('对方账卡号/ 钱包ID', '对方账卡号/钱包ID')
                return s
            df.columns = [ _normalize_col(c) for c in df.columns ]
            logger.info(f"DataFrame列名: {list(df.columns)}")
            logger.info(f"DataFrame形状: {df.shape}")
            logger.info(f"DataFrame前5行:\n{df.head()}")

            # 🔧 调试：检查"对方账户开户行名"列的数据
            if "对方账户开户行名" in df.columns:
                counterpart_bank_data = df["对方账户开户行名"].dropna()
                logger.info(f"对方账户开户行名列非空数据数量: {len(counterpart_bank_data)}")
                if len(counterpart_bank_data) > 0:
                    logger.info(f"对方账户开户行名前5个非空值: {counterpart_bank_data.head().tolist()}")
                    # 🔧 调试：检查清理后的银行名称
                    cleaned_names = [self._clean_bank_name(name) for name in counterpart_bank_data.head().tolist()]
                    logger.info(f"清理后的银行名称: {cleaned_names}")
                else:
                    logger.info("对方账户开户行名列全部为空")
            else:
                logger.warning("未找到'对方账户开户行名'列")

            # 检查必需的列
            required_columns = ["序号", "折/卡号/存单号", "交易时间", "交易摘要", "借贷标志", "交易金额", "账户余额"]
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                logger.warning(f"缺少必需列: {missing_columns}")
                logger.info(f"实际列名: {list(df.columns)}")
                return

            # 🔧 修复：使用连续序号计数器
            transaction_counter = 0

            for index, row in df.iterrows():
                try:
                    # 跳过空行或无效行，但检查更多字段来确定是否为有效交易
                    if (pd.isna(row.get("序号")) or str(row.get("序号")).strip() == "") and \
                       (pd.isna(row.get("折/卡号/存单号")) or str(row.get("折/卡号/存单号")).strip() == ""):
                        continue

                    # 从每条记录提取账号和卡号（根据需求，账号卡号相同）
                    record_account_number = str(row.get("折/卡号/存单号", "")).strip()
                    if not record_account_number or record_account_number == "nan":
                        record_account_number = header_info.get("account_number", "未知")

                    # 格式化账号，避免科学计数法问题
                    record_account_number = self._format_account_number(record_account_number)

                    # 🔧 修复：递增连续序号
                    transaction_counter += 1

                    # 解析交易时间
                    transaction_date, transaction_time = self._parse_transaction_time(row.get("交易时间"))

                    # 解析金额（保持原始正负号）
                    amount = self._parse_amount(row.get("交易金额"))
                    balance = self._parse_amount(row.get("账户余额"))

                    # 解析借贷标志（首先看“借贷标志”存/取；否则用借/贷发生额推断）
                    debit_credit_flag = row.get("借贷标志")
                    income_expense = self._parse_income_expense(debit_credit_flag)
                    if income_expense == "未知":
                        debit_val = self._parse_amount(row.get("借方发生额")) if "借方发生额" in df.columns else 0.0
                        credit_val = self._parse_amount(row.get("贷方发生额")) if "贷方发生额" in df.columns else 0.0
                        if credit_val and abs(credit_val) > 0:
                            income_expense = "收入"
                        elif debit_val and abs(debit_val) > 0:
                            income_expense = "支出"

                    # 添加调试信息
                    if index < 5:  # 只打印前5条记录的调试信息
                        logger.info(f"第{index+1}条记录 - 借贷标志: '{debit_credit_flag}' -> 收支类型: '{income_expense}', 金额: {amount}")

                    # 根据借贷标志确保金额符号正确
                    if income_expense == "收入" and amount < 0:
                        amount = abs(amount)
                    elif income_expense == "支出" and amount > 0:
                        amount = abs(amount)  # 支出金额统一为正数显示

                    transaction = {
                        "序号": str(transaction_counter),  # 🔧 修复：使用连续序号
                        "持卡人姓名": header_info.get("cardholder_name", "未知"),
                        "银行名称": header_info.get("bank_name", "邮政储蓄银行"),
                        "账号": record_account_number,  # 使用记录中的账号
                        "卡号": record_account_number,     # 根据需求，卡号与账号相同
                        "交易日期": transaction_date,
                        "交易时间": transaction_time,
                        "交易方式": str(row.get("交易渠道", "")),
                        "交易金额": amount,
                        "交易余额": balance,
                        "收支符号": income_expense,
                        "对方户名": str(row.get("对方名称/钱包昵称", "")).replace("nan", ""),
                        "对方账号": str(row.get("对方账卡号/钱包ID", "")).replace("nan", ""),
                        "对方银行": self._clean_bank_name(row.get("对方账户开户行名", "")),
                        "备注1": str(row.get("交易摘要", "")),
                        "备注2": "",
                        "备注3": "",
                        # 保留原有字段以兼容后端处理
                        "serial_number": str(transaction_counter),  # 🔧 修复：使用连续序号
                        "cardholder_name": header_info.get("cardholder_name", "未知"),
                        "holder_name": header_info.get("cardholder_name", "未知"),
                        "bank_name": header_info.get("bank_name", "邮政储蓄银行"),
                        "account_number": record_account_number,
                        "card_number": record_account_number,
                        "transaction_date": transaction_date,
                        "transaction_time": transaction_time,
                        "transaction_type": str(row.get("交易摘要", "")),
                        "income_expense": income_expense,
                        "amount": amount,
                        "balance": balance,
                        "counterpart_name": str(row.get("对方名称/钱包昵称", "")).replace("nan", ""),
                        "counterpart_account": str(row.get("对方账卡号/钱包ID", "")).replace("nan", ""),
                        "counterparty_bank": self._clean_bank_name(row.get("对方账户开户行名", "")),
                        "transaction_channel": str(row.get("交易渠道", "")),
                        "transaction_location": str(row.get("交易机构名称", "")),
                        "currency": "人民币",
                        "remark": str(row.get("交易摘要", "")),
                        # 🔧 修复：添加前端期望的字段名
                        "transaction_amount": amount,  # 前端期望的交易金额字段
                        "balance_amount": balance,     # 前端期望的交易余额字段
                        "dr_cr_flag": ("收" if income_expense == "收入" else ("支" if income_expense == "支出" else "未知")),  # 前端期望的收支符号字段（收/支）
                        "sequence_number": str(transaction_counter),  # 🔧 修复：前端期望的序号字段
                        "transaction_method": str(row.get("交易渠道", "")),  # 前端期望的交易方式字段
                        "counterparty_name": str(row.get("对方名称/钱包昵称", "")).replace("nan", ""),  # 前端期望的对方户名字段
                        "counterparty_account": str(row.get("对方账卡号/钱包ID", "")).replace("nan", ""),  # 前端期望的对方账号字段
                        "remark1": str(row.get("交易摘要", "")),  # 前端期望的备注1字段
                        "remark2": "",       # 前端期望的备注2字段置空
                        "remark3": ""       # 前端期望的备注3字段置空
                    }

                    self.transactions.append(transaction)

                except Exception as e:
                    logger.warning(f"解析第{index}行交易记录失败: {e}")
                    continue

            logger.info(f"成功解析{len(self.transactions)}条交易记录")

        except Exception as e:
            logger.error(f"解析交易记录失败: {e}")
    
    def _parse_transaction_time(self, time_str: str) -> Tuple[str, str]:
        """解析交易时间，拆分为日期和时间"""
        try:
            if pd.isna(time_str) or str(time_str).strip() == "":
                return "", ""
            
            time_str = str(time_str).strip()
            
            # 尝试不同的时间格式
            date_formats = self.config.get("parsing", {}).get("date_formats", [
                "%Y/%m/%d %H:%M:%S",
                "%Y/%m/%d %H:%M",
                "%Y-%m-%d %H:%M:%S",
                "%Y-%m-%d %H:%M"
            ])
            
            for fmt in date_formats:
                try:
                    dt = datetime.strptime(time_str, fmt)
                    return dt.strftime("%Y-%m-%d"), dt.strftime("%H:%M:%S")
                except ValueError:
                    continue
            
            # 如果无法解析，尝试正则表达式
            match = re.match(r'(\d{4}[/-]\d{1,2}[/-]\d{1,2})\s+(\d{1,2}:\d{1,2}(?::\d{1,2})?)', time_str)
            if match:
                date_part = match.group(1).replace('/', '-')
                time_part = match.group(2)
                if len(time_part.split(':')) == 2:
                    time_part += ":00"
                return date_part, time_part
            
            logger.warning(f"无法解析时间格式: {time_str}")
            return str(time_str), ""
            
        except Exception as e:
            logger.error(f"解析交易时间失败: {e}")
            return "", ""
    
    def _clean_bank_name(self, bank_name) -> str:
        """清理银行名称，处理NaN值但保留包含'nan'的正常银行名称"""
        if pd.isna(bank_name) or bank_name is None:
            return ""

        bank_str = str(bank_name).strip()

        # 如果整个字符串就是"nan"（不区分大小写），则返回空字符串
        if bank_str.lower() == "nan":
            return ""

        # 否则保留原始值（即使包含"nan"字符串的正常银行名称）
        return bank_str

    def _parse_amount(self, amount_str: str) -> float:
        """解析金额"""
        try:
            if pd.isna(amount_str):
                return 0.0

            amount_str = str(amount_str).strip()

            # 如果是空字符串或nan，返回0
            if not amount_str or amount_str.lower() == 'nan':
                return 0.0

            # 移除货币符号和逗号，但保留正负号
            amount_str = re.sub(r'[￥¥,\s]', '', amount_str)

            # 处理正负号 - 保持原始符号
            is_negative = False
            if amount_str.startswith('-'):
                is_negative = True
                amount_str = amount_str[1:]
            elif amount_str.startswith('+'):
                amount_str = amount_str[1:]

            # 转换为浮点数
            amount = float(amount_str)

            # 应用正负号
            if is_negative:
                amount = -amount

            return amount

        except (ValueError, TypeError) as e:
            logger.warning(f"解析金额失败: {amount_str}, 错误: {e}")
            return 0.0
    
    def _parse_income_expense(self, flag: str) -> str:
        """解析借贷标志"""
        try:
            if pd.isna(flag):
                return "未知"

            flag = str(flag).strip()

            # 如果是空字符串或nan，返回未知
            if not flag or flag.lower() == 'nan':
                return "未知"

            # 根据邮政储蓄银行规则：存=收入，取=支出
            if flag == "存":
                return "收入"
            elif flag == "取":
                return "支出"
            else:
                logger.debug(f"未识别的借贷标志: {flag}")
                return "未知"

        except Exception as e:
            logger.warning(f"解析借贷标志失败: {flag}, 错误: {e}")
            return "未知"
    
    def _calculate_statistics(self) -> Dict[str, Any]:
        """计算统计信息"""
        try:
            if not self.transactions:
                return {
                    "total_inflow": 0.0,
                    "total_outflow": 0.0,
                    "net_flow": 0.0,
                    "transactions_count": 0,
                    "date_range": "无数据"
                }

            total_inflow = sum(t["amount"] for t in self.transactions if t["income_expense"] == "收入")
            total_outflow = sum(t["amount"] for t in self.transactions if t["income_expense"] == "支出")
            net_flow = total_inflow - total_outflow
            
            # 计算日期范围
            dates = [t["transaction_date"] for t in self.transactions if t["transaction_date"]]
            date_range = "无数据"
            if dates:
                min_date = min(dates)
                max_date = max(dates)
                date_range = f"{min_date} 至 {max_date}"
            
            return {
                "total_inflow": total_inflow,
                "total_outflow": total_outflow,
                "net_flow": net_flow,
                "transactions_count": len(self.transactions),
                "date_range": date_range
            }
            
        except Exception as e:
            logger.error(f"计算统计信息失败: {e}")
            return {
                "total_inflow": 0.0,
                "total_outflow": 0.0,
                "net_flow": 0.0,
                "transactions_count": 0,
                "date_range": "计算失败"
            }
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取插件健康状态"""
        return {
            "healthy": self.error_count < 5,
            "last_check": time.time(),
            "memory_usage": "正常",
            "error_count": self.error_count,
            "last_error": self.last_error,
            "uptime": time.time() - self.start_time
        }

    def extract_sample(self, file_path: str = None, limit: int = 10) -> Dict[str, Any]:
        """
        提取样本数据用于快速置信度评估
        中国邮政储蓄银行专用版本 - 支持4维度评估
        
        Args:
            file_path: 文件路径
            limit: 样本数量限制
            
        Returns:
            Dict: 包含样本账户和交易的字典
        """
        try:
            target_file = file_path or self.file_path
            if not target_file:
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
            
            logger.info(f"中国邮政储蓄银行解析器开始提取样本数据，限制条数: {limit}")
            
            # 快速读取Excel文件前几行
            try:
                if target_file.endswith('.xlsx'):
                    df = pd.read_excel(target_file, nrows=limit * 2)
                else:
                    df = pd.read_excel(target_file, nrows=limit * 2)
                
                if df.empty:
                    return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
                
                # 提取样本账户信息
                sample_accounts = []
                sample_transactions = []
                
                # 从第一行提取基本信息
                # 选择含有交易列的首个sheet（header=None读取前30行，自动识别表头行）
                try:
                    xls = pd.ExcelFile(target_file)
                    df = None
                    for sn in xls.sheet_names:
                        head = pd.read_excel(xls, sheet_name=sn, nrows=30, header=None)
                        header_row = -1
                        header_names = None
                        for i in range(min(30, len(head))):
                            cells = [str(v) for v in list(head.iloc[i].values)]
                            norm = [str(c).replace('\u3000',' ').strip() for c in cells]
                            norm = [c.replace('借贷 标志','借贷标志').replace('对方名称/ 钱包昵称','对方名称/钱包昵称') for c in norm]
                            row_text = ' '.join(norm)
                            if (('交易时间' in row_text or '交易日期' in row_text) and
                                ('交易金额' in row_text or '贷方发生额' in row_text or '借方发生额' in row_text) and
                                ('账户余额' in row_text or '余额' in row_text)):
                                header_row = i
                                header_names = norm
                                break
                        if header_row >= 0:
                            full = pd.read_excel(xls, sheet_name=sn, header=None)
                            # 设置列名为识别到的表头行
                            full.columns = [str(c) for c in full.iloc[header_row].values]
                            full.columns = [str(c).replace('\u3000',' ').strip().replace('借贷 标志','借贷标志').replace('对方名称/ 钱包昵称','对方名称/钱包昵称') for c in full.columns]
                            df = full.iloc[header_row+1:].reset_index(drop=True)
                            break
                except Exception:
                    df = None

                if len(df) > 0:
                    # 列名标准化（去除全角空格）
                    df.columns = [str(c).replace('\u3000',' ').strip() for c in df.columns]
                    # 按列名直接读取
                    holder_name = str(df.get('持卡人姓名', ['']).iloc[0] if '持卡人姓名' in df.columns else '').strip()
                    account_number = str(df.get('折/卡号/存单号', ['']).iloc[0] if '折/卡号/存单号' in df.columns else '').strip()
                    if not account_number:
                        account_number = str(df.get('账号', ['']).iloc[0] if '账号' in df.columns else '').strip()
                    if not holder_name:
                        holder_name = '中国邮政储蓄银行测试用户'
                    if not account_number:
                        account_number = '****************'
                    
                    sample_account = {
                        'cardholder_name': holder_name,  # 🔧 4维度姓名识别需要
                        'holder_name': holder_name,
                        'account_number': account_number,
                        'card_number': "",
                        'bank_name': '中国邮政储蓄银行',
                        'account_type': '个人账户' if len(holder_name) <= 4 else '企业账户'
                    }
                    sample_accounts.append(sample_account)
                
                # 提取样本交易数据
                transaction_count = 0
                for idx, row in df.iterrows():
                    if transaction_count >= limit:
                        break
                    
                    try:
                        # 尝试多种可能的字段名
                        date_fields = ['交易日期', '记账日期', '日期', '交易时间']
                        amount_fields = ['交易金额', '金额', '发生额', '交易额', '贷方发生额', '借方发生额']
                        balance_fields = ['余额', '账户余额', '当前余额', '结余']

                        transaction_date = ""
                        amount = None
                        balance = None

                        # 查找交易日期（若只有交易时间，取其日期部分）
                        for field in date_fields:
                            if field in row and pd.notna(row[field]):
                                val = str(row[field]).strip()
                                if field == '交易时间' and ' ' in val:
                                    transaction_date = val.split(' ')[0]
                                else:
                                    transaction_date = val
                                break

                        # 查找交易金额
                        for field in amount_fields:
                            if field in row and pd.notna(row[field]):
                                try:
                                    amount = float(str(row[field]).replace(',', '').replace('，',''))
                                    break
                                except Exception:
                                    continue
                        # 若未取到金额，尝试借/贷发生额取其一
                        if amount is None:
                            try:
                                debit = float(str(row.get('借方发生额', '')).replace(',', '').replace('，','')) if '借方发生额' in row and pd.notna(row['借方发生额']) else 0.0
                            except Exception:
                                debit = 0.0
                            try:
                                credit = float(str(row.get('贷方发生额', '')).replace(',', '').replace('，','')) if '贷方发生额' in row and pd.notna(row['贷方发生额']) else 0.0
                            except Exception:
                                credit = 0.0
                            amount = credit if abs(credit) > 0 else (debit if abs(debit) > 0 else None)

                        # 查找余额
                        for field in balance_fields:
                            if field in row and pd.notna(row[field]):
                                try:
                                    balance = float(str(row[field]).replace(',', '').replace('，',''))
                                    break
                                except Exception:
                                    continue

                        # 基本验证：必须有交易日期且金额可解析
                        if not transaction_date or amount is None:
                            continue

                        # 构建样本交易（确保满足评估器所需字段）
                        transaction = {
                            'cardholder_name': holder_name,
                            'holder_name': holder_name,
                            'account_number': account_number,
                            'transaction_date': transaction_date,
                            'transaction_amount': abs(amount),
                            'balance': balance if balance is not None else 0.0,
                            'amount': abs(amount),
                            'balance_amount': balance if balance is not None else 0.0,
                            'dr_cr_flag': '收' if amount >= 0 else '支',
                            'currency': 'CNY',
                            'transaction_method': '中国邮政储蓄银行交易',
                            'bank_name': '中国邮政储蓄银行'
                        }

                        sample_transactions.append(transaction)
                        transaction_count += 1

                    except Exception as e:
                        logger.debug(f"跳过第{idx+1}行: {str(e)}")
                        continue
                
                # 预解析不再回退到 parse，保持“真实轻量读取”原则
                # 若未构造出样本交易，则返回空交易列表，由评估器给出相应分数

                return {
                    'accounts': sample_accounts,
                    'transactions': sample_transactions[:limit],
                    'metadata': {
                        'sample_size': len(sample_transactions),
                        'evaluation_mode': 'extract_sample',
                        'plugin_name': self.name,
                        'bank_name': '中国邮政储蓄银行'
                    }
                }
                
            except Exception as e:
                logger.error(f"中国邮政储蓄银行解析器样本提取失败: {str(e)}")
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0, 'error': str(e)}}
            
        except Exception as e:
            logger.error(f"中国邮政储蓄银行解析器extract_sample方法失败: {str(e)}")
            return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0, 'error': str(e)}}
