{"name": "cmbc_format1_plugin", "version": "1.0.0", "description": "民生银行Format1解析器插件，支持民生银行多子表结构Excel格式的银行流水解析", "author": "银行流水系统团队", "license": "MIT", "homepage": "https://github.com/your-org/bank-parser", "supported_formats": ["Excel (.xlsx)", "Excel (.xls)"], "supported_banks": ["中国民生银行", "民生银行"], "dependencies": ["pandas>=1.3.0", "openpyxl>=3.0.0", "xlrd>=2.0.0", "numpy>=1.21.0"], "entry_point": "plugin.Plugin", "confidence_threshold": 0.7, "keywords": ["民生银行", "银行流水", "解析器", "插件", "Format1", "多子表"], "format_features": {"multi_sheet": true, "multi_account": true, "time_format": "YYYY/MM/DD HH:MM:SS", "amount_format": "debit_credit_flag", "header_info": true, "customer_blocks": true, "account_separation": true, "special_features": {"account_number_with_comma": true, "no_card_number": true, "header_customer_info": true, "transaction_mapping": {"进": "收入", "出": "支出", "交易类型": "交易方式", "交易摘要": "备注1"}}}, "changelog": {"1.0.0": "初始版本，支持民生银行多子表结构解析，包含表头信息提取、账号处理（去顿号）、借贷标志映射、多账户分离处理"}}