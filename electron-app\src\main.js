const { app, BrowserWindow, ipcMain, dialog, Menu, shell } = require('electron');
const path = require('path');
const isDev = require('electron-is-dev');
const { spawn } = require('child_process');

// 全局变量
let mainWindow;
let backendProcess;
let frontendProcess;

// 创建主窗口
function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    // icon: path.join(__dirname, '../assets/icon.png'), // 暂时注释掉图标
    show: false,
    titleBarStyle: 'default'
  });

  // 设置窗口标题
  mainWindow.setTitle('银行流水分析工具 v1.0.0');

  // 在开发模式下打开DevTools
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // 聚焦窗口
    if (isDev) {
      mainWindow.focus();
    }
  });

  // 处理窗口关闭
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // 防止新窗口在外部浏览器打开
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
}

// 启动后端服务
function startBackendService() {
  return new Promise((resolve, reject) => {
    console.log('启动后端服务...');
    
    const backendPath = path.join(__dirname, '../../backend');
    const pythonPath = path.join(__dirname, '../../bankflow_env/Scripts/python.exe');
    
    // 检查Python环境
    if (!require('fs').existsSync(pythonPath)) {
      console.error('Python环境不存在:', pythonPath);
      reject(new Error('Python环境未找到'));
      return;
    }

    // 设置环境变量，强制使用UTF-8编码
    const env = { 
      ...process.env, 
      PYTHONIOENCODING: 'utf-8',
      PYTHONUNBUFFERED: '1'
    };

    backendProcess = spawn(pythonPath, [
      '-m', 'uvicorn', 
      'app.main:app', 
      '--host', '127.0.0.1', 
      '--port', '8000'
    ], {
      cwd: backendPath,
      stdio: ['pipe', 'pipe', 'pipe'],
      env: env
    });

    backendProcess.stdout.on('data', (data) => {
      const output = data.toString('utf8');
      console.log('Backend:', output);
      if (output.includes('Uvicorn running')) {
        console.log('后端服务启动成功');
        resolve();
      }
    });

    backendProcess.stderr.on('data', (data) => {
      const output = data.toString('utf8');
      console.error('Backend Error:', output);
    });

    backendProcess.on('close', (code) => {
      console.log(`后端服务退出，代码: ${code}`);
    });

    backendProcess.on('error', (error) => {
      console.error('后端服务启动失败:', error);
      reject(error);
    });

    // 超时处理
    setTimeout(() => {
      if (backendProcess && !backendProcess.killed) {
        console.log('后端服务启动超时，但继续运行');
        resolve();
      }
    }, 10000);
  });
}

// 启动前端服务（开发模式）或加载构建文件（生产模式）
function startFrontendService() {
  return new Promise((resolve) => {
    if (isDev) {
      console.log('开发模式：启动前端开发服务器...');
      
      const frontendPath = path.join(__dirname, '../../frontend/bankflow-client');
      
      frontendProcess = spawn('npm', ['start'], {
        cwd: frontendPath,
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true
      });

      frontendProcess.stdout.on('data', (data) => {
        console.log('Frontend:', data.toString());
        if (data.toString().includes('webpack compiled') || 
            data.toString().includes('Local:')) {
          console.log('前端服务启动成功');
          setTimeout(() => {
            mainWindow.loadURL('http://localhost:3000');
            resolve();
          }, 2000);
        }
      });

      frontendProcess.stderr.on('data', (data) => {
        console.error('Frontend Error:', data.toString());
      });

      // 超时处理
      setTimeout(() => {
        console.log('前端服务启动超时，尝试加载页面');
        mainWindow.loadURL('http://localhost:3000');
        resolve();
      }, 15000);
    } else {
      console.log('生产模式：加载构建文件...');
      
      const buildPath = path.join(__dirname, '../../frontend/bankflow-client/build/index.html');
      
      if (require('fs').existsSync(buildPath)) {
        mainWindow.loadFile(buildPath);
      } else {
        console.error('构建文件不存在:', buildPath);
        mainWindow.loadURL('http://localhost:3000');
      }
      
      resolve();
    }
  });
}

// 创建应用菜单
function createMenu() {
  const template = [
    {
      label: '文件',
      submenu: [
        {
          label: '导入银行流水',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            dialog.showOpenDialog(mainWindow, {
              title: '选择银行流水文件',
              filters: [
                { name: 'Excel文件', extensions: ['xlsx', 'xls'] },
                { name: 'CSV文件', extensions: ['csv'] },
                { name: '所有文件', extensions: ['*'] }
              ],
              properties: ['openFile', 'multiSelections']
            }).then(result => {
              if (!result.canceled) {
                mainWindow.webContents.send('files-selected', result.filePaths);
              }
            });
          }
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: '编辑',
      submenu: [
        { label: '撤销', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: '重做', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: '剪切', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: '复制', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: '粘贴', accelerator: 'CmdOrCtrl+V', role: 'paste' }
      ]
    },
    {
      label: '视图',
      submenu: [
        { label: '重载', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: '强制重载', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: '开发者工具', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: '实际大小', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
        { label: '放大', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
        { label: '缩小', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
        { type: 'separator' },
        { label: '全屏', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: '帮助',
      submenu: [
        {
          label: '关于银行流水分析工具',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: '关于',
              message: '银行流水分析工具',
              detail: '版本: 1.0.0\n基于 Electron + React + FastAPI + DuckDB 构建\n\n提供专业的银行流水数据分析功能'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// IPC 通信处理
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('get-app-path', () => {
  return app.getAppPath();
});

ipcMain.handle('show-save-dialog', async () => {
  const result = await dialog.showSaveDialog(mainWindow, {
    title: '保存分析结果',
    defaultPath: 'bank_analysis_result.xlsx',
    filters: [
      { name: 'Excel文件', extensions: ['xlsx'] },
      { name: 'CSV文件', extensions: ['csv'] }
    ]
  });
  return result;
});

// 应用事件处理
app.whenReady().then(async () => {
  console.log('Electron应用启动');
  
  // 创建主窗口
  createMainWindow();
  
  // 创建菜单
  createMenu();
  
  try {
    // 启动后端服务
    console.log('正在启动后端服务...');
    await startBackendService();
    console.log('后端服务启动成功');
    
    // 启动前端服务
    console.log('正在启动前端服务...');
    await startFrontendService();
    console.log('前端服务启动成功');
    
    console.log('Electron应用启动完成');
  } catch (error) {
    console.error('应用启动失败:', error);
    
    dialog.showErrorBox('启动失败', `应用启动失败: ${error.message}`);
  }
});

app.on('window-all-closed', () => {
  // 清理后端进程
  if (backendProcess) {
    backendProcess.kill();
  }
  
  // 清理前端进程
  if (frontendProcess) {
    frontendProcess.kill();
  }
  
  // 在 macOS 上，通常应用会保持活动状态，直到用户使用 Cmd + Q 退出
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // 在 macOS 上，当点击 dock 图标并且没有其他窗口打开时，
  // 通常会重新创建一个窗口
  if (BrowserWindow.getAllWindows().length === 0) {
    createMainWindow();
  }
});

// 阻止导航到外部URL
app.on('web-contents-created', (event, contents) => {
  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    
    if (parsedUrl.origin !== 'http://localhost:3000' && 
        parsedUrl.origin !== 'http://127.0.0.1:3000') {
      event.preventDefault();
    }
  });
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('收到 SIGINT 信号，正在关闭...');
  app.quit();
});

process.on('SIGTERM', () => {
  console.log('收到 SIGTERM 信号，正在关闭...');
  app.quit();
});

// 新增表格字重配置
ConfigProvider.config({
  theme: {
    'table-font-weight-body': 300,
    'table-font-weight-header': 500
  }
});