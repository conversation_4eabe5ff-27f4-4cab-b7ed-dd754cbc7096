#!/usr/bin/env python3
"""
邮政储蓄银行Format3解析器插件

支持解析包含多个工作表的邮政储蓄银行流水文件：
- 个人开户信息
- 个人账户基本信息
- 个人交易明细信息

版本: 1.0.0
作者: 银行流水分析系统开发团队
"""

import os
import logging
import xlrd
from datetime import datetime
from typing import Dict, List, Any, Optional

# 修复导入路径
try:
    from ...core.plugin_interface import BasePlugin
except ImportError:
    # 如果相对导入失败，创建一个简单的基础类
    class BasePlugin:
        def __init__(self):
            self.name = "psbc_format3_plugin"
            self.version = "1.0.0"
            self.description = "邮政储蓄银行Format3解析器插件"
            self.bank_name = "邮政储蓄银行"

# 配置日志
logger = logging.getLogger(__name__)

class PSBCFormat3Parser(BasePlugin):
    """邮政储蓄银行Format3解析器"""
    
    def __init__(self, file_path: str = None):
        super().__init__()
        self.plugin_id = "psbc_format3_plugin"
        self.plugin_name = "邮政储蓄银行Format3解析器插件"
        self.supported_banks = ["中国邮政储蓄银行"]
        self.version = "1.0.0"
        self.description = "解析邮政储蓄银行Format3格式的流水文件，支持多工作表结构"
        self.file_path = file_path
        
        # 工作表名称映射
        self.sheet_names = {
            'account_info': '个人账户基本信息',
            'transaction_details': '个人交易明细信息',
            'open_info': '个人开户信息'
        }
        
        logger.info(f"初始化 {self.plugin_name} v{self.version}")
    
    def parse_file(self, file_path: str) -> Dict[str, Any]:
        """
        解析邮政储蓄银行Format3格式文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            dict: 解析结果
        """
        try:
            logger.info(f"开始解析文件: {file_path}")
            
            # 1. 文件验证
            self._validate_file(file_path)
            
            # 2. 读取Excel文件
            workbook = self._read_excel_file(file_path)
            
            # 3. 验证工作表结构
            self._validate_workbook_structure(workbook)
            
            # 4. 解析各个工作表
            account_info_data = self._parse_account_info_sheet(workbook)
            transaction_data = self._parse_transaction_sheet(workbook, account_info_data)
            
            # 5. 生成最终结果
            accounts = transaction_data['accounts']
            transactions = transaction_data['transactions']
            
            # 6. 数据验证
            self._validate_parsed_data(accounts, transactions)
            
            logger.info(f"解析完成: {len(accounts)}个账户, {len(transactions)}条交易记录")
            
            return {
                'success': True,
                'accounts': accounts,
                'transactions': transactions,
                'summary': self._generate_summary(accounts, transactions),
                'errors': []
            }
            
        except Exception as e:
            error_msg = f"解析失败: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'accounts': [],
                'transactions': [],
                'summary': {},
                'errors': [error_msg]
            }
    
    def _validate_file(self, file_path: str) -> None:
        """验证文件"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        if not file_path.lower().endswith(('.xls', '.xlsx')):
            raise ValueError("不支持的文件格式，仅支持 .xls 和 .xlsx 文件")
        
        logger.debug(f"文件验证通过: {file_path}")
    
    def _read_excel_file(self, file_path: str) -> xlrd.Book:
        """读取Excel文件"""
        try:
            workbook = xlrd.open_workbook(file_path)
            logger.debug(f"成功读取Excel文件，包含 {workbook.nsheets} 个工作表")
            return workbook
        except Exception as e:
            raise ValueError(f"无法读取Excel文件: {str(e)}")
    
    def _validate_workbook_structure(self, workbook: xlrd.Book) -> None:
        """验证工作表结构"""
        sheet_names = workbook.sheet_names()
        logger.info(f"🔧 工作表列表: {sheet_names}")

        # 🔧 输出每个工作表的基本信息
        for i, sheet_name in enumerate(sheet_names):
            sheet = workbook.sheet_by_index(i)
            logger.info(f"🔧 工作表{i}: '{sheet_name}' ({sheet.nrows}行 x {sheet.ncols}列)")

            # 🔧 输出前几行的内容来判断结构
            if sheet.nrows > 0:
                header_row = []
                for col in range(min(sheet.ncols, 15)):  # 只看前15列
                    cell_value = sheet.cell(0, col).value
                    header_row.append(str(cell_value))
                logger.info(f"🔧 工作表{i}表头: {header_row}")

        # 检查必需的工作表
        required_sheets = [self.sheet_names['transaction_details']]
        missing_sheets = []

        for required_sheet in required_sheets:
            if required_sheet not in sheet_names:
                missing_sheets.append(required_sheet)

        if missing_sheets:
            # 🔧 如果找不到预期的工作表，尝试使用第一个工作表
            logger.warning(f"找不到工作表: {missing_sheets}，尝试使用第一个工作表: {sheet_names[0]}")
            self.sheet_names['transaction_details'] = sheet_names[0]

        logger.debug("工作表结构验证通过")
    
    def _parse_account_info_sheet(self, workbook: xlrd.Book) -> Dict[str, Any]:
        """解析个人账户基本信息工作表"""
        account_info = {}
        
        # 尝试解析个人账户基本信息工作表
        sheet_name = self.sheet_names['account_info']
        if sheet_name in workbook.sheet_names():
            account_info.update(self._parse_basic_account_info(workbook, sheet_name))
        
        # 尝试解析个人开户信息工作表（作为补充）
        open_info_sheet = self.sheet_names['open_info']
        if open_info_sheet in workbook.sheet_names():
            open_info = self._parse_open_account_info(workbook, open_info_sheet)
            # 合并开户信息到账户信息中
            for account_num, info in open_info.items():
                if account_num not in account_info:
                    account_info[account_num] = info
                else:
                    # 补充缺失的信息
                    account_info[account_num].update({k: v for k, v in info.items() if not account_info[account_num].get(k)})
        
        logger.info(f"解析到 {len(account_info)} 个账户的基本信息")
        return account_info
    
    def _parse_basic_account_info(self, workbook: xlrd.Book, sheet_name: str) -> Dict[str, Any]:
        """解析个人账户基本信息工作表"""
        try:
            sheet = workbook.sheet_by_name(sheet_name)
            logger.debug(f"解析工作表: {sheet_name} ({sheet.nrows}行 x {sheet.ncols}列)")
            
            account_info = {}
            
            # 从第2行开始读取数据（第1行是表头）
            for row_idx in range(1, sheet.nrows):
                try:
                    account_number = self._safe_get_cell_value(sheet, row_idx, 0)  # 账/卡号
                    holder_name = self._safe_get_cell_value(sheet, row_idx, 1)     # 客户名称
                    id_type = self._safe_get_cell_value(sheet, row_idx, 2)         # 证件类型
                    id_number = self._safe_get_cell_value(sheet, row_idx, 3)       # 证件号码
                    open_date = self._safe_get_cell_value(sheet, row_idx, 4)       # 开户日期
                    bank_branch = self._safe_get_cell_value(sheet, row_idx, 5)     # 开户机构
                    account_status = self._safe_get_cell_value(sheet, row_idx, 6)  # 账户状态
                    account_type = self._safe_get_cell_value(sheet, row_idx, 7)    # 账户类别
                    
                    if account_number and holder_name:
                        account_info[str(account_number)] = {
                            'holder_name': str(holder_name),
                            'id_type': str(id_type) if id_type else '',
                            'id_number': str(id_number) if id_number else '',
                            'open_date': self._safe_parse_date(open_date),
                            'bank_branch': str(bank_branch) if bank_branch else '',
                            'account_status': str(account_status) if account_status else '',
                            'account_type': str(account_type) if account_type else ''
                        }
                        
                except Exception as e:
                    logger.warning(f"解析账户基本信息第{row_idx+1}行失败: {e}")
                    continue
            
            logger.debug(f"从 {sheet_name} 解析到 {len(account_info)} 个账户信息")
            return account_info
            
        except Exception as e:
            logger.warning(f"解析账户基本信息工作表失败: {e}")
            return {}
    
    def _parse_open_account_info(self, workbook: xlrd.Book, sheet_name: str) -> Dict[str, Any]:
        """解析个人开户信息工作表"""
        try:
            sheet = workbook.sheet_by_name(sheet_name)
            logger.debug(f"解析工作表: {sheet_name} ({sheet.nrows}行 x {sheet.ncols}列)")
            
            account_info = {}
            
            # 从第2行开始读取数据（第1行是表头）
            for row_idx in range(1, sheet.nrows):
                try:
                    account_number = self._safe_get_cell_value(sheet, row_idx, 0)  # 账号
                    card_number = self._safe_get_cell_value(sheet, row_idx, 1)     # 卡号
                    bank_branch = self._safe_get_cell_value(sheet, row_idx, 2)     # 开户机构
                    open_date = self._safe_get_cell_value(sheet, row_idx, 3)       # 开户日期
                    business_type = self._safe_get_cell_value(sheet, row_idx, 4)   # 业务品种
                    holder_name = self._safe_get_cell_value(sheet, row_idx, 5)     # 客户名称
                    id_type = self._safe_get_cell_value(sheet, row_idx, 6)         # 证件类型
                    id_number = self._safe_get_cell_value(sheet, row_idx, 7)       # 证件号码
                    gender = self._safe_get_cell_value(sheet, row_idx, 8)          # 性别
                    birth_date = self._safe_get_cell_value(sheet, row_idx, 9)      # 出生日期
                    
                    # 使用账号或卡号作为主键
                    key = str(account_number) if account_number else str(card_number)
                    
                    if key and holder_name:
                        account_info[key] = {
                            'holder_name': str(holder_name),
                            'id_type': str(id_type) if id_type else '',
                            'id_number': str(id_number) if id_number else '',
                            'open_date': self._safe_parse_date(open_date),
                            'bank_branch': str(bank_branch) if bank_branch else '',
                            'business_type': str(business_type) if business_type else '',
                            'gender': str(gender) if gender else '',
                            'birth_date': self._safe_parse_date(birth_date),
                            'card_number': str(card_number) if card_number else key
                        }
                        
                except Exception as e:
                    logger.warning(f"解析开户信息第{row_idx+1}行失败: {e}")
                    continue
            
            logger.debug(f"从 {sheet_name} 解析到 {len(account_info)} 个开户信息")
            return account_info
            
        except Exception as e:
            logger.warning(f"解析开户信息工作表失败: {e}")
            return {}
    
    def _parse_transaction_sheet(self, workbook: xlrd.Book, account_info_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析个人交易明细信息工作表"""
        sheet_name = self.sheet_names['transaction_details']
        sheet = workbook.sheet_by_name(sheet_name)

        logger.debug(f"解析工作表: {sheet_name} ({sheet.nrows}行 x {sheet.ncols}列)")
        
        transactions = []
        accounts_dict = {}
        
        # 从第2行开始读取数据（第1行是表头）
        for row_idx in range(1, sheet.nrows):
            try:
                transaction = self._parse_transaction_row(sheet, row_idx, account_info_data)
                if transaction:
                    transactions.append(transaction)
                    
                    # 收集账户信息
                    account_number = transaction['account_number']
                    if account_number not in accounts_dict:
                        accounts_dict[account_number] = {
                            'transactions': [],
                            'holder_name': transaction['holder_name'],
                            'account_number': account_number
                        }
                    
                    accounts_dict[account_number]['transactions'].append(transaction)
                    
            except Exception as e:
                logger.warning(f"解析交易记录第{row_idx+1}行失败: {e}")
                continue
        
        # 🔧 修复序号：为所有交易分配连续序号
        sequence_counter = 1
        for transaction in transactions:
            transaction['sequence_number'] = sequence_counter
            sequence_counter += 1

        # 生成账户信息
        accounts = []
        for account_number, account_data in accounts_dict.items():
            account = self._generate_account_info(
                account_data,
                account_info_data.get(account_number, {}),
                sheet_name
            )

            # 🔧 过滤无效账户：收支都为0且只有1条记录的账户
            if self._should_skip_account(account):
                logger.info(f"🚫 跳过无效账户: {account['holder_name']} ({account['account_number']}) - 收支都为0且只有1条记录")
                continue

            accounts.append(account)
        
        logger.info(f"解析完成: {len(accounts)}个账户, {len(transactions)}条交易记录")
        
        return {
            'accounts': accounts,
            'transactions': transactions
        }

    def _should_skip_account(self, account: Dict[str, Any]) -> bool:
        """判断是否应该跳过该账户（过滤无效账户）"""
        try:
            total_inflow = float(account.get('total_inflow', 0))
            total_outflow = float(account.get('total_outflow', 0))
            transaction_count = int(account.get('transaction_count', 0))

            # 过滤条件：收支都为0且只有1条记录
            if total_inflow == 0 and total_outflow == 0 and transaction_count <= 1:
                return True

            return False
        except (ValueError, TypeError):
            # 如果数据解析失败，保守起见不跳过
            return False

    def _parse_transaction_row(self, sheet: xlrd.sheet.Sheet, row_idx: int, account_info_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """解析单行交易记录"""
        try:
            # 读取各列数据 - 根据实际Excel结构
            account_number = self._safe_get_cell_value(sheet, row_idx, 0)      # 账/卡号
            transaction_date = self._safe_get_cell_value(sheet, row_idx, 1)    # 交易日期
            transaction_flow = self._safe_get_cell_value(sheet, row_idx, 2)    # 交易流水
            summary = self._safe_get_cell_value(sheet, row_idx, 3)             # 摘要
            transaction_code = self._safe_get_cell_value(sheet, row_idx, 4)    # 交易码
            transfer_flag = self._safe_get_cell_value(sheet, row_idx, 5)       # 现转标志
            sub_account = self._safe_get_cell_value(sheet, row_idx, 6)         # 子账号序号
            direction_flag = self._safe_get_cell_value(sheet, row_idx, 7)      # 存取标志
            amount = self._safe_get_cell_value(sheet, row_idx, 8)              # 交易金额
            balance = self._safe_get_cell_value(sheet, row_idx, 9)             # 余额
            counterpart_name = self._safe_get_cell_value(sheet, row_idx, 10)   # 交易对方名称
            counterpart_account = self._safe_get_cell_value(sheet, row_idx, 11) # 交易对方账卡号
            counterpart_bank = self._safe_get_cell_value(sheet, row_idx, 12)   # 交易对方账户开户行

            # 🔧 调试：输出对方信息提取结果
            if row_idx <= 5:  # 只输出前5行的调试信息
                logger.info(f"🔧 第{row_idx}行对方信息: 户名='{counterpart_name}' 账号='{counterpart_account}' 银行='{counterpart_bank}'")
                # 🔧 调试：输出原始单元格值
                raw_k = sheet.cell(row_idx, 10).value if row_idx < sheet.nrows and 10 < sheet.ncols else None
                raw_l = sheet.cell(row_idx, 11).value if row_idx < sheet.nrows and 11 < sheet.ncols else None
                raw_m = sheet.cell(row_idx, 12).value if row_idx < sheet.nrows and 12 < sheet.ncols else None
                logger.info(f"🔧 第{row_idx}行原始单元格值: K列='{raw_k}' L列='{raw_l}' M列='{raw_m}'")

                # 🔧 调试：输出整行数据
                row_data = []
                for col in range(min(sheet.ncols, 18)):  # 输出前18列
                    cell_value = sheet.cell(row_idx, col).value
                    row_data.append(str(cell_value))
                logger.info(f"🔧 第{row_idx}行完整数据: {row_data}")

                # 🔧 调试：输出账号和摘要信息
                logger.info(f"🔧 第{row_idx}行基本信息: 账号='{account_number}' 摘要='{summary}' 金额='{amount}' 存取标志='{direction_flag}'")
            branch_name = self._safe_get_cell_value(sheet, row_idx, 13)        # 交易网点名称
            branch_code = self._safe_get_cell_value(sheet, row_idx, 14)        # 交易网点代码
            operator_id = self._safe_get_cell_value(sheet, row_idx, 15)        # 操作员号
            transaction_success = self._safe_get_cell_value(sheet, row_idx, 16) # 交易是否成功
            transaction_time = self._safe_get_cell_value(sheet, row_idx, 17)   # 交易时间
            
            # 数据验证 - 跳过表头行
            if not account_number or str(account_number).strip() == '账/卡号':
                return None
            
            # 转换为字符串并清理
            account_number_str = str(account_number).strip()
            
            # 解析金额
            parsed_amount = self._safe_parse_amount(amount)
            parsed_balance = self._safe_parse_amount(balance)
            
            # 解析日期
            parsed_date = self._safe_parse_date(transaction_date)
            
            # 解析收支方向
            direction = self._parse_direction(direction_flag)

            # 🔧 根据收支方向设置正负金额（前端期望格式）
            if direction == '支':
                # 支出：负数
                transaction_amount = -float(parsed_amount)
                logger.info(f"🔧 支出交易：{summary} 金额={parsed_amount} -> transaction_amount={transaction_amount}")
            else:
                # 收入：正数
                transaction_amount = float(parsed_amount)
                logger.info(f"🔧 收入交易：{summary} 金额={parsed_amount} -> transaction_amount={transaction_amount}")

            # 解析交易时间 - 从完整时间戳中提取时间部分
            parsed_time = None
            if transaction_time and str(transaction_time).strip():
                time_str = str(transaction_time).strip()
                # 如果是14位时间戳格式：YYYYMMDDHHMMSS
                if len(time_str) == 14 and time_str.isdigit():
                    try:
                        # 提取时间部分 HHMMSS（后6位）
                        time_part = time_str[8:14]  # 后6位
                        hour = time_part[0:2]
                        minute = time_part[2:4]
                        second = time_part[4:6]
                        parsed_time = f"{hour}:{minute}:{second}"
                        logger.info(f"🔧 时间解析：{time_str} -> {parsed_time}")
                    except (ValueError, IndexError):
                        parsed_time = time_str
                        logger.warning(f"⚠️ 时间解析失败，使用原始值：{time_str}")
                else:
                    parsed_time = time_str
            else:
                parsed_time = ''

            # 🔧 关键修复：通过账号获取持卡人姓名
            account_info = account_info_data.get(account_number_str, {})
            holder_name = account_info.get('holder_name', '')
            
            # 如果没有找到持卡人姓名，记录警告但继续处理
            if not holder_name:
                logger.warning(f"账号 {account_number_str} 未找到对应的持卡人姓名")
                holder_name = f"未知持卡人({account_number_str})"
            
            # 🔧 使用标准字段映射（基于经验总结）
            transaction = {
                # === 前端期望的字段名（必需） ===
                'holder_name': holder_name,                                    # ⭐ 持卡人姓名
                'bank_name': '邮政储蓄银行',                                   # ⭐ 银行名称
                'account_number': account_number_str,                          # ⭐ 账号
                'transaction_date': parsed_date,                               # ⭐ 交易日期
                'transaction_type': str(summary) if summary else '未知',       # ⭐ 交易方式
                'transaction_amount': transaction_amount,                      # ⭐ 交易金额（前端期望字段名，带正负号）
                'amount': float(parsed_amount),                                # ⭐ 原始金额（兼容性）
                'balance': float(parsed_balance),                              # ⭐ 交易余额
                'direction': direction,                                        # ⭐ 收支符号
                'dr_cr_flag': direction,                                       # ⭐ 前端期望的收支符号字段

                # === 重要字段 ===
                'card_number': account_number_str,                            # 卡号（与账号相同）
                'transaction_time': parsed_time or '',  # 交易时间（格式化为HH:MM:SS）
                'counterparty_name': str(counterpart_name) if counterpart_name else '',  # 对方户名（前端期望字段名）
                'counterparty_account': str(counterpart_account) if counterpart_account else '',  # 对方账号（前端期望字段名）
                'counterparty_bank': str(counterpart_bank) if counterpart_bank else '',  # 对方银行（前端期望字段名）

                # === 可选字段 ===
                'transaction_id': str(transaction_flow) if transaction_flow else '',
                'transaction_code': str(transaction_code) if transaction_code else '',
                'transfer_flag': str(transfer_flag) if transfer_flag else '',
                'sub_account': str(sub_account) if sub_account else '',
                'branch_name': str(branch_name) if branch_name else '',
                'branch_code': str(branch_code) if branch_code else '',
                'operator_id': str(operator_id) if operator_id else '',
                'transaction_success': str(transaction_success) if transaction_success else '',
                'remark1': str(summary) if summary else '',
                'remark2': str(transaction_code) if transaction_code else '',
                'remark3': str(transfer_flag) if transfer_flag else '',
                'summary': str(summary) if summary else '',

                # === 🔧 新增：序号字段（前端需要） ===
                'sequence_number': 0,                                         # ⭐ 序号（稍后统一分配）

                # === 保持向后兼容性 ===
                'cardholder_name': holder_name,
                'transaction_method': str(summary) if summary else '未知',
                'transaction_amount': float(parsed_amount),
                'balance_amount': float(parsed_balance),
                'income_expense_flag': direction,
            }
            
            return transaction
            
        except Exception as e:
            logger.error(f"解析交易记录第{row_idx+1}行失败: {e}")
            return None
    
    def _generate_account_info(self, account_data: Dict[str, Any], basic_info: Dict[str, Any], sheet_name: str) -> Dict[str, Any]:
        """生成账户信息"""
        transactions = account_data['transactions']
        account_number = account_data['account_number']
        holder_name = account_data['holder_name']
        
        # 计算统计数据
        total_income = sum(t['amount'] for t in transactions if t['direction'] == '收')
        total_expense = sum(t['amount'] for t in transactions if t['direction'] == '支')
        net_flow = total_income - total_expense
        
        # 🔧 修复：计算账户余额（取最后一条交易的余额）
        account_balance = 0.0
        if transactions:
            sorted_transactions = sorted(transactions, key=lambda x: x.get('transaction_date', ''))
            last_transaction = sorted_transactions[-1]
            account_balance = last_transaction.get('balance', 0.0)
            logger.debug(f"账户余额取自最后交易: {last_transaction.get('transaction_date')} 余额: {account_balance}")
        
        # 时间范围
        dates = [t['transaction_date'] for t in transactions if t['transaction_date']]
        start_date = min(dates) if dates else "未知"
        end_date = max(dates) if dates else "未知"
        date_range = f"{start_date} 至 {end_date}" if dates else "未知"
        
        # 生成账户ID
        account_id = f"{account_number}_{sheet_name}"
        
        # 🔧 标准字段映射（基于经验总结）
        account = {
            # === 前端期望的字段名（必需） ===
            'account_id': account_id,
            'holder_name': holder_name,                                        # ⭐ 持卡人姓名
            'bank_name': '邮政储蓄银行',                                       # ⭐ 银行名称
            'account_number': account_number,                                  # ⭐ 账号
            'card_number': account_number,                                     # 卡号（与账号相同）
            'account_type': '个人账户',
            'account_balance': float(account_balance),                         # ⭐ 账户余额
            'total_inflow': float(total_income),                               # ⭐ 总收入
            'total_outflow': float(total_expense),                             # ⭐ 总支出
            'net_flow': float(net_flow),
            'transaction_count': len(transactions),                            # ⭐ 交易笔数
            'date_range': date_range,
            'start_date': start_date,
            'end_date': end_date,
            'bank_branch': basic_info.get('bank_branch', ''),
            'currency': '人民币',
            'sheet_name': sheet_name,
            
            # === 额外信息（来自基本信息工作表） ===
            'id_type': basic_info.get('id_type', ''),
            'id_number': basic_info.get('id_number', ''),
            'open_date': basic_info.get('open_date', ''),
            
            # === 保持向后兼容性 ===
            'cardholder_name': holder_name,
            'total_income': float(total_income),
            'total_expense': float(total_expense),
            'balance': float(account_balance),
        }
        
        return account
    
    def _parse_direction(self, direction_flag: Any) -> str:
        """解析收支方向"""
        if not direction_flag:
            return '未知'

        direction_str = str(direction_flag).strip()

        # 🔧 修复：根据实际数据格式精确判断
        if '存（收入）' in direction_str:
            return '收'
        elif '取（支出）' in direction_str:
            return '支'
        elif '存' in direction_str:
            return '收'
        elif '取' in direction_str:
            return '支'
        else:
            logger.warning(f"未识别的收支标志: {direction_str}")
            return '未知'
    
    def _safe_get_cell_value(self, sheet: xlrd.sheet.Sheet, row: int, col: int) -> Any:
        """安全获取单元格值"""
        try:
            if row < sheet.nrows and col < sheet.ncols:
                value = sheet.cell_value(row, col)
                # 处理空值
                if value == '' or value is None:
                    return ''
                return value
            return ''
        except Exception:
            return ''
    
    def _safe_parse_amount(self, value: Any) -> float:
        """安全解析金额"""
        if value is None or value == '':
            return 0.0
        
        try:
            # 移除货币符号和逗号
            if isinstance(value, str):
                value = value.replace('¥', '').replace(',', '').strip()
            
            return float(value)
        except (ValueError, TypeError):
            logger.warning(f"无法解析金额: {value}")
            return 0.0
    
    def _safe_parse_date(self, value: Any) -> str:
        """安全解析日期"""
        if value is None or value == '':
            return ''
        
        try:
            if isinstance(value, datetime):
                return value.strftime('%Y-%m-%d')
            elif isinstance(value, (int, float)):
                # Excel日期数字格式
                if value > 30000:  # 可能是YYYYMMDD格式
                    date_str = str(int(value))
                    if len(date_str) == 8:
                        year = date_str[:4]
                        month = date_str[4:6]
                        day = date_str[6:8]
                        return f"{year}-{month}-{day}"
                else:
                    # Excel日期序列号
                    from xlrd.xldate import xldate_as_datetime
                    date_obj = xldate_as_datetime(value, 0)
                    return date_obj.strftime('%Y-%m-%d')
            elif isinstance(value, str):
                # 尝试多种日期格式
                for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%Y.%m.%d', '%Y%m%d']:
                    try:
                        date_obj = datetime.strptime(value, fmt)
                        return date_obj.strftime('%Y-%m-%d')
                    except ValueError:
                        continue
            
            return str(value)
        except Exception:
            logger.warning(f"无法解析日期: {value}")
            return ''
    
    def _validate_parsed_data(self, accounts: List[Dict], transactions: List[Dict]) -> None:
        """验证解析后的数据"""
        # 验证账户数据
        for account in accounts:
            required_fields = [
                'holder_name', 'account_number', 'total_inflow', 
                'total_outflow', 'account_balance', 'transaction_count'
            ]
            
            for field in required_fields:
                if field not in account:
                    raise ValueError(f"账户缺少必需字段: {field}")
            
            # 验证数据类型
            if not isinstance(account['total_inflow'], (int, float)):
                raise ValueError("total_inflow 必须是数字类型")
            if not isinstance(account['total_outflow'], (int, float)):
                raise ValueError("total_outflow 必须是数字类型")
            if not isinstance(account['account_balance'], (int, float)):
                raise ValueError("account_balance 必须是数字类型")
        
        # 验证交易数据
        for transaction in transactions:
            required_fields = [
                'holder_name', 'transaction_date', 'transaction_type',
                'amount', 'balance', 'direction'
            ]
            
            for field in required_fields:
                if field not in transaction:
                    raise ValueError(f"交易记录缺少必需字段: {field}")
            
            # 验证收支符号
            if transaction['direction'] not in ['收', '支', '未知']:
                raise ValueError(f"无效的收支符号: {transaction['direction']}")
    
    def _generate_summary(self, accounts: List[Dict], transactions: List[Dict]) -> Dict[str, Any]:
        """生成解析摘要"""
        total_accounts = len(accounts)
        total_transactions = len(transactions)
        
        total_inflow = sum(account['total_inflow'] for account in accounts)
        total_outflow = sum(account['total_outflow'] for account in accounts)
        
        return {
            'total_accounts': total_accounts,
            'total_transactions': total_transactions,
            'total_inflow': total_inflow,
            'total_outflow': total_outflow,
            'net_flow': total_inflow - total_outflow,
            'parser_version': self.version,
            'parse_time': datetime.now().isoformat()
        }

    def parse(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """
        插件容器标准接口方法

        Args:
            file_path: 文件路径
            **kwargs: 其他参数

        Returns:
            Dict[str, Any]: 解析结果
        """
        return self.parse_file(file_path)



    def extract_sample(self, file_path: str = None, limit: int = 10) -> Dict[str, Any]:
        """
        提取样本数据用于快速置信度评估
        邮储银行Format3专用版本 - 支持4维度评估
        
        Args:
            file_path: 文件路径
            limit: 样本数量限制
            
        Returns:
            Dict: 包含样本账户和交易的字典
        """
        try:
            target_file = file_path or self.file_path
            if not target_file:
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
            
            logger.info(f"邮储银行Format3解析器开始提取样本数据，限制条数: {limit}")
            
            # 快速读取Excel文件前几行
            try:
                if target_file.endswith('.xlsx'):
                    df = pd.read_excel(target_file, nrows=limit * 2)
                else:
                    df = pd.read_excel(target_file, nrows=limit * 2)
                
                if df.empty:
                    return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
                
                # 提取样本账户信息
                sample_accounts = []
                sample_transactions = []
                
                # 从第一行提取基本信息
                if len(df) > 0:
                    first_row = df.iloc[0]
                    
                    # 尝试多种可能的字段名
                    holder_name_fields = ['持卡人姓名', '户名', '姓名', '账户名称', '持卡人']
                    account_fields = ['账号', '账户号', '卡号', '帐号']
                    
                    holder_name = ""
                    account_number = ""
                    
                    # 查找持卡人姓名
                    for field in holder_name_fields:
                        if field in first_row and pd.notna(first_row[field]):
                            holder_name = str(first_row[field]).strip()
                            break
                    
                    # 查找账号
                    for field in account_fields:
                        if field in first_row and pd.notna(first_row[field]):
                            account_number = str(first_row[field]).strip()
                            break
                    
                    # 如果没有找到有效数据，使用默认值
                    if not holder_name:
                        holder_name = "邮储银行测试用户"
                    if not account_number:
                        account_number = "****************"
                    
                    sample_account = {
                        'cardholder_name': holder_name,  # 🔧 4维度姓名识别需要
                        'holder_name': holder_name,
                        'account_number': account_number,
                        'card_number': "",
                        'bank_name': '中国邮政储蓄银行',
                        'account_type': '个人账户' if len(holder_name) <= 4 else '企业账户'
                    }
                    sample_accounts.append(sample_account)
                
                # 提取样本交易数据
                transaction_count = 0
                for idx, row in df.iterrows():
                    if transaction_count >= limit:
                        break
                    
                    try:
                        # 尝试多种可能的字段名
                        date_fields = ['交易日期', '日期', '交易时间', '记账日期']
                        amount_fields = ['交易金额', '金额', '发生额', '交易额']
                        balance_fields = ['余额', '账户余额', '当前余额', '结余']
                        
                        transaction_date = ""
                        amount = 0.0
                        balance = 0.0
                        
                        # 查找交易日期
                        for field in date_fields:
                            if field in row and pd.notna(row[field]):
                                transaction_date = str(row[field]).strip()
                                break
                        
                        # 查找交易金额
                        for field in amount_fields:
                            if field in row and pd.notna(row[field]):
                                try:
                                    amount = float(row[field])
                                    break
                                except:
                                    continue
                        
                        # 查找余额
                        for field in balance_fields:
                            if field in row and pd.notna(row[field]):
                                try:
                                    balance = float(row[field])
                                    break
                                except:
                                    continue
                        
                        # 基本验证
                        if not transaction_date or amount == 0:
                            continue
                        
                        # 构建样本交易
                        transaction = {
                            'cardholder_name': holder_name,  # 🔧 4维度姓名识别需要
                            'holder_name': holder_name,
                            'account_number': account_number,
                            'transaction_date': transaction_date,
                            'transaction_amount': abs(amount),
                            'balance': balance,  # 🔧 4维度金额解析需要
                            'dr_cr_flag': '收' if amount >= 0 else '支',
                            'currency': 'CNY',
                            'transaction_method': '邮储银行交易',
                            'bank_name': '中国邮政储蓄银行'
                        }
                        
                        sample_transactions.append(transaction)
                        transaction_count += 1
                        
                    except Exception as e:
                        logger.debug(f"跳过第{idx+1}行: {str(e)}")
                        continue
                
                return {
                    'accounts': sample_accounts,
                    'transactions': sample_transactions[:limit],
                    'metadata': {
                        'sample_size': len(sample_transactions),
                        'evaluation_mode': 'extract_sample',
                        'plugin_name': self.name,
                        'bank_name': '中国邮政储蓄银行'
                    }
                }
                
            except Exception as e:
                logger.error(f"邮储银行Format3解析器样本提取失败: {str(e)}")
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0, 'error': str(e)}}
            
        except Exception as e:
            logger.error(f"邮储银行Format3解析器extract_sample方法失败: {str(e)}")
            return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0, 'error': str(e)}}

# 为插件容器提供标准的Plugin类别名
Plugin = PSBCFormat3Parser