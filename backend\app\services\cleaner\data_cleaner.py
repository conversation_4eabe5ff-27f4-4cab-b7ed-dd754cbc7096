"""
数据清洗器模块
用于对原始交易数据进行清洗和标准化处理
"""
import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import json

from sqlalchemy.orm import Session
from ...models import RawTransaction, Transaction
from .cleaner_rules import CleanerRule, DateTimeRule, AmountRule, TextRule

logger = logging.getLogger(__name__)

class DataCleaner:
    """
    数据清洗器
    负责对原始交易数据进行清洗和标准化处理
    """
    
    def __init__(self):
        """
        初始化数据清洗器
        """
        self.rules: List[CleanerRule] = []
        self._init_default_rules()
    
    def _init_default_rules(self):
        """
        初始化默认清洗规则
        """
        # 日期时间处理规则
        self.rules.append(DateTimeRule(
            date_field="raw_transaction_date", 
            time_field="raw_transaction_time", 
            target_field="transaction_datetime",
            default_time="00:00:00"
        ))
        
        # 金额处理规则
        self.rules.append(AmountRule(
            debit_field="raw_amount_debit",
            credit_field="raw_amount_credit",
            single_field="raw_amount_single",
            sign_field="raw_sign_keyword",
            target_field="transaction_amount",
            dr_cr_field="dr_cr_flag",
            debit_flag="支",
            credit_flag="收"
        ))
        
        # 文本处理规则 - 交易对手信息
        self.rules.append(TextRule(
            fields_mapping={
                "raw_counterparty_account": "counterparty_account_number",
                "raw_counterparty_name": "counterparty_account_name",
                "raw_counterparty_bank": "counterparty_bank_name"
            }
        ))
        
        # 文本处理规则 - 备注信息
        self.rules.append(TextRule(
            fields_mapping={
                "raw_remarks": "remarks"
            }
        ))
        
        # 按优先级排序
        self.rules.sort(key=lambda x: x.priority, reverse=True)
    
    def add_rule(self, rule: CleanerRule) -> None:
        """
        添加清洗规则
        
        Args:
            rule: 清洗规则实例
        """
        self.rules.append(rule)
        # 按优先级重新排序
        self.rules.sort(key=lambda x: x.priority, reverse=True)
    
    def clean_transaction(self, raw_transaction: Union[Dict[str, Any], RawTransaction]) -> Dict[str, Any]:
        """
        清洗单条交易记录
        
        Args:
            raw_transaction: 原始交易数据，可以是字典或RawTransaction实例
            
        Returns:
            Dict[str, Any]: 清洗后的交易数据
        """
        # 将RawTransaction转换为字典
        if isinstance(raw_transaction, RawTransaction):
            raw_data = {
                "raw_transaction_id": raw_transaction.raw_transaction_id,
                "account_id": raw_transaction.account_id,
                "project_id": raw_transaction.project_id,
                "person_name": raw_transaction.person_name,
                "bank_name": raw_transaction.bank_name,
                "account_name": raw_transaction.account_name,
                "card_number": raw_transaction.card_number,
                "raw_transaction_date": raw_transaction.raw_transaction_date,
                "raw_transaction_time": raw_transaction.raw_transaction_time,
                "raw_transaction_method": raw_transaction.raw_transaction_method,
                "raw_amount_debit": raw_transaction.raw_amount_debit,
                "raw_amount_credit": raw_transaction.raw_amount_credit,
                "raw_amount_single": raw_transaction.raw_amount_single,
                "raw_sign_keyword": raw_transaction.raw_sign_keyword,
                "raw_balance": raw_transaction.raw_balance,
                "raw_counterparty_account": raw_transaction.raw_counterparty_account,
                "raw_counterparty_name": raw_transaction.raw_counterparty_name,
                "raw_counterparty_bank": raw_transaction.raw_counterparty_bank,
                "raw_remarks": raw_transaction.raw_remarks,
                "raw_other_fields": raw_transaction.raw_other_fields
            }
        else:
            raw_data = raw_transaction.copy()
        
        # 初始化结果，包含基本信息
        result = {
            "raw_transaction_id": raw_data.get("raw_transaction_id"),
            "account_id": raw_data.get("account_id"),
            "project_id": raw_data.get("project_id"),
            "person_name": raw_data.get("person_name"),
            "bank_name": raw_data.get("bank_name"),
            "account_name": raw_data.get("account_name"),
            "card_number": raw_data.get("card_number"),
            "original_currency": "CNY",  # 默认币种
            "raw_data_snapshot": raw_data.get("raw_other_fields", {})
        }
        
        # 记录应用的规则
        applied_rules = []
        
        # 应用所有适用的规则
        for rule in self.rules:
            if rule.should_apply(raw_data):
                try:
                    # 应用规则，更新结果
                    cleaned_data = rule.clean(raw_data)
                    result.update(cleaned_data)
                    
                    # 记录已应用的规则
                    applied_rules.append(rule.to_dict())
                except Exception as e:
                    logger.error(f"应用规则 {rule.name} 时出错: {str(e)}")
        
        # 记录清洗规则和时间
        result["cleaning_rules_applied"] = applied_rules
        result["cleaning_timestamp"] = datetime.utcnow().isoformat()
        
        # 尝试处理余额
        self._process_balance(raw_data, result)
        
        # 尝试处理交易方式
        self._process_transaction_method(raw_data, result)
        
        return result
    
    def _process_balance(self, raw_data: Dict[str, Any], result: Dict[str, Any]) -> None:
        """
        处理余额字段
        
        Args:
            raw_data: 原始数据
            result: 结果数据
        """
        raw_balance = raw_data.get("raw_balance")
        if raw_balance:
            try:
                # 清洗余额
                clean_balance = str(raw_balance).replace(',', '').replace(' ', '')
                clean_balance = clean_balance.replace('¥', '').replace('￥', '')
                result["balance"] = float(clean_balance)
            except (ValueError, TypeError):
                logger.warning(f"无法解析余额: {raw_balance}")
    
    def _process_transaction_method(self, raw_data: Dict[str, Any], result: Dict[str, Any]) -> None:
        """
        处理交易方式字段
        
        Args:
            raw_data: 原始数据
            result: 结果数据
        """
        method = raw_data.get("raw_transaction_method")
        if method and isinstance(method, str):
            result["transaction_method"] = method.strip()
    
    def clean_batch(self, raw_transactions: List[Union[Dict[str, Any], RawTransaction]]) -> List[Dict[str, Any]]:
        """
        批量清洗交易记录
        
        Args:
            raw_transactions: 原始交易数据列表
            
        Returns:
            List[Dict[str, Any]]: 清洗后的交易数据列表
        """
        return [self.clean_transaction(raw_tx) for raw_tx in raw_transactions]
    
    def process_and_save(self, db: Session, raw_transaction_ids: List[str]) -> int:
        """
        处理并保存数据到清洗后的交易表
        
        Args:
            db: 数据库会话
            raw_transaction_ids: 要处理的原始交易ID列表
            
        Returns:
            int: 成功处理的记录数
        """
        # 查询所有指定的原始交易
        raw_transactions = db.query(RawTransaction).filter(
            RawTransaction.raw_transaction_id.in_(raw_transaction_ids)
        ).all()
        
        processed_count = 0
        
        for raw_tx in raw_transactions:
            # 检查是否已有对应的清洗记录
            existing = db.query(Transaction).filter(
                Transaction.raw_transaction_id == raw_tx.raw_transaction_id
            ).first()
            
            if existing:
                logger.info(f"原始交易 {raw_tx.raw_transaction_id} 已有清洗记录，跳过")
                continue
            
            # 清洗数据
            cleaned_data = self.clean_transaction(raw_tx)
            
            # 创建新的Transaction记录
            transaction = Transaction(
                transaction_id=cleaned_data.get("transaction_id"),  # 可能为None，让ORM生成
                raw_transaction_id=raw_tx.raw_transaction_id,
                account_id=raw_tx.account_id,
                project_id=raw_tx.project_id,
                person_name=raw_tx.person_name,
                bank_name=raw_tx.bank_name,
                account_name=raw_tx.account_name,
                card_number=raw_tx.card_number,
                transaction_datetime=cleaned_data.get("transaction_datetime"),
                transaction_method=cleaned_data.get("transaction_method"),
                transaction_amount=cleaned_data.get("transaction_amount"),
                balance=cleaned_data.get("balance"),
                dr_cr_flag=cleaned_data.get("dr_cr_flag"),
                counterparty_account_number=cleaned_data.get("counterparty_account_number"),
                counterparty_account_name=cleaned_data.get("counterparty_account_name"),
                counterparty_bank_name=cleaned_data.get("counterparty_bank_name"),
                remarks=cleaned_data.get("remarks"),
                original_currency=cleaned_data.get("original_currency", "CNY"),
                raw_data_snapshot=cleaned_data.get("raw_data_snapshot"),
                cleaning_rules_applied=cleaned_data.get("cleaning_rules_applied"),
                cleaning_timestamp=cleaned_data.get("cleaning_timestamp")
            )
            
            # 保存到数据库
            try:
                db.add(transaction)
                processed_count += 1
            except Exception as e:
                logger.error(f"保存清洗后的交易记录时出错: {str(e)}")
                db.rollback()
        
        # 提交事务
        try:
            db.commit()
            logger.info(f"成功处理并保存 {processed_count} 条交易记录")
        except Exception as e:
            logger.error(f"提交事务时出错: {str(e)}")
            db.rollback()
            processed_count = 0
        
        return processed_count 