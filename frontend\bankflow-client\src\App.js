import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Layout, ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import './App.css';
import { productionSecurityCheck, logEnvironmentInfo } from './config/environment';



// 导入认证相关组件
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import DevAutoLogin from './components/DevAutoLogin';
import DevToolbar from './components/DevToolbar';
import Login from './pages/Login';

// 导入布局组件
import AppHeader from './components/layout/AppHeader';
import AppSider from './components/layout/AppSider';

// 导入主应用层级页面
import ProjectList from './pages/projects/ProjectList';
import SystemSettings from './pages/settings/SystemSettings';

// 导入项目工作区页面
import ProjectDashboard from './pages/projects/ProjectDashboard';

// 案情简要模块
import CaseBriefClues from './pages/projects/caseBrief/CaseBriefClues';
import CaseBriefSubject from './pages/projects/caseBrief/CaseBriefSubject';
import CaseBriefRelatedPersons from './pages/projects/caseBrief/CaseBriefRelatedPersons';
import CaseBriefRelatedUnits from './pages/projects/caseBrief/CaseBriefRelatedUnits';
import CaseBriefAssets from './pages/projects/caseBrief/CaseBriefAssets';
import CaseBriefRelationship from './pages/projects/caseBrief/CaseBriefRelationship';

// 银行流水模块
import BankStatementsImport from './pages/projects/bankStatements/BankStatementsImport';
import BankStatementsClean from './pages/projects/bankStatements/BankStatementsClean';
import BankStatementsParsers from './pages/projects/bankStatements/BankStatementsParsers';
import BankStatementsQuery from './pages/projects/bankStatements/BankStatementsQuery';
import BankStatementsExport from './pages/projects/bankStatements/BankStatementsExport';

// 银行流水分析模块
import AnalysisTactics from './pages/projects/analysis/AnalysisTactics';
import AnalysisAI from './pages/projects/analysis/AnalysisAI';

// 谈话笔录模块
import Transcripts from './pages/projects/transcripts/Transcripts';

// 全局智能查询模块
import AIQuery from './pages/projects/aiQuery/AIQuery';

// 调试工具已清理

const { Content } = Layout;

/**
 * 错误边界组件 - 防止 stagewise 错误影响整个应用
 */
class StagewiseErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.warn('Stagewise工具栏错误已隔离:', error.message);
  }

  render() {
    if (this.state.hasError) {
      return null; // 发生错误时不渲染任何内容
    }

    return this.props.children;
  }
}

// 全局单例标志，防止多个 stagewise 实例
let stagewise_initialized = false;

/**
 * Stagewise工具栏包装组件 - 单例安全版本
 */
const StagewiseToolbarWrapper = () => {
  const [toolbarReady, setToolbarReady] = React.useState(false);
  
  React.useEffect(() => {
    // 只在开发环境下启用
    if (process.env.NODE_ENV !== 'development') {
      return;
    }
    
    // 防止重复初始化
    if (stagewise_initialized) {
      console.warn('Stagewise已经初始化，跳过重复实例');
      return;
    }
    
    // 检查是否已存在 stagewise companion
    const existingCompanion = document.querySelector('[data-stagewise-companion]');
    if (existingCompanion) {
      console.warn('检测到现有的stagewise实例，跳过初始化');
      return;
    }
    
    stagewise_initialized = true;
    setToolbarReady(true);
    
    return () => {
      // 清理时重置标志
      stagewise_initialized = false;
    };
  }, []);

  return null; // 移除了Stagewise工具栏相关代码
};

/**
 * 主应用布局组件
 * 包含头部和侧边栏的布局
 */
const AppLayout = ({ children }) => {
  return (
    <Layout style={{ minHeight: '100vh' }}>
      <AppHeader />
      <Layout>
        <AppSider />
        <Layout style={{ padding: '0 24px 24px' }}>
          <Content
            className="site-layout-background"
            style={{
              padding: 24,
              margin: '16px 0',
              minHeight: 280,
              background: '#fff',
            }}
          >
            {children}
          </Content>
        </Layout>
      </Layout>
    </Layout>
  );
};

/**
 * 主应用组件
 */
function App() {
  // 应用启动时执行环境检查
  useEffect(() => {
    try {
      // 执行生产环境安全检查
      productionSecurityCheck();
      // 输出环境信息
      logEnvironmentInfo();
    } catch (error) {
      console.error('环境检查失败:', error);
      // 在生产环境配置错误时，可以选择阻止应用启动
      // throw error;
    }
  }, []);

  return (
    <ConfigProvider locale={zhCN}>
      <AuthProvider>
        <DevAutoLogin>
          <Router>
            <Routes>
              {/* 登录页面 - 不需要认证 */}
              <Route path="/login" element={<Login />} />
            
            {/* 调试页面已清理 */}
            
            {/* 受保护的路由 - 需要登录 */}
            <Route path="/*" element={
              <ProtectedRoute>
                <AppLayout>
                  <Routes>
                    {/* 重定向根路径到项目列表 */}
                    <Route path="/" element={<Navigate to="/projects" replace />} />
                    
                    {/* 主应用层级路由 */}
                    <Route path="/projects" element={<ProjectList />} />
                    <Route path="/settings" element={<SystemSettings />} />
                    
                    {/* 项目工作区路由 */}
                    <Route path="/projects/:projectId" element={<ProjectDashboard />} />
                    
                    {/* 案情简要模块路由 */}
                    <Route path="/projects/:projectId/case-brief/clues" element={<CaseBriefClues />} />
                    <Route path="/projects/:projectId/case-brief/subject" element={<CaseBriefSubject />} />
                    <Route path="/projects/:projectId/case-brief/related-persons" element={<CaseBriefRelatedPersons />} />
                    <Route path="/projects/:projectId/case-brief/related-units" element={<CaseBriefRelatedUnits />} />
                    <Route path="/projects/:projectId/case-brief/assets" element={<CaseBriefAssets />} />
                    <Route path="/projects/:projectId/case-brief/relationship" element={<CaseBriefRelationship />} />
                    
                    {/* 银行流水模块路由 */}
                    <Route path="/projects/:projectId/bankStatements/import" element={<BankStatementsImport />} />
                    <Route path="/projects/:projectId/bankStatements/clean" element={<BankStatementsClean />} />
                    <Route path="/projects/:projectId/bankStatements/parsers" element={<BankStatementsParsers />} />
                    <Route path="/projects/:projectId/bankStatements/query" element={<BankStatementsQuery />} />
                    <Route path="/projects/:projectId/bankStatements/export" element={<BankStatementsExport />} />
                    
                    {/* 银行流水分析模块路由 */}
                    <Route path="/projects/:projectId/analysis/tactics" element={<AnalysisTactics />} />
                    <Route path="/projects/:projectId/analysis/ai" element={<AnalysisAI />} />
                    
                    {/* 谈话笔录模块路由 */}
                    <Route path="/projects/:projectId/transcripts" element={<Transcripts />} />
                    
                    {/* 全局智能查询模块路由 */}
                    <Route path="/projects/:projectId/aiQuery" element={<AIQuery />} />
                  </Routes>
                </AppLayout>
              </ProtectedRoute>
            } />
                      </Routes>
          </Router>
          <DevToolbar />
          {/* Stagewise 工具栏 - 单例安全版本 */}
          <StagewiseToolbarWrapper />
        </DevAutoLogin>
      </AuthProvider>
    </ConfigProvider>
  );
}

export default App;
