# 解析器开发规范

## 📋 概述

本文档定义了银行流水解析器的开发标准和规范，确保所有解析器的一致性和可维护性。

## 🏗️ 解析器架构

### 1. 目录结构

```
backend/app/services/parser_plugin_system/plugins/
├── {bank_name}_{format}_plugin/
│   ├── __init__.py
│   ├── plugin.py          # 主解析器类
│   ├── config.json        # 插件配置
│   ├── README.md          # 插件说明
│   └── tests/
│       ├── __init__.py
│       ├── test_parser.py # 单元测试
│       └── test_data/     # 测试数据
│           └── sample.xls
```

### 2. 插件基类

所有解析器必须继承 `BaseParserPlugin` 基类：

```python
from app.services.parser_plugin_system.base_plugin import BaseParserPlugin

class YourBankParser(BaseParserPlugin):
    def __init__(self):
        super().__init__()
        self.plugin_id = "your_bank_plugin"
        self.plugin_name = "您的银行解析器"
        self.supported_banks = ["您的银行"]
        self.version = "1.0.0"
    
    def parse_file(self, file_path: str) -> dict:
        \"\"\"解析银行流水文件\"\"\"
        # 实现解析逻辑
        pass
```

## 📝 字段映射标准

### 1. 账户信息字段

**必需字段**（前端期望的字段名）：

```python
account_info = {
    # === 基本信息 ===
    'account_id': str,           # 账户唯一标识
    'holder_name': str,          # 持卡人姓名 ⭐
    'bank_name': str,            # 银行名称
    'account_number': str,       # 账号 ⭐
    'card_number': str,          # 卡号（通常与账号相同）
    'account_type': str,         # 账户类型
    
    # === 金额统计 ===
    'total_inflow': float,       # 总收入 ⭐
    'total_outflow': float,      # 总支出 ⭐
    'net_flow': float,           # 净流水
    'account_balance': float,    # 账户余额 ⭐
    
    # === 交易统计 ===
    'transaction_count': int,    # 交易笔数 ⭐
    'date_range': str,           # 时间范围
    'start_date': str,           # 开始日期
    'end_date': str,             # 结束日期
    
    # === 其他信息 ===
    'bank_branch': str,          # 开户行
    'currency': str,             # 币种
    'sheet_name': str,           # 工作表名称
}
```

**兼容性字段**（保持向后兼容）：

```python
# 同时提供旧字段名以保持兼容性
compatibility_fields = {
    'cardholder_name': holder_name,      # 兼容旧字段名
    'total_income': total_inflow,        # 兼容旧字段名
    'total_expense': total_outflow,      # 兼容旧字段名
    'balance': account_balance,          # 兼容旧字段名
}
```

### 2. 交易记录字段

**必需字段**：

```python
transaction = {
    # === 基本信息 ===
    'transaction_id': str,       # 交易唯一标识
    'holder_name': str,          # 持卡人姓名 ⭐
    'bank_name': str,            # 银行名称 ⭐
    'account_number': str,       # 账号 ⭐
    'card_number': str,          # 卡号
    
    # === 交易信息 ===
    'transaction_date': str,     # 交易日期 (YYYY-MM-DD) ⭐
    'transaction_time': str,     # 交易时间 (HH:MM:SS)
    'transaction_type': str,     # 交易方式 ⭐
    'amount': float,             # 交易金额 ⭐
    'balance': float,            # 交易余额 ⭐
    'direction': str,            # 收支符号 (收/支) ⭐
    
    # === 对方信息 ===
    'counterpart_name': str,     # 对方户名
    'counterpart_account': str,  # 对方账号
    'counterpart_bank': str,     # 对方银行
    
    # === 备注信息 ===
    'remark1': str,              # 备注1
    'remark2': str,              # 备注2
    'remark3': str,              # 备注3
    'summary': str,              # 摘要
}
```

## 🔧 实现规范

### 1. 解析器主类实现

```python
class YourBankParser(BaseParserPlugin):
    def __init__(self):
        super().__init__()
        self.plugin_id = "your_bank_plugin"
        self.plugin_name = "您的银行解析器"
        self.supported_banks = ["您的银行"]
        self.version = "1.0.0"
        self.description = "解析您的银行流水文件"
    
    def parse_file(self, file_path: str) -> dict:
        \"\"\"
        解析银行流水文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            dict: 解析结果
            {
                'success': bool,
                'accounts': List[dict],
                'transactions': List[dict],
                'summary': dict,
                'errors': List[str]
            }
        \"\"\"
        try:
            # 1. 文件验证
            self._validate_file(file_path)
            
            # 2. 读取文件
            workbook = self._read_excel_file(file_path)
            
            # 3. 解析数据
            accounts = []
            all_transactions = []
            
            for sheet_name in workbook.sheet_names:
                sheet_data = self._parse_sheet(workbook[sheet_name], sheet_name)
                if sheet_data:
                    accounts.extend(sheet_data['accounts'])
                    all_transactions.extend(sheet_data['transactions'])
            
            # 4. 数据验证
            self._validate_parsed_data(accounts, all_transactions)
            
            # 5. 返回结果
            return {
                'success': True,
                'accounts': accounts,
                'transactions': all_transactions,
                'summary': self._generate_summary(accounts, all_transactions),
                'errors': []
            }
            
        except Exception as e:
            logger.error(f"解析失败: {str(e)}")
            return {
                'success': False,
                'accounts': [],
                'transactions': [],
                'summary': {},
                'errors': [str(e)]
            }
    
    def _parse_sheet(self, sheet, sheet_name: str) -> dict:
        \"\"\"解析单个工作表\"\"\"
        # 1. 解析表头信息
        header_info = self._parse_header(sheet)
        
        # 2. 解析交易记录
        transactions = self._parse_transactions(sheet, header_info)
        
        # 3. 生成账户信息
        account = self._generate_account_info(header_info, transactions, sheet_name)
        
        return {
            'accounts': [account],
            'transactions': transactions
        }
    
    def _generate_account_info(self, header_info: dict, transactions: list, sheet_name: str) -> dict:
        \"\"\"生成账户信息\"\"\"
        # 计算统计数据
        total_income = sum(t['amount'] for t in transactions if t['direction'] == '收')
        total_expense = sum(t['amount'] for t in transactions if t['direction'] == '支')
        net_flow = total_income - total_expense
        
        # 计算账户余额（取最后一条交易的余额）
        account_balance = 0.0
        if transactions:
            sorted_transactions = sorted(transactions, key=lambda x: x.get('transaction_date', ''))
            last_transaction = sorted_transactions[-1]
            account_balance = last_transaction.get('balance', 0.0)
        
        # 时间范围
        dates = [t['transaction_date'] for t in transactions if t['transaction_date']]
        start_date = min(dates) if dates else "未知"
        end_date = max(dates) if dates else "未知"
        date_range = f"{start_date} 至 {end_date}" if dates else "未知"
        
        # 生成账户ID
        account_id = f"{header_info.get('account_number', '')}_{sheet_name}"
        
        # ✅ 标准字段映射
        account = {
            # === 前端期望的字段名 ===
            'account_id': account_id,
            'holder_name': header_info.get('cardholder_name', ''),
            'bank_name': header_info.get('bank_name', '您的银行'),
            'account_number': header_info.get('account_number', ''),
            'card_number': header_info.get('card_number', header_info.get('account_number', '')),
            'account_type': header_info.get('account_type', '个人账户'),
            'total_inflow': float(total_income),
            'total_outflow': float(total_expense),
            'net_flow': float(net_flow),
            'account_balance': float(account_balance),
            'transaction_count': len(transactions),
            'date_range': date_range,
            'start_date': start_date,
            'end_date': end_date,
            'bank_branch': header_info.get('bank_branch', ''),
            'currency': '人民币',
            'sheet_name': sheet_name,
            
            # === 保持向后兼容性 ===
            'cardholder_name': header_info.get('cardholder_name', ''),
            'total_income': float(total_income),
            'total_expense': float(total_expense),
            'balance': float(account_balance),
        }
        
        return account
```

### 2. 数据验证

```python
def _validate_parsed_data(self, accounts: list, transactions: list):
    \"\"\"验证解析后的数据\"\"\"
    
    # 验证账户数据
    for account in accounts:
        required_fields = [
            'holder_name', 'account_number', 'total_inflow', 
            'total_outflow', 'account_balance', 'transaction_count'
        ]
        
        for field in required_fields:
            if field not in account:
                raise ValueError(f"账户缺少必需字段: {field}")
        
        # 验证数据类型
        if not isinstance(account['total_inflow'], (int, float)):
            raise ValueError("total_inflow 必须是数字类型")
        if not isinstance(account['total_outflow'], (int, float)):
            raise ValueError("total_outflow 必须是数字类型")
        if not isinstance(account['account_balance'], (int, float)):
            raise ValueError("account_balance 必须是数字类型")
    
    # 验证交易数据
    for transaction in transactions:
        required_fields = [
            'holder_name', 'transaction_date', 'transaction_type',
            'amount', 'balance', 'direction'
        ]
        
        for field in required_fields:
            if field not in transaction:
                raise ValueError(f"交易记录缺少必需字段: {field}")
        
        # 验证收支符号
        if transaction['direction'] not in ['收', '支']:
            raise ValueError(f"无效的收支符号: {transaction['direction']}")
```

### 3. 错误处理

```python
def _safe_parse_amount(self, value) -> float:
    \"\"\"安全解析金额\"\"\"
    if value is None or value == '':
        return 0.0
    
    try:
        # 移除货币符号和逗号
        if isinstance(value, str):
            value = value.replace('¥', '').replace(',', '').strip()
        
        return float(value)
    except (ValueError, TypeError):
        logger.warning(f"无法解析金额: {value}")
        return 0.0

def _safe_parse_date(self, value) -> str:
    \"\"\"安全解析日期\"\"\"
    if value is None or value == '':
        return ''
    
    try:
        if isinstance(value, datetime):
            return value.strftime('%Y-%m-%d')
        elif isinstance(value, str):
            # 尝试多种日期格式
            for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%Y.%m.%d']:
                try:
                    date_obj = datetime.strptime(value, fmt)
                    return date_obj.strftime('%Y-%m-%d')
                except ValueError:
                    continue
        
        return str(value)
    except Exception:
        logger.warning(f"无法解析日期: {value}")
        return ''
```

## 🧪 测试规范

### 1. 单元测试

```python
import unittest
from your_bank_plugin.plugin import YourBankParser

class TestYourBankParser(unittest.TestCase):
    def setUp(self):
        self.parser = YourBankParser()
        self.test_file = "tests/test_data/sample.xls"
    
    def test_parse_file_success(self):
        \"\"\"测试文件解析成功\"\"\"
        result = self.parser.parse_file(self.test_file)
        
        self.assertTrue(result['success'])
        self.assertGreater(len(result['accounts']), 0)
        self.assertGreater(len(result['transactions']), 0)
    
    def test_account_fields(self):
        \"\"\"测试账户字段完整性\"\"\"
        result = self.parser.parse_file(self.test_file)
        account = result['accounts'][0]
        
        # 验证必需字段
        required_fields = [
            'holder_name', 'account_number', 'total_inflow',
            'total_outflow', 'account_balance', 'transaction_count'
        ]
        
        for field in required_fields:
            self.assertIn(field, account)
            self.assertIsNotNone(account[field])
    
    def test_transaction_fields(self):
        \"\"\"测试交易字段完整性\"\"\"
        result = self.parser.parse_file(self.test_file)
        transaction = result['transactions'][0]
        
        # 验证必需字段
        required_fields = [
            'holder_name', 'transaction_date', 'transaction_type',
            'amount', 'balance', 'direction'
        ]
        
        for field in required_fields:
            self.assertIn(field, transaction)
    
    def test_data_types(self):
        \"\"\"测试数据类型\"\"\"
        result = self.parser.parse_file(self.test_file)
        account = result['accounts'][0]
        
        self.assertIsInstance(account['total_inflow'], (int, float))
        self.assertIsInstance(account['total_outflow'], (int, float))
        self.assertIsInstance(account['account_balance'], (int, float))
        self.assertIsInstance(account['transaction_count'], int)
    
    def test_account_balance_calculation(self):
        \"\"\"测试账户余额计算\"\"\"
        result = self.parser.parse_file(self.test_file)
        account = result['accounts'][0]
        transactions = result['transactions']
        
        # 账户余额应该等于最后一条交易的余额
        if transactions:
            sorted_transactions = sorted(transactions, key=lambda x: x['transaction_date'])
            last_balance = sorted_transactions[-1]['balance']
            self.assertEqual(account['account_balance'], last_balance)
```

### 2. 集成测试

```python
def test_api_integration():
    \"\"\"测试API集成\"\"\"
    import requests
    
    with open("tests/test_data/sample.xls", 'rb') as f:
        files = {'file': ('sample.xls', f, 'application/vnd.ms-excel')}
        data = {
            'bank_name': '您的银行',
            'template_id': 'your_bank_plugin'
        }
        
        response = requests.post(
            "http://127.0.0.1:8000/api/parser/analyze",
            files=files,
            data=data
        )
        
        assert response.status_code == 200
        result = response.json()
        
        # 验证返回结构
        assert 'parse_result' in result
        assert 'accounts' in result['parse_result']
        assert 'transactions' in result['parse_result']
        
        # 验证账户余额字段
        account = result['parse_result']['accounts'][0]
        assert 'account_balance' in account
        assert isinstance(account['account_balance'], (int, float))
```

## 📋 开发检查清单

### 开发前检查
- [ ] 确定银行名称和格式标识
- [ ] 分析文件结构和字段映射
- [ ] 准备测试数据文件
- [ ] 创建插件目录结构

### 开发中检查
- [ ] 继承 BaseParserPlugin 基类
- [ ] 实现所有必需方法
- [ ] 使用标准字段映射
- [ ] 添加数据验证逻辑
- [ ] 实现错误处理
- [ ] 添加详细日志

### 开发后检查
- [ ] 编写单元测试
- [ ] 运行集成测试
- [ ] 验证字段映射完整性
- [ ] 测试错误处理
- [ ] 更新文档

### 部署前检查
- [ ] 所有测试通过
- [ ] 代码审查完成
- [ ] 文档更新完整
- [ ] 性能测试通过

## 📚 相关文档

- [解析器修复经验总结](./解析器开发修复经验总结.md)
- [字段映射标准](./字段映射标准.md)
- [测试验证流程](./测试验证流程.md)
- [API接口文档](./API接口文档.md)

---

**更新时间**: 2025-01-04  
**版本**: v1.0  
**维护者**: 开发团队