import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Card, Table, Button, Space, Modal, Form, Input, message, Upload, Tag } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, UploadOutlined, RobotOutlined, FileImageOutlined, FilePdfOutlined } from '@ant-design/icons';
import { clueAPI } from '../../../services/api';

/**
 * 问题线索管理组件
 * 支持线索录入、附件上传、OCR自动识别等功能
 */
const CaseBriefClues = () => {
  const { projectId } = useParams();
  const [clues, setClues] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editingId, setEditingId] = useState(null);
  const [fileList, setFileList] = useState([]);

  // 组件挂载时获取数据
  useEffect(() => {
    if (projectId) {
      fetchClues();
    }
  }, [projectId]);

  // 获取问题线索列表
  const fetchClues = async () => {
    try {
      setLoading(true);
      const response = await clueAPI.getClues(projectId);
      
      // 直接使用返回的数组数据，因为新的API直接返回线索列表
      const formattedClues = response.data.map(clue => ({
        id: clue.clue_id,
        clueNumber: clue.clue_number,
        subjectInfo: {
          name: clue.subject_name,
          position: clue.subject_position
        },
        source: clue.source,
        content: clue.content,
        attachments: clue.attachments || [],
        createTime: new Date(clue.created_at).toLocaleString('zh-CN'),
        updateTime: clue.updated_at ? new Date(clue.updated_at).toLocaleString('zh-CN') : null
      }));
      setClues(formattedClues);
      message.success('获取问题线索成功');
    } catch (error) {
      console.error('获取问题线索出错:', error);
      message.error('获取问题线索失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '线索编号',
      dataIndex: 'clueNumber',
      key: 'clueNumber',
      width: 120,
      render: (text) => <Tag color="blue">{text}</Tag>
    },
    {
      title: '被反映人及岗位',
      dataIndex: 'subjectInfo',
      key: 'subjectInfo',
      width: 200,
      render: (text) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text?.name || '未填写'}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>{text?.position || '未填写'}</div>
        </div>
      )
    },
    {
      title: '线索来源',
      dataIndex: 'source',
      key: 'source',
      width: 150,
    },
    {
      title: <div style={{ textAlign: 'center' }}>线索内容</div>,
      dataIndex: 'content',
      key: 'content',
      render: (text) => (
        <div style={{ 
          wordWrap: 'break-word', 
          wordBreak: 'break-all',
          whiteSpace: 'pre-wrap',
        }}>
          {text || '未填写'}
        </div>
      ),
    },
    {
      title: '附件',
      dataIndex: 'attachments',
      key: 'attachments',
      width: 100,
      render: (attachments) => (
        <div>
          {attachments && attachments.length > 0 ? (
            <Space>
              {attachments.map((file, index) => (
                <span key={index}>
                  {file.type === 'pdf' ? <FilePdfOutlined style={{ color: '#ff4d4f' }} /> : <FileImageOutlined style={{ color: '#52c41a' }} />}
                </span>
              ))}
              <Tag color="green">{attachments.length}个</Tag>
            </Space>
          ) : (
            <Tag color="default">无</Tag>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="text" 
            size="small"
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record)}
          />
          <Button 
            type="text" 
            size="small"
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDelete(record.id)}
          />
        </Space>
      ),
    },
  ];

  // 处理添加或编辑线索
  const handleAddOrEdit = () => {
    setIsModalVisible(true);
    if (editingId === null) {
      form.resetFields();
      setFileList([]);
    }
  };

  // 处理编辑线索
  const handleEdit = (record) => {
    setEditingId(record.id);
    form.setFieldsValue({
      clueNumber: record.clueNumber,
      subjectName: record.subjectInfo?.name || '',
      subjectPosition: record.subjectInfo?.position || '',
      source: record.source,
      content: record.content,
    });
    // 设置附件列表
    if (record.attachments && record.attachments.length > 0) {
      setFileList(record.attachments.map((file, index) => ({
        uid: index,
        name: file.name,
        status: 'done',
        type: file.type,
        url: file.url,
        size: file.size
      })));
    } else {
      setFileList([]);
    }
    setIsModalVisible(true);
  };

  // 处理删除线索
  const handleDelete = async (id) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条线索吗？删除后无法恢复。',
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await clueAPI.deleteClue(id);
          message.success('线索已删除');
          // 重新获取数据
          await fetchClues();
        } catch (error) {
          console.error('删除线索失败:', error);
          message.error('删除失败，请重试');
        }
      },
    });
  };

  // 处理模态框确认
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      
      const clueData = {
        project_id: projectId,
        clue_number: values.clueNumber,
        subject_name: values.subjectName,
        subject_position: values.subjectPosition,
        source: values.source,
        content: values.content,
        attachments: fileList.map(file => ({
          name: file.name,
          type: file.name?.toLowerCase().endsWith('.pdf') ? 'pdf' : 'image',
          url: file.url || file.response?.url,
          size: file.size
        }))
      };

      if (editingId === null) {
        // 新增线索
        await clueAPI.createClue(clueData);
        message.success('线索已添加');
      } else {
        // 更新线索
        const updateData = {
          clue_number: values.clueNumber,
          subject_name: values.subjectName,
          subject_position: values.subjectPosition,
          source: values.source,
          content: values.content,
          attachments: fileList.map(file => ({
            name: file.name,
            type: file.name?.toLowerCase().endsWith('.pdf') ? 'pdf' : 'image',
            url: file.url || file.response?.url,
            size: file.size
          }))
        };
        await clueAPI.updateClue(editingId, updateData);
        message.success('线索已更新');
      }

      setIsModalVisible(false);
      setEditingId(null);
      form.resetFields();
      setFileList([]);
      // 重新获取数据
      await fetchClues();
    } catch (error) {
      console.error('表单验证或提交失败:', error);
      message.error('操作失败，请重试');
    }
  };

  // 处理模态框取消
  const handleCancel = () => {
    setIsModalVisible(false);
    setEditingId(null);
    form.resetFields();
    setFileList([]);
  };

  // 处理文件上传
  const handleUpload = {
    name: 'file',
    multiple: true,
    fileList: fileList,
    beforeUpload: (file) => {
      const isPdfOrImage = file.type === 'application/pdf' || file.type.startsWith('image/');
      if (!isPdfOrImage) {
        message.error('只能上传PDF或图片文件！');
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('文件大小不能超过10MB！');
        return false;
      }
      return false; // 阻止自动上传，手动处理
    },
    onChange: (info) => {
      setFileList(info.fileList);
    },
    onRemove: (file) => {
      setFileList(fileList.filter(item => item.uid !== file.uid));
    }
  };

  // 处理OCR自动录入（预留功能）
  const handleOCRInput = () => {
    message.info('OCR自动录入功能正在开发中，敬请期待！');
    // TODO: 实现OCR功能
    // 1. 选择已上传的PDF或图片文件
    // 2. 调用OCR API解析文件内容
    // 3. 自动填充表单字段
  };

  return (
    <div>
      <Card 
        title="问题线索管理" 
        extra={
          <Space>
            <Button 
              icon={<RobotOutlined />} 
              onClick={() => message.info('OCR识别功能开发中...')}
            >
              OCR识别
            </Button>
            <Button 
              loading={loading}
              onClick={fetchClues}
            >
              刷新
            </Button>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={handleAddOrEdit}
            >
              添加线索
            </Button>
          </Space>
        }
      >
        <Table 
          columns={columns} 
          dataSource={clues} 
          rowKey="id" 
          loading={loading}
          pagination={{ 
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }}
          scroll={{ x: 1000 }}
        />
      </Card>

      <Modal
        title={editingId === null ? "新增问题线索" : "编辑问题线索"}
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          requiredMark={false}
        >
          <Form.Item
            name="clueNumber"
            label="线索编号"
            rules={[{ required: true, message: '请输入线索编号（上级提供）' }]}
            extra="请输入上级部门提供的线索编号，不能自己编制"
          >
            <Input placeholder="请输入上级提供的线索编号" />
          </Form.Item>
          
          <Form.Item label="被反映人及岗位（职务）">
            <Input.Group compact>
              <Form.Item
                name="subjectName"
                style={{ width: '50%', marginBottom: 0 }}
                rules={[{ required: true, message: '请输入被反映人姓名' }]}
              >
                <Input placeholder="被反映人姓名" />
              </Form.Item>
              <Form.Item
                name="subjectPosition"
                style={{ width: '50%', marginBottom: 0 }}
                rules={[{ required: true, message: '请输入岗位职务' }]}
              >
                <Input placeholder="岗位职务" />
              </Form.Item>
            </Input.Group>
          </Form.Item>

          <Form.Item
            name="source"
            label="线索来源"
            rules={[{ required: true, message: '请输入线索来源' }]}
          >
            <Input placeholder="如：群众举报、上级转办、巡察发现等" />
          </Form.Item>

          <Form.Item
            name="content"
            label="线索内容"
            rules={[{ required: true, message: '请输入线索内容' }]}
          >
            <Input.TextArea 
              rows={10}
              autoSize={{ minRows: 10, maxRows: 20 }}
              placeholder="请详细描述问题线索的具体内容..."
              showCount
              maxLength={2000}
              style={{ 
                resize: 'vertical',
                minHeight: '200px'
              }}
            />
          </Form.Item>

          <Form.Item
            label="附件上传"
            extra="支持上传PDF文档或JPG/PNG图片，单个文件不超过10MB"
          >
            <Upload.Dragger {...handleUpload}>
              <p className="ant-upload-drag-icon">
                <UploadOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p className="ant-upload-hint">
                支持PDF文档和图片格式（JPG、PNG等）
              </p>
            </Upload.Dragger>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CaseBriefClues; 