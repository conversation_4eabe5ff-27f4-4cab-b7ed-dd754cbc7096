import React, { useEffect, useState } from 'react';
import { Row, Col, Card, Statistic, Button, Spin } from 'antd';
import { ProjectOutlined, BankOutlined, TeamOutlined, FileOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

/**
 * 仪表盘页面组件
 * 
 * @returns {JSX.Element} 仪表盘页面
 */
const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    projectCount: 0,
    bankCount: 0,
    accountCount: 0,
    templateCount: 0
  });
  const navigate = useNavigate();

  useEffect(() => {
    // 模拟API数据加载
    const timer = setTimeout(() => {
      setStats({
        projectCount: 5,
        bankCount: 17,
        accountCount: 23,
        templateCount: 8
      });
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
    // 实际项目中这里应该调用API获取统计数据
    // fetchDashboardStats().then(data => {
    //   setStats(data);
    //   setLoading(false);
    // });
  }, []);

  return (
    <div>
      <h2>系统概览</h2>
      
      <Spin spinning={loading}>
        <Row gutter={[16, 16]} style={{ marginTop: 20 }}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="项目数量"
                value={stats.projectCount}
                prefix={<ProjectOutlined />}
              />
              <Button 
                type="primary" 
                style={{ marginTop: 16 }} 
                onClick={() => navigate('/projects')}
              >
                查看项目
              </Button>
            </Card>
          </Col>
          
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="银行数量"
                value={stats.bankCount}
                prefix={<BankOutlined />}
              />
              <Button 
                type="primary" 
                style={{ marginTop: 16 }} 
                onClick={() => navigate('/settings/banks')}
              >
                管理银行
              </Button>
            </Card>
          </Col>
          
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="账户数量"
                value={stats.accountCount}
                prefix={<TeamOutlined />}
              />
              <Button 
                style={{ marginTop: 16 }} 
                disabled
              >
                账户查询
              </Button>
            </Card>
          </Col>
          
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="解析模板"
                value={stats.templateCount}
                prefix={<FileOutlined />}
              />
              <Button 
                type="primary" 
                style={{ marginTop: 16 }} 
                onClick={() => navigate('/settings/templates')}
              >
                管理模板
              </Button>
            </Card>
          </Col>
        </Row>
      </Spin>
      
      <Row style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="快速操作">
            <Button 
              type="primary" 
              icon={<ProjectOutlined />} 
              size="large" 
              style={{ marginRight: 16 }}
              onClick={() => navigate('/projects')}
            >
              新建项目
            </Button>
            <Button 
              type="default" 
              icon={<FileOutlined />} 
              size="large"
              onClick={() => navigate('/import')}
            >
              导入数据
            </Button>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard; 