"""
北部湾银行Format1解析器插件
支持黄宪文1.xls格式的银行流水解析
"""

import pandas as pd
import time
import os
import traceback
from datetime import datetime
from typing import Dict, Any, List, Optional
import logging
import json
from pathlib import Path

# 导入基础插件接口
try:
    from ...core.plugin_interface import BasePlugin
except ImportError:
    # 如果在测试环境中，创建一个简单的基础类
    class BasePlugin:
        def __init__(self):
            self.name = "default_plugin"
            self.version = "1.0.0"
            self.description = "默认解析器插件"
            self.bank_name = "通用银行"
            self.format_type = "standard"

logger = logging.getLogger(__name__)

class Plugin(BasePlugin):
    """北部湾银行Format1解析器插件"""
    
    def __init__(self, file_path: str = None):
        super().__init__()
        self.name = "beibuwan_format1_plugin"
        self.version = "1.0.0"
        self.description = "北部湾银行Format1解析器插件"
        self.bank_name = "北部湾银行"
        self.format_type = "format1"
        self.start_time = time.time()
        self.error_count = 0
        self.file_path = file_path
        
        # 加载配置
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载插件配置"""
        try:
            config_path = Path(__file__).parent / "config.json"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"无法加载配置文件: {e}")
        
        # 默认配置
        return {
            "parsing_config": {
                "target_sheet": "工作表2",
                "header_row": 3,
                "negative_outflow": True,
                "time_split": True,
                "extract_header_info": True
            },
            "field_mapping": {
                "date_time_field": "交易时间",
                "amount_field": "发生额",
                "balance_field": "交易后余额",
                "counterparty_field": "对方户名",
                "counterparty_account_field": "对方账号",
                "reference_field": "流水",
                "remark_field": "交易备注",
                "summary_field": "交易备注",
                "channel_field": "交易机构"
            }
        }
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取插件元信息"""
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "bank_name": self.bank_name,
            "format_type": self.format_type,
            "supported_formats": ["Excel (.xls)"],
            "confidence_threshold": 0.8,
            "author": "银行流水系统团队",
            "license": "MIT",
            "format_features": {
                "multi_sheet": True,
                "time_format": "YYYY-MM-DD HH:MM:SS",
                "amount_format": "negative_for_outflow",
                "header_info": True,
                "transaction_sheet": "工作表2"
            }
        }
    
    def validate_file(self, file_path: str) -> bool:
        """验证文件是否适用于此解析器"""
        try:
            # 检查文件扩展名
            if not file_path.lower().endswith('.xls'):
                return False
            
            # 检查文件是否可读
            if not os.path.exists(file_path):
                return False
            
            # 检查Excel文件结构
            excel_file = pd.ExcelFile(file_path)
            
            # 检查是否有工作表2
            if "工作表2" not in excel_file.sheet_names:
                return False
            
            # 检查工作表2的基本结构
            df = pd.read_excel(file_path, sheet_name="工作表2", header=3)
            
            # 检查必需列
            required_columns = ["交易时间", "发生额", "交易后余额"]
            if not all(col in df.columns for col in required_columns):
                return False
            
            # 检查数据行数
            if len(df) == 0:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"文件验证失败: {e}")
            return False
    
    def calculate_confidence(self, file_path: str) -> float:
        """计算解析置信度"""
        try:
            if not self.validate_file(file_path):
                return 0.0
            
            confidence = 0.0
            
            # 1. 文件扩展名检查 (10%)
            if file_path.lower().endswith('.xls'):
                confidence += 0.1
            
            # 2. 工作表结构检查 (20%)
            excel_file = pd.ExcelFile(file_path)
            if "工作表2" in excel_file.sheet_names:
                confidence += 0.2
            
            # 3. 列名匹配度检查 (30%)
            df = pd.read_excel(file_path, sheet_name="工作表2", header=3)
            expected_columns = ["交易时间", "流水", "发生额", "对方户名", "交易后余额", "交易备注"]
            matching_columns = sum(1 for col in expected_columns if col in df.columns)
            confidence += (matching_columns / len(expected_columns)) * 0.3
            
            # 4. 时间格式验证 (20%)
            if "交易时间" in df.columns:
                time_samples = df["交易时间"].dropna().head(5)
                valid_time_count = 0
                for sample in time_samples:
                    try:
                        if isinstance(sample, str):
                            # 检查是否包含日期和时间
                            if len(sample.split()) == 2:
                                datetime.strptime(sample, "%Y-%m-%d %H:%M:%S")
                                valid_time_count += 1
                        elif hasattr(sample, 'strftime'):
                            # datetime对象
                            valid_time_count += 1
                    except:
                        continue
                
                if len(time_samples) > 0:
                    confidence += (valid_time_count / len(time_samples)) * 0.2
            
            # 5. 金额格式验证 (20%)
            if "发生额" in df.columns:
                amount_samples = df["发生额"].dropna().head(10)
                valid_amount_count = 0
                has_negative = False
                
                for sample in amount_samples:
                    try:
                        amount = float(sample)
                        valid_amount_count += 1
                        if amount < 0:
                            has_negative = True
                    except:
                        continue
                
                if len(amount_samples) > 0:
                    amount_confidence = (valid_amount_count / len(amount_samples)) * 0.15
                    if has_negative:  # 北部湾银行Format1的特征：有负数
                        amount_confidence += 0.05
                    confidence += amount_confidence
            
            return min(confidence, 1.0)
            
        except Exception as e:
            logger.error(f"置信度计算失败: {e}")
            self.error_count += 1
            return 0.0
    
    def extract_sample(self, limit: int = 10) -> Dict[str, Any]:
        """提取样本数据进行快速评估"""
        try:
            logger.info(f"开始提取北部湾银行Format1文件样本数据: {limit}条")
            
            # 1. 读取Excel文件
            file_path = self.file_path
            if not file_path:
                raise ValueError("未设置文件路径")
            excel_file = pd.ExcelFile(file_path)
            
            # 2. 读取工作表2 - 交易明细（只读取前N条）
            df_transactions = pd.read_excel(file_path, sheet_name="工作表2", header=3, nrows=limit)
            
            # 3. 提取账户信息（从工作表1或表头）
            accounts = self._extract_accounts(file_path, df_transactions)
            
            # 4. 提取交易记录（样本）
            transactions = self._extract_transactions(df_transactions, accounts)
            
            # 5. 构建样本结果
            sample_result = {
                'accounts': accounts,
                'transactions': transactions,
                'metadata': {
                    'total_accounts_found': len(accounts),
                    'total_transactions_found': len(transactions),
                    'sample_size': limit,
                    'parsing_successful': True,
                    'evaluation_mode': 'extract_sample'
                }
            }
            
            logger.info(f"样本数据提取完成: {len(accounts)}个账户, {len(transactions)}条交易")
            return sample_result
            
        except Exception as e:
            logger.error(f"样本数据提取失败: {str(e)}")
            return {
                'accounts': [],
                'transactions': [],
                'metadata': {
                    'total_accounts_found': 0,
                    'total_transactions_found': 0,
                    'sample_size': 0,
                    'parsing_successful': False,
                    'error': str(e)
                }
            }
    
    def parse(self, file_path: str = None) -> Dict[str, Any]:
        """执行解析"""
        try:
            # 使用传入的文件路径或实例的文件路径
            if file_path:
                self.file_path = file_path
            elif not self.file_path:
                raise ValueError("未设置文件路径")
            
            logger.info(f"🔍 开始解析北部湾银行Format1文件: {os.path.basename(self.file_path)}")
            
            # 验证文件
            if not self.validate_file(self.file_path):
                raise ValueError("文件验证失败")
            
            # 1. 读取Excel文件
            excel_file = pd.ExcelFile(self.file_path)
            
            # 2. 读取工作表2 - 交易明细
            df_transactions = pd.read_excel(self.file_path, sheet_name="工作表2", header=3)
            logger.info(f"读取到{len(df_transactions)}条原始交易记录")
            
            # 3. 提取账户信息（从工作表2表头）
            accounts = self._extract_accounts(self.file_path, df_transactions)
            logger.info(f"提取到{len(accounts)}个账户")
            
            # 4. 提取交易记录
            transactions = self._extract_transactions(df_transactions, accounts)
            logger.info(f"处理后得到{len(transactions)}条交易记录")
            
            # 5. 数据验证
            self._validate_data_consistency(accounts, transactions)
            
            # 6. 计算置信度
            confidence = self.calculate_confidence(self.file_path)
            
            result = {
                "success": True,
                "message": f"解析成功，共{len(accounts)}个账户，{len(transactions)}笔交易",
                "accounts": accounts,
                "transactions": transactions,
                "parser_type": self.name,
                "confidence_score": confidence,
                "total_accounts": len(accounts),
                "total_transactions": len(transactions),
                "validation_passed": True,
                "format_features": {
                    "multi_sheet": True,
                    "time_format": "YYYY-MM-DD HH:MM:SS",
                    "amount_format": "negative_for_outflow",
                    "header_info": True
                }
            }
            
            logger.info(f"✅ 北部湾Format1解析完成: {len(accounts)}个账户, {len(transactions)}条交易")
            return result
            
        except Exception as e:
            error_msg = f"北部湾Format1解析失败: {str(e)}"
            logger.error(error_msg)
            logger.error(f"完整错误堆栈:\n{traceback.format_exc()}")
            self.error_count += 1
            
            return {
                "success": False,
                "message": error_msg,
                "accounts": [],
                "transactions": [],
                "parser_type": self.name,
                "confidence_score": 0.0,
                "error_details": str(e),
                "error_trace": traceback.format_exc(),
                "error_type": "parse_error"
            }
    
    def _extract_accounts(self, file_path: str, df_transactions: pd.DataFrame) -> List[Dict[str, Any]]:
        """提取账户信息 - 完全从Excel内容提取，不依赖文件名"""
        accounts = []
        
        try:
            # 初始化默认值
            cardholder_name = "Unknown"
            account_number = "Unknown"
            bank_name = "广西北部湾银行"
            start_date = "未知"
            end_date = "未知"
            
            # 🔧 完全修复：只从Excel文件内容提取持卡人信息
            try:
                # 读取工作表2的表头信息（前5行）
                df_header = pd.read_excel(file_path, sheet_name="工作表2", header=None, nrows=5)
                logger.info(f"读取表头信息成功，形状: {df_header.shape}")
                
                # 第3行包含账户信息：账号 持卡人 时间范围
                if len(df_header) > 2:
                    info_row = df_header.iloc[2]  # 第3行 (索引2)
                    logger.info(f"第3行数据: {dict(enumerate(info_row))}")
                    
                    # 第1列：账号
                    if pd.notna(info_row.iloc[0]):
                        account_number = str(info_row.iloc[0])
                        logger.info(f"提取账号: {account_number}")
                    
                    # 第2列：持卡人姓名 - 关键修复点
                    if len(info_row) > 1 and pd.notna(info_row.iloc[1]):
                        extracted_name = str(info_row.iloc[1]).strip()
                        # 验证提取的姓名是否为有效中文姓名
                        if len(extracted_name) >= 2 and len(extracted_name) <= 4:
                            # 检查是否包含中文字符
                            if any('\u4e00' <= char <= '\u9fff' for char in extracted_name):
                                cardholder_name = extracted_name
                                logger.info(f"从工作表2第3行第2列提取到持卡人姓名: {cardholder_name}")
                            else:
                                logger.warning(f"第3行第2列不是有效中文姓名: {extracted_name}")
                        else:
                            logger.warning(f"第3行第2列长度异常: {extracted_name}")
                    
                    # 第4列：开始时间
                    if len(info_row) > 3 and pd.notna(info_row.iloc[3]):
                        start_date_raw = info_row.iloc[3]
                        if hasattr(start_date_raw, 'strftime'):
                            start_date = start_date_raw.strftime("%Y-%m-%d")
                        else:
                            start_date = str(start_date_raw).split()[0]  # 只取日期部分
                    
                    # 第6列：结束时间
                    if len(info_row) > 5 and pd.notna(info_row.iloc[5]):
                        end_date_raw = info_row.iloc[5]
                        if hasattr(end_date_raw, 'strftime'):
                            end_date = end_date_raw.strftime("%Y-%m-%d")
                        else:
                            end_date = str(end_date_raw).split()[0]  # 只取日期部分
                
                # 🔧 备用方案：如果第3行没有有效持卡人姓名，从明细数据查找
                if cardholder_name == "Unknown" and not df_transactions.empty:
                    logger.info("第3行未找到有效持卡人姓名，从明细数据查找...")
                    if '对方户名' in df_transactions.columns:
                        # 在前20条记录中查找可能的持卡人姓名
                        for idx, row in df_transactions.head(20).iterrows():
                            counterparty = str(row.get('对方户名', '')).strip()
                            # 检查是否为2-4位中文姓名
                            if counterparty and len(counterparty) >= 2 and len(counterparty) <= 4:
                                if all('\u4e00' <= char <= '\u9fff' for char in counterparty):
                                    # 排除明显的机构名称
                                    if not any(keyword in counterparty for keyword in ['银行', '公司', '有限', '股份', '支行', 'ATM']):
                                        cardholder_name = counterparty
                                        logger.info(f"从明细数据中提取到持卡人姓名: {cardholder_name}")
                                        break
                
                # 确认银行名称
                for i, row in df_header.iterrows():
                    for j, cell in enumerate(row):
                        if pd.notna(cell) and isinstance(cell, str):
                            if "广西北部湾银行" in cell:
                                bank_name = "广西北部湾银行"
                                break
                            elif "北部湾银行" in cell:
                                bank_name = "北部湾银行"
                                break
                
                logger.info(f"最终账户信息: 持卡人={cardholder_name}, 账号={account_number}, 银行={bank_name}")
                
            except Exception as e:
                logger.warning(f"从Excel内容提取账户信息失败: {e}")
                # 🔧 关键修复：即使异常也不使用文件名
                cardholder_name = "Unknown"
                logger.warning("无法从Excel文件中提取持卡人姓名，设置为Unknown")
            
            # 使用工作表2的明细数据计算统计信息
            total_inflow = 0.0
            total_outflow = 0.0
            transaction_count = len(df_transactions)
            account_balance = 0.0  # 🔧 新增：账户余额
            
            try:
                if not df_transactions.empty:
                    if '发生额' in df_transactions.columns:
                        amounts = pd.to_numeric(df_transactions['发生额'], errors='coerce').fillna(0)
                        # 正数表示收入，负数表示支出
                        inflow_amounts = amounts[amounts > 0]
                        outflow_amounts = amounts[amounts < 0]
                        
                        total_inflow = float(inflow_amounts.sum())
                        total_outflow = float(abs(outflow_amounts.sum()))
                        transaction_count = len(amounts[amounts != 0])
                        
                        logger.info(f"统计信息: 收入={total_inflow}, 支出={total_outflow}, 交易数={transaction_count}")
                    
                    # 🔧 修复1：从最后一行交易获取账户余额
                    if '交易后余额' in df_transactions.columns:
                        # 按交易时间排序，获取最后一笔交易的余额
                        df_sorted = df_transactions.copy()
                        if '交易时间' in df_sorted.columns:
                            # 去除空值，按交易时间排序
                            df_valid = df_sorted.dropna(subset=['交易时间'])
                            if not df_valid.empty:
                                df_sorted = df_valid.sort_values('交易时间')
                        
                        # 获取最后一行的余额
                        last_balance = df_sorted['交易后余额'].iloc[-1] if not df_sorted.empty else 0
                        if pd.notna(last_balance):
                            account_balance = float(pd.to_numeric(last_balance, errors='coerce') or 0)
                            logger.info(f"从最后一行交易获取账户余额: ¥{account_balance:,.2f}")
                        else:
                            logger.warning("最后一行交易余额为空，使用0作为默认值")
                    else:
                        logger.warning("未找到'交易后余额'字段，无法获取账户余额")
                        
            except Exception as e:
                logger.warning(f"计算统计信息失败: {e}")
            
            # 构建账户信息
            account = {
                "person_name": cardholder_name,
                "bank_name": bank_name,
                "account_name": cardholder_name,
                "account_number": account_number,
                "card_number": account_number,
                "date_range": f"{start_date} 至 {end_date}",
                "start_date": start_date,
                "end_date": end_date,
                "total_inflow": total_inflow,
                "total_outflow": total_outflow,
                "net_flow": total_inflow - total_outflow,
                "transactions_count": transaction_count,
                "account_balance": account_balance,  # 🔧 新增：账户余额字段
                "currency": "CNY",
                "account_type": "个人账户"
            }
            
            accounts.append(account)
            logger.info(f"成功构建账户信息: {account['person_name']}")
            
        except Exception as e:
            logger.error(f"提取账户信息失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
        
        return accounts
    
    def _extract_transactions(self, df: pd.DataFrame, accounts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """提取交易记录"""
        transactions = []
        
        try:
            # 获取账户信息
            account_info = accounts[0] if accounts else {}
            # 🔧 修复2: 使用正确提取的持卡人姓名
            default_cardholder_name = account_info.get("person_name", "未知持卡人")
            
            account_number = account_info.get("account_number", "Unknown")
            bank_name = account_info.get("bank_name", "广西北部湾银行")
            
            # 🔧 修复3: 直接使用传入的df（应该是工作表2的明细数据）
            if df.empty:
                logger.warning("传入的明细数据为空")
                return []
            
            logger.info(f"明细数据列名: {list(df.columns)}")
            sequence_number = 1  # 序号从1开始
            
            for index, row in df.iterrows():
                # 跳过无效行 - 检查交易时间字段
                if pd.isna(row.get("交易时间")):
                    continue
                
                # 处理交易时间 - 拆分为日期和时间
                transaction_date, transaction_time = self._split_datetime(row.get("交易时间"))
                if not transaction_date or transaction_date == "1900-01-01":
                    continue
                
                # 🔧 修复4: 正确处理发生额字段（正数=收入，负数=支出）
                amount = pd.to_numeric(row.get("发生额", 0), errors='coerce')
                if pd.isna(amount) or amount == 0:
                    continue
                
                # 确定交易金额和类型
                if amount < 0:
                    # 负数表示支出
                    transaction_amount = float(abs(amount))
                    dr_cr_flag = "支"
                    transaction_type = "支出"
                else:
                    # 正数表示收入
                    transaction_amount = float(amount)
                    dr_cr_flag = "收"
                    transaction_type = "收入"
                
                # 🔧 修复5: 使用正确的余额字段
                balance_raw = row.get("交易后余额", 0)
                balance = pd.to_numeric(balance_raw, errors='coerce')
                if pd.isna(balance):
                    balance = 0.0
                
                # 处理其他字段
                summary = str(row.get("交易备注", "")).strip()
                if summary.lower() in ["nan", "none", ""]:
                    summary = ""
                
                # 获取对方信息
                counterparty_name = str(row.get("对方户名", "")).strip() or ""
                counterparty_account = str(row.get("对方账号", "")).strip() or ""
                
                # 🔧 修复6: 确保每条交易记录都有持卡人姓名
                cardholder_name = default_cardholder_name
                
                transaction = {
                    "sequence_number": sequence_number,
                    "cardholder_name": cardholder_name,  # 使用正确的持卡人姓名
                    "bank_name": bank_name,
                    "account_number": account_number,
                    "card_number": account_number,
                    "transaction_date": transaction_date,
                    "transaction_time": transaction_time,
                    "transaction_amount": transaction_amount,
                    "balance": float(balance),
                    "dr_cr_flag": dr_cr_flag,
                    "transaction_type": transaction_type,
                    "counterparty_name": counterparty_name,
                    "counterparty_account": counterparty_account,
                    "summary": summary,
                    "remark1": summary,
                    "remark2": "",
                    "channel": str(row.get("交易机构", "")).strip() or "",
                    "reference_number": str(row.get("流水", "")).strip() or "",
                    "currency": "CNY",
                    # 保留原始金额用于调试
                    "original_amount": float(amount)
                }
                
                transactions.append(transaction)
                sequence_number += 1
                
        except Exception as e:
            logger.error(f"提取交易记录失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
        
        logger.info(f"成功提取 {len(transactions)} 条交易记录")
        return transactions
    
    def _split_datetime(self, datetime_value) -> tuple:
        """拆分日期时间"""
        try:
            if pd.isna(datetime_value):
                return "1900-01-01", "00:00:00"
            
            if isinstance(datetime_value, str):
                # 字符串格式：2017-02-06 11:00:00
                parts = datetime_value.split()
                if len(parts) == 2:
                    date_part = parts[0]
                    time_part = parts[1]
                    
                    # 验证并格式化日期
                    try:
                        date_obj = datetime.strptime(date_part, "%Y-%m-%d")
                        formatted_date = date_obj.strftime("%Y-%m-%d")
                    except:
                        # 尝试其他日期格式
                        try:
                            date_obj = datetime.strptime(date_part, "%Y/%m/%d")
                            formatted_date = date_obj.strftime("%Y-%m-%d")
                        except:
                            formatted_date = "1900-01-01"
                    
                    # 验证并格式化时间
                    try:
                        time_obj = datetime.strptime(time_part, "%H:%M:%S")
                        formatted_time = time_obj.strftime("%H:%M:%S")
                    except:
                        try:
                            time_obj = datetime.strptime(time_part, "%H:%M")
                            formatted_time = time_obj.strftime("%H:%M:00")
                        except:
                            formatted_time = "00:00:00"
                    
                    return formatted_date, formatted_time
                else:
                    return "1900-01-01", "00:00:00"
            
            elif hasattr(datetime_value, 'strftime'):
                # datetime对象
                return datetime_value.strftime("%Y-%m-%d"), datetime_value.strftime("%H:%M:%S")
            
            else:
                return "1900-01-01", "00:00:00"
                
        except Exception as e:
            logger.warning(f"时间拆分失败: {e}")
            return "1900-01-01", "00:00:00"
    
    def _validate_data_consistency(self, accounts: List[Dict], transactions: List[Dict]) -> None:
        """验证数据一致性"""
        try:
            if not accounts or not transactions:
                return
            
            account = accounts[0]
            
            # 验证交易笔数
            assert len(transactions) == account["transactions_count"], \
                f"交易笔数不一致: 账户{account['transactions_count']} vs 交易{len(transactions)}"
            
            # 验证金额统计
            actual_inflow = sum(t["transaction_amount"] for t in transactions if t["dr_cr_flag"] == "收")
            actual_outflow = sum(t["transaction_amount"] for t in transactions if t["dr_cr_flag"] == "支")
            
            tolerance = 0.01
            assert abs(actual_inflow - account["total_inflow"]) <= tolerance, \
                f"收入金额不一致: 账户{account['total_inflow']} vs 交易{actual_inflow}"
            
            assert abs(actual_outflow - account["total_outflow"]) <= tolerance, \
                f"支出金额不一致: 账户{account['total_outflow']} vs 交易{actual_outflow}"
            
            logger.info("数据一致性验证通过")
            
        except AssertionError as e:
            logger.error(f"数据一致性验证失败: {e}")
            raise
        except Exception as e:
            logger.warning(f"数据一致性验证异常: {e}")
    
    def handle_error(self, error: Exception) -> None:
        """增强的错误处理"""
        self.error_count += 1
        self.last_error = str(error)
        
        # 根据错误类型分类处理
        if isinstance(error, FileNotFoundError):
            logger.error(f"文件不存在错误: {error}")
        elif isinstance(error, PermissionError):
            logger.error(f"文件权限错误: {error}")
        elif isinstance(error, pd.errors.EmptyDataError):
            logger.error(f"文件数据为空: {error}")
        elif isinstance(error, (ValueError, TypeError)):
            logger.error(f"数据格式错误: {error}")
        else:
            logger.error(f"未知错误: {error}")
            logger.error(f"错误堆栈:\n{traceback.format_exc()}")
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        uptime = time.time() - self.start_time
        
        return {
            "healthy": self.error_count < 10,  # 错误阈值
            "last_check": time.time(),
            "memory_usage": "normal",
            "error_count": self.error_count,
            "last_error": getattr(self, 'last_error', None),
            "uptime": uptime,
            "supported_formats": ["Excel (.xls)"],
            "performance_metrics": {
                "avg_processing_time": "unknown",
                "success_rate": "unknown",
                "error_rate": f"{self.error_count}/hour" if uptime > 0 else "0"
            },
            "plugin_features": {
                "multi_sheet_support": True,
                "time_parsing": True,
                "negative_amount_handling": True,
                "header_extraction": True
            }
        } 