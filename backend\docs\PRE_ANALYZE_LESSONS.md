# 预解析开发经验与教训（以民生银行为例）

本文总结民生银行（cmbc_format1_plugin）预解析功能开发过程中的关键经验与教训，作为后续其它银行解析器预解析实现的参考，帮助团队少走弯路。

## 1. 原则对齐（Align）
- 预解析的唯一职责：让解析器跑起来，拿到“解析器自身语义下的第一条标准化数据”，用这条记录做四维度二元校核。不得在预解析中另起规则体系。
- 四维度评估是“二元通过制”：满足即各 25 分，总分 100；不满足即 0。不要引入复杂加权或启发式。
- 任何对“如何识别首条数据”的逻辑，应以解析器 parse 的输出为准，避免与解析器内部“第一行”的概念发生偏差。

## 2. 技术要点（Architect / Atomize）
- 样本获取顺序：
  1) 优先 parser.parse(file_path)，从返回的标准化结构中选择首条交易（和首个账户）；
  2) 失败再回退 parser.extract_sample(limit=1)；
  3) 仍失败才兜底简单读取 Excel 前几行（仅用于日志与排查，不参与打分）。
- 字段容错：
  - 姓名：cardholder_name / holder_name / person_name
  - 账号：account_number / card_number（≥10 位数字）
  - 时间：transaction_datetime 或 transaction_date（YYYY-MM-DD），可择一
  - 金额/余额：transaction_amount / balance 存在且可解析数字
- 仅依据“解析器输出的首条记录”打分，不对原始 Excel 做任何自行猜测、重命名或正则清洗，以免与解析器规则冲突。

## 3. 常见误区与教训（Assess）
- 误区1：在预解析里实现“轻量级表头识别/字段推断”。教训：这会与解析器规则产生偏差，导致预解析分数与真实解析效果不一致。
- 误区2：将“Excel 的第一行”当作“解析器语义的第一条记录”。教训：多子表、多表头偏移时，此理解错误会直接造成四维校核失败。
- 误区3：维度键名不统一导致误判。教训：应在评估器中对标准化键名做最小集合容错，而不是修改解析器规则。
- 误区4：接口未返回 details，前端四维度无法展示。教训：确保后端 confidence_evaluation.details 字段齐全，且 candidates 组装正确。
- 误区5：银行过滤或文件校验过严，导致插件未参与评估。教训：路由层与 Analyzer 保持一致的过滤逻辑，并在日志中清晰打印“被跳过的原因”。

## 4. 日志与可观测性
- 在 Analyzer 中：日志打印插件发现/加载列表、银行过滤结果、参与评估的插件名。
- 在 Evaluator 中：打印样本来源（parse / extract_sample / excel_head）、首条记录关键字段值。
- 在插件中：打印 parse 成功的账户/交易总量，便于核对与预解析得分一致性。

## 5. 最小变更策略（Seiri/Seiton/Seiso/Seiketsu/Shitsuke）
- 优先修改评估器的样本来源与字段容错，避免改动解析器逻辑。
- 变更后先本地以 Analyzer 直接调用验证，再通过 /api/parser/pre-analyze 端到端复测。
- 保持代码结构与命名的一致性，及时清理临时“轻量规则”试验代码。

## 6. 回归清单（Approve）
- 不同银行样例各跑一次：确认 candidates 至少包含该银行对应插件，四维得分能达到 100（在解析器首条数据合规的前提下）。
- 前端四维显示：确认 details 中四个维度百分比均为 25.0，UI 显示正确。
- 错误路径：当 parse 与 extract_sample 均失败时，API 仍返回合理的 candidates（可为空）并附带日志线索。

## 7. 面向未来的建议
- 在 Parser 接口层约定“标准化字段键名”与“首条记录挑选约定”，并在 README 中显式文档化。
- 为常见维度提供统一校核工具函数，减少评估器与解析器之间的字段名耦合。
- 为每个插件提供一个最小 e2e 预解析测试（样本文件 + 期望四维 100 分），纳入 CI。

