{"success": true, "message": "解析成功", "parse_result": {"accounts": [{"account_number": "************", "card_number": "6216612600000706666", "total_income": 58000.0, "total_expense": 0.0, "transaction_count": 5, "data_source": "新线", "earliest_date": "2018-02-10T00:00:00", "latest_date": "2018-08-03T00:00:00", "holder_name": "******************", "bank_name": "中国银行", "total_inflow": 58000.0, "total_outflow": 0.0, "date_range": "2018-02-10 至 2018-08-03"}], "transactions": [{"sequence_number": 1, "cardholder_name": "******************", "holder_name": "******************", "bank_name": "中国银行", "account_number": "************", "card_number": "6216612600000706666", "transaction_date": "2018-02-10", "transaction_time": "15:22:41", "transaction_type": "冲正", "transaction_method": "冲正", "transaction_amount": 6000.0, "amount": 6000.0, "account_balance": 21012.02, "balance_after": 21012.02, "balance_amount": 21012.02, "debit_credit_indicator": "收", "income_expense_flag": "收", "dr_cr_flag": "收", "counterpart_name": "莫肖平", "counterpart_account": "6229920900000770237", "counterpart_bank": "广西壮族自治区农村信用社联合社", "counterparty_name": "莫肖平", "counterparty_account": "6229920900000770237", "counterparty_bank": "广西壮族自治区农村信用社联合社", "remark1": "", "remark2": "IBPS1041000000042018021060882927", "remark3": "", "sheet_source": "新线"}, {"sequence_number": 2, "cardholder_name": "******************", "holder_name": "******************", "bank_name": "中国银行", "account_number": "************", "card_number": "6216612600000706666", "transaction_date": "2018-07-30", "transaction_time": "00:52:09", "transaction_type": "冲正", "transaction_method": "冲正", "transaction_amount": 13000.0, "amount": 13000.0, "account_balance": 14271.06, "balance_after": 14271.06, "balance_amount": 14271.06, "debit_credit_indicator": "收", "income_expense_flag": "收", "dr_cr_flag": "收", "counterpart_name": "王跃", "counterpart_account": "6230520140008168173", "counterpart_bank": "中国农业银行股份有限公司", "counterparty_name": "王跃", "counterparty_account": "6230520140008168173", "counterparty_bank": "中国农业银行股份有限公司", "remark1": "", "remark2": "IBPS1041000000042018073074406243", "remark3": "", "sheet_source": "新线"}, {"sequence_number": 3, "cardholder_name": "******************", "holder_name": "******************", "bank_name": "中国银行", "account_number": "************", "card_number": "6216612600000706666", "transaction_date": "2018-07-30", "transaction_time": "00:52:09", "transaction_type": "冲正", "transaction_method": "冲正", "transaction_amount": 13000.0, "amount": 13000.0, "account_balance": 14271.06, "balance_after": 14271.06, "balance_amount": 14271.06, "debit_credit_indicator": "收", "income_expense_flag": "收", "dr_cr_flag": "收", "counterpart_name": "王跃", "counterpart_account": "6230520140008168173", "counterpart_bank": "中国农业银行股份有限公司", "counterparty_name": "王跃", "counterparty_account": "6230520140008168173", "counterparty_bank": "中国农业银行股份有限公司", "remark1": "", "remark2": "IBPS1041000000042018073074408474", "remark3": "", "sheet_source": "新线"}, {"sequence_number": 4, "cardholder_name": "******************", "holder_name": "******************", "bank_name": "中国银行", "account_number": "************", "card_number": "6216612600000706666", "transaction_date": "2018-07-30", "transaction_time": "00:52:09", "transaction_type": "冲正", "transaction_method": "冲正", "transaction_amount": 13000.0, "amount": 13000.0, "account_balance": 14271.06, "balance_after": 14271.06, "balance_amount": 14271.06, "debit_credit_indicator": "收", "income_expense_flag": "收", "dr_cr_flag": "收", "counterpart_name": "王跃", "counterpart_account": "6230520140008168173", "counterpart_bank": "中国农业银行股份有限公司", "counterparty_name": "王跃", "counterparty_account": "6230520140008168173", "counterparty_bank": "中国农业银行股份有限公司", "remark1": "", "remark2": "IBPS1041000000042018073074423957", "remark3": "", "sheet_source": "新线"}, {"sequence_number": 5, "cardholder_name": "******************", "holder_name": "******************", "bank_name": "中国银行", "account_number": "************", "card_number": "6216612600000706666", "transaction_date": "2018-08-03", "transaction_time": "13:12:18", "transaction_type": "冲正", "transaction_method": "冲正", "transaction_amount": 13000.0, "amount": 13000.0, "account_balance": 13960.14, "balance_after": 13960.14, "balance_amount": 13960.14, "debit_credit_indicator": "收", "income_expense_flag": "收", "dr_cr_flag": "收", "counterpart_name": "王岩", "counterpart_account": "6228480178538807474", "counterpart_bank": "中国农业银行股份有限公司", "counterparty_name": "王岩", "counterparty_account": "6228480178538807474", "counterparty_bank": "中国农业银行股份有限公司", "remark1": "", "remark2": "IBPS1041000000042018080395217332", "remark3": "", "sheet_source": "新线"}], "summary": {"total_accounts": 1, "total_transactions": 5}, "metadata": {}}, "parser_used": "boc_format1_plugin", "final_confidence": 75.0, "confidence_details": {"boc_format1_plugin": {"confidence": 75.0, "confidence_percentage": 75.0, "match_reason": "4维度智能评估完成 (总分:75.0/100)", "details": {"cardholder_name_score": {"score": 0.0, "max_score": 25, "description": "持卡人姓名识别", "details": "有效姓名 0/6", "percentage": 0.0}, "time_format_score": {"score": 25.0, "max_score": 25, "description": "时间格式准确性", "details": "有效日期 5/5", "percentage": 100.0}, "account_number_score": {"score": 25.0, "max_score": 25, "description": "账号或卡号识别", "details": "有效账号 6/6", "percentage": 100.0}, "amount_parsing_score": {"score": 25.0, "max_score": 25, "description": "金额解析能力", "details": "有效金额 5/5", "percentage": 100.0}}, "evaluation_breakdown": {"total_score": 75.0, "max_possible_score": 100, "overall_percentage": 75.0, "account_count": 1, "transaction_count": 5}, "sample_analysis": {"sample_accounts": [{"account_number": "************", "card_number": "6216612600000706666", "total_income": 58000.0, "total_expense": 0.0, "transaction_count": 5, "data_source": "新线", "earliest_date": "2018-02-10T00:00:00", "latest_date": "2018-08-03T00:00:00", "holder_name": "******************", "bank_name": "中国银行", "total_inflow": 58000.0, "total_outflow": 0.0, "date_range": "2018-02-10 至 2018-08-03"}], "sample_transactions": [{"sequence_number": 1, "cardholder_name": "******************", "holder_name": "******************", "bank_name": "中国银行", "account_number": "************", "card_number": "6216612600000706666", "transaction_date": "2018-02-10", "transaction_time": "15:22:41", "transaction_type": "冲正", "transaction_method": "冲正", "transaction_amount": 6000.0, "amount": 6000.0, "account_balance": 21012.02, "balance_after": 21012.02, "balance_amount": 21012.02, "debit_credit_indicator": "收", "income_expense_flag": "收", "dr_cr_flag": "收", "counterpart_name": "莫肖平", "counterpart_account": "6229920900000770237", "counterpart_bank": "广西壮族自治区农村信用社联合社", "counterparty_name": "莫肖平", "counterparty_account": "6229920900000770237", "counterparty_bank": "广西壮族自治区农村信用社联合社", "remark1": "", "remark2": "IBPS1041000000042018021060882927", "remark3": "", "sheet_source": "新线"}, {"sequence_number": 2, "cardholder_name": "******************", "holder_name": "******************", "bank_name": "中国银行", "account_number": "************", "card_number": "6216612600000706666", "transaction_date": "2018-07-30", "transaction_time": "00:52:09", "transaction_type": "冲正", "transaction_method": "冲正", "transaction_amount": 13000.0, "amount": 13000.0, "account_balance": 14271.06, "balance_after": 14271.06, "balance_amount": 14271.06, "debit_credit_indicator": "收", "income_expense_flag": "收", "dr_cr_flag": "收", "counterpart_name": "王跃", "counterpart_account": "6230520140008168173", "counterpart_bank": "中国农业银行股份有限公司", "counterparty_name": "王跃", "counterparty_account": "6230520140008168173", "counterparty_bank": "中国农业银行股份有限公司", "remark1": "", "remark2": "IBPS1041000000042018073074406243", "remark3": "", "sheet_source": "新线"}, {"sequence_number": 3, "cardholder_name": "******************", "holder_name": "******************", "bank_name": "中国银行", "account_number": "************", "card_number": "6216612600000706666", "transaction_date": "2018-07-30", "transaction_time": "00:52:09", "transaction_type": "冲正", "transaction_method": "冲正", "transaction_amount": 13000.0, "amount": 13000.0, "account_balance": 14271.06, "balance_after": 14271.06, "balance_amount": 14271.06, "debit_credit_indicator": "收", "income_expense_flag": "收", "dr_cr_flag": "收", "counterpart_name": "王跃", "counterpart_account": "6230520140008168173", "counterpart_bank": "中国农业银行股份有限公司", "counterparty_name": "王跃", "counterparty_account": "6230520140008168173", "counterparty_bank": "中国农业银行股份有限公司", "remark1": "", "remark2": "IBPS1041000000042018073074408474", "remark3": "", "sheet_source": "新线"}], "data_extraction_success": true}, "analysis_time": "2025-08-11T22:53:19.739523"}}}