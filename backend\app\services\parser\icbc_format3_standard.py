"""
工商银行格式3标准解析器
专门处理3.xls文件的标准个人账户格式：
- 单工作表带账户信息头部的交易流水
- 标准个人账户解析
- 账户信息头部识别
- 标准时间格式处理
"""
import pandas as pd
import logging
from typing import Dict, List, Any, Tuple
from datetime import datetime
import re
import os

logger = logging.getLogger(__name__)

class ICBCFormat3StandardParser:
    """工商银行格式3标准解析器"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.accounts = []
        self.transactions = []
    
    def _clean_field(self, value) -> str:
        """清理字段值"""
        if value is None or pd.isna(value):
            return ""
        return str(value).strip().replace('\t', '').replace('\n', '').replace('\r', '')
    
    def _clean_amount_string(self, value) -> str:
        """
        清理金额字符串，移除空格、逗号等格式字符
        
        Args:
            value: 原始金额值
            
        Returns:
            str: 清理后的金额字符串
        """
        if value is None:
            return ""
        
        # 转换为字符串并清理各种格式字符
        clean_value = str(value).replace(" ", "").replace(",", "").replace("，", "").replace("\t", "").replace("\n", "").replace("\r", "").strip()
        
        # 移除可能的货币符号
        clean_value = clean_value.replace("¥", "").replace("$", "").replace("€", "").replace("￥", "")
        
        return clean_value
    
    def _parse_amount(self, amount_str: str, dr_cr_flag: str = "") -> float:
        """
        解析金额
        
        Args:
            amount_str: 金额字符串
            dr_cr_flag: 借贷标志
            
        Returns:
            float: 解析后的金额（负数表示支出）
        """
        if not amount_str:
            return 0.0
        
        clean_amount = self._clean_amount_string(amount_str)
        
        try:
            amount = float(clean_amount)
            
            # 根据借贷标志调整正负
            if dr_cr_flag in ["借", "支出", "支"]:  # 借方=支出，转为负数
                return -abs(amount)
            elif dr_cr_flag in ["贷", "收入", "收"]:  # 贷方=收入，保持正数
                return abs(amount)
            else:
                return amount
        except (ValueError, TypeError):
            logger.warning(f"无法解析金额: {amount_str}")
            return 0.0
    
    def _standardize_time_format(self, date_str: str, time_str: str = "") -> str:
        """
        标准化时间格式为 YYYY-MM-DD HH:MM:SS
        
        Args:
            date_str: 日期字符串
            time_str: 时间字符串
            
        Returns:
            str: 标准化后的时间字符串
        """
        if not date_str:
            return ""
        
        try:
            # 清理输入字符串
            full_timestamp_str = str(date_str).strip()
            
            # 核心逻辑：尝试匹配 YYYY-MM-DD-HH.MM.SS 格式
            match = re.search(r'(\d{4}-\d{2}-\d{2})-(\d{2}\.\d{2}\.\d{2})', full_timestamp_str)
            
            if match:
                date_part = match.group(1)
                time_part = match.group(2).replace('.', ':') # 将 21.54.28 转换为 21:54:28
                return f"{date_part} {time_part}"
            else:
                # 如果不匹配，尝试解析为普通日期
                # 使用errors='coerce'可以在解析失败时返回NaT，避免异常
                date_obj = pd.to_datetime(full_timestamp_str, errors='coerce')
                if pd.notna(date_obj):
                    return date_obj.strftime('%Y-%m-%d 00:00:00')
                else:
                    logger.warning(f"无法将 '{full_timestamp_str}' 解析为任何已知日期格式。")
                    return full_timestamp_str # 返回原始字符串以便调试
                    
        except Exception as e:
            logger.error(f"时间格式标准化时发生意外错误: '{date_str}', 错误: {str(e)}")
            return str(date_str) # 保证返回字符串
    
    def _extract_account_info_from_header(self, df: pd.DataFrame) -> Tuple[str, str, str]:
        """
        从文件头部提取账户信息
        
        Args:
            df: 数据框
            
        Returns:
            Tuple[str, str, str]: (账户名, 账号, 卡号)
        """
        account_name = ""
        account_number = ""
        card_number = ""
        
        # 在前5行中搜索账户信息
        for idx in range(min(5, len(df))):
            for col_idx in range(min(10, len(df.columns))):
                try:
                    cell_value = df.iloc[idx, col_idx]
                    if pd.isna(cell_value):
                        continue
                    
                    cell_str = str(cell_value).strip()
                    
                    # 特定位置的信息提取（根据配置文件映射）
                    if idx == 0 and col_idx == 1:  # B1: 账户名
                        if re.match(r'^[\u4e00-\u9fa5]{2,10}$', cell_str):
                            account_name = cell_str
                    
                    elif idx == 1 and col_idx == 1:  # B2: 账号
                        if re.match(r'^\d{16,20}$', cell_str):
                            account_number = cell_str
                    
                    elif idx == 1 and col_idx == 3:  # D2: 卡号
                        if re.match(r'^\d{16,19}$', cell_str):
                            card_number = cell_str
                    
                    # 通用搜索：如果特定位置没有找到，进行模式匹配
                    if not account_number and re.match(r'^\d{16,20}$', cell_str):
                        account_number = cell_str
                    
                    if not card_number and re.match(r'^\d{16,19}$', cell_str):
                        card_number = cell_str
                    
                    if not account_name and re.match(r'^[\u4e00-\u9fa5]{2,10}$', cell_str):
                        account_name = cell_str
                        
                except Exception as e:
                    logger.debug(f"处理单元格 ({idx}, {col_idx}) 时出错: {str(e)}")
                    continue
        
        return account_name, account_number, card_number
    
    def _detect_header_row(self, df: pd.DataFrame) -> int:
        """
        检测表头行位置
        
        Args:
            df: 数据框
            
        Returns:
            int: 表头行索引，-1表示未找到
        """
        # 修复：支持多种表头格式
        mandatory_keywords_set1 = ["交易日期", "发生额", "余额"]  # 原格式
        mandatory_keywords_set2 = ["交易时间戳", "发生额", "余额"]  # 3.xls格式
        optional_keywords = ["摘要", "对方户名", "交易时间", "借贷标志", "账号", "币种"]
        
        for idx in range(min(15, len(df))):
            row = df.iloc[idx]
            row_text = " ".join([str(cell) for cell in row if not pd.isna(cell)])
            
            # 检查格式1：交易日期 + 发生额 + 余额
            mandatory_count1 = sum(1 for keyword in mandatory_keywords_set1 if keyword in row_text)
            # 检查格式2：交易时间戳 + 发生额 + 余额  
            mandatory_count2 = sum(1 for keyword in mandatory_keywords_set2 if keyword in row_text)
            optional_count = sum(1 for keyword in optional_keywords if keyword in row_text)
            
            # 如果匹配任一格式且有可选关键词，认为是表头行
            if (mandatory_count1 == len(mandatory_keywords_set1) or 
                mandatory_count2 == len(mandatory_keywords_set2)) and optional_count >= 1:
                logger.info(f"找到表头行 {idx}: {row_text[:100]}")
                return idx
        
        # 如果没找到，默认第1行为表头（0-based索引为1）
        logger.warning("未找到明确的表头行，使用默认行1")
        return 1
    
    def _detect_sheet_format(self, df: pd.DataFrame, header_row: int) -> Dict[str, int]:
        """
        检测工作表的字段格式
        
        Args:
            df: 数据框
            header_row: 表头行索引
            
        Returns:
            Dict[str, int]: 字段名到列索引的映射
        """
        field_mapping = {}
        
        # 首先查找实际的表头行（通常是行1）
        actual_header_row = -1
        for row_idx in range(min(3, len(df))):
            row = df.iloc[row_idx]
            row_text = ' '.join([str(cell) for cell in row if pd.notna(cell)])
            if '交易时间戳' in row_text and '借贷标志' in row_text:
                actual_header_row = row_idx
                break
        
        if actual_header_row >= 0:
            headers = df.iloc[actual_header_row]
            logger.info(f"找到表头行{actual_header_row}: {[str(h) for h in headers[:10] if pd.notna(h)]}")
            
            # 建立字段映射
            for col_idx, header in enumerate(headers):
                if pd.notna(header):
                    header_str = str(header).strip()
                    
                    # 精确映射字段名称，优先选择第一个匹配的字段
                    if header_str == '交易时间戳' and 'transaction_timestamp' not in field_mapping:
                        field_mapping['transaction_timestamp'] = col_idx
                    elif header_str == '账号' and 'account_number' not in field_mapping:
                        field_mapping['account_number'] = col_idx
                    elif header_str == '卡号' and 'card_number' not in field_mapping:
                        field_mapping['card_number'] = col_idx
                    elif header_str == '交易日期' and 'transaction_date' not in field_mapping:
                        field_mapping['transaction_date'] = col_idx
                    elif header_str == '借贷标志' and 'dr_cr_flag' not in field_mapping:
                        field_mapping['dr_cr_flag'] = col_idx
                    elif header_str == '发生额' and 'amount' not in field_mapping:
                        field_mapping['amount'] = col_idx
                    elif header_str == '余额' and 'balance' not in field_mapping:
                        field_mapping['balance'] = col_idx
                    elif header_str in ['币种', '交易币种'] and 'currency' not in field_mapping:
                        field_mapping['currency'] = col_idx
                    elif header_str in ['摘要', '注释'] and 'remark' not in field_mapping:
                        field_mapping['remark'] = col_idx
                    elif header_str == '交易金额' and 'transaction_amount' not in field_mapping:
                        field_mapping['transaction_amount'] = col_idx
                    # --- 新增智能字段映射 START ---
                    elif header_str in ['对方帐户', '对方账户', '对方账号'] and 'counterparty_account' not in field_mapping:
                        field_mapping['counterparty_account'] = col_idx
                    elif header_str in ['对方户名', '对方名称'] and 'counterparty_name' not in field_mapping:
                        field_mapping['counterparty_name'] = col_idx
                    # --- 新增智能字段映射 END ---
        
        logger.info(f"检测到字段映射: {field_mapping}")
        return field_mapping

    def _process_transaction_data(self, df: pd.DataFrame, start_row: int, 
                                account_name: str, account_number: str, card_number: str) -> None:
        """
        处理交易数据 - 【重要修复】动态检测字段映射，支持不同工作表格式
        
        Args:
            df: 数据框
            start_row: 数据起始行
            account_name: 账户名
            account_number: 账号
            card_number: 卡号
        """
        # 【关键修复】动态检测工作表的字段格式
        field_mapping = self._detect_sheet_format(df, start_row)
        
        # 重新确定数据起始行：表头行之后
        actual_start_row = start_row
        for row_idx in range(min(3, len(df))):
            row = df.iloc[row_idx]
            row_text = ' '.join([str(cell) for cell in row if pd.notna(cell)])
            if '交易时间戳' in row_text and '借贷标志' in row_text:
                actual_start_row = row_idx + 1  # 表头行之后
                break
        
        logger.info(f"检测到字段映射: {field_mapping}")
        
        for idx in range(actual_start_row, len(df)):
            row = df.iloc[idx]
            
            # 跳过空行
            if row.isna().all():
                continue
                
            # 跳过表头行（检查是否包含字段名）
            row_text = ' '.join([str(cell) for cell in row if pd.notna(cell)])
            if any(keyword in row_text for keyword in ['交易时间戳', '交易日期', '发生额', '余额', '借贷标志', '账户', '卡号']):
                logger.debug(f"跳过表头行 {idx}: {row_text[:50]}...")
                continue
            
            try:
                # 【重要修复】安全获取字段值，防止索引越界
                def safe_get_field(col_index: int) -> str:
                    """安全地获取字段值，处理越界和空值"""
                    if col_index is not None and col_index < len(row):
                        return self._clean_field(row[col_index])
                    return ""
                
                # 1. 获取原始时间字符串，优先使用"交易时间戳"
                raw_time_str = safe_get_field(field_mapping.get('transaction_timestamp'))
                if not raw_time_str:
                    raw_time_str = safe_get_field(field_mapping.get('transaction_date'))
                
                # 2. 调用标准时间格式化函数处理
                transaction_time_std = self._standardize_time_format(raw_time_str)
                
                # 从标准化后的时间中拆分日期和时间
                parts = transaction_time_std.split(' ')
                transaction_date = parts[0] if len(parts) > 0 else ""
                transaction_time = parts[1] if len(parts) > 1 else "00:00:00"
                
                # 【关键修复】使用动态检测的字段映射
                transaction_timestamp_raw = safe_get_field(field_mapping.get('transaction_timestamp'))
                account_number_field = safe_get_field(field_mapping.get('account_number'))
                currency = safe_get_field(field_mapping.get('currency'))
                card_number_field = safe_get_field(field_mapping.get('card_number'))
                dr_cr_flag_raw = safe_get_field(field_mapping.get('dr_cr_flag'))
                
                # 优先使用发生额，如果没有则使用交易金额
                amount_raw = safe_get_field(field_mapping.get('amount'))
                if not amount_raw:
                    amount_raw = safe_get_field(field_mapping.get('transaction_amount'))
                
                balance_raw = safe_get_field(field_mapping.get('balance'))
                
                # 处理特殊情况：如果有独立的交易日期字段，优先使用
                transaction_date_raw = safe_get_field(field_mapping.get('transaction_date'))
                
                if transaction_date_raw:
                    # 使用独立的交易日期字段
                    transaction_date = transaction_date_raw.strip()
                elif transaction_timestamp_raw:
                    # 从时间戳提取日期
                    transaction_timestamp = transaction_timestamp_raw.strip()
                    if '-' in transaction_timestamp and len(transaction_timestamp.split('-')) >= 3:
                        date_parts = transaction_timestamp.split('-')[:3]
                        transaction_date = '-'.join(date_parts)  # 2003-01-06
                    else:
                        transaction_date = transaction_timestamp
                else:
                    continue  # 没有时间信息，跳过
                
                # --- 修改硬编码为动态映射 START ---
                remark1 = safe_get_field(field_mapping.get('remark'))
                counterparty_name = safe_get_field(field_mapping.get('counterparty_name'))
                counterparty_account = safe_get_field(field_mapping.get('counterparty_account'))
                # --- 修改硬编码为动态映射 END ---
                
                # 字段值修正和验证
                if not account_number_field:
                    account_number_field = account_number
                if not card_number_field and card_number:
                    card_number_field = card_number
                
                # 跳过无效行
                if not transaction_date or not amount_raw:
                    logger.debug(f"跳过无效行 {idx}: 缺少交易日期或金额")
                    continue
                
                # 处理借贷标志
                dr_cr_flag = "收" if dr_cr_flag_raw in ["贷", "收入"] else "支"
                
                # 解析金额
                amount = self._parse_amount(amount_raw, dr_cr_flag_raw)
                balance = self._parse_amount(balance_raw)
                
                # 构建符合17个必需字段的交易记录
                transaction = {
                    'sequence_number': len(self.transactions) + 1,
                    'holder_name': account_name,
                    'bank_name': '中国工商银行',
                    'account_number': account_number_field,
                    'card_number': card_number_field or "",  # 允许空卡号
                    'transaction_date': transaction_date,
                    'transaction_time': transaction_time,
                    'transaction_method': remark1 or "",
                    'transaction_amount': amount,
                    'balance_amount': balance,
                    'dr_cr_flag': dr_cr_flag,
                    'counterparty_name': counterparty_name or "",
                    'counterparty_account': counterparty_account or "",
                    'counterparty_bank': "",
                    'remark1': remark1 or "",
                    'remark2': "",
                    'remark3': remark1 if remark1 else "",
                    'currency': currency or 'CNY'
                }
                
                self.transactions.append(transaction)
                
            except Exception as e:
                logger.warning(f"处理交易记录失败 (行 {idx}): {str(e)}")
                continue
    
    def parse(self) -> Dict[str, Any]:
        """
        解析Excel文件（多工作表结构）
        
        Returns:
            Dict[str, Any]: 解析结果
        """
        try:
            logger.info(f"开始解析文件: {self.file_path}")
            
            all_accounts = []
            all_transactions = []
            
            # 读取所有工作表
            with pd.ExcelFile(self.file_path) as xls:
                sheet_names = xls.sheet_names
                logger.info(f"发现工作表: {sheet_names}")
                
                for sheet_name in sheet_names:
                    logger.info(f"处理工作表: {sheet_name}")
                    
                    # 读取工作表
                    df = pd.read_excel(xls, sheet_name=sheet_name, header=None)
                    
                    if df.empty:
                        logger.warning(f"工作表 {sheet_name} 为空，跳过")
                        continue
                    
                    # 检查是否是银行流水工作表（包含交易数据）
                    if self._is_transaction_sheet(df):
                        # 处理银行流水工作表
                        # 从工作表第一行提取账户名
                        account_name = ""
                        if len(df) > 0:
                            first_row = df.iloc[0]
                            for cell in first_row:
                                if pd.notna(cell) and isinstance(cell, str):
                                    cell_clean = str(cell).strip()
                                    if re.match(r'^[\u4e00-\u9fa5]{2,10}$', cell_clean):
                                        account_name = cell_clean
                                        break
                        
                        if not account_name:
                            account_name = sheet_name.strip()
                        
                        # 检测表头行
                        header_row = self._detect_header_row(df)
                        if header_row == -1:
                            header_row = 1  # 默认第2行为表头
                        
                        data_start_row = header_row + 1
                        
                        # 从数据行中查找账户信息
                        account_number = ""
                        card_number = ""
                        
                        for idx in range(data_start_row, min(data_start_row + 5, len(df))):
                            if idx < len(df):
                                row = df.iloc[idx]
                                account_field = self._clean_field(row.iloc[0]) if len(row) > 0 else ""
                                card_field = self._clean_field(row.iloc[2]) if len(row) > 2 else ""
                                
                                if re.match(r'^\d{16,20}$', account_field):
                                    account_number = account_field
                                if re.match(r'^\d{16,19}$', card_field):
                                    card_number = card_field
                                
                                if account_number:
                                    break
                        
                        logger.info(f"工作表 {sheet_name}: 账户={account_number}, 持卡人={account_name}")
                        
                        # 重置交易列表为当前工作表准备
                        current_transactions = []
                        self.transactions = current_transactions
                        
                        # 处理交易数据
                        self._process_transaction_data(df, data_start_row, account_name, account_number, card_number)
                        
                        if current_transactions:
                            # 创建账户信息
                            account_data = {
                                'account_number': account_number or f"UNKNOWN_{len(all_accounts)+1}",
                                'card_number': card_number,
                                'holder_name': account_name,
                                'bank_name': '中国工商银行',
                                'account_type': '个人账户'
                            }
                            all_accounts.append(account_data)
                            all_transactions.extend(current_transactions)
                            
                            logger.info(f"工作表 {sheet_name} 解析完成: {len(current_transactions)} 条交易")
                        else:
                            logger.warning(f"工作表 {sheet_name} 未找到有效交易数据")
                    else:
                        logger.info(f"工作表 {sheet_name} 不是交易数据表，跳过")
            
            # 更新实例变量
            self.accounts = all_accounts
            self.transactions = all_transactions
            
            # 构建解析结果
            result = self._build_result()
            
            logger.info(f"解析完成: 共 {len(self.accounts)} 个账户, {len(self.transactions)} 条交易")
            return result
            
        except Exception as e:
            logger.error(f"文件解析失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'accounts': [],
                'transactions_by_account': {},
                'summary': {'total_accounts': 0, 'total_transactions': 0}
            }
    
    def _is_transaction_sheet(self, df: pd.DataFrame) -> bool:
        """
        判断工作表是否包含交易数据
        
        Args:
            df: 工作表数据框
            
        Returns:
            bool: 是否为交易数据表
        """
        # 修复：更严格的交易表识别逻辑
        
        # 1. 排除账户信息表（通常包含查询ID、姓名、证件类型等）
        exclude_keywords = ["查询ID", "姓名", "证件类型", "证件号码", "客户编号", "账号类型"]
        for idx in range(min(5, len(df))):
            row_text = " ".join([str(cell) for cell in df.iloc[idx] if pd.notna(cell)])
            exclude_count = sum(1 for keyword in exclude_keywords if keyword in row_text)
            if exclude_count >= 3:
                logger.info(f"识别为账户信息表，排除: {row_text[:50]}...")
                return False
        
        # 2. 检查交易表特征关键词
        keywords_set1 = ["账号", "币种", "卡号", "交易时间戳", "借贷标志", "发生额", "余额"]  # 3.xls格式
        keywords_set2 = ["交易日期", "发生额", "余额", "摘要"]  # 2.xls格式
        keywords_set3 = ["卡号", "交易时间戳", "交易日期", "借贷标志", "交易金额"]  # 其他格式
        
        has_transaction_keywords = False
        for idx in range(min(15, len(df))):
            row_text = " ".join([str(cell) for cell in df.iloc[idx] if pd.notna(cell)])
            
            # 检查各种格式的关键词匹配
            keyword_count1 = sum(1 for keyword in keywords_set1 if keyword in row_text)
            keyword_count2 = sum(1 for keyword in keywords_set2 if keyword in row_text)
            keyword_count3 = sum(1 for keyword in keywords_set3 if keyword in row_text)
            
            # 如果任一格式匹配度高，认为是交易表
            if (keyword_count1 >= 4 or keyword_count2 >= 3 or keyword_count3 >= 3):
                logger.info(f"识别为交易工作表，关键词匹配: {keyword_count1}/{keyword_count2}/{keyword_count3}")
                has_transaction_keywords = True
                break
        
        # 3. 检查数据行数，交易表通常有较多行（至少30行）
        has_enough_rows = len(df) >= 30

        # 4. 检查是否有金额类型的数据
        has_amount_data = False
        for idx in range(min(20, len(df))):
            for col_idx in range(min(15, len(df.columns))):
                try:
                    cell_value = df.iloc[idx, col_idx]
                    if pd.notna(cell_value):
                        # 检查是否为大于100的数值（可能是金额）
                        if isinstance(cell_value, (int, float)) and cell_value >= 100:
                            has_amount_data = True
                            break
                        # 检查是否为金额格式的字符串
                        cell_str = str(cell_value)
                        if re.match(r'^\d+\.?\d*$', cell_str) and float(cell_str) >= 100:
                            has_amount_data = True
                            break
                except:
                    continue
            if has_amount_data:
                break

        # 5. 综合判断
        result = has_transaction_keywords and has_enough_rows and has_amount_data
        
        if result:
            logger.info(f"确认为交易工作表: 关键词={has_transaction_keywords}, 行数={len(df)}, 金额数据={has_amount_data}")
        else:
            logger.debug(f"非交易工作表: 关键词={has_transaction_keywords}, 行数={len(df)}, 金额数据={has_amount_data}")
        
        return result
    
    def _build_result(self) -> Dict[str, Any]:
        """构建解析结果 - 按照PARSER_DEVELOPMENT_RULES.md规范"""
        # 按账户分组交易
        transactions_by_account = {}
        accounts = []
        
        # 为每条交易分配序号
        for index, transaction in enumerate(self.transactions, 1):
            transaction['sequence_number'] = index
            
            account_number = transaction['account_number']
            
            if account_number not in transactions_by_account:
                transactions_by_account[account_number] = []
                
                # 创建符合规范的账户信息
                account_data = {
                    'account_number': account_number,
                    'card_number': transaction['card_number'],
                    'holder_name': transaction['holder_name'],
                    'bank_name': '中国工商银行',
                    'account_type': '个人账户',
                    
                    # 统计信息
                    'transactions_count': 0,  # 将在后续计算
                    'total_inflow': 0.0,
                    'total_outflow': 0.0,
                    'date_range': "",
                    
                    # 兼容字段
                    'account_id': account_number,
                    'account_name': transaction['holder_name'],
                    'currency': transaction['currency'],
                    'is_primary': len(accounts) == 0
                }
                accounts.append(account_data)
            
            transactions_by_account[account_number].append(transaction)
        
        # 计算统计信息
        for account in accounts:
            account_transactions = transactions_by_account[account['account_number']]
            account['transactions_count'] = len(account_transactions)

            # 计算收支总额
            inflow = sum(t['transaction_amount'] for t in account_transactions if t['transaction_amount'] > 0)
            outflow = sum(abs(t['transaction_amount']) for t in account_transactions if t['transaction_amount'] < 0)

            account['total_inflow'] = inflow
            account['total_outflow'] = outflow

            # 计算时间范围
            dates = [t['transaction_date'] for t in account_transactions if t['transaction_date']]
            if dates:
                account['date_range'] = f"{min(dates)} 至 {max(dates)}"

            # 🔧 修复：计算账户余额 - 从最后一个时间段的交易记录获取余额
            account_balance = 0.0
            if account_transactions:
                # 按交易日期和时间排序，获取最新的交易记录
                # 对于同一时间的交易，取原始顺序的最后一条（余额最终状态）
                def get_datetime_key(transaction):
                    date = transaction.get('transaction_date', '')
                    time = transaction.get('transaction_time', '')
                    # 添加原始索引确保同一时间的交易按原始顺序排列
                    original_index = account_transactions.index(transaction)
                    return f"{date} {time}", original_index

                sorted_transactions = sorted(account_transactions, key=get_datetime_key, reverse=True)
                latest_transaction = sorted_transactions[0]  # 最新的交易

                # 从最新交易的balance_amount字段获取账户余额
                if 'balance_amount' in latest_transaction and latest_transaction['balance_amount'] is not None:
                    try:
                        account_balance = float(latest_transaction['balance_amount'])
                        datetime_key, _ = get_datetime_key(latest_transaction)
                        logger.info(f"账户 {account['account_number']} 余额: ¥{account_balance:,.2f} (来自最新交易: {datetime_key})")
                    except (ValueError, TypeError):
                        logger.warning(f"账户 {account['account_number']} 余额转换失败: {latest_transaction['balance_amount']}")
                        account_balance = 0.0
                else:
                    logger.warning(f"账户 {account['account_number']} 最新交易缺少余额信息")

            account['account_balance'] = account_balance
        
        # 计算置信度
        confidence_score = self._calculate_confidence_score(accounts, self.transactions)
        
        # 构建最终结果
        return {
            'success': True,
            'file_info': {
                'name': os.path.basename(self.file_path),
                'format': 'xls'
            },
            'summary': {
                'total_accounts': len(accounts),
                'total_transactions': len(self.transactions),
                'date_range': self._calculate_overall_date_range()
            },
            'accounts': accounts,
            'transactions_by_account': transactions_by_account,
            'metadata': {
                'parser_version': '1.0.0',
                'parsing_time': datetime.now().isoformat(),
                'confidence_score': confidence_score
            }
        }
    
    def _calculate_overall_date_range(self) -> str:
        """计算整体时间范围"""
        if not self.transactions:
            return ""
        
        dates = [t['transaction_date'] for t in self.transactions if t['transaction_date']]
        if dates:
            return f"{min(dates)} 至 {max(dates)}"
        
        return ""
    
    def extract_sample(self, limit: int = 10) -> Dict[str, Any]:
        """
        提取样本数据用于快速置信度评估
        
        Args:
            limit: 样本数据限制数量
            
        Returns:
            Dict: 样本数据
        """
        try:
            # 【重要修复】检查多个工作表，优先使用包含流水数据的工作表
            with pd.ExcelFile(self.file_path) as xls:
                sheet_names = xls.sheet_names
                
                # 优先选择包含"Sheet3"或流水数据的工作表
                target_sheet = None
                for sheet_name in sheet_names:
                    if 'Sheet3' in sheet_name or '桂网' in sheet_name:
                        target_sheet = sheet_name
                        break
                
                # 如果没找到Sheet3类型的，查找包含交易数据的工作表
                if not target_sheet:
                    for sheet_name in sheet_names:
                        temp_df = pd.read_excel(xls, sheet_name=sheet_name, header=None, nrows=5)
                        # 检查是否包含交易相关字段
                        if any(any('交易' in str(cell) or '发生额' in str(cell) or '余额' in str(cell) 
                                  for cell in row if pd.notna(cell)) for _, row in temp_df.iterrows()):
                            target_sheet = sheet_name
                            break
                
                # 默认使用第一个工作表
                if not target_sheet:
                    target_sheet = sheet_names[0]
                
                logger.info(f"使用工作表进行样本提取: {target_sheet}")
                
                # 读取目标工作表
                df = pd.read_excel(xls, sheet_name=target_sheet, header=None, nrows=30)
            
            # 【修复】从第一行提取账户信息
            account_name = ""
            account_number = ""
            card_number = ""
            
            # 检查第一行是否有账户名（如Sheet3的第一行：周波 身份证 ******************）
            if len(df) > 0:
                first_row = df.iloc[0]
                for cell in first_row:
                    if pd.notna(cell):
                        cell_str = str(cell).strip()
                        if re.match(r'^[\u4e00-\u9fa5]{2,10}$', cell_str):
                            account_name = cell_str
                            break
            
            # 检测表头行
            header_row = self._detect_header_row(df)
            if header_row == -1:
                header_row = 1  # 默认第2行为表头
            
            data_start = header_row + 1
            
            # 【修复】快速处理样本数据 - 使用正确的列索引
            sample_transactions = []
            sample_accounts = []
            
            for idx in range(data_start, min(data_start + limit, len(df))):
                if idx >= len(df):
                    break
                
                row = df.iloc[idx]
                if row.isna().all():
                    continue
                
                # 【重要修复】根据Sheet3的实际格式解析
                # Sheet3实际格式：账号|币种|卡号|交易时间戳|工作日期|借贷标志|发生额|余额|注释|对方帐户|...
                account_number_field = self._clean_field(row.iloc[0]) if len(row) > 0 else ""
                currency = self._clean_field(row.iloc[1]) if len(row) > 1 else ""
                card_number_field = self._clean_field(row.iloc[2]) if len(row) > 2 else ""
                timestamp_raw = self._clean_field(row.iloc[3]) if len(row) > 3 else ""
                work_date = self._clean_field(row.iloc[4]) if len(row) > 4 else ""  # 工作日期
                dr_cr_flag = self._clean_field(row.iloc[5]) if len(row) > 5 else ""  # 借贷标志
                amount_raw = self._clean_field(row.iloc[6]) if len(row) > 6 else ""  # 发生额
                
                # 【重要修复】解析时间戳为标准格式
                timestamp = ""
                if timestamp_raw:
                    # 处理格式：2012-01-05-11.17.15.216489
                    try:
                        if '-' in timestamp_raw and '.' in timestamp_raw:
                            # 分离日期和时间部分
                            parts = timestamp_raw.split('-')
                            if len(parts) >= 4:
                                date_part = f"{parts[0]}-{parts[1]}-{parts[2]}"
                                time_part = parts[3].replace('.', ':')
                                # 确保时间格式正确
                                time_components = time_part.split(':')
                                if len(time_components) >= 3:
                                    hour = time_components[0].zfill(2)
                                    minute = time_components[1].zfill(2)
                                    second = time_components[2].zfill(2)
                                    timestamp = f"{date_part} {hour}:{minute}:{second}"
                                else:
                                    timestamp = f"{date_part} 00:00:00"
                            else:
                                timestamp = timestamp_raw
                        else:
                            timestamp = timestamp_raw
                    except Exception:
                        timestamp = timestamp_raw
                
                # 跳过无效行
                if not account_number_field or not amount_raw:
                    continue
                
                # 更新账户信息
                if account_number_field and account_number_field not in [acc.get('account_number') for acc in sample_accounts]:
                    sample_accounts.append({
                        'account_number': account_number_field,
                        'card_number': card_number_field,
                        'cardholder_name': account_name or "未知",
                        'bank_name': '中国工商银行'
                    })
                
                # 解析金额
                amount = self._parse_amount(amount_raw, dr_cr_flag)
                
                # 构建标准交易记录
                transaction = {
                    'holder_name': account_name or "未知",
                    'account_number': account_number_field,
                    'card_number': card_number_field,
                    'transaction_amount': amount,
                    'dr_cr_flag': dr_cr_flag,
                    'currency': currency or 'CNY',
                    'timestamp': timestamp,  # 【重要修复】添加完整时间戳
                    'transaction_date': timestamp.split(' ')[0] if ' ' in timestamp else timestamp,
                    'transaction_time': timestamp.split(' ')[1] if ' ' in timestamp else "00:00:00"
                }
                
                sample_transactions.append(transaction)
            
            return {
                'accounts': sample_accounts,
                'transactions': sample_transactions[:limit],
                'metadata': {
                    'sample_size': len(sample_transactions),
                    'file_format': 'xls',
                    'sheet_used': target_sheet
                }
            }
            
        except Exception as e:
            logger.error(f"样本数据提取失败: {str(e)}")
            return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
    
    def _calculate_confidence_score(self, accounts: List[Dict], transactions: List[Dict]) -> float:
        """
        计算解析置信度
        
        Returns:
            float: 置信度分数 (0-100)
        """
        if not accounts or not transactions:
            return 0.0
        
        total_score = 0
        max_score = 100
        
        # 1. 账户信息完整性评估 (25分)
        account_score = 0
        valid_accounts = 0
        for account in accounts:
            # 账号或卡号有其一即可得分 - 按照用户要求修正
            account_number = account.get('account_number', '')
            card_number = account.get('card_number', '')
            if (account_number and len(account_number) >= 10) or card_number:
                account_score += 15  # 账号或卡号有效得15分
            if account.get('holder_name') and len(account['holder_name']) >= 2:
                account_score += 10  # 持卡人姓名有效得10分
            valid_accounts += 1
        
        if valid_accounts > 0:
            account_score = min(25, account_score / valid_accounts)
        
        # 2. 交易数据完整性评估 (25分)  
        transaction_score = 0
        valid_transactions = 0
        for transaction in transactions:
            score = 0
            if transaction.get('transaction_date'):
                score += 5
            if transaction.get('transaction_amount') != 0:
                score += 10
            if transaction.get('dr_cr_flag'):
                score += 5
            if transaction.get('counterparty_name') or transaction.get('remark1'):
                score += 5
            
            transaction_score += score
            valid_transactions += 1
        
        if valid_transactions > 0:
            transaction_score = min(25, transaction_score / valid_transactions)
        
        # 3. 数据格式一致性评估 (25分)
        format_score = 25  # 基础分
        
        # 检查日期格式一致性
        date_formats = set()
        for transaction in transactions[:10]:  # 只检查前10条
            date = transaction.get('transaction_date', '')
            if date:
                if re.match(r'^\d{4}-\d{2}-\d{2}$', date):
                    date_formats.add('standard')
                else:
                    date_formats.add('non_standard')
        
        if len(date_formats) > 1:
            format_score -= 10  # 日期格式不一致扣分
        
        # 4. 数据量合理性评估 (25分)
        volume_score = 25
        
        # 检查账户数量是否合理（1-50个账户比较正常）
        account_count = len(accounts)
        if account_count == 0:
            volume_score = 0
        elif account_count > 100:
            volume_score -= 10  # 账户过多可能是解析错误
        
        # 检查交易数量是否合理
        transaction_count = len(transactions)
        if transaction_count == 0:
            volume_score = 0
        elif transaction_count < 10:
            volume_score -= 5  # 交易过少可能不完整
        
        total_score = account_score + transaction_score + format_score + volume_score
        return min(100.0, max(0.0, total_score)) 