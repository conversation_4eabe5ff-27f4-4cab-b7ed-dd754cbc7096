"""
银行流水解析器API端点 - 纯插件系统架构
"""
from fastapi import APIRouter, UploadFile, File, Form, HTTPException
from typing import Dict, Any, List
import logging
import tempfile
import os
import pandas as pd
import re
import uuid
from datetime import datetime
from sqlalchemy.orm import Session
from fastapi import Depends
import time

from ..database.duckdb_config import get_db
from ..middleware.auth_middleware import get_current_user, get_user_db
from ..models.duckdb_models import DuckDBAccount as Account

# 🔧 重构：仅使用插件管理器 - 移除所有Enhanced解析器引用
from ..services.parser_plugin_system.core.plugin_manager import PluginManager
from ..services.parser.confidence_evaluator import ConfidenceEvaluator

logger = logging.getLogger(__name__)

router = APIRouter()

class SmartParserAnalyzer:
    """智能解析器分析器 - 纯插件系统架构"""
    
    def __init__(self):
        # 🔧 重构：统一使用插件管理器，移除Legacy解析器系统
        self.plugin_manager = None
        self.confidence_evaluator = ConfidenceEvaluator()  # 🔧 添加4维度评估器
        self._initialize_plugin_manager()
    
    def _initialize_plugin_manager(self):
        """初始化插件管理器"""
        try:
            logger.info("🔌 初始化插件管理器...")
            
            # 确保插件目录路径是绝对路径
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            plugins_dir = os.path.join(os.path.dirname(current_dir), "services", "parser_plugin_system", "plugins")
            logger.info(f"🔌 插件目录: {plugins_dir}")
            
            if not os.path.exists(plugins_dir):
                logger.error(f"❌ 插件目录不存在: {plugins_dir}")
                self.plugin_manager = None
                return False
            
            self.plugin_manager = PluginManager(plugins_dir)
            self.plugin_manager.start()
            
            # 检查插件状态
            loaded_plugins = self.plugin_manager.list_loaded_plugins()
            available_plugins = self.plugin_manager.registry.list_plugins()
            
            logger.info(f"✅ 插件管理器启动成功")
            logger.info(f"📦 已加载插件: {loaded_plugins}")
            logger.info(f"📋 可用插件: {available_plugins}")
            
            if len(available_plugins) == 0:
                logger.warning("⚠️ 未发现任何插件，检查插件目录和文件格式")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 插件管理器初始化失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            logger.error(f"❌ 插件管理器是唯一解析器入口，无法继续运行")
            self.plugin_manager = None
            return False
    
    def analyze_file_structure(self, file_path: str, bank_name: str) -> List[Dict[str, Any]]:
        """
        分析文件结构 - 纯插件系统架构
        """
        candidates = []
        
        try:
            logger.info(f"🔍 开始插件系统分析文件: {file_path}, 银行: {bank_name}")
            
            # 🔧 重构：仅使用插件系统，移除Enhanced解析器回退逻辑
            if not self.plugin_manager:
                logger.error("❌ 插件管理器未初始化，无法进行解析")
                return []
            
            candidates = self._analyze_with_plugin_system(file_path, bank_name)
            
            if candidates:
                logger.info(f"✅ 插件系统分析成功，找到{len(candidates)}个候选解析器")
            else:
                logger.warning("⚠️ 插件系统未找到合适解析器")
            
            logger.info(f"📊 分析完成，共找到{len(candidates)}个候选解析器")
            return candidates
            
        except Exception as e:
            logger.error(f"❌ 文件结构分析失败: {str(e)}")
            return []
    
    def _analyze_with_plugin_system(self, file_path: str, bank_name: str) -> List[Dict[str, Any]]:
        """使用插件系统分析文件"""
        candidates = []
        
        try:
            # 获取所有可用插件
            available_plugins = self.plugin_manager.registry.list_plugins()
            logger.info(f"🔍 检查 {len(available_plugins)} 个可用插件: {available_plugins}")
            
            # 过滤匹配的插件
            for plugin_name in available_plugins:
                try:
                    plugin_info = self.plugin_manager.registry.get_plugin(plugin_name)
                    if not plugin_info:
                        continue
                    
                    # 检查银行匹配（工商银行插件）
                    plugin_desc = plugin_info.get('description', '').lower()
                    plugin_meta = plugin_info.get('metadata', {})
                    supported_formats = plugin_meta.get('supported_formats', [])
                    
                    # 银行名称匹配检查（严格隔离 + 规范化同义词）
                    def norm_bank(s: str) -> str:
                        if not s:
                            return ''
                        raw = str(s).lower().strip()
                        # 先在原始字符串上匹配同义词，避免被“去中国/银行”等处理后变空串
                        mapping = {
                            '邮政储蓄': 'psbc', '邮储': 'psbc', 'psbc': 'psbc',
                            '工商': 'icbc', '工行': 'icbc', 'icbc': 'icbc',
                            '建设': 'ccb', '建行': 'ccb', 'ccb': 'ccb',
                            '中国银行': 'boc', '中行': 'boc', 'boc': 'boc',
                            '交通银行': 'bocm', '交行': 'bocm', 'bocm': 'bocm',
                            '农业': 'abc', '农行': 'abc', 'abc': 'abc',
                            '民生': 'cmbc', 'cmbc': 'cmbc',
                            '兴业': 'cib', 'cib': 'cib',
                            '平安': 'pingan', 'pingan': 'pingan',
                            '浦发': 'spdb', 'spdb': 'spdb',
                            '北部湾': 'beibuwan', 'beibuwan': 'beibuwan',
                            '农村信用社': 'rccu', '农信': 'rccu', 'rccu': 'rccu',
                            # 🔧 插件名称精确匹配
                            'bocm_format1_plugin': 'bocm',
                            'boc_format1_plugin': 'boc'
                        }
                        for k, v in mapping.items():
                            if k in raw:
                                return v
                        # 再做通用归一化
                        t = raw
                        for ch in ['中国', '股份有限公司', '股份', '有限公司', '银行', ' ']:
                            t = t.replace(ch.lower(), '')
                        if not t:
                            return raw
                        return t

                    target_code = norm_bank(bank_name)
                    meta_bank = plugin_meta.get('bank_name') or ''
                    plugin_code = norm_bank(meta_bank) or norm_bank(plugin_name) or norm_bank(plugin_desc)
                    # 🚫 关键修正：bocm/boc 歧义消解，优先依据插件名/描述进行强制判定
                    pl_name_l = plugin_name.lower()
                    pl_desc_l = plugin_desc.lower()
                    if 'bocm' in pl_name_l or '交通' in pl_desc_l:
                        plugin_code = 'bocm'
                    elif 'boc_format' in pl_name_l or '中国银行' in pl_desc_l:
                        plugin_code = 'boc'

                    name_l = plugin_name.lower()
                    is_universal = ('universal' in name_l) or ('通用' in plugin_desc)
                    # 仅当未指定银行时才返回通用模板
                    is_bank_match = (plugin_code == target_code) or (is_universal and not target_code)

                    if not is_bank_match:
                        logger.info(f"  ⏭️ 跳过不匹配银行的插件: {plugin_name} (target_code={target_code}, plugin_code={plugin_code}, meta_bank={meta_bank})")
                        continue

                    logger.info(f"  🎯 测试插件: {plugin_name}")
                    
                    # 加载插件（如果尚未加载）
                    if plugin_name not in self.plugin_manager.containers:
                        if not self.plugin_manager.load_plugin(plugin_name):
                            logger.warning(f"  ❌ 插件加载失败: {plugin_name}")
                            continue
                    
                    container = self.plugin_manager.containers[plugin_name]
                    if not container.plugin_instance:
                        logger.warning(f"  ❌ 插件实例不存在: {plugin_name}")
                        continue
                    
                    # 🔧 修复：使用4维度评估系统获取详细评分
                    logger.info(f"  🎯 开始4维度评估: {plugin_name}")

                    try:
                        # 使用ConfidenceEvaluator进行详细的4维度评估
                        evaluation_result = self.confidence_evaluator.quick_evaluate_parser(
                            container.plugin_instance, file_path, plugin_info
                        )

                        # 获取总分和详细评分
                        total_score = evaluation_result.get('total_score', 0)
                        core_metrics = evaluation_result.get('core_metrics', {})

                        # 🔧 兼容旧前端键名，构建别名结构（仅保留score/percentage）
                        alias_metrics = {}
                        if core_metrics:
                            def pick(m, k):
                                v = m.get(k, {})
                                return {
                                    'score': v.get('score', 0),
                                    'percentage': v.get('percentage', 0)
                                }
                            alias_metrics = {
                                'cardholder_recognition': pick(core_metrics, 'cardholder_name_score'),
                                'time_format_accuracy': pick(core_metrics, 'time_format_score'),
                                'account_recognition': pick(core_metrics, 'account_number_score'),
                                'amount_parsing': pick(core_metrics, 'amount_parsing_score')
                            }

                        logger.info(f"  📊 插件 {plugin_name} 4维度总分: {total_score}/100")

                        # 只有评分大于0才添加到候选列表；否则对“银行已匹配”的插件提供保底候选，便于人工选择
                        if total_score > 0:
                            # 构建详细的4维度评分信息
                            detailed_evaluation = {
                                'confidence': total_score,  # 使用4维度总分作为置信度
                                'confidence_percentage': total_score,
                                'match_reason': f"4维度智能评估完成 (总分:{total_score:.1f}/100)",
                                # 同时返回新旧两套键名，前端任取其一
                                'details': {**core_metrics, **alias_metrics},
                                'evaluation_breakdown': evaluation_result.get('evaluation_breakdown', {}),
                                'sample_analysis': evaluation_result.get('sample_analysis', {}),
                                'analysis_time': datetime.now().isoformat()
                            }

                            candidates.append({
                                'parser_id': plugin_name,
                                'parser_info': {
                                    'templateId': plugin_name,
                                    'templateName': plugin_info.get('description', plugin_name),
                                    'description': plugin_info.get('description', ''),
                                    'match_reason': f"4维度智能评估完成 (总分:{total_score:.1f}/100)"
                                },
                                'confidence_evaluation': detailed_evaluation
                            })

                            logger.info(f"  ✅ 插件 {plugin_name} 添加到候选列表，4维度评分: {total_score:.1f}/100")
                        else:
                            # 若银行名称已匹配但评分为0，尝试保底匹配：
                            # 1) 首先尝试 validate_file/ calculate_confidence；
                            # 2) 若仍无法确定，也返回一个极低置信度的候选，供前端人工选择。
                            fallback_conf = 0.0
                            try:
                                # 加强日志：验证/置信度接口是否可用
                                has_validate = hasattr(container.plugin_instance, 'validate_file')
                                has_calc = hasattr(container.plugin_instance, 'calculate_confidence')
                                logger.info(f"  🔎 保底检查: has_validate={has_validate}, has_calc={has_calc}")

                                if has_validate and container.plugin_instance.validate_file(file_path):
                                    fallback_conf = max(fallback_conf, 10.0)
                                if has_calc:
                                    c = container.plugin_instance.calculate_confidence(file_path)
                                    if c is not None:
                                        fallback_conf = max(fallback_conf, (c*100 if c <= 1 else float(c)))
                                logger.info(f"  🔎 保底结果: fallback_conf={fallback_conf}")
                            except Exception as _e:
                                logger.info(f"  ⚠️ 保底匹配评估异常(忽略): {plugin_name}: {_e}")

                            # 仅当银行匹配时才进行保底添加
                            if is_bank_match:
                                minimal_conf = fallback_conf if fallback_conf > 0 else 1.0
                                candidates.append({
                                    'parser_id': plugin_name,
                                    'parser_info': {
                                        'templateId': plugin_name,
                                        'templateName': plugin_info.get('description', plugin_name),
                                        'description': plugin_info.get('description', ''),
                                        'match_reason': '银行名称匹配(保底)，建议人工确认'
                                    },
                                    'confidence_evaluation': {
                                        'confidence': minimal_conf,
                                        'confidence_percentage': minimal_conf,
                                        'match_reason': '银行名称匹配(保底候选)',
                                        'details': alias_metrics or core_metrics or {},
                                        'evaluation_breakdown': evaluation_result.get('evaluation_breakdown', {}),
                                        'sample_analysis': evaluation_result.get('sample_analysis', {}),
                                        'analysis_time': datetime.now().isoformat()
                                    }
                                })
                                logger.info(f"  🤝 插件 {plugin_name} 评分为0，但银行匹配，作为保底候选返回")
                            else:
                                logger.info(f"  ❌ 插件 {plugin_name} 评分过低且银行不匹配，跳过: {total_score:.1f}/100")

                    except Exception as e:
                        logger.error(f"  ❌ 插件 {plugin_name} 4维度评估失败: {str(e)}")
                        # 回退到简单置信度计算
                        confidence = 0.0
                        if hasattr(container.plugin_instance, 'calculate_confidence'):
                            confidence = container.plugin_instance.calculate_confidence(file_path)
                        elif hasattr(container.plugin_instance, 'validate_file'):
                            if container.plugin_instance.validate_file(file_path):
                                confidence = 0.8

                        if confidence > 0:
                            candidates.append({
                                'parser_id': plugin_name,
                                'parser_info': {
                                    'templateId': plugin_name,
                                    'templateName': plugin_info.get('description', plugin_name),
                                    'description': plugin_info.get('description', ''),
                                    'match_reason': f"插件系统置信度: {confidence:.2f}"
                                },
                                'confidence_evaluation': {
                                    'confidence': confidence * 100,
                                    'confidence_percentage': confidence * 100,
                                    'match_reason': f"插件系统匹配，置信度: {confidence:.2f}",
                                    'details': {
                                        'plugin_name': plugin_name,
                                        'plugin_version': plugin_info.get('version', '1.0.0'),
                                        'supported_formats': plugin_info.get('supported_formats', []),
                                        'evaluation_error': str(e)
                                    },
                                    'analysis_time': datetime.now().isoformat()
                                }
                            })
                        else:
                            # 保底：评估异常且无置信度接口时，如果银行已匹配，也返回最低置信候选，避免仅剩通用解析器
                            if is_bank_match:
                                minimal_conf = 1.0
                                candidates.append({
                                    'parser_id': plugin_name,
                                    'parser_info': {
                                        'templateId': plugin_name,
                                        'templateName': plugin_info.get('description', plugin_name),
                                        'description': plugin_info.get('description', ''),
                                        'match_reason': '评估异常(保底候选)，建议人工确认'
                                    },
                                    'confidence_evaluation': {
                                        'confidence': minimal_conf,
                                        'confidence_percentage': minimal_conf,
                                        'match_reason': '评估异常(保底候选)',
                                        'details': {
                                            'plugin_name': plugin_name,
                                            'plugin_version': plugin_info.get('version', '1.0.0'),
                                            'fallback': 'exception_fallback'
                                        },
                                        'analysis_time': datetime.now().isoformat()
                                    }
                                })

                except Exception as e:
                    logger.error(f"  ❌ 测试插件失败 {plugin_name}: {e}")
                    continue
            
            # 按置信度排序
            candidates.sort(key=lambda x: x['confidence_evaluation']['confidence'], reverse=True)
            
            logger.info(f"🔌 插件系统分析完成，找到 {len(candidates)} 个候选插件")
            return candidates
            
        except Exception as e:
            logger.error(f"❌ 插件系统分析异常: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return []
    

    
    def execute_parser(self, parser_id: str, file_path: str) -> Dict[str, Any]:
        """执行解析器 - 纯插件系统版本"""
        try:
            logger.info(f"🚀 执行解析器: {parser_id}")
            
            # 检查插件管理器是否可用
            if not self.plugin_manager:
                error_msg = "插件管理器未初始化，无法执行解析"
                logger.error(f"❌ {error_msg}")
                return {
                    'success': False,
                    'error': error_msg,
                    'message': error_msg,
                    'accounts': [],
                    'transactions': []
                }
            
            # 检查解析器是否存在
            if parser_id not in self.plugin_manager.registry.list_plugins():
                error_msg = f"未找到解析器插件: {parser_id}"
                logger.error(f"❌ {error_msg}")
                return {
                    'success': False,
                    'error': error_msg,
                    'message': error_msg,
                    'accounts': [],
                    'transactions': []
                }
            
            # 使用插件系统执行解析
            logger.info(f"🔌 使用插件系统执行: {parser_id}")
            result = self.plugin_manager.execute_plugin(parser_id, file_path)
            
            if result.get('success'):
                logger.info(f"✅ 插件解析成功")
            else:
                logger.warning(f"❌ 插件解析失败: {result.get('message', '未知错误')}")
            
            return result
                
        except Exception as e:
            error_msg = f"解析器执行异常: {str(e)}"
            logger.error(f"❌ {error_msg}")
            import traceback
            logger.error(traceback.format_exc())
            
            return {
                'success': False,
                'error': error_msg,
                'message': error_msg,
                'accounts': [],
                'transactions': []
            }
    
    def get_templates_list(self, bank_name: str = None) -> List[Dict[str, Any]]:
        """获取模板列表 - 兼容前端API调用"""
        templates = []
        
        try:
            logger.info(f"🔍 获取模板列表，请求银行: {bank_name}")
            
            # 从插件系统获取模板
            if self.plugin_manager:
                available_plugins = self.plugin_manager.registry.list_plugins()
                logger.info(f"📋 插件系统可用插件数量: {len(available_plugins)}")
                logger.info(f"📋 插件列表: {available_plugins}")
                
                for plugin_name in available_plugins:
                    plugin_info = self.plugin_manager.registry.get_plugin(plugin_name)
                    if plugin_info:
                        logger.info(f"📦 处理插件: {plugin_name}")
                        
                        # 🔧 关键修复：动态获取插件元数据以获取正确的银行名称
                        logger.info(f"   🔍 开始处理插件银行名称: {plugin_name}")
                        try:
                            # 通过容器获取插件实例
                            logger.info(f"   🔍 检查插件容器是否存在: {plugin_name in self.plugin_manager.containers}")
                            if plugin_name in self.plugin_manager.containers:
                                container = self.plugin_manager.containers[plugin_name]
                                logger.info(f"   🔍 容器.plugin_instance存在: {container.plugin_instance is not None}")
                                logger.info(f"   🔍 容器.plugin_instance有get_metadata: {hasattr(container.plugin_instance, 'get_metadata') if container.plugin_instance else False}")
                                
                                if container.plugin_instance and hasattr(container.plugin_instance, 'get_metadata'):
                                    dynamic_metadata = container.plugin_instance.get_metadata()
                                    plugin_bank_name = dynamic_metadata.get('bank_name', '未知银行')
                                    logger.info(f"   ✅ 从插件实例获取银行名称: {plugin_name} -> {plugin_bank_name}")
                                    logger.info(f"   🔍 动态元数据keys: {list(dynamic_metadata.keys())}")
                                else:
                                    plugin_bank_name = self._extract_bank_name(plugin_info)
                                    logger.info(f"   📝 从静态信息提取银行名称: {plugin_name} -> {plugin_bank_name}")
                            else:
                                plugin_bank_name = self._extract_bank_name(plugin_info)
                                logger.warning(f"   ⚠️ 插件容器不存在: {plugin_name}")
                        except Exception as e:
                            logger.error(f"   ❌ 获取插件元数据异常 {plugin_name}: {e}")
                            logger.exception("异常详情:")
                            plugin_bank_name = self._extract_bank_name(plugin_info)
                        
                        # 银行过滤（与预分析相同的归一化匹配，兼容“中国××银行”等同义词）
                        if bank_name:
                            def _norm_bank(s: str) -> str:
                                if not s:
                                    return ''
                                raw = str(s).lower().strip()
                                # 先在原始字符串上匹配同义词，避免被“去中国/银行”等处理后变空串
                                mapping = {
                                    '邮政储蓄': 'psbc', '邮储': 'psbc', 'psbc': 'psbc',
                                    '工商': 'icbc', '工行': 'icbc', 'icbc': 'icbc',
                                    '建设': 'ccb', '建行': 'ccb', 'ccb': 'ccb',
                                    '中国银行': 'boc', '中行': 'boc', 'boc': 'boc',
                                    '交通银行': 'bocm', '交行': 'bocm', 'bocm': 'bocm',
                                    '农业': 'abc', '农行': 'abc', 'abc': 'abc',
                                    '民生': 'cmbc', 'cmbc': 'cmbc',
                                    '兴业': 'cib', 'cib': 'cib',
                                    '平安': 'pingan', 'pingan': 'pingan',
                                    '浦发': 'spdb', 'spdb': 'spdb',
                                    '北部湾': 'beibuwan', 'beibuwan': 'beibuwan',
                                    '农村信用社': 'rccu', '农信': 'rccu', 'rccu': 'rccu'
                                }
                                for k, v in mapping.items():
                                    if k in raw:
                                        return v
                                # 再做通用归一化
                                t = raw
                                for ch in ['中国', '股份有限公司', '股份', '有限公司', '银行', ' ']:
                                    t = t.replace(ch.lower(), '')
                                if not t:
                                    return raw
                                return t
                            target_code = _norm_bank(bank_name)
                            plugin_code = _norm_bank(plugin_bank_name) or _norm_bank(plugin_info.get('name', '')) or _norm_bank(plugin_info.get('description', ''))
                            is_universal = ('universal' in str(plugin_info.get('name','')).lower()) or ('通用' in str(plugin_info.get('description','')).lower())
                            # 仅当未指定银行时才允许通用模板
                            bank_matched = (plugin_code == target_code) or (is_universal and not target_code)

                            # 🔧 调试日志：显示匹配过程
                            logger.info(f"   🔍 银行匹配调试 {plugin_name}: 请求银行='{bank_name}' -> code='{target_code}', 插件银行='{plugin_bank_name}' -> code='{plugin_code}', 匹配={bank_matched}")
                            if not bank_matched:
                                logger.info(f"   ⏭️ 插件 {plugin_name} 不匹配银行 {bank_name} (插件银行: {plugin_bank_name})")
                                continue
                        
                        template_info = {
                            'templateId': plugin_name,
                            'templateName': plugin_info.get('description', plugin_name),
                            'bankName': plugin_bank_name,
                            'description': plugin_info.get('description', ''),
                            'version': plugin_info.get('version', '1.0.0'),
                            'source': 'plugin_system'
                        }
                        templates.append(template_info)
                        logger.info(f"   ✅ 添加模板: {template_info['templateName']} (银行: {plugin_bank_name})")
            else:
                logger.warning("⚠️ 插件管理器未初始化，提供备用模板")
                # 提供备用模板确保系统可用性
                backup_templates = [
                    {
                        'templateId': 'icbc_format1_backup',
                        'templateName': '工商银行个人账户模板(备用)',
                        'bankName': '中国工商银行',
                        'description': '工商银行个人账户解析模板',
                        'version': '1.0.0',
                        'source': 'backup_system'
                    },
                    {
                        'templateId': 'ccb_format1_backup',
                        'templateName': '建设银行标准模板(备用)',
                        'bankName': '中国建设银行',
                        'description': '建设银行标准Excel格式解析模板',
                        'version': '1.0.0',
                        'source': 'backup_system'
                    },
                    {
                        'templateId': 'beibuwan_format1_backup',
                        'templateName': '北部湾银行模板(备用)',
                        'bankName': '北部湾银行',
                        'description': '北部湾银行流水解析模板',
                        'version': '1.0.0',
                        'source': 'backup_system'
                    }
                ]
                
                # 银行过滤备用模板
                for template in backup_templates:
                    if not bank_name or bank_name in template['bankName']:
                        templates.append(template)
            
            logger.info(f"✅ 最终返回模板数量: {len(templates)}")
            return templates
            
        except Exception as e:
            logger.error(f"❌ 获取模板列表失败: {e}")
            return []
    
    def _extract_bank_name(self, plugin_info: Dict) -> str:
        """从插件信息中提取银行名称"""
        plugin_name = plugin_info.get('name', '').lower()
        plugin_desc = plugin_info.get('description', '').lower()
        
        # 🔧 关键修复：优先使用插件元数据中的 bank_name
        plugin_meta = plugin_info.get('metadata', {})
        if plugin_meta and 'bank_name' in plugin_meta:
            return plugin_meta['bank_name']
        
        # 🔧 扩展银行名称识别逻辑，添加更多银行支持
        if 'icbc' in plugin_name or '工商银行' in plugin_desc:
            return '中国工商银行'
        elif 'cmbc' in plugin_name or '民生银行' in plugin_desc:
            return '中国民生银行'
        elif 'beibuwan' in plugin_name or '北部湾银行' in plugin_desc:
            return '北部湾银行'
        elif 'ccb' in plugin_name or '建设银行' in plugin_desc:
            return '中国建设银行'
        elif 'boc' in plugin_name or '中国银行' in plugin_desc and '工商' not in plugin_desc and '建设' not in plugin_desc:
            return '中国银行'
        elif 'abc' in plugin_name or '农业银行' in plugin_desc:
            return '中国农业银行'
        elif 'bocm' in plugin_name or '交通银行' in plugin_desc:
            return '交通银行'
        elif 'psbc' in plugin_name or '邮政储蓄' in plugin_desc or '邮政银行' in plugin_desc:
            return '中国邮政储蓄银行'
        elif 'spdb' in plugin_name or '浦发' in plugin_desc or '上海浦东发展银行' in plugin_desc:
            return '上海浦东发展银行'
        elif 'cib' in plugin_name or '兴业银行' in plugin_desc:
            return '兴业银行'
        elif 'pingan' in plugin_name or '平安银行' in plugin_desc:
            return '平安银行'
        elif 'rccu' in plugin_name or '农村信用社' in plugin_desc or '农信' in plugin_desc:
            return '农村信用社'
        elif 'universal' in plugin_name or '通用' in plugin_desc:
            return '通用模板'
        else:
            return '未知银行'
    
    def _evaluate_parser_with_new_system(self, parser_id: str, parser_info: Dict, 
                                       file_path: str, file_ext: str) -> Dict[str, Any]:
        """使用新的快速置信度评估系统"""
        
        try:
            # 文件格式预检查
            if file_ext not in parser_info['supported_formats']:
                return {
                    'confidence': 0,
                    'confidence_percentage': 0,
                    'match_reason': f"文件格式{file_ext}不支持，期望格式: {parser_info['supported_formats']}",
                    'details': {
                        'file_format_supported': False,
                        'evaluation_results': {}
                    },
                    'analysis_time': datetime.now().isoformat()
                }
            
            # 使用新的快速评估系统
            parser_class = parser_info['class']
            result = self.confidence_evaluator.quick_evaluate_parser(
                parser_class, file_path, parser_info
            )
            
            # 添加文件格式匹配信息
            if result['confidence'] > 0:
                result['match_reason'] += f"; 文件格式{file_ext}匹配"
            
            return result
            
        except Exception as e:
            logger.error(f"快速评估解析器{parser_id}失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            
            return {
                'confidence': 0,
                'confidence_percentage': 0,
                'match_reason': f"评估失败: {str(e)}",
                'details': {
                    'evaluation_error': str(e),
                    'evaluation_results': {}
                },
                'analysis_time': datetime.now().isoformat()
            }

# 创建全局分析器实例
smart_analyzer = SmartParserAnalyzer()

@router.get("/")
async def parser_root():
    """解析器API根路径"""
    return {
        "message": "银行流水解析器API",
        "version": "2.0.0",
        "endpoints": {
            "pre_analyze": "/pre-analyze",
            "analyze": "/analyze", 
            "save": "/save",
            "check_duplicates": "/check-duplicates"
        },
        "available_parsers": len(smart_analyzer.plugin_manager.registry.list_plugins()) if smart_analyzer.plugin_manager else 0
    }

@router.post("/pre-analyze")
async def pre_analyze_file(
    file: UploadFile = File(...),
    bank_name: str = Form(...)
) -> Dict[str, Any]:
    """智能预解析文件，返回真实置信率"""

    # 🔧 修复：确保银行名称正确编码
    try:
        # 处理可能的编码问题
        if isinstance(bank_name, bytes):
            bank_name = bank_name.decode('utf-8')
        elif isinstance(bank_name, str):
            # 确保字符串是正确的UTF-8编码
            bank_name = bank_name.encode('utf-8').decode('utf-8')
    except (UnicodeDecodeError, UnicodeEncodeError) as e:
        logger.warning(f"银行名称编码处理失败: {e}, 原始值: {repr(bank_name)}")
        # 使用默认值
        bank_name = "未知银行"

    logger.info(f"🔍 预解析API被调用 - 文件: {file.filename}, 银行: {bank_name}")

    # 写入调试日志
    with open("debug_preanalyze.log", "a", encoding="utf-8") as f:
        f.write(f"🔍 预解析API被调用 - 文件: {file.filename}, 银行: {bank_name}\n")
        f.flush()

    temp_file_path = None
    
    try:
        import sys
        print(f"🚨🚨🚨 预解析API被调用: {file.filename}, 银行: {bank_name} 🚨🚨🚨", flush=True)
        sys.stdout.flush()
        logger.error(f"🚨🚨🚨 预解析API被调用: {file.filename}, 银行: {bank_name} 🚨🚨🚨")
        logger.info(f"开始智能预解析文件: {file.filename}, 银行: {bank_name}")
        
        # 保存临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        # 进行智能分析
        logger.info(f"🔍 API调用analyze_file_structure，使用全局smart_analyzer实例")
        logger.info(f"📦 插件管理器状态: {smart_analyzer.plugin_manager is not None}")
        if smart_analyzer.plugin_manager:
            logger.info(f"📋 可用插件数量: {len(smart_analyzer.plugin_manager.registry.list_plugins())}")
            logger.info(f"📋 可用插件列表: {smart_analyzer.plugin_manager.registry.list_plugins()}")

        candidates = smart_analyzer.analyze_file_structure(temp_file_path, bank_name)

        logger.info(f"📊 API返回候选解析器数量: {len(candidates)}")
        for i, candidate in enumerate(candidates):
            parser_id = candidate.get('parser_id', 'Unknown')
            confidence = candidate.get('confidence_evaluation', {}).get('confidence', 0)
            logger.info(f"   {i+1}. {parser_id}: {confidence}分")
        
        if not candidates:
            return {
                'success': False,
                'message': '未找到适合的解析器',
                'candidates': [],
                'best_parser': None
            }
        
        # 选择最佳解析器
        best_parser = candidates[0]
        
        logger.info(f"智能分析完成，最佳解析器: {best_parser['parser_id']}, 置信度: {best_parser['confidence_evaluation']['confidence']}%")
        
        return {
            'success': True,
            'message': '🚨🚨🚨 调试模式：智能分析完成 🚨🚨🚨',
            'candidates': candidates,
            'best_parser': best_parser,
            'analysis_summary': {
                'total_candidates': len(candidates),
                'best_confidence': best_parser['confidence_evaluation']['confidence'],
                'file_name': file.filename,
                'bank_name': bank_name,
                'debug_info': '🚨🚨🚨 预解析API被调用 🚨🚨🚨'
            }
        }
        
    except Exception as e:
        logger.error(f"智能预解析失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        
        raise HTTPException(
            status_code=500,
            detail=f"智能预解析失败: {str(e)}"
        )
    
    finally:
        # 清理临时文件
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
            except:
                pass

@router.post("/analyze")
async def analyze_file(
    file: UploadFile = File(...),
    bank_name: str = Form(...),
    template_id: str = Form(None)
) -> Dict[str, Any]:
    """解析银行流水文件"""
    temp_file_path = None
    
    try:
        logger.info(f"开始解析文件: {file.filename}, 银行: {bank_name}, 模板: {template_id}")
        
        # 保存临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        # 🔧 先进行置信度评估以获取置信度详情
        logger.info("新置信度评估开始...")
        candidates = smart_analyzer.analyze_file_structure(temp_file_path, bank_name)
        confidence_details = {}
        
        if candidates:
            for candidate in candidates:
                parser_id = candidate['parser_id']
                confidence_eval = candidate['confidence_evaluation']
                confidence_details[parser_id] = confidence_eval
                logger.info(f"解析器 {parser_id} 置信度: {confidence_eval.get('confidence', 0)}%")
        
        logger.info(f"新置信度评估完成，找到{len(candidates)}个候选解析器")
        
        # 🔧 重要修复：选择解析器 - 支持插件系统
        if template_id:
            # 检查指定的模板ID是否存在
            if (template_id not in confidence_details and 
                (not smart_analyzer.plugin_manager or template_id not in smart_analyzer.plugin_manager.registry.list_plugins())):
                raise HTTPException(status_code=400, detail=f"未知的模板ID: {template_id}")
                
            # 获取指定解析器的置信度
            selected_confidence = confidence_details.get(template_id, {}).get('confidence', 0)
        else:
            # 如果没有指定模板，进行智能选择
            if not candidates:
                raise HTTPException(status_code=400, detail="未找到适合的解析器")
            
            best_parser = candidates[0]
            template_id = best_parser['parser_id']
            selected_confidence = best_parser['confidence_evaluation']['confidence']
            
            logger.info(f"智能选择解析器: {template_id}, 置信度: {selected_confidence}%")
        
        # 🔧 重要修复：使用统一的执行方法
        logger.info(f"🚀 执行解析，解析器: {template_id}")
        result = smart_analyzer.execute_parser(template_id, temp_file_path)
        
        if result.get('success'):
            # 【重要修复】使用正确的字段获取统计信息
            total_accounts = result.get('metadata', {}).get('total_accounts', len(result.get('accounts', [])))
            
            # 🔧 修复：正确处理所有交易数据
            all_transactions = []
            transactions_by_account = result.get('transactions_by_account', {})
            
            # 收集所有账户的交易
            for account_number, transactions in transactions_by_account.items():
                if transactions:
                    all_transactions.extend(transactions)
            
            # 如果没有transactions_by_account，尝试从直接的transactions字段获取
            if not all_transactions and result.get('transactions'):
                all_transactions = result.get('transactions', [])
                
            total_transactions = len(all_transactions)
            
            logger.info(f"解析成功，账户数: {total_accounts}, 交易数: {total_transactions}")
            logger.info(f"API返回交易总数: {len(all_transactions)}")
            
            return {
                'success': True,
                'message': '解析成功',
                'parse_result': {
                    'accounts': result.get('accounts', []),
                    'transactions': all_transactions,
                    'summary': {
                        'total_accounts': total_accounts,
                        'total_transactions': total_transactions
                    },
                    'metadata': result.get('metadata', {})
                },
                'parser_used': template_id,
                'final_confidence': selected_confidence,  # 🔧 添加置信度信息
                'confidence_details': confidence_details  # 🔧 添加详细置信度信息
            }
        else:
            error_msg = result.get('error', '解析失败')
            logger.error(f"解析失败: {error_msg}")
            raise HTTPException(status_code=400, detail=error_msg)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"解析文件失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        
        raise HTTPException(
            status_code=500,
            detail=f"解析文件失败: {str(e)}"
        )
    
    finally:
        # 清理临时文件
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
            except:
                pass

@router.post("/save")
async def save_parsing_result_deprecated(
    data: Dict[str, Any],
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    ⚠️ 已弃用：此API性能较差，请使用 save-optimized 替代
    
    此API平均耗时217秒处理15K条记录，已被性能优化版本替代。
    请改用 /api/v1/parser/save-optimized，可获得14倍性能提升（15秒）。
    """
    raise HTTPException(
        status_code=410, 
        detail="此API已弃用，请使用 /api/v1/parser/save-optimized 获得14倍性能提升（217秒→15秒）"
    )

@router.post("/check-duplicates")
async def check_duplicate_accounts(
    data: Dict[str, Any],
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    检查重复账户数据
    
    Args:
        data: 包含账户列表的数据
        db: 数据库会话
        
    Returns:
        Dict: 重复检查结果
    """
    try:
        from ..models.duckdb_models import DuckDBAccount as Account
        
        accounts = data.get('accounts', [])
        project_id = data.get('project_id', '')
        
        logger.info(f"检查{len(accounts)}个账户的重复情况，项目ID: {project_id}")
        
        duplicates = []
        unique = []
        
        for account in accounts:
            holder_name = account.get('holder_name', '')
            account_number = account.get('account_number', '')
            card_number = account.get('card_number', '')
            
            if not holder_name or (not account_number and not card_number):
                logger.warning(f"跳过无效账户数据: {account}")
                continue
            
            # 查询数据库中是否存在相同的账户
            # 基于项目ID + 人员姓名 + (账户号OR卡号)
            query = db.query(Account).filter(
                Account.project_id == project_id,
                Account.person_name == holder_name
            )
            
            # 添加账户号或卡号的OR条件
            account_conditions = []
            if account_number:
                account_conditions.extend([
                    Account.account_number == account_number,
                    Account.card_number == account_number
                ])
            if card_number and card_number != account_number:
                account_conditions.extend([
                    Account.account_number == card_number,
                    Account.card_number == card_number
                ])
            
            existing_account = None
            if account_conditions:
                from sqlalchemy import or_
                existing_account = query.filter(or_(*account_conditions)).first()
            
            if existing_account:
                duplicates.append({
                    "holder_name": holder_name,
                    "account_number": account_number,
                    "card_number": card_number,
                    "existing_account_id": existing_account.account_id,
                    "existing_account_number": existing_account.account_number,
                    "existing_card_number": existing_account.card_number,
                    "duplicate_reason": f"账户信息与数据库中现有数据重复（持卡人: {existing_account.person_name}）"
                })
            else:
                unique.append(account)
        
        logger.info(f"发现{len(duplicates)}个重复账户，{len(unique)}个唯一账户")
        
        return {
            "duplicates": duplicates,
            "unique": unique,
            "total_checked": len(accounts),
            "duplicate_count": len(duplicates),
            "unique_count": len(unique),
            "message": f"检查完成，发现{len(duplicates)}个重复账户",
            "has_duplicates": len(duplicates) > 0
        }
        
    except Exception as e:
        logger.error(f"检查重复账户失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"检查重复账户失败: {str(e)}"
        )

@router.post("/save-optimized")
async def save_parsing_result_optimized(
    data: Dict[str, Any],
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> Dict[str, Any]:
    """
    超级优化版保存解析结果
    - 上传性能：单次提交，批量操作，预处理优化
    - 读取性能：保持索引，优化表结构，列式存储友好
    """
    try:
        from sqlalchemy import text
        import pandas as pd
        import tempfile
        from ..models.duckdb_models import DuckDBAccount, DuckDBTransaction
        
        logger.info(f"🚀 开始超级优化保存，项目ID: {data.get('project_id')}")
        
        project_id = data.get('project_id')
        accounts_data = data.get('accounts', [])
        transactions_data = data.get('transactions', [])
        metadata = data.get('metadata', {})
        force_overwrite = data.get('force_overwrite', False)
        
        if not project_id:
            raise HTTPException(status_code=400, detail="项目ID不能为空")
        
        start_time = time.time()
        
        # 🚀 优化1: 关闭自动提交，使用大事务 (移除手动BEGIN)
        # SQLAlchemy会自动管理事务
        
        # 🚀 优化2: 预加载现有账户映射 (避免重复查询)
        logger.info("📊 预加载账户映射...")
        existing_accounts = {}
        if force_overwrite:
            accounts_query = db.execute(text("""
                SELECT account_id, person_name, account_number, card_number 
                FROM accounts 
                WHERE project_id = :project_id
            """), {"project_id": project_id})
            
            for row in accounts_query:
                key = f"{row.person_name}_{row.account_number}_{row.card_number}"
                existing_accounts[key] = row.account_id
            
            logger.info(f"   预加载了 {len(existing_accounts)} 个现有账户")
        
        # 🚀 优化3: 批量删除 (覆盖模式)
        deleted_transactions = 0
        if force_overwrite and existing_accounts:
            delete_start = time.time()
            account_ids = list(existing_accounts.values())
            
            # 删除现有交易记录
            for account_id in account_ids:
                delete_result = db.execute(text("""
                    DELETE FROM transactions WHERE account_id = :account_id
                """), {"account_id": account_id})
                deleted_transactions += 1
            
            delete_time = time.time() - delete_start
            logger.info(f"⚡ 高速删除完成，用时: {delete_time:.2f}秒")
        
        # 🚀 优化4: 预处理数据
        accounts_saved = 0
        transactions_saved = 0
        overwritten_accounts = 0  # 添加覆盖账户计数
        
        # 账户映射 (新建或现有)
        account_mapping = {}
        accounts_to_create = []
        
        for account_data in accounts_data:
            holder_name = account_data.get('holder_name', '')
            account_number = account_data.get('account_number', '')
            card_number = account_data.get('card_number', '')
            account_key = f"{holder_name}_{account_number}_{card_number}"
            
            if account_key in existing_accounts:
                # 🔧 修复：重用现有账户时也要计数
                account_mapping[account_key] = existing_accounts[account_key]
                overwritten_accounts += 1  # 覆盖现有账户
            else:
                account_id = str(uuid.uuid4())
                account_mapping[account_key] = account_id
                accounts_to_create.append({
                    'account_id': account_id,
                    'project_id': project_id,
                    'person_name': holder_name,
                    'bank_name': account_data.get('bank_name', ''),
                    'account_name': account_data.get('account_name', ''),
                    'account_number': account_number,
                    'card_number': card_number,
                    'import_file_source': metadata.get('file_name', ''),
                    'creation_timestamp': datetime.now().isoformat(),
                    'created_at': datetime.now(),
                    'updated_at': datetime.now()
                })
                accounts_saved += 1  # 新建账户
        
        # 🚀 优化5: 单次批量插入账户
        if accounts_to_create:
            db.bulk_insert_mappings(DuckDBAccount, accounts_to_create)
            logger.info(f"✅ 高速插入 {len(accounts_to_create)} 个账户")
        
        # 🚀 优化6: 预处理交易数据为单次批量插入
        if transactions_data:
            logger.info(f"🔄 开始超高速批量插入 {len(transactions_data)} 条交易...")
            
            current_time = datetime.now()
            transactions_list = []
            
            for tx_data in transactions_data:
                holder_name = tx_data.get('holder_name', '')
                account_number = tx_data.get('account_number', '')
                card_number = tx_data.get('card_number', '')
                triplet_key = f"{holder_name}_{account_number}_{card_number}"
                account_id = account_mapping.get(triplet_key)
                
                if not account_id:
                    continue
                
                # 预计算所有字段
                remarks = f"{tx_data.get('remark1', '')} {tx_data.get('remark2', '')} {tx_data.get('remark3', '')}".strip()
                balance = float(tx_data.get('balance_amount', 0)) if tx_data.get('balance_amount') else None
                
                transactions_list.append({
                    'transaction_id': str(uuid.uuid4()),
                    'project_id': project_id,
                    'account_id': account_id,
                    'person_name': holder_name,
                    'bank_name': tx_data.get('bank_name', ''),
                    'account_name': tx_data.get('account_name', ''),
                    'account_number': account_number,
                    'card_number': card_number,
                    'transaction_datetime': tx_data.get('transaction_datetime', ''),
                    'transaction_method': tx_data.get('transaction_method', ''),
                    'transaction_amount': float(tx_data.get('transaction_amount', 0)),
                    'balance': balance,
                    'dr_cr_flag': tx_data.get('dr_cr_flag', ''),
                    'counterparty_account_number': tx_data.get('counterparty_account', ''),
                    'counterparty_account_name': tx_data.get('counterparty_name', ''),
                    'counterparty_bank_name': tx_data.get('counterparty_bank', ''),
                    'remarks': remarks,
                    'original_currency': 'CNY',
                    'raw_data_snapshot': str(tx_data.get('raw_data', {})),
                    'cleaning_timestamp': current_time.isoformat(),
                    'created_at': current_time,
                    'updated_at': current_time
                })
            
            # 🚀 优化7: 单次批量插入所有交易
            if transactions_list:
                copy_start = time.time()
                
                db.bulk_insert_mappings(DuckDBTransaction, transactions_list)
                transactions_saved = len(transactions_list)
                
                copy_time = time.time() - copy_start
                copy_speed = transactions_saved / copy_time if copy_time > 0 else 0
                
                logger.info(f"🎉 超大批次插入完成！{transactions_saved} 条交易，用时: {copy_time:.2f}秒，速度: {copy_speed:.0f}条/秒")
        
        # 🚀 优化8: 单次提交所有操作 (SQLAlchemy自动管理)
        commit_start = time.time()
        db.commit()
        commit_time = time.time() - commit_start
        
        total_time = time.time() - start_time
        overall_speed = (accounts_saved + transactions_saved) / total_time if total_time > 0 else 0
        
        logger.info(f"🎉 超级优化保存完成！总用时: {total_time:.2f}秒，速度: {overall_speed:.0f}条/秒")
        
        return {
            'success': True,
            'message': '超级优化保存成功',
            'saved_accounts': accounts_saved,
            'saved_transactions': transactions_saved,
            'overwritten_accounts': overwritten_accounts,
            'deleted_transactions': deleted_transactions,
            'performance_info': {
                'total_time': round(total_time, 2),
                'overall_speed': round(overall_speed, 0),
                'optimization_level': 'SINGLE_BATCH_COMMIT',
                'batch_strategy': 'BULK_INSERT_ALL_AT_ONCE',
                'commit_strategy': 'SINGLE_TRANSACTION'
            }
        }
        
    except Exception as e:
        logger.error(f"超级优化保存失败: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"超级优化保存失败: {str(e)}")

@router.post("/create-performance-indexes")
async def create_performance_indexes(db: Session = Depends(get_db)):
    """
    创建性能优化索引 - 平衡写入和读取性能
    """
    try:
        from sqlalchemy import text
        
        logger.info("📊 开始创建性能优化索引...")
        
        # 索引策略：支持常用查询模式，不过度索引影响写入性能
        indexes = [
            # 项目查询索引 (最常用)
            "CREATE INDEX IF NOT EXISTS idx_accounts_project ON accounts(project_id)",
            "CREATE INDEX IF NOT EXISTS idx_transactions_project ON transactions(project_id)",
            
            # 账户关联索引 (连接查询)
            "CREATE INDEX IF NOT EXISTS idx_transactions_account ON transactions(account_id)",
            
            # 时间范围查询索引 (AI分析常用)
            "CREATE INDEX IF NOT EXISTS idx_transactions_datetime ON transactions(transaction_datetime)",
            
            # 金额查询索引 (大额交易分析)
            "CREATE INDEX IF NOT EXISTS idx_transactions_amount ON transactions(transaction_amount)",
            
            # 复合索引 (常用组合查询)
            "CREATE INDEX IF NOT EXISTS idx_transactions_project_datetime ON transactions(project_id, transaction_datetime)",
            "CREATE INDEX IF NOT EXISTS idx_accounts_project_person ON accounts(project_id, person_name)"
        ]
        
        for idx_sql in indexes:
            try:
                db.execute(text(idx_sql))
                logger.info(f"✅ 创建索引: {idx_sql.split()[-1]}")
            except Exception as e:
                logger.warning(f"⚠️ 索引创建跳过: {e}")
        
        # 更新统计信息以优化查询计划
        db.execute(text("ANALYZE"))
        db.commit()
        
        return {
            'success': True,
            'message': '性能索引创建完成',
            'indexes_created': len(indexes)
        }
        
    except Exception as e:
        logger.error(f"创建索引失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建索引失败: {str(e)}") 

@router.get("/templates")
async def get_templates(bank_name: str = None):
    """获取可用的解析器模板列表 - 插件系统版本"""
    try:
        logger.info(f"获取解析器模板列表，银行: {bank_name}")
        
        # 创建SmartParserAnalyzer实例
        smart_analyzer = SmartParserAnalyzer()
        
        # 获取模板列表
        templates = smart_analyzer.get_templates_list(bank_name)
        
        logger.info(f"获取到 {len(templates)} 个模板")
        
        return {
            "success": True,
            "templates": templates,
            "count": len(templates),
            "bank_name": bank_name
        }
        
    except Exception as e:
        logger.error(f"获取模板列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取模板列表失败: {str(e)}")

@router.get("/v2/templates")
async def get_templates_v2(bank_name: str = None):
    """获取可用的解析器模板列表 - v2版本（兼容性）"""
    try:
        logger.info(f"V2 API: 获取解析器模板列表，银行: {bank_name}")
        
        # 创建SmartParserAnalyzer实例
        smart_analyzer = SmartParserAnalyzer()
        
        # 获取模板列表
        templates = smart_analyzer.get_templates_list(bank_name)
        
        logger.info(f"V2 API: 获取到 {len(templates)} 个模板")
        
        # V2 API直接返回模板数组（保持向后兼容）
        return templates
        
    except Exception as e:
        logger.error(f"V2 API: 获取模板列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取模板列表失败: {str(e)}")