"""
数据清洗API模块
提供数据清洗规则管理和应用功能
"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel

from ..middleware.auth_middleware import get_current_user, get_user_db
from ..services.cleaner.data_cleaner import DataCleaner
from ..services.cleaner.cleaner_rules import CleanerRule, DateTimeRule, AmountRule, TextRule
from ..models.duckdb_models import DuckDBRawTransaction as RawTransaction, DuckDBTransaction as Transaction, DuckDBAccount as Account, DuckDBProject as Project

logger = logging.getLogger(__name__)

router = APIRouter()

# 请求模型
class PreviewRequest(BaseModel):
    raw_transaction_id: str

class ApplyRequest(BaseModel):
    project_id: str
    account_id: Optional[str] = None

class RuleUpdateRequest(BaseModel):
    type: str
    name: str
    description: str
    priority: int = 1
    config: Dict[str, Any] = {}

@router.get("/")
async def cleaner_root():
    """数据清洗API根路径"""
    return {
        "message": "数据清洗API",
        "version": "1.0.0",
        "endpoints": {
            "rules": "/rules",
            "preview": "/preview",
            "apply": "/apply"
        }
    }

@router.get("/rules")
async def get_cleaner_rules(
    current_user: str = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """获取所有清洗规则"""
    try:
        logger.info(f"用户 '{current_user}' 获取清洗规则列表")
        
        # 创建数据清洗器实例
        cleaner = DataCleaner()
        
        # 获取所有规则
        rules = []
        for rule in cleaner.rules:
            rule_dict = rule.to_dict()
            rules.append(rule_dict)
        
        logger.info(f"返回 {len(rules)} 个清洗规则")
        return rules
        
    except Exception as e:
        logger.error(f"获取清洗规则失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取清洗规则失败: {str(e)}"
        )

@router.post("/rules/update")
async def update_cleaner_rule(
    request: RuleUpdateRequest,
    current_user: str = Depends(get_current_user)
) -> Dict[str, str]:
    """更新清洗规则"""
    try:
        logger.info(f"用户 '{current_user}' 更新清洗规则: {request.name}")
        
        # 这里可以实现规则更新逻辑
        # 目前返回成功消息
        
        return {"message": "规则更新成功"}
        
    except Exception as e:
        logger.error(f"更新清洗规则失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新清洗规则失败: {str(e)}"
        )

@router.post("/preview")
async def preview_cleaning(
    request: PreviewRequest,
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> Dict[str, Any]:
    """预览清洗结果"""
    try:
        logger.info(f"用户 '{current_user}' 预览清洗结果，交易ID: {request.raw_transaction_id}")
        
        # 查询原始交易记录
        raw_transaction = db.query(RawTransaction).filter(
            RawTransaction.raw_transaction_id == request.raw_transaction_id
        ).first()
        
        if not raw_transaction:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"原始交易记录 {request.raw_transaction_id} 不存在"
            )
        
        # 创建数据清洗器
        cleaner = DataCleaner()
        
        # 清洗数据
        cleaned_data = cleaner.clean_transaction(raw_transaction)
        
        # 返回预览结果
        return {
            "raw_transaction_id": request.raw_transaction_id,
            "original_data": {
                "transaction_date": raw_transaction.raw_transaction_date,
                "transaction_time": raw_transaction.raw_transaction_time,
                "amount": raw_transaction.raw_amount_single or raw_transaction.raw_amount_debit or raw_transaction.raw_amount_credit,
                "counterparty_name": raw_transaction.raw_counterparty_name,
                "description": raw_transaction.raw_description
            },
            "cleaned_data": cleaned_data,
            "preview_time": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"预览清洗结果失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"预览清洗结果失败: {str(e)}"
        )

@router.post("/apply")
async def apply_cleaning(
    request: ApplyRequest,
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> Dict[str, Any]:
    """应用清洗规则到项目或账户"""
    try:
        logger.info(f"用户 '{current_user}' 应用清洗规则，项目ID: {request.project_id}, 账户ID: {request.account_id}")
        
        # 验证项目存在
        project = db.query(Project).filter(Project.project_id == request.project_id).first()
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"项目 {request.project_id} 不存在"
            )
        
        # 构建查询条件
        query = db.query(RawTransaction).filter(RawTransaction.project_id == request.project_id)
        
        if request.account_id:
            # 验证账户存在
            account = db.query(Account).filter(Account.account_id == request.account_id).first()
            if not account:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"账户 {request.account_id} 不存在"
                )
            query = query.filter(RawTransaction.account_id == request.account_id)
        
        # 获取需要清洗的原始交易记录
        raw_transactions = query.all()
        
        if not raw_transactions:
            return {
                "message": "没有找到需要清洗的交易记录",
                "cleaned_count": 0,
                "project_id": request.project_id,
                "account_id": request.account_id
            }
        
        # 创建数据清洗器
        cleaner = DataCleaner()
        
        # 获取原始交易ID列表
        raw_transaction_ids = [tx.raw_transaction_id for tx in raw_transactions]
        
        # 批量处理并保存
        cleaned_count = cleaner.process_and_save(db, raw_transaction_ids)
        
        logger.info(f"成功清洗 {cleaned_count} 条交易记录")
        
        return {
            "message": "数据清洗完成",
            "cleaned_count": cleaned_count,
            "total_raw_count": len(raw_transactions),
            "project_id": request.project_id,
            "account_id": request.account_id,
            "apply_time": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"应用清洗规则失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"应用清洗规则失败: {str(e)}"
        )
