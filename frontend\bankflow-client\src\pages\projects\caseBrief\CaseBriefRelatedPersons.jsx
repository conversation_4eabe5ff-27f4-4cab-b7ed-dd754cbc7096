import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Card, Table, Button, Space, Modal, Form, Input, Select, message, Upload, Tag, Descriptions } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, UploadOutlined } from '@ant-design/icons';
import moment from 'moment';
import { relatedPersonAPI } from '../../../services/api';

const { TextArea } = Input;
const { Option } = Select;

/**
 * 相关人员资料管理组件
 * 支持涉案人员信息的录入、编辑、删除等功能
 */
const CaseBriefRelatedPersons = () => {
  const { projectId } = useParams();
  const [persons, setPersons] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editingId, setEditingId] = useState(null);

  // 组件挂载时获取数据
  useEffect(() => {
    if (projectId) {
      fetchRelatedPersons();
    }
  }, [projectId]);

  // 获取相关人员列表
  const fetchRelatedPersons = async () => {
    try {
      setLoading(true);
      const response = await relatedPersonAPI.getRelatedPersons(projectId);
      
      const formattedPersons = response.data.map(person => ({
        id: person.person_id,
        name: person.name,
        idNumber: person.id_number,
        role: person.role,
        gender: person.gender,
        birthday: person.birthday,
        phone: person.phone,
        address: person.address,
        notes: person.notes,
        createTime: new Date(person.created_at).toLocaleString('zh-CN'),
        updateTime: person.updated_at ? new Date(person.updated_at).toLocaleString('zh-CN') : null
      }));
      setPersons(formattedPersons);
      message.success('获取相关人员列表成功');
    } catch (error) {
      console.error('获取相关人员列表出错:', error);
      message.error('获取相关人员列表失败，请检查网络连接');
      setPersons([]);
    } finally {
      setLoading(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
    },
    {
      title: '身份证号',
      dataIndex: 'idNumber',
      key: 'idNumber',
      width: 180,
      render: (text) => text || '未填写'
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 120,
      render: (text) => {
        const colorMap = {
          '嫌疑人': 'red',
          '证人': 'blue',
          '被害人': 'orange',
          '其他相关方': 'green'
        };
        return <span style={{ color: colorMap[text] || 'black' }}>{text}</span>;
      }
    },
    {
      title: '性别',
      dataIndex: 'gender',
      key: 'gender',
      width: 80,
      render: (text) => text || '未知'
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      key: 'phone',
      width: 130,
      render: (text) => text || '未填写'
    },
    {
      title: '住址',
      dataIndex: 'address',
      key: 'address',
      ellipsis: true,
      render: (text) => text || '未填写'
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="text" 
            size="small"
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record)}
          />
          <Button 
            type="text" 
            size="small"
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDelete(record.id)}
          />
        </Space>
      ),
    },
  ];

  // 处理添加或编辑
  const handleAddOrEdit = () => {
    setIsModalVisible(true);
    if (editingId === null) {
      form.resetFields();
    }
  };

  // 处理编辑
  const handleEdit = (record) => {
    setEditingId(record.id);
    form.setFieldsValue({
      name: record.name,
      idNumber: record.idNumber,
      role: record.role,
      gender: record.gender,
      birthday: record.birthday ? moment(record.birthday) : null,
      phone: record.phone,
      address: record.address,
      notes: record.notes,
    });
    setIsModalVisible(true);
  };

  // 处理删除
  const handleDelete = async (id) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条人员信息吗？删除后无法恢复。',
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await relatedPersonAPI.deleteRelatedPerson(id);
          message.success('人员信息已删除');
          // 重新获取数据
          await fetchRelatedPersons();
        } catch (error) {
          console.error('删除人员信息失败:', error);
          message.error('删除失败，请重试');
        }
      },
    });
  };

  // 处理模态框确认
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      
      const personData = {
        project_id: projectId,
        name: values.name,
        id_number: values.idNumber || null,
        role: values.role,
        gender: values.gender || null,
        birthday: values.birthday ? values.birthday.format('YYYY-MM-DD') : null,
        phone: values.phone || null,
        address: values.address || null,
        notes: values.notes || null
      };

      if (editingId === null) {
        // 新增人员
        await relatedPersonAPI.createRelatedPerson(personData);
        message.success('人员信息已添加');
      } else {
        // 更新人员
        const updateData = { ...personData };
        delete updateData.project_id; // 更新时不需要project_id
        
        await relatedPersonAPI.updateRelatedPerson(editingId, updateData);
        message.success('人员信息已更新');
      }

      setIsModalVisible(false);
      setEditingId(null);
      form.resetFields();
      // 重新获取数据
      await fetchRelatedPersons();
    } catch (error) {
      console.error('表单验证或提交失败:', error);
      message.error('操作失败，请重试');
    }
  };

  // 处理模态框取消
  const handleCancel = () => {
    setIsModalVisible(false);
    setEditingId(null);
    form.resetFields();
  };

  return (
    <div>
      <Card 
        title="涉案人员管理" 
        extra={
          <Space>
            <Button 
              loading={loading}
              onClick={fetchRelatedPersons}
            >
              刷新
            </Button>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={handleAddOrEdit}
            >
              添加人员
            </Button>
          </Space>
        }
      >
        <Table 
          columns={columns} 
          dataSource={persons} 
          rowKey="id" 
          loading={loading}
          pagination={{ 
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }}
          scroll={{ x: 1000 }}
        />
      </Card>

      <Modal
        title={editingId === null ? "添加人员" : "编辑人员"}
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={700}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          requiredMark={false}
        >
          <Form.Item
            name="name"
            label="姓名"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input placeholder="请输入姓名" />
          </Form.Item>
          
          <Form.Item
            name="idNumber"
            label="身份证号"
            rules={[
              { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号' }
            ]}
          >
            <Input placeholder="请输入18位身份证号" />
          </Form.Item>
          
          <Form.Item
            name="role"
            label="角色"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select placeholder="请选择角色">
              <Option value="嫌疑人">嫌疑人</Option>
              <Option value="证人">证人</Option>
              <Option value="被害人">被害人</Option>
              <Option value="其他相关方">其他相关方</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="gender"
            label="性别"
          >
            <Select placeholder="请选择性别">
              <Option value="男">男</Option>
              <Option value="女">女</Option>
              <Option value="未知">未知</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="birthday"
            label="出生日期"
          >
            <Space>
              <Form.Item name="birth_year" style={{ marginBottom: 0 }}>
                <Select 
                  placeholder="选择年份" 
                  style={{ width: 100 }}
                >
                  {Array.from({ length: 80 }, (_, i) => {
                    const year = new Date().getFullYear() - i;
                    return (
                      <Option key={year} value={year}>
                        {year}年
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
              <Form.Item name="birth_month" style={{ marginBottom: 0 }}>
                <Select 
                  placeholder="选择月份" 
                  style={{ width: 80 }}
                >
                  {Array.from({ length: 12 }, (_, i) => {
                    const month = i + 1;
                    return (
                      <Option key={month} value={month}>
                        {month.toString().padStart(2, '0')}月
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Space>
          </Form.Item>
          
          <Form.Item
            name="phone"
            label="联系电话"
            rules={[
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
            ]}
          >
            <Input placeholder="请输入联系电话" />
          </Form.Item>
          
          <Form.Item
            name="address"
            label="住址"
          >
            <Input placeholder="请输入住址" />
          </Form.Item>
          
          <Form.Item
            name="notes"
            label="备注"
          >
            <Input.TextArea 
              rows={3} 
              placeholder="其他需要说明的信息..."
              showCount
              maxLength={500}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CaseBriefRelatedPersons; 