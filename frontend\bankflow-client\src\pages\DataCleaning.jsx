import React, { useState, useEffect } from 'react';
import {
  Card, Table, Button, Form, Select, Input, InputNumber,
  Tabs, Collapse, Typography, Space, Switch, Tag, Tooltip,
  Alert, Modal, Divider, Radio, Spin, message, Empty
} from 'antd';
import {
  CleanOutlined, SettingOutlined, SaveOutlined,
  SyncOutlined, CheckCircleOutlined, ExclamationCircleOutlined
} from '@ant-design/icons';
import { buildApiUrl } from '../config/api';
import { getCurrentUser } from '../services/api';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Option } = Select;

/**
 * 数据清洗规则管理和应用界面
 * 
 * @returns {JSX.Element} 数据清洗页面
 */
const DataCleaning = () => {
  // 状态管理
  const [rules, setRules] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedRule, setSelectedRule] = useState(null);
  const [editForm] = Form.useForm();
  const [previewData, setPreviewData] = useState(null);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [projects, setProjects] = useState([]);
  const [accounts, setAccounts] = useState([]);
  const [selectedProject, setSelectedProject] = useState(null);
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [applyLoading, setApplyLoading] = useState(false);
  const [applyResult, setApplyResult] = useState(null);
  const [error, setError] = useState(null);

  // 加载清洗规则
  useEffect(() => {
    fetchRules();
    fetchProjects();
  }, []);

  // 当选择项目变化时，加载相关账户
  useEffect(() => {
    if (selectedProject) {
      fetchAccounts(selectedProject);
    } else {
      setAccounts([]);
    }
  }, [selectedProject]);

  // 获取清洗规则
  const fetchRules = async () => {
    setLoading(true);
    setError(null);
    try {
      const currentUser = getCurrentUser();
      const response = await fetch('http://localhost:8000/api/cleaner/rules', {
        headers: {
          'Content-Type': 'application/json',
          'X-Current-User': currentUser
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setRules(data);
      // 默认选择第一个规则
      if (data.length > 0) {
        setSelectedRule(data[0]);
        editForm.setFieldsValue(data[0]);
      }
    } catch (err) {
      console.error('获取清洗规则失败:', err);
      setError('获取清洗规则失败，请刷新页面重试');
    } finally {
      setLoading(false);
    }
  };

  // 获取项目列表
  const fetchProjects = async () => {
    try {
      const currentUser = getCurrentUser();
      const response = await fetch('http://localhost:8000/api/projects', {
        headers: {
          'Content-Type': 'application/json',
          'X-Current-User': currentUser
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setProjects(data);
    } catch (err) {
      console.error('获取项目列表失败:', err);
    }
  };

  // 获取账户列表
  const fetchAccounts = async (projectId) => {
    try {
      const currentUser = getCurrentUser();
      const response = await fetch(`http://localhost:8000/api/projects/${projectId}/accounts`, {
        headers: {
          'Content-Type': 'application/json',
          'X-Current-User': currentUser
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setAccounts(data);
    } catch (err) {
      console.error('获取账户列表失败:', err);
    }
  };

  // 选择规则
  const handleRuleSelect = (rule) => {
    setSelectedRule(rule);
    editForm.setFieldsValue(rule);
  };

  // 更新规则
  const handleUpdateRule = async (values) => {
    if (!selectedRule) return;

    setLoading(true);
    try {
      const currentUser = getCurrentUser();
      const response = await fetch('http://localhost:8000/api/cleaner/rules/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Current-User': currentUser
        },
        body: JSON.stringify({
          ...values,
          type: selectedRule.type
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      message.success('规则更新成功');
      // 重新获取规则列表
      fetchRules();
    } catch (err) {
      console.error('更新规则失败:', err);
      message.error(`更新规则失败: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 预览清洗结果
  const handlePreview = async (transactionId) => {
    if (!transactionId) {
      message.warning('请输入交易ID进行预览');
      return;
    }

    setPreviewLoading(true);
    setPreviewData(null);
    try {
      const currentUser = getCurrentUser();
      const response = await fetch('http://localhost:8000/api/cleaner/preview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Current-User': currentUser
        },
        body: JSON.stringify({
          raw_transaction_id: transactionId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setPreviewData(data);
    } catch (err) {
      console.error('预览清洗结果失败:', err);
      message.error(`预览失败: ${err.message}`);
    } finally {
      setPreviewLoading(false);
    }
  };

  // 应用清洗规则
  const handleApplyCleaning = async () => {
    if (!selectedProject) {
      message.warning('请选择要清洗的项目');
      return;
    }

    setApplyLoading(true);
    setApplyResult(null);
    try {
      const currentUser = getCurrentUser();
      const response = await fetch('http://localhost:8000/api/cleaner/apply', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Current-User': currentUser
        },
        body: JSON.stringify({
          project_id: selectedProject,
          account_id: selectedAccount
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setApplyResult(data);
      message.success(`成功清洗 ${data.cleaned_count} 条交易记录`);
    } catch (err) {
      console.error('应用清洗规则失败:', err);
      message.error(`应用清洗规则失败: ${err.message}`);
    } finally {
      setApplyLoading(false);
    }
  };

  // 根据规则类型渲染不同的表单
  const renderRuleForm = () => {
    if (!selectedRule) return null;

    switch (selectedRule.type) {
      case 'DateTimeRule':
        return (
          <>
            <Form.Item
              name="date_field"
              label="日期字段"
              rules={[{ required: true, message: '请输入日期字段名' }]}
            >
              <Input placeholder="原始日期字段名，如raw_transaction_date" />
            </Form.Item>
            <Form.Item
              name="time_field"
              label="时间字段"
            >
              <Input placeholder="原始时间字段名，如raw_transaction_time" />
            </Form.Item>
            <Form.Item
              name="target_field"
              label="目标字段"
              rules={[{ required: true, message: '请输入目标字段名' }]}
            >
              <Input placeholder="清洗后的字段名，如transaction_datetime" />
            </Form.Item>
            <Form.Item
              name="default_time"
              label="默认时间"
            >
              <Input placeholder="当时间为空时使用的默认时间，如00:00:00" />
            </Form.Item>
          </>
        );
      case 'AmountRule':
        return (
          <>
            <Form.Item
              name="debit_field"
              label="借方字段"
            >
              <Input placeholder="借方金额字段名，如raw_amount_debit" />
            </Form.Item>
            <Form.Item
              name="credit_field"
              label="贷方字段"
            >
              <Input placeholder="贷方金额字段名，如raw_amount_credit" />
            </Form.Item>
            <Form.Item
              name="single_field"
              label="单一金额字段"
            >
              <Input placeholder="单一金额字段名，如raw_amount_single" />
            </Form.Item>
            <Form.Item
              name="sign_field"
              label="借贷标志字段"
            >
              <Input placeholder="借贷标志字段名，如raw_sign_keyword" />
            </Form.Item>
            <Form.Item
              name="target_field"
              label="目标金额字段"
              rules={[{ required: true, message: '请输入目标金额字段名' }]}
            >
              <Input placeholder="清洗后的金额字段名，如transaction_amount" />
            </Form.Item>
            <Form.Item
              name="dr_cr_field"
              label="目标借贷标志字段"
              rules={[{ required: true, message: '请输入目标借贷标志字段名' }]}
            >
              <Input placeholder="清洗后的借贷标志字段名，如dr_cr_flag" />
            </Form.Item>
            <Form.Item
              name="debit_flag"
              label="借方标志"
            >
              <Input placeholder="借方标志，如支" />
            </Form.Item>
            <Form.Item
              name="credit_flag"
              label="贷方标志"
            >
              <Input placeholder="贷方标志，如收" />
            </Form.Item>
          </>
        );
      case 'TextRule':
        return (
          <>
            <Form.Item
              name="fields_mapping"
              label="字段映射"
              rules={[{ required: true, message: '请输入字段映射' }]}
            >
              <Input.TextArea 
                placeholder="字段映射(JSON格式)，如：{&quot;raw_counterparty_name&quot;: &quot;counterparty_account_name&quot;}" 
                rows={6}
              />
            </Form.Item>
          </>
        );
      default:
        return (
          <Alert
            message="未知规则类型"
            description={`规则类型 ${selectedRule.type} 不支持编辑`}
            type="warning"
            showIcon
          />
        );
    }
  };

  // 渲染预览数据对比
  const renderPreviewComparison = () => {
    if (!previewData) return null;

    const { raw_data, cleaned_data, applied_rules } = previewData;

    return (
      <Card title="清洗预览结果" style={{ marginTop: 16 }}>
        <Tabs defaultActiveKey="1">
          <TabPane tab="数据对比" key="1">
            <div style={{ display: 'flex' }}>
              <div style={{ flex: 1, marginRight: 16 }}>
                <Title level={5}>原始数据</Title>
                <pre style={{ background: '#f5f5f5', padding: 16, borderRadius: 4 }}>
                  {JSON.stringify(raw_data, null, 2)}
                </pre>
              </div>
              <div style={{ flex: 1 }}>
                <Title level={5}>清洗后数据</Title>
                <pre style={{ background: '#f5f5f5', padding: 16, borderRadius: 4 }}>
                  {JSON.stringify(cleaned_data, null, 2)}
                </pre>
              </div>
            </div>
          </TabPane>
          <TabPane tab="应用的规则" key="2">
            <Table
              dataSource={applied_rules}
              rowKey="name"
              columns={[
                { title: '规则名称', dataIndex: 'name' },
                { title: '类型', dataIndex: 'type' },
                { title: '优先级', dataIndex: 'priority' },
                { title: '描述', dataIndex: 'description' }
              ]}
              pagination={false}
            />
          </TabPane>
        </Tabs>
      </Card>
    );
  };

  return (
    <div>
      <Title level={2}>数据清洗中心</Title>
      
      {error && (
        <Alert
          message="错误"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}
      
      <Tabs defaultActiveKey="1">
        <TabPane tab="清洗规则管理" key="1">
          <div style={{ display: 'flex' }}>
            <div style={{ width: 300, marginRight: 16 }}>
              <Card title="规则列表" loading={loading}>
                {rules.map((rule) => (
                  <div
                    key={rule.name}
                    style={{
                      padding: '8px 16px',
                      borderRadius: 4,
                      marginBottom: 8,
                      cursor: 'pointer',
                      background: selectedRule?.name === rule.name ? '#e6f7ff' : '#fff',
                      border: selectedRule?.name === rule.name ? '1px solid #1890ff' : '1px solid #d9d9d9'
                    }}
                    onClick={() => handleRuleSelect(rule)}
                  >
                    <div>{rule.name}</div>
                    <div>
                      <Tag color="blue">{rule.type}</Tag>
                      <Tag color="green">优先级: {rule.priority}</Tag>
                    </div>
                  </div>
                ))}
              </Card>
              
              <Card title="规则预览测试" style={{ marginTop: 16 }}>
                <Input.Search
                  placeholder="输入原始交易ID进行预览"
                  enterButton="预览"
                  loading={previewLoading}
                  onSearch={handlePreview}
                />
                <Paragraph style={{ marginTop: 8 }}>
                  输入任意交易ID，查看清洗前后对比
                </Paragraph>
              </Card>
            </div>
            
            <div style={{ flex: 1 }}>
              <Card 
                title={selectedRule ? `编辑规则: ${selectedRule.name}` : "选择一个规则来编辑"}
                loading={loading}
              >
                {selectedRule ? (
                  <Form
                    form={editForm}
                    layout="vertical"
                    onFinish={handleUpdateRule}
                  >
                    <Form.Item
                      name="name"
                      label="规则名称"
                      rules={[{ required: true, message: '请输入规则名称' }]}
                    >
                      <Input placeholder="规则名称" />
                    </Form.Item>
                    
                    <Form.Item
                      name="description"
                      label="规则描述"
                    >
                      <Input.TextArea placeholder="规则描述" rows={2} />
                    </Form.Item>
                    
                    <Form.Item
                      name="priority"
                      label="规则优先级"
                      rules={[{ required: true, message: '请输入优先级' }]}
                      help="数字越大优先级越高"
                    >
                      <InputNumber placeholder="优先级" style={{ width: '100%' }} />
                    </Form.Item>
                    
                    <Divider>规则特定配置</Divider>
                    
                    {renderRuleForm()}
                    
                    <Form.Item>
                      <Button type="primary" htmlType="submit" loading={loading}>
                        <SaveOutlined /> 保存规则
                      </Button>
                    </Form.Item>
                  </Form>
                ) : (
                  <Empty description="请选择要编辑的规则" />
                )}
              </Card>
              
              {renderPreviewComparison()}
            </div>
          </div>
        </TabPane>
        
        <TabPane tab="应用清洗规则" key="2">
          <Card title="批量应用清洗规则">
            <Form layout="vertical">
              <Form.Item
                label="选择项目"
                required
                tooltip="选择要应用清洗规则的项目"
              >
                <Select
                  placeholder="请选择项目"
                  style={{ width: '100%' }}
                  onChange={setSelectedProject}
                  value={selectedProject}
                >
                  {projects.map(project => (
                    <Option key={project.project_id} value={project.project_id}>
                      {project.project_name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              
              <Form.Item
                label="选择账户（可选）"
                tooltip="可以选择特定账户进行清洗，不选择则清洗项目下所有账户"
              >
                <Select
                  placeholder="请选择账户（可选）"
                  style={{ width: '100%' }}
                  onChange={setSelectedAccount}
                  value={selectedAccount}
                  allowClear
                  disabled={!selectedProject}
                >
                  {accounts.map(account => (
                    <Option key={account.account_id} value={account.account_id}>
                      {account.account_name || account.card_number} ({account.person_name})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              
              <Alert
                message="清洗说明"
                description={
                  <div>
                    <p>点击【开始清洗】按钮后，系统将对选定的项目（和可选的账户）应用当前的清洗规则。</p>
                    <p>清洗过程会对原始交易数据进行处理，生成标准化的交易记录。</p>
                    <p>清洗不会修改原始数据，可以随时重新清洗。</p>
                  </div>
                }
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
              
              <Button
                type="primary"
                icon={<CleanOutlined />}
                size="large"
                onClick={handleApplyCleaning}
                loading={applyLoading}
                disabled={!selectedProject}
              >
                开始清洗
              </Button>
            </Form>
            
            {applyResult && (
              <Alert
                message="清洗完成"
                description={`成功清洗 ${applyResult.cleaned_count} 条交易记录`}
                type="success"
                showIcon
                style={{ marginTop: 16 }}
              />
            )}
          </Card>
          
          <Card title="清洗规则说明" style={{ marginTop: 16 }}>
            <Collapse>
              <Panel header="日期时间清洗规则" key="1">
                <p>将原始日期和时间字段标准化为YYYY-MM-DD HH:MM:SS格式。</p>
                <p>处理多种日期格式，如中文日期、特殊分隔符等。</p>
                <p>可以合并单独的日期和时间字段。</p>
              </Panel>
              <Panel header="金额清洗规则" key="2">
                <p>统一金额格式并确保正负号一致性。</p>
                <p>支持多种金额表示方式：</p>
                <ul>
                  <li>单列金额+借贷标志</li>
                  <li>借方/贷方分列</li>
                  <li>带有正负号的单列金额</li>
                </ul>
                <p>标准化为"负数表示支出，正数表示收入"的一致格式。</p>
              </Panel>
              <Panel header="文本清洗规则" key="3">
                <p>处理文本字段，去除特殊字符，统一格式。</p>
                <p>去除多余空格、标点符号。</p>
                <p>统一中英文符号。</p>
              </Panel>
            </Collapse>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default DataCleaning; 