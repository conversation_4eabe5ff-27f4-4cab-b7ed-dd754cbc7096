const { contextBridge, ipc<PERSON><PERSON>er } = require('electron');

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 应用信息
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  getAppPath: () => ipcRenderer.invoke('get-app-path'),

  // 文件操作
  showSaveDialog: () => ipcRenderer.invoke('show-save-dialog'),

  // 监听文件选择事件
  onFilesSelected: (callback) => {
    ipcRenderer.on('files-selected', (event, filePaths) => {
      callback(filePaths);
    });
  },

  // 移除监听器
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  },

  // 发送消息到主进程
  sendMessage: (channel, data) => {
    // 只允许特定的频道
    const validChannels = [
      'app-ready',
      'import-files',
      'export-data',
      'open-settings'
    ];
    
    if (validChannels.includes(channel)) {
      ipcRenderer.send(channel, data);
    }
  },

  // 监听来自主进程的消息
  onMessage: (channel, callback) => {
    const validChannels = [
      'files-selected',
      'export-complete',
      'settings-updated'
    ];
    
    if (validChannels.includes(channel)) {
      ipcRenderer.on(channel, (event, ...args) => callback(...args));
    }
  }
});

// 暴露平台信息
contextBridge.exposeInMainWorld('platform', {
  isWindows: process.platform === 'win32',
  isMacOS: process.platform === 'darwin',
  isLinux: process.platform === 'linux',
  platform: process.platform
});

// 暴露节点版本信息
contextBridge.exposeInMainWorld('versions', {
  node: process.versions.node,
  chrome: process.versions.chrome,
  electron: process.versions.electron
});

console.log('Preload script loaded successfully');

// 检测到日期选择器单独设置字号  
const datePickerStyle = {
  fontSize: '12px', /* 应统一为14px */
  ...antdConfig
}