"""
数据存储服务
负责将解析结果保存到DuckDB数据库
"""
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
import json

from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import func

from ..models.duckdb_models import Project, Account, Transaction, RawTransaction

logger = logging.getLogger(__name__)

class DataStorageService:
    """数据存储服务，负责将解析结果保存到数据库"""
    
    def __init__(self):
        """初始化数据存储服务"""
        pass
    
    def save_to_duckdb(
        self, 
        project_id: str, 
        parse_results: Dict[str, Any], 
        bank_name: str, 
        file_source: str, 
        db: Session
    ) -> Dict[str, Any]:
        """
        将解析结果保存到DuckDB数据库
        
        Args:
            project_id: 项目ID
            parse_results: 解析结果
            bank_name: 银行名称
            file_source: 源文件名/路径
            db: 数据库会话
            
        Returns:
            Dict[str, Any]: 保存结果统计
        """
        # 检查项目是否存在
        project = db.query(Project).filter(Project.project_id == project_id).first()
        if not project:
            raise ValueError(f"项目ID {project_id} 不存在")
        
        # 获取解析结果中的账户和交易信息
        accounts = parse_results.get("accounts", [])
        transactions_by_account = parse_results.get("transactions_by_account", {})
        
        # 统计数据
        stats = {
            "total_accounts": len(accounts),
            "total_transactions": sum(len(txs) for txs in transactions_by_account.values()),
            "saved_accounts": 0,
            "saved_transactions": 0,
            "accounts_details": []
        }
        
        # 获取当前时间戳
        timestamp = datetime.utcnow().isoformat()
        
        try:
            # 开始事务处理
            for account in accounts:
                account_number = account.get("account_number", "")
                card_number = account.get("card_number", "")
                
                # 确保至少有账号或卡号
                if not account_number and not card_number:
                    logger.warning(f"跳过没有账号和卡号的账户: {account}")
                    continue
                
                # 使用账号作为主键，如果没有账号则使用卡号
                account_key = account_number or card_number
                
                # 检查交易记录是否存在
                transactions = transactions_by_account.get(account_key, [])
                if not transactions:
                    logger.warning(f"账户 {account_key} 没有交易记录，跳过")
                    continue
                
                # 查找或创建账户
                existing_account = db.query(Account).filter(
                    Account.project_id == project_id,
                    Account.card_number == card_number if card_number else Account.account_number == account_number
                ).first()
                
                if not existing_account:
                    # 创建新账户
                    account_obj = Account(
                        account_id=str(uuid.uuid4()),
                        project_id=project_id,
                        person_name=account.get("person_name", "") or account.get("cardholder_name", "") or account.get("account_name", "") or "未知",
                        bank_name=bank_name,
                        account_name=account.get("account_name", ""),
                        account_number=account_number,
                        card_number=card_number,
                        import_file_source=file_source,
                        creation_timestamp=timestamp
                    )
                    db.add(account_obj)
                    db.flush()  # 获取生成的ID
                    
                    account_id = account_obj.account_id
                    stats["saved_accounts"] += 1
                else:
                    # 使用已存在的账户
                    account_id = existing_account.account_id
                    
                    # 如果提供了更新的信息，则更新账户信息
                    person_name = account.get("person_name", "") or account.get("cardholder_name", "")
                    account_name = account.get("account_name", "")
                    
                    if person_name and person_name != existing_account.person_name:
                        existing_account.person_name = person_name
                    
                    if account_name and account_name != existing_account.account_name:
                        existing_account.account_name = account_name
                    
                    existing_account.import_file_source = file_source
                    existing_account.updated_at = datetime.utcnow()
                    db.flush()
                
                # 记录账户详情
                account_stats = {
                    "account_id": account_id,
                    "account_number": account_number,
                    "card_number": card_number,
                    "account_name": account.get("account_name", ""),
                    "person_name": account.get("person_name", "") or account.get("cardholder_name", "") or existing_account.person_name if existing_account else "未知",
                    "transaction_count": len(transactions),
                    "saved_transactions": 0
                }
                
                # 保存交易记录
                for transaction in transactions:
                    # 创建唯一交易标识
                    tx_date = transaction.get("transaction_datetime", "")
                    tx_amount = transaction.get("transaction_amount", 0)
                    tx_balance = transaction.get("balance", 0)
                    tx_remarks = transaction.get("remarks", "")
                    
                    # 检查是否已存在相同交易
                    existing_tx = db.query(Transaction).filter(
                        Transaction.account_id == account_id,
                        Transaction.transaction_datetime == tx_date,
                        Transaction.transaction_amount == tx_amount,
                        Transaction.balance == tx_balance,
                        Transaction.remarks == tx_remarks
                    ).first()
                    
                    if existing_tx:
                        logger.info(f"跳过已存在的交易记录: {tx_date}, {tx_amount}")
                        continue
                    
                    # 创建原始交易记录
                    # 🔧 修复：从交易记录本身获取持卡人姓名，而不是从账户信息
                    cardholder_name = (transaction.get("cardholder_name", "") or 
                                     transaction.get("person_name", "") or
                                     account.get("cardholder_name", "") or 
                                     account.get("person_name", "") or 
                                     existing_account.person_name if existing_account else "未知")
                    raw_tx = RawTransaction(
                        raw_transaction_id=str(uuid.uuid4()),
                        account_id=account_id,
                        project_id=project_id,
                        person_name=cardholder_name,
                        bank_name=bank_name,
                        account_name=account.get("account_name", ""),
                        account_number=account_number,
                        card_number=card_number,
                        raw_transaction_date=tx_date.split(" ")[0] if " " in tx_date else tx_date,
                        raw_transaction_time=tx_date.split(" ")[1] if " " in tx_date else "",
                        raw_transaction_method=transaction.get("transaction_method", ""),
                        raw_amount_debit=str(abs(tx_amount)) if tx_amount and float(tx_amount) < 0 else None,
                        raw_amount_credit=str(tx_amount) if tx_amount and float(tx_amount) >= 0 else None,
                        raw_amount_single=str(tx_amount) if tx_amount is not None else None,
                        raw_sign_keyword=transaction.get("dr_cr_flag", ""),
                        raw_balance=str(tx_balance) if tx_balance is not None else None,
                        raw_counterparty_account=transaction.get("counterparty_account_number", ""),
                        raw_counterparty_name=transaction.get("counterparty_account_name", ""),
                        raw_counterparty_bank=transaction.get("counterparty_bank_name", ""),
                        raw_remarks=tx_remarks,
                        raw_other_fields=transaction.get("raw_data", {}),
                        import_file_source=file_source,
                        import_timestamp=timestamp
                    )
                    db.add(raw_tx)
                    db.flush()  # 获取生成的ID
                    
                    # 创建清洗后的交易记录
                    tx_obj = Transaction(
                        transaction_id=str(uuid.uuid4()),
                        raw_transaction_id=raw_tx.raw_transaction_id,
                        account_id=account_id,
                        project_id=project_id,
                        person_name=cardholder_name,  # 🔧 修复：使用与原始交易记录相同的持卡人姓名
                        bank_name=bank_name,
                        account_name=account.get("account_name", ""),
                        account_number=account_number,
                        card_number=card_number,
                        transaction_datetime=tx_date,
                        transaction_method=transaction.get("transaction_method", ""),
                        transaction_amount=float(tx_amount) if tx_amount is not None else 0.0,
                        balance=float(tx_balance) if tx_balance is not None else None,
                        dr_cr_flag=transaction.get("dr_cr_flag", ""),
                        counterparty_account_number=transaction.get("counterparty_account_number", ""),
                        counterparty_account_name=transaction.get("counterparty_account_name", ""),
                        counterparty_bank_name=transaction.get("counterparty_bank_name", ""),
                        remarks=tx_remarks,
                        original_currency=transaction.get("original_currency", "CNY"),
                        raw_data_snapshot=transaction.get("raw_data_snapshot", {}),
                        cleaning_rules_applied=None,  # 暂无清洗规则
                        cleaning_timestamp=timestamp
                    )
                    db.add(tx_obj)
                    
                    stats["saved_transactions"] += 1
                    account_stats["saved_transactions"] += 1
                
                stats["accounts_details"].append(account_stats)
            
            # 提交事务
            db.commit()
            logger.info(f"成功保存 {stats['saved_accounts']} 个账户和 {stats['saved_transactions']} 条交易记录")
            
            return stats
        
        except SQLAlchemyError as e:
            # 回滚事务
            db.rollback()
            logger.error(f"保存到DuckDB数据库失败: {str(e)}")
            raise
    
    def get_account_transaction_summary(self, account_id: str, db: Session) -> Dict[str, Any]:
        """
        获取账户交易摘要信息
        
        Args:
            account_id: 账户ID
            db: 数据库会话
            
        Returns:
            Dict[str, Any]: 账户交易摘要
        """
        account = db.query(Account).filter(Account.account_id == account_id).first()
        if not account:
            raise ValueError(f"账户ID {account_id} 不存在")
        
        # 获取交易记录总数
        tx_count = db.query(func.count(Transaction.transaction_id)).filter(
            Transaction.account_id == account_id
        ).scalar() or 0
        
        # 获取收入总额
        income_sum = db.query(func.sum(Transaction.transaction_amount)).filter(
            Transaction.account_id == account_id,
            Transaction.transaction_amount > 0
        ).scalar() or 0
        
        # 获取支出总额
        expense_sum = db.query(func.sum(Transaction.transaction_amount)).filter(
            Transaction.account_id == account_id,
            Transaction.transaction_amount < 0
        ).scalar() or 0
        
        # 计算净额
        net_amount = income_sum + expense_sum  # expense_sum已经是负数
        
        # 获取最早和最晚交易日期
        earliest_tx = db.query(func.min(Transaction.transaction_datetime)).filter(
            Transaction.account_id == account_id
        ).scalar()
        
        latest_tx = db.query(func.max(Transaction.transaction_datetime)).filter(
            Transaction.account_id == account_id
        ).scalar()
        
        return {
            "account_id": account_id,
            "account_number": account.account_number,
            "card_number": account.card_number,
            "account_name": account.account_name,
            "person_name": account.person_name,
            "bank_name": account.bank_name,
            "transaction_count": tx_count,
            "income_total": float(income_sum),
            "expense_total": float(abs(expense_sum)),
            "net_amount": float(net_amount),
            "earliest_transaction": earliest_tx,
            "latest_transaction": latest_tx
        }
    
    def get_project_summary(self, project_id: str, db: Session) -> Dict[str, Any]:
        """
        获取项目摘要信息
        
        Args:
            project_id: 项目ID
            db: 数据库会话
            
        Returns:
            Dict[str, Any]: 项目摘要
        """
        project = db.query(Project).filter(Project.project_id == project_id).first()
        if not project:
            raise ValueError(f"项目ID {project_id} 不存在")
        
        # 获取账户总数
        account_count = db.query(func.count(Account.account_id)).filter(
            Account.project_id == project_id
        ).scalar() or 0
        
        # 获取交易记录总数
        tx_count = db.query(func.count(Transaction.transaction_id)).filter(
            Transaction.project_id == project_id
        ).scalar() or 0
        
        # 获取收入总额
        income_sum = db.query(func.sum(Transaction.transaction_amount)).filter(
            Transaction.project_id == project_id,
            Transaction.transaction_amount > 0
        ).scalar() or 0
        
        # 获取支出总额
        expense_sum = db.query(func.sum(Transaction.transaction_amount)).filter(
            Transaction.project_id == project_id,
            Transaction.transaction_amount < 0
        ).scalar() or 0
        
        # 计算净额
        net_amount = income_sum + expense_sum  # expense_sum已经是负数
        
        # 获取最早和最晚交易日期
        earliest_tx = db.query(func.min(Transaction.transaction_datetime)).filter(
            Transaction.project_id == project_id
        ).scalar()
        
        latest_tx = db.query(func.max(Transaction.transaction_datetime)).filter(
            Transaction.project_id == project_id
        ).scalar()
        
        return {
            "project_id": project_id,
            "project_name": project.project_name,
            "person_name": project.person_name,
            "account_count": account_count,
            "transaction_count": tx_count,
            "income_total": float(income_sum),
            "expense_total": float(abs(expense_sum)),
            "net_amount": float(net_amount),
            "earliest_transaction": earliest_tx,
            "latest_transaction": latest_tx
        } 