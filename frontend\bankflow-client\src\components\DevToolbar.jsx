import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Button, Space, Tag, Typography } from 'antd';
import { UserOutlined, LogoutOutlined, ReloadOutlined } from '@ant-design/icons';
import { devConfig, isProduction } from '../config/environment';

const { Text } = Typography;

/**
 * 开发环境工具条
 * 显示当前登录状态和快速操作按钮
 */
const DevToolbar = () => {
  const { isAuthenticated, userInfo, logout } = useAuth();

  // 🔒 生产环境强制隐藏
  if (isProduction() || !devConfig.devToolbar.enabled) {
    return null;
  }

  const handleQuickLogin = () => {
    window.location.reload();
  };

  const handleLogout = () => {
    logout();
    window.location.reload();
  };

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        right: 0,
        zIndex: 9999,
        background: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        padding: '8px 12px',
        borderRadius: '0 0 0 8px',
        fontSize: '12px',
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      }}
    >
      <Tag color="blue" icon={<UserOutlined />}>
        开发模式
      </Tag>
      
      {isAuthenticated ? (
        <>
          <Text style={{ color: 'white', fontSize: '12px' }}>
            已登录: {userInfo?.username}
          </Text>
          <Button
            type="text"
            size="small"
            icon={<LogoutOutlined />}
            onClick={handleLogout}
            style={{ color: 'white', fontSize: '12px' }}
          >
            登出
          </Button>
        </>
      ) : (
        <>
          <Text style={{ color: 'yellow', fontSize: '12px' }}>
            未登录
          </Text>
          <Button
            type="text"
            size="small"
            icon={<ReloadOutlined />}
            onClick={handleQuickLogin}
            style={{ color: 'white', fontSize: '12px' }}
          >
            自动登录
          </Button>
        </>
      )}
    </div>
  );
};

export default DevToolbar; 