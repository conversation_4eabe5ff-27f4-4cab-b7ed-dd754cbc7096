{"plugin_id": "cmbc_format1_plugin", "plugin_name": "民生银行Format1解析器插件", "version": "1.0.0", "description": "民生银行Format1解析器插件，支持民生银行多子表结构Excel格式的银行流水解析", "author": "银行流水系统团队", "license": "MIT", "bank_name": "中国民生银行", "bank_code": "CMBC", "format_type": "format1", "supported_file_types": [".xlsx", ".xls"], "confidence_threshold": 0.7, "priority": 10, "enabled": true, "parser_config": {"encoding": "utf-8", "sheet_detection": {"auto_detect": true, "header_keywords": ["日期", "金额", "收入", "支出", "进", "出"], "skip_empty_sheets": true}, "field_mapping": {"transaction_date": ["交易日期", "日期", "记账日期"], "transaction_amount": ["交易金额", "金额", "收入", "支出", "进", "出"], "balance": ["余额", "账户余额", "结余"], "summary": ["摘要", "用途", "备注", "说明", "交易摘要"], "cardholder_name": ["户名", "姓名", "客户", "持卡人"], "account_number": ["账号", "账户", "卡号"]}, "data_validation": {"required_fields": ["transaction_date", "transaction_amount"], "date_formats": ["YYYY-MM-DD", "YYYY/MM/DD", "YYYYMMDD"], "amount_validation": {"min_value": 0.01, "max_value": *********.99}}, "special_features": {"multi_sheet_support": true, "account_separation": true, "header_info_extraction": true, "debit_credit_mapping": {"进": "收入", "出": "支出", "收入": "收入", "支出": "支出"}, "account_number_processing": {"remove_comma": true, "take_before_comma": true}}}, "performance": {"max_file_size_mb": 50, "max_rows_per_sheet": 100000, "timeout_seconds": 300}, "logging": {"level": "INFO", "enable_debug": false}}