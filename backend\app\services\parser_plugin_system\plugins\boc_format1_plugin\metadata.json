{"name": "boc_format1_plugin", "version": "1.0.0", "description": "解析中国银行Format1格式的流水文件，支持多工作表结构，智能识别有效交易数据", "author": "银行流水分析系统开发团队", "supported_formats": ["xlsx", "xls"], "dependencies": ["xlrd==1.2.0", "pandas>=1.3.0", "openpyxl>=3.0.0"], "entry_point": "plugin.Plugin", "bank_name": "中国银行", "confidence": 0.9, "keywords": ["中国银行", "BOC", "申请查询信息", "借贷标识", "交易类型描述", "多工作表"], "file_patterns": ["*中国银行*.xlsx", "*中国银行*.xls", "*BOC*.xlsx", "*BOC*.xls", "*1.xlsx", "*1.xls"], "features": ["多工作表智能识别", "中文姓名自动提取", "借贷标识D/C转换", "交易对象智能映射", "标准字段映射", "数据质量保证"]}