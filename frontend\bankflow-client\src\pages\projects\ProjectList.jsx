import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Table, 
  Card, 
  Button, 
  Space, 
  Modal, 
  Form, 
  Input, 
  message, 
  Tooltip, 
  Row, 
  Col,
  Empty,
  Alert,
  Divider,
  Select,
  ConfigProvider
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  FolderOpenOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { projectAPI } from '../../services/api';
import zhCN from 'antd/locale/zh_CN';

const { Search } = Input;
const { Option } = Select;

/**
 * 项目列表页面组件
 * 
 * @returns {JSX.Element} 项目列表页面
 */
const ProjectList = () => {
  const [loading, setLoading] = useState(true);
  const [projects, setProjects] = useState([]);
  const [filteredProjects, setFilteredProjects] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [currentProject, setCurrentProject] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [dateRange, setDateRange] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    fetchProjects();
  }, []);

  // 应用筛选条件
  useEffect(() => {
    let filtered = [...projects];

    // 搜索筛选
    if (searchText) {
      filtered = filtered.filter(project => 
        project.project_name.toLowerCase().includes(searchText.toLowerCase()) ||
        (project.description && project.description.toLowerCase().includes(searchText.toLowerCase()))
      );
    }

    // 日期范围筛选
    if (dateRange && dateRange.length === 2) {
      filtered = filtered.filter(project => {
        const projectDate = new Date(project.created_at);
        return projectDate >= dateRange[0].toDate() && projectDate <= dateRange[1].toDate();
      });
    }

    setFilteredProjects(filtered);
  }, [projects, searchText, dateRange]);

  const fetchProjects = async () => {
    try {
      setLoading(true);
      const response = await projectAPI.getProjects();
      
      // 新的API函数直接返回JSON数据，不是Axios响应对象
      setProjects(response);
      setFilteredProjects(response);
    } catch (error) {
      console.error('获取项目列表出错:', error);
      message.error('获取项目列表失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  const showModal = (project = null) => {
    setCurrentProject(project);
    setIsModalVisible(true);
    
    if (project) {
      form.setFieldsValue({
        project_name: project.project_name,
        description: project.description
      });
    } else {
      form.resetFields();
    }
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
    setCurrentProject(null);
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (currentProject) {
        // 编辑现有项目
        const response = await projectAPI.updateProject(currentProject.project_id, values);
        
        // 新的API函数直接返回JSON数据
        const updatedProject = response;
        setProjects(projects.map(p => 
          p.project_id === currentProject.project_id ? updatedProject : p
        ));
        message.success('项目更新成功');
      } else {
        // 创建新项目
        const response = await projectAPI.createProject(values);
        
        // 新的API函数直接返回JSON数据
        const newProject = response;
        setProjects([...projects, newProject]);
        message.success('项目创建成功');
      }
      
      setIsModalVisible(false);
      form.resetFields();
      setCurrentProject(null);
    } catch (error) {
      console.error('操作失败:', error);
      message.error('项目操作失败，请重试');
    }
  };

  const handleDelete = (projectId) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除此项目吗？此操作不可恢复，将删除项目下的所有数据。',
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await projectAPI.deleteProject(projectId);
          
          // axios删除成功
          setProjects(projects.filter(p => p.project_id !== projectId));
          message.success('项目已删除');
        } catch (error) {
          console.error('删除项目出错:', error);
          message.error('删除项目失败，请重试');
        }
      }
    });
  };

  const handleSearch = (value) => {
    setSearchText(value);
  };

  const handleDateRangeChange = (dates) => {
    setDateRange(dates);
  };

  const clearFilters = () => {
    setSearchText('');
    setDateRange(null);
  };

  const columns = [
    {
      title: '项目名称',
      dataIndex: 'project_name',
      key: 'project_name',
      width: 220,
      render: (text, record) => (
        <div>
          <div className="highlight-text" style={{ fontSize: '18px', fontWeight: '600' }}>
            {text}
          </div>
          <div style={{ fontSize: '16px', color: '#8c8c8c', marginTop: '4px' }}>
            ID: {record.project_id.slice(0, 8)}...
          </div>
        </div>
      ),
    },
    {
      title: '项目描述',
      dataIndex: 'description',
      key: 'description',
      width: 300,
      render: (text) => (
        <div style={{ fontSize: '17px', color: '#595959', lineHeight: '1.4' }}>
          {text || '暂无描述'}
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text) => (
        <div style={{ fontSize: '17px' }}>
          <div>{new Date(text).toLocaleDateString()}</div>
          <div style={{ color: '#8c8c8c', fontSize: '16px' }}>
            {new Date(text).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </div>
        </div>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 180,
      render: (text) => (
        <div style={{ fontSize: '17px' }}>
          <div>{new Date(text).toLocaleDateString()}</div>
          <div style={{ color: '#8c8c8c', fontSize: '16px' }}>
            {new Date(text).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </div>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 240,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="进入项目工作区">
            <Button
              type="primary"
              size="middle"
              icon={<FolderOpenOutlined />}
              onClick={() => navigate(`/projects/${record.project_id}`)}
              style={{ fontSize: '16px' }}
            >
              进入
            </Button>
          </Tooltip>
          <Tooltip title="编辑项目信息">
            <Button
              size="middle"
              icon={<EditOutlined />}
              onClick={() => showModal(record)}
              style={{ 
                fontSize: '16px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            />
          </Tooltip>
          <Tooltip title="删除项目">
            <Button
              danger
              size="middle"
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record.project_id)}
              style={{ 
                fontSize: '16px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div className="compact-spacing">
      {/* 主要内容卡片 - 紧凑设计 */}
      <Card 
        title={
          <div className="page-title" style={{ fontSize: '28px', margin: 0 }}>
            <FolderOpenOutlined className="highlight-text" style={{ marginRight: '12px' }} />
            项目管理
          </div>
        }
        extra={
          <Space size="middle">
            <Button 
              icon={<ReloadOutlined />} 
              onClick={fetchProjects}
              loading={loading}
              size="middle"
              style={{ fontSize: '16px' }}
            >
              刷新
            </Button>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={() => showModal()}
              size="middle"
              style={{ fontSize: '16px' }}
            >
              新建项目
            </Button>
          </Space>
        }
        className="content-section"
        bodyStyle={{ padding: '20px' }}
      >
        {/* 筛选工具栏 - 紧凑布局 */}
        <div className="toolbar">
          <div className="toolbar-left">
            <Search
              placeholder="搜索项目名称或描述"
              allowClear
              enterButton={<SearchOutlined />}
              size="middle"
              onSearch={handleSearch}
              onChange={(e) => setSearchText(e.target.value)}
              value={searchText}
              style={{ width: '320px', fontSize: '16px' }}
            />
            <Space>
              <Select
                placeholder="开始年份"
                size="middle"
                value={dateRange?.[0]}
                onChange={(value) => handleDateRangeChange([value, dateRange?.[1]])}
                style={{ width: '120px', fontSize: '16px' }}
                allowClear
              >
                {Array.from({ length: 20 }, (_, i) => {
                  const year = new Date().getFullYear() - i;
                  return (
                    <Option key={year} value={year}>
                      {year}年
                    </Option>
                  );
                })}
              </Select>
              <span style={{ fontSize: '16px', color: '#8c8c8c' }}>至</span>
              <Select
                placeholder="结束年份"
                size="middle"
                value={dateRange?.[1]}
                onChange={(value) => handleDateRangeChange([dateRange?.[0], value])}
                style={{ width: '120px', fontSize: '16px' }}
                allowClear
              >
                {Array.from({ length: 20 }, (_, i) => {
                  const year = new Date().getFullYear() - i;
                  return (
                    <Option key={year} value={year}>
                      {year}年
                    </Option>
                  );
                })}
              </Select>
            </Space>
          </div>
          <div className="toolbar-right">
            <Button size="middle" onClick={clearFilters} style={{ fontSize: '16px' }}>
              清除筛选
            </Button>
            {(searchText || dateRange) && (
              <Alert
                message={`已筛选: ${[searchText && '搜索', dateRange && '日期'].filter(Boolean).join('、')}`}
                type="info"
                showIcon
                closable
                onClose={clearFilters}
                style={{ 
                  display: 'inline-block', 
                  padding: '8px 12px',
                  fontSize: '16px',
                  lineHeight: '1.4'
                }}
              />
            )}
          </div>
        </div>

        {/* 项目表格 - 紧凑设计 */}
        {filteredProjects.length === 0 && !loading ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              <div style={{ fontSize: '18px', color: '#8c8c8c' }}>
                {projects.length === 0 ? 
                  "还没有任何项目，点击上方按钮创建第一个项目" : 
                  "没有找到符合条件的项目，请调整筛选条件"}
              </div>
            }
            style={{ padding: '60px 20px' }}
          >
            {projects.length === 0 && (
              <Button type="primary" icon={<PlusOutlined />} onClick={() => showModal()} size="large" style={{ fontSize: '18px' }}>
                创建项目
              </Button>
            )}
          </Empty>
        ) : (
          <Table 
            columns={columns} 
            dataSource={filteredProjects} 
            rowKey="project_id" 
            loading={loading}
            size="middle"
            pagination={{
              total: filteredProjects.length,
              pageSize: 15,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => (
                <span style={{ fontSize: '16px' }}>
                  第 {range[0]}-{range[1]} 条，共 <span className="important-number">{total}</span> 条
                </span>
              ),
              pageSizeOptions: ['10', '15', '20', '50'],
            }}
            style={{ marginTop: '12px' }}
          />
        )}
      </Card>

      {/* 新建/编辑项目模态框 - 紧凑设计 */}
      <Modal
        title={
          <div style={{ fontSize: '24px', fontWeight: '600' }}>
            {currentProject ? <EditOutlined /> : <PlusOutlined />}
            <span style={{ marginLeft: '8px' }}>
              {currentProject ? "编辑项目" : "新建项目"}
            </span>
          </div>
        }
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        okText="保存"
        cancelText="取消"
        width={500}
        bodyStyle={{ padding: '20px' }}
      >
        <Form
          form={form}
          layout="vertical"
          requiredMark={false}
        >
          <Form.Item
            name="project_name"
            label={<span style={{ fontSize: '14px', fontWeight: '500' }}>项目名称</span>}
            rules={[
              { required: true, message: '请输入项目名称' },
              { min: 2, message: '项目名称至少2个字符' },
              { max: 50, message: '项目名称不能超过50个字符' }
            ]}
            style={{ marginBottom: '16px' }}
          >
            <Input 
              placeholder="请输入项目名称" 
              prefix={<FolderOpenOutlined />}
              size="middle"
            />
          </Form.Item>
          <Form.Item
            name="description"
            label={<span style={{ fontSize: '14px', fontWeight: '500' }}>项目描述</span>}
            rules={[
              { max: 500, message: '项目描述不能超过500个字符' }
            ]}
            style={{ marginBottom: '8px' }}
          >
            <Input.TextArea 
              rows={3} 
              placeholder="请输入项目描述（可选）"
              showCount
              maxLength={500}
              style={{ fontSize: '14px' }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProjectList; 