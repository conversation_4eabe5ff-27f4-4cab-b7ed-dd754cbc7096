{"name": "bankflow-analyzer-desktop", "version": "1.0.0", "description": "银行流水分析工具 - 桌面版", "main": "src/main.js", "author": "银行流水分析工具开发团队", "license": "MIT", "homepage": "./", "scripts": {"start": "set PYTHONIOENCODING=utf-8 && chcp 65001 && electron .", "dev": "set PYTHONIOENCODING=utf-8 && chcp 65001 && concurrently \"npm run start:backend\" \"npm run start:frontend\" \"wait-on http://localhost:3000 && electron .\"", "start:backend": "set PYTHONIOENCODING=utf-8 && cd ../backend && python -m uvicorn app.main:app --host 127.0.0.1 --port 8000", "start:frontend": "cd ../frontend/bankflow-client && npm start", "build": "npm run build:frontend && npm run build:electron", "build:frontend": "cd ../frontend/bankflow-client && npm run build", "build:electron": "electron-builder", "dist": "npm run build && electron-builder --publish=never", "pack": "electron-builder --dir", "test": "jest"}, "keywords": ["electron", "react", "bank-statement", "analysis", "desktop"], "devDependencies": {"concurrently": "^8.2.2", "electron": "^29.0.0", "electron-builder": "^24.9.1", "wait-on": "^7.2.0", "jest": "^29.7.0"}, "dependencies": {"electron-is-dev": "^2.0.0", "path": "^0.12.7"}, "build": {"appId": "com.bankflow.analyzer", "productName": "银行流水分析工具", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "../frontend/bankflow-client/build/**/*"], "extraResources": [{"from": "../backend", "to": "backend", "filter": ["**/*", "!**/__pycache__", "!**/*.pyc", "!**/node_modules"]}], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}