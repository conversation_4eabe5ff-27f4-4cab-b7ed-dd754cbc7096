{"name": "beibuwan_format2_plugin", "version": "1.0.0", "description": "北部湾银行Format2解析器插件，支持黄宪文2.xlsx格式的银行流水解析", "author": "银行流水系统团队", "license": "MIT", "homepage": "https://github.com/your-org/bank-parser", "supported_formats": ["Excel (.xlsx)"], "supported_banks": ["北部湾银行"], "dependencies": ["pandas>=1.3.0", "openpyxl>=3.0.0"], "entry_point": "plugin.Plugin", "confidence_threshold": 0.8, "keywords": ["北部湾银行", "银行流水", "解析器", "插件", "Format2"], "format_features": {"multi_sheet": false, "time_format": "YYYY-MM-DD", "amount_format": "debit_credit_columns", "header_info": true, "transaction_sheet": "Sheet1"}, "changelog": {"1.0.0": "初始版本，支持黄宪文2.xlsx格式解析，包含借方贷方处理、表头信息提取、日期格式处理"}}