"""
项目管理API - 支持用户数据隔离的DuckDB数据库存储
"""
import logging
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, status, Depends
from sqlalchemy.orm import Session
from pydantic import BaseModel

from ..middleware.auth_middleware import get_current_user, get_user_db
from ..models.duckdb_models import DuckDBProject as Project, DuckDBAccount as Account, DuckDBTransaction as Transaction

logger = logging.getLogger(__name__)

router = APIRouter()

class ProjectCreate(BaseModel):
    """创建项目的请求模式"""
    project_name: str
    person_name: Optional[str] = None
    description: Optional[str] = None

class ProjectUpdate(BaseModel):
    """更新项目的请求模式"""
    project_name: Optional[str] = None
    person_name: Optional[str] = None
    description: Optional[str] = None

class ProjectResponse(BaseModel):
    """项目响应模式"""
    id: Optional[str] = None  # 前端期望的字段名
    project_id: str
    name: Optional[str] = None  # 前端期望的字段名  
    project_name: str
    person_name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
        
    @classmethod
    def from_orm(cls, obj):
        """自定义序列化方法，处理字段名映射"""
        return cls(
            id=obj.project_id,  # 映射给前端期望的id字段
            project_id=obj.project_id,
            name=obj.project_name,  # 映射给前端期望的name字段
            project_name=obj.project_name,
            person_name=obj.person_name,
            description=obj.description,
            is_active=obj.is_active,
            created_at=obj.created_at,
            updated_at=obj.updated_at
        )

@router.post("/", response_model=ProjectResponse)
async def create_project(
    project: ProjectCreate, 
    db: Session = Depends(get_user_db),
    current_user: str = Depends(get_current_user)
) -> ProjectResponse:
    """
    创建新项目
    """
    try:
        project_id = str(uuid.uuid4())
        
        logger.info(f"用户 '{current_user}' 创建项目: {project.project_name}")
        
        new_project = Project(
            project_id=project_id,
            project_name=project.project_name,
            person_name=project.person_name,
            description=project.description,
            is_active=True
        )
        
        db.add(new_project)
        db.commit()
        db.refresh(new_project)
        
        logger.info(f"用户 '{current_user}' 创建项目成功: {project_id}")
        
        return ProjectResponse.from_orm(new_project)
        
    except Exception as e:
        db.rollback()
        logger.error(f"用户 '{current_user}' 创建项目失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建项目失败: {str(e)}"
        )

@router.get("/", response_model=List[ProjectResponse])
async def get_projects(
    db: Session = Depends(get_user_db),
    current_user: str = Depends(get_current_user)
) -> List[ProjectResponse]:
    """
    获取当前用户的所有项目列表
    """
    try:
        logger.info(f"用户 '{current_user}' 获取项目列表")
        
        projects = db.query(Project).order_by(Project.created_at.desc()).all()
        
        logger.info(f"用户 '{current_user}' 获取项目列表成功，共{len(projects)}个项目")
        return [ProjectResponse.from_orm(project) for project in projects]
        
    except Exception as e:
        logger.error(f"用户 '{current_user}' 获取项目列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取项目列表失败: {str(e)}"
        )

@router.get("/{project_id}", response_model=ProjectResponse)
async def get_project(
    project_id: str, 
    db: Session = Depends(get_user_db),
    current_user: str = Depends(get_current_user)
) -> ProjectResponse:
    """
    获取指定项目详情
    """
    try:
        project = db.query(Project).filter(Project.project_id == project_id).first()
        
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"项目 {project_id} 不存在"
            )
        
        logger.info(f"用户 '{current_user}' 获取项目详情成功: {project_id}")
        
        return ProjectResponse.from_orm(project)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户 '{current_user}' 获取项目详情失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取项目详情失败: {str(e)}"
        )

@router.put("/{project_id}", response_model=ProjectResponse)
async def update_project(
    project_id: str,
    project_update: ProjectUpdate,
    db: Session = Depends(get_user_db),
    current_user: str = Depends(get_current_user)
) -> ProjectResponse:
    """
    更新项目信息
    """
    try:
        project = db.query(Project).filter(Project.project_id == project_id).first()
        
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"项目 {project_id} 不存在"
            )
        
        # 更新字段
        if project_update.project_name is not None:
            project.project_name = project_update.project_name
        if project_update.person_name is not None:
            project.person_name = project_update.person_name
        if project_update.description is not None:
            project.description = project_update.description
        
        project.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(project)
        
        logger.info(f"用户 '{current_user}' 更新项目成功: {project_id}")
        
        return ProjectResponse.from_orm(project)
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"用户 '{current_user}' 更新项目失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新项目失败: {str(e)}"
        )

@router.delete("/{project_id}", response_model=Dict[str, Any])
async def delete_project(
    project_id: str, 
    db: Session = Depends(get_user_db),
    current_user: str = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    删除指定项目
    """
    try:
        project = db.query(Project).filter(Project.project_id == project_id).first()
        
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"项目 {project_id} 不存在"
            )
        
        db.delete(project)
        db.commit()
        
        logger.info(f"用户 '{current_user}' 删除项目成功: {project_id}")
        
        return {
            "success": True,
            "message": f"项目 {project_id} 删除成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"用户 '{current_user}' 删除项目失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除项目失败: {str(e)}"
        )

@router.get("/{project_id}/stats", response_model=Dict[str, Any])
async def get_project_stats(
    project_id: str, 
    db: Session = Depends(get_user_db),
    current_user: str = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取项目统计信息
    """
    try:
        project = db.query(Project).filter(Project.project_id == project_id).first()
        
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"项目 {project_id} 不存在"
            )
        
        # 获取项目下的账户
        accounts = db.query(Account).filter(Account.project_id == project_id).all()
        
        # 获取项目下的交易
        transactions = db.query(Transaction).filter(Transaction.project_id == project_id).all()
        
        # 计算统计信息
        total_inflow = sum(float(t.transaction_amount or 0) for t in transactions if float(t.transaction_amount or 0) > 0)
        total_outflow = abs(sum(float(t.transaction_amount or 0) for t in transactions if float(t.transaction_amount or 0) < 0))
        
        # 获取银行列表
        banks = list(set(acc.bank_name for acc in accounts if acc.bank_name))
        
        # 获取日期范围
        dates = []
        for t in transactions:
            if t.transaction_datetime:
                # 只提取日期部分
                if " " in str(t.transaction_datetime):
                    date_part = str(t.transaction_datetime).split(" ")[0]
                else:
                    date_part = str(t.transaction_datetime)
                dates.append(date_part)
        
        date_range = {"start": "未知", "end": "未知"}
        if dates:
            date_range = {"start": min(dates), "end": max(dates)}
        
        stats = {
            "bank_statements": {
                "accounts_count": len(accounts),
                "transactions_count": len(transactions),
                "total_inflow": total_inflow,
                "total_outflow": total_outflow,
                "date_range": date_range,
                "banks": banks
            },
            "case_brief": {
                "subject_name": "待录入",
                "subject_id_card": "待录入", 
                "clues_summary": "案情简要待录入，请在案情简要模块中补充相关信息",
                "related_persons_count": 0,
                "assets_info": {
                    "vehicles": 0,
                    "houses": 0,
                    "others": 0
                }
            },
            "transcripts": {
                "count": 0,
                "latest_date": None
            }
        }
        
        logger.info(f"用户 '{current_user}' 获取项目统计信息成功: {project_id}")
        return stats
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户 '{current_user}' 获取项目统计信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取项目统计信息失败: {str(e)}"
        ) 