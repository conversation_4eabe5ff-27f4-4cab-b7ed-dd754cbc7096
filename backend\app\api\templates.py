"""
模板API路由 - 作为解析器API的别名
用于兼容前端的模板API调用
"""
from fastapi import APIRouter, File, UploadFile, Form, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import Optional, List, Dict, Any
import logging
import tempfile
import os

# 🔧 重要修复：只导入需要的组件，移除直接解析器类导入
from .parser import smart_analyzer

from ..middleware.auth_middleware import get_current_user, get_user_db
from ..database.duckdb_config import get_db

logger = logging.getLogger(__name__)

router = APIRouter()

# 测试路由已删除

@router.get("/")
async def get_templates(bank_name: Optional[str] = None):
    """
    获取解析模板列表（兼容前端模板API调用）
    """
    try:
        logger.info(f"获取模板列表，银行: {bank_name}")
        
        # 🔧 使用smart_analyzer获取模板列表
        templates = smart_analyzer.get_templates_list(bank_name)
        
        result = {
            "templates": templates,
            "total": len(templates),
            "bank_name": bank_name
        }
        return result
            
    except Exception as e:
        logger.error(f"获取模板列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模板列表失败: {str(e)}")

@router.post("/smart-analyze")
async def smart_analyze_file(
    file: UploadFile = File(...),
    bank_name: Optional[str] = Form(None)
):
    """
    智能分析文件并推荐解析模板（兼容前端模板API调用）
    """
    temp_file_path = None
    try:
        logger.info(f"开始智能分析文件: {file.filename}, 银行: {bank_name}")
        
        # 保存临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as tmp_file:
            content = await file.read()
            tmp_file.write(content)
            temp_file_path = tmp_file.name
        
        # 调用智能分析器分析文件
        analysis_result = smart_analyzer.analyze_file_structure(temp_file_path, bank_name)
        logger.info(f"分析结果类型: {type(analysis_result)}, 内容: {analysis_result}")
        
        # 转换为前端期望的模板格式
        recommended_templates = []
        
        # 处理分析结果 - 兼容不同的返回格式
        if isinstance(analysis_result, dict):
            # 如果是字典格式，尝试获取analysis_results
            results = analysis_result.get('analysis_results', [])
            if not results and 'parsers' in analysis_result:
                # 如果没有analysis_results，尝试从parsers获取
                results = analysis_result.get('parsers', [])
        elif isinstance(analysis_result, list):
            # 如果直接是列表格式
            results = analysis_result
        else:
            results = []
        
        for result in results:
            if isinstance(result, dict):
                recommended_templates.append({
                    "templateId": result.get('parser_id', result.get('id', 'unknown')),
                    "templateName": result.get('parser_name', result.get('name', '未知解析器')),
                    "bankName": result.get('bank_name', bank_name or '未知银行'),
                    "confidence": result.get('confidence', 0),
                    "isRecommended": result.get('confidence', 0) >= 80,
                    "analysisDetails": result.get('details', result.get('description', {}))
                })
        
        return {
            "success": True,
            "recommendedTemplates": recommended_templates,
            "analysisResult": analysis_result,
            "fileName": file.filename
        }
                
    except Exception as e:
        logger.error(f"智能分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"智能分析失败: {str(e)}")
    
    finally:
        # 清理临时文件
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
            except:
                pass

@router.get("/universal/download")
async def download_universal_template():
    """
    下载通用解析器模板文件
    """
    try:
        from fastapi.responses import FileResponse
        import os
        
        template_path = "f:/流水清洗/backend/data/templates/通用解析器模板.xlsx"
        
        # 检查文件是否存在
        if not os.path.exists(template_path):
            # 如果不存在，创建模板文件
            from ..services.create_universal_template import create_universal_template
            template_path = create_universal_template()
        
        # 返回文件下载响应
        return FileResponse(
            path=template_path,
            filename="通用解析器模板.xlsx",
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        
    except Exception as e:
        logger.error(f"下载通用模板失败: {e}")
        raise HTTPException(status_code=500, detail=f"下载通用模板失败: {str(e)}")

@router.get("/universal/info")
async def get_universal_template_info():
    """
    获取通用解析器模板信息和使用说明
    """
    try:
        from ..services.parser.universal_parser import UniversalParser
        
        # 从通用模板配置文件读取说明
        import json
        import os
        
        config_path = "f:/流水清洗/parsing_templates/universal_template.json"
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            instructions = config.get('instructions', {})
        else:
            instructions = {
                "title": "通用解析器使用说明",
                "description": "当银行流水格式无法被现有解析器识别时，可使用通用解析器",
                "steps": [
                    "1. 下载通用格式Excel模板",
                    "2. 将您的银行流水数据按照模板的列顺序手工整理", 
                    "3. 确保必填字段都有数据",
                    "4. 保存文件并上传解析"
                ],
                "notes": [
                    "• 交易金额：正数表示收入，负数表示支出",
                    "• 日期格式：YYYY-MM-DD",
                    "• 建议在文件名中包含'通用'关键词"
                ]
            }
        
        return {
            "success": True,
            "templateInfo": {
                "templateId": "universal_parser",
                "templateName": "通用解析器模板",
                "description": "适用于所有银行的标准格式模板",
                "version": "1.0.0",
                "downloadUrl": "/api/templates/universal/download",
                "supportedFormats": ["XLS", "XLSX"],
                "standardFields": [
                    {"column": "A", "name": "序号", "required": True},
                    {"column": "B", "name": "持卡人", "required": True},
                    {"column": "C", "name": "银行名称", "required": True},
                    {"column": "D", "name": "账号", "required": True},
                    {"column": "E", "name": "卡号", "required": False},
                    {"column": "F", "name": "交易日期", "required": True},
                    {"column": "G", "name": "交易时间", "required": False},
                    {"column": "H", "name": "交易方式", "required": False},
                    {"column": "I", "name": "交易金额", "required": True},
                    {"column": "J", "name": "账户余额", "required": True},
                    {"column": "K", "name": "借贷标志", "required": False},
                    {"column": "L", "name": "对方户名", "required": False},
                    {"column": "M", "name": "对方账号", "required": False},
                    {"column": "N", "name": "对方开户行", "required": False},
                    {"column": "O", "name": "备注1", "required": False},
                    {"column": "P", "name": "备注2", "required": False},
                    {"column": "Q", "name": "备注3", "required": False},
                    {"column": "R", "name": "币种", "required": False}
                ]
            },
            "instructions": instructions
        }
        
    except Exception as e:
        logger.error(f"获取通用模板信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取通用模板信息失败: {str(e)}")