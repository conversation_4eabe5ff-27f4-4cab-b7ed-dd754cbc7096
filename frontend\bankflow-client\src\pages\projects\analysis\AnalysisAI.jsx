import React, { useState } from 'react';
import { Card, Input, Button, Typography, Space, Tabs, Divider, Timeline, List, Tag, Spin, Empty } from 'antd';
import { SendOutlined, RobotOutlined, FileTextOutlined, BarChartOutlined, DotChartOutlined } from '@ant-design/icons';

const { TextArea } = Input;
const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

const AnalysisAI = () => {
  const [loading, setLoading] = useState(false);
  const [query, setQuery] = useState('');
  const [chatHistory, setChatHistory] = useState([
    {
      role: 'system',
      content: '欢迎使用银行流水AI分析助手，我可以帮助您分析银行流水数据，请问有什么可以帮到您?',
      time: '2023-05-23 09:00:00',
    },
  ]);
  const [activeTab, setActiveTab] = useState('chat');
  const [analysisResult, setAnalysisResult] = useState(null);

  // 处理发送查询
  const handleSendQuery = () => {
    if (!query.trim()) return;

    // 添加用户消息到聊天历史
    const userMessage = {
      role: 'user',
      content: query,
      time: new Date().toLocaleString(),
    };
    
    setChatHistory([...chatHistory, userMessage]);
    setLoading(true);

    // 模拟AI响应
    setTimeout(() => {
      const aiResponse = {
        role: 'system',
        content: getAIResponse(query),
        time: new Date().toLocaleString(),
      };
      
      setChatHistory(prevHistory => [...prevHistory, aiResponse]);
      setLoading(false);
      setQuery('');

      // 如果是请求分析，模拟生成分析结果
      if (query.includes('分析') || query.includes('统计') || query.includes('报告')) {
        setAnalysisResult({
          title: '银行流水AI分析报告',
          date: new Date().toLocaleDateString(),
          summary: '本报告基于对所选账户的银行流水数据分析，生成了资金流向、交易模式和异常交易的相关信息。',
          findings: [
            '发现大额交易3笔，总计金额¥152,000元',
            '交易频率最高的对手方为"XX供应商"，共15笔交易',
            '资金主要流向为企业账户(65%)和个人账户(35%)',
            '发现可疑交易模式：短期内频繁小额转入后大额转出',
          ],
          accounts: [
            { name: '张三', accountNumber: '6222021234567890123', bank: '工商银行', transactionCount: 156 },
            { name: '张三', accountNumber: '6227001234567890123', bank: '建设银行', transactionCount: 87 },
          ],
        });
        
        // 切换到分析报告标签
        setActiveTab('report');
      }
    }, 1500);
  };

  // 模拟AI响应内容
  const getAIResponse = (userQuery) => {
    if (userQuery.includes('分析') || userQuery.includes('统计')) {
      return '我已为您分析了银行流水数据，生成了详细报告。您可以在"分析报告"标签查看完整分析结果。主要发现：1) 大额交易集中在每月初; 2) 有三个可疑的频繁交易对手; 3) 存在短期内资金快进快出的模式。';
    } else if (userQuery.includes('异常') || userQuery.includes('可疑')) {
      return '我检测到以下异常交易：1) 2023年1月15日向"李某"的50,000元转账; 2) 2023年1月18日至1月25日期间，收到10笔小额转入后，于1月26日转出一笔大额资金; 3) 与"某贸易公司"的往来频率异常，一个月内有27笔交易。';
    } else if (userQuery.includes('建议') || userQuery.includes('怎么办')) {
      return '基于当前分析，我的建议是：1) 重点关注"某贸易公司"的交易背景; 2) 核实1月26日的大额转出交易; 3) 调查这些交易是否与案件主体有关联; 4) 考虑扩大调查范围，查看关联人的银行账户情况。';
    }
    return '我理解您的问题。请问您想了解哪个方面的银行流水分析？我可以帮您进行交易模式分析、异常交易检测、资金流向追踪等。';
  };

  // 渲染聊天消息
  const renderChatMessage = (message) => {
    const isSystem = message.role === 'system';
    
    return (
      <div 
        style={{ 
          textAlign: isSystem ? 'left' : 'right',
          marginBottom: 16,
        }}
      >
        <Space direction="vertical" style={{ maxWidth: '80%', display: 'inline-block' }}>
          <div
            style={{
              background: isSystem ? '#f0f2f5' : '#1890ff',
              color: isSystem ? 'rgba(0, 0, 0, 0.85)' : '#fff',
              padding: '8px 12px',
              borderRadius: 8,
              textAlign: 'left',
              display: 'inline-block',
            }}
          >
            {message.content}
          </div>
          <Text type="secondary" style={{ fontSize: 12 }}>{message.time}</Text>
        </Space>
      </div>
    );
  };

  return (
    <div>
      <Card title="银行流水AI分析">
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <Tabs.TabPane
            tab={
              <span>
                <RobotOutlined />
                AI对话
              </span>
            }
            key="chat"
          >
            <div style={{ height: 400, overflowY: 'auto', padding: '0 16px', marginBottom: 16 }}>
              {chatHistory.map((message, index) => (
                <div key={index}>
                  {renderChatMessage(message)}
                </div>
              ))}
              {loading && (
                <div style={{ textAlign: 'center', margin: '20px 0' }}>
                  <Spin tip="AI思考中..." />
                </div>
              )}
            </div>
            <Divider style={{ margin: '0 0 16px 0' }} />
            <Space.Compact style={{ width: '100%' }}>
              <TextArea 
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="输入问题，例如：分析这些账户的异常交易模式"
                autoSize={{ minRows: 2, maxRows: 4 }}
                onPressEnter={(e) => {
                  if (!e.shiftKey) {
                    e.preventDefault();
                    handleSendQuery();
                  }
                }}
              />
              <Button 
                type="primary" 
                icon={<SendOutlined />} 
                onClick={handleSendQuery}
                loading={loading}
              >
                发送
              </Button>
            </Space.Compact>
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">提示：可以询问流水分析、异常交易检测、资金流向等问题</Text>
            </div>
          </Tabs.TabPane>
          
          <Tabs.TabPane
            tab={
              <span>
                <FileTextOutlined />
                分析报告
              </span>
            }
            key="report"
          >
            {analysisResult ? (
              <div>
                <Title level={3}>{analysisResult.title}</Title>
                <Text type="secondary">生成日期：{analysisResult.date}</Text>
                <Paragraph style={{ marginTop: 16 }}>{analysisResult.summary}</Paragraph>
                
                <Divider orientation="left">主要发现</Divider>
                <Timeline>
                  {analysisResult.findings.map((finding, index) => (
                    <Timeline.Item key={index}>{finding}</Timeline.Item>
                  ))}
                </Timeline>
                
                <Divider orientation="left">账户信息</Divider>
                <List
                  bordered
                  dataSource={analysisResult.accounts}
                  renderItem={item => (
                    <List.Item>
                      <List.Item.Meta
                        title={`${item.name} - ${item.bank}`}
                        description={`${item.accountNumber.substring(0, 4)}****${item.accountNumber.substring(item.accountNumber.length - 4)}`}
                      />
                      <div>交易记录：{item.transactionCount}笔</div>
                    </List.Item>
                  )}
                />
                
                <Divider orientation="left">交易模式</Divider>
                <Empty 
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="交易模式可视化图表将在此显示"
                />
              </div>
            ) : (
              <Empty description="暂无分析报告，请在AI对话中询问生成分析报告" />
            )}
          </Tabs.TabPane>
          
          <Tabs.TabPane
            tab={
              <span>
                <BarChartOutlined />
                数据统计
              </span>
            }
            key="statistics"
          >
            <Empty 
              description="数据统计功能开发中"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </Tabs.TabPane>
          
          <Tabs.TabPane
            tab={
              <span>
                <DotChartOutlined />
                关系图谱
              </span>
            }
            key="graph"
          >
            <Empty 
              description="关系图谱功能开发中"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </Tabs.TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default AnalysisAI; 