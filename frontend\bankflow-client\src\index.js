import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';

const root = ReactDOM.createRoot(document.getElementById('root'));

// 注意：移除了React.StrictMode以避免开发环境中的双重渲染
// React.StrictMode在开发环境中会故意双重执行useEffect等副作用来检测代码问题
// 移除后将失去这些代码质量检查，但可以减少开发时的API调用次数
root.render(
  <App />
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
