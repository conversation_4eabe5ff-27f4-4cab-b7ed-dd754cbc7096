#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

def preview_xls(path: str, max_rows: int = 30, max_cols: int = 20):
    try:
        import xlrd
    except Exception as e:
        print(f"xlrd 导入失败: {e}")
        return 1

    if not os.path.exists(path):
        print(f"文件不存在: {path}")
        return 1

    print(f"预览: {path}")
    book = xlrd.open_workbook(path, formatting_info=False)
    print("SHEETS:", book.sheet_names())
    for sn in book.sheet_names():
        sh = book.sheet_by_name(sn)
        print("\n=== Sheet:", sn, "rows=", sh.nrows, "cols=", sh.ncols, "===")
        rows = min(max_rows, sh.nrows)
        cols = min(max_cols, sh.ncols)
        for r in range(rows):
            row_vals = []
            for c in range(cols):
                try:
                    v = sh.cell_value(r, c)
                except Exception:
                    v = ""
                row_vals.append(str(v))
            print(r, "\t".join(row_vals))
    return 0


if __name__ == '__main__':
    target = None
    if len(sys.argv) >= 2:
        target = sys.argv[1]
    else:
        target = r"F:\\流水清洗\\银行流水数据参考\\9信用社\\1.xls"
    sys.exit(preview_xls(target))











