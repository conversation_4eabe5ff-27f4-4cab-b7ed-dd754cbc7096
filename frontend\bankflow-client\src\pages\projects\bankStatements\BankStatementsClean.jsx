import React, { useState, useEffect } from 'react';
import {
  Card, Table, Button, Form, Select, Input, InputNumber,
  Tabs, Collapse, Typography, Space, Switch, Tag,
  Alert, Divider, message, Empty
} from 'antd';
import {
  ClearOutlined, SettingOutlined, SaveOutlined,
  SyncOutlined, CheckCircleOutlined, ExclamationCircleOutlined,
  CloseCircleOutlined, FileSearchOutlined, EditOutlined
} from '@ant-design/icons';
import { getCurrentUser } from '../../../services/api';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Option } = Select;

/**
 * 银行流水数据清洗页面
 *
 * @returns {JSX.Element} 数据清洗页面
 */
const BankStatementsClean = () => {
  // 状态管理
  const [rules, setRules] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedRule, setSelectedRule] = useState(null);
  const [editForm] = Form.useForm();
  const [previewData, setPreviewData] = useState(null);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [projects, setProjects] = useState([]);
  const [accounts, setAccounts] = useState([]);
  const [selectedProject, setSelectedProject] = useState(null);
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [applyLoading, setApplyLoading] = useState(false);
  const [applyResult, setApplyResult] = useState(null);
  const [error, setError] = useState(null);

  // 获取清洗规则
  const fetchRules = async () => {
    setLoading(true);
    setError(null);
    try {
      const currentUser = getCurrentUser();
      const response = await fetch('http://localhost:8000/api/cleaner/rules', {
        headers: {
          'Content-Type': 'application/json',
          'X-Current-User': currentUser
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setRules(data);
      // 默认选择第一个规则
      if (data.length > 0) {
        setSelectedRule(data[0]);
        editForm.setFieldsValue(data[0]);
      }
    } catch (err) {
      console.error('获取清洗规则失败:', err);
      setError('获取清洗规则失败，请刷新页面重试');
    } finally {
      setLoading(false);
    }
  };

  // 获取项目列表
  const fetchProjects = async () => {
    try {
      const currentUser = getCurrentUser();
      const response = await fetch('http://localhost:8000/api/projects', {
        headers: {
          'Content-Type': 'application/json',
          'X-Current-User': currentUser
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setProjects(data);
    } catch (err) {
      console.error('获取项目列表失败:', err);
    }
  };

  // 获取账户列表
  const fetchAccounts = async (projectId) => {
    try {
      const currentUser = getCurrentUser();
      const response = await fetch(`http://localhost:8000/api/accounts?project_id=${projectId}`, {
        headers: {
          'Content-Type': 'application/json',
          'X-Current-User': currentUser
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setAccounts(data.accounts || []);
    } catch (err) {
      console.error('获取账户列表失败:', err);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchRules();
    fetchProjects();
  }, []);

  // 当选择项目时获取账户列表
  useEffect(() => {
    if (selectedProject) {
      fetchAccounts(selectedProject);
    }
  }, [selectedProject, fetchAccounts]);



  // 选择规则
  const handleRuleSelect = (rule) => {
    setSelectedRule(rule);
    editForm.setFieldsValue(rule);
  };

  // 更新规则
  const handleUpdateRule = async (values) => {
    setLoading(true);
    try {
      const currentUser = getCurrentUser();
      const response = await fetch(`http://localhost:8000/api/cleaner/rules/${selectedRule.name}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-Current-User': currentUser
        },
        body: JSON.stringify(values)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const updatedRule = await response.json();
      setRules(rules.map(rule =>
        rule.name === selectedRule.name ? updatedRule : rule
      ));
      setSelectedRule(updatedRule);
      message.success('规则更新成功');
    } catch (err) {
      console.error('更新规则失败:', err);
      message.error(`更新规则失败: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 预览清洗效果
  const handlePreview = async () => {
    if (!selectedProject) {
      message.warning('请先选择项目');
      return;
    }

    setPreviewLoading(true);
    setPreviewData(null);
    try {
      // 模拟预览数据，因为实际的预览需要具体的交易数据
      const mockPreviewData = {
        raw_data: {
          "交易日期": "2023-01-15",
          "交易金额": "1500.00",
          "对方户名": "张三",
          "摘要": "转账"
        },
        cleaned_data: {
          "transaction_datetime": "2023-01-15 00:00:00",
          "transaction_amount": 1500.00,
          "counterparty_name": "张三",
          "summary": "转账"
        },
        applied_rules: [
          {
            name: "日期时间清洗规则",
            type: "DateTimeRule",
            priority: 10,
            description: "标准化日期时间格式"
          },
          {
            name: "金额清洗规则",
            type: "AmountRule",
            priority: 9,
            description: "统一金额格式"
          }
        ]
      };

      setPreviewData(mockPreviewData);
      message.success('预览数据已生成（演示数据）');
    } catch (err) {
      console.error('预览清洗效果失败:', err);
      message.error(`预览失败: ${err.message}`);
    } finally {
      setPreviewLoading(false);
    }
  };

  // 应用清洗规则
  const handleApplyCleaning = async () => {
    if (!selectedProject) {
      message.warning('请先选择项目');
      return;
    }

    setApplyLoading(true);
    setApplyResult(null);
    try {
      // 模拟清洗结果，因为实际的清洗需要具体的交易数据
      const mockResult = {
        cleaned_count: Math.floor(Math.random() * 100) + 50, // 随机生成50-150条记录
        success: true,
        message: "数据清洗完成"
      };

      // 模拟处理时间
      await new Promise(resolve => setTimeout(resolve, 2000));

      setApplyResult(mockResult);
      message.success(`成功清洗 ${mockResult.cleaned_count} 条交易记录（演示功能）`);
    } catch (err) {
      console.error('应用清洗规则失败:', err);
      message.error(`应用清洗规则失败: ${err.message}`);
    } finally {
      setApplyLoading(false);
    }
  };

  // 根据规则类型渲染不同的表单
  const renderRuleForm = () => {
    if (!selectedRule) return null;

    switch (selectedRule.type) {
      case 'DateTimeRule':
        return (
          <>
            <Form.Item
              name={['config', 'date_column']}
              label="日期列名"
              help="原始数据中的日期列名"
            >
              <Input placeholder="如: 交易日期" />
            </Form.Item>
            <Form.Item
              name={['config', 'time_column']}
              label="时间列名"
              help="原始数据中的时间列名（可选）"
            >
              <Input placeholder="如: 交易时间" />
            </Form.Item>
            <Form.Item
              name={['config', 'output_format']}
              label="输出格式"
              help="标准化后的日期时间格式"
            >
              <Input placeholder="如: %Y-%m-%d %H:%M:%S" />
            </Form.Item>
          </>
        );
      case 'AmountRule':
        return (
          <>
            <Form.Item
              name={['config', 'amount_column']}
              label="金额列名"
              help="原始数据中的金额列名"
            >
              <Input placeholder="如: 交易金额" />
            </Form.Item>
            <Form.Item
              name={['config', 'debit_column']}
              label="借方列名"
              help="借方金额列名（可选）"
            >
              <Input placeholder="如: 借方发生额" />
            </Form.Item>
            <Form.Item
              name={['config', 'credit_column']}
              label="贷方列名"
              help="贷方金额列名（可选）"
            >
              <Input placeholder="如: 贷方发生额" />
            </Form.Item>
            <Form.Item
              name={['config', 'direction_column']}
              label="借贷标志列名"
              help="借贷方向标志列名（可选）"
            >
              <Input placeholder="如: 借贷标志" />
            </Form.Item>
          </>
        );
      case 'TextRule':
        return (
          <>
            <Form.Item
              name={['config', 'target_columns']}
              label="目标列名"
              help="需要清洗的文本列名，多个用逗号分隔"
            >
              <Input placeholder="如: 对方户名,摘要" />
            </Form.Item>
            <Form.Item
              name={['config', 'remove_special_chars']}
              label="移除特殊字符"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
            <Form.Item
              name={['config', 'normalize_whitespace']}
              label="标准化空白字符"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </>
        );
      default:
        return <Text type="secondary">该规则类型暂无特定配置项</Text>;
    }
  };

  // 渲染预览对比结果
  const renderPreviewComparison = () => {
    if (!previewData) return null;

    const { raw_data, cleaned_data, applied_rules } = previewData;

    return (
      <Card title="清洗预览结果" style={{ marginTop: 16 }}>
        <Tabs defaultActiveKey="1">
          <TabPane tab="数据对比" key="1">
            <div style={{ display: 'flex' }}>
              <div style={{ flex: 1, marginRight: 16 }}>
                <Title level={5}>原始数据</Title>
                <pre style={{ background: '#f5f5f5', padding: 16, borderRadius: 4 }}>
                  {JSON.stringify(raw_data, null, 2)}
                </pre>
              </div>
              <div style={{ flex: 1 }}>
                <Title level={5}>清洗后数据</Title>
                <pre style={{ background: '#f5f5f5', padding: 16, borderRadius: 4 }}>
                  {JSON.stringify(cleaned_data, null, 2)}
                </pre>
              </div>
            </div>
          </TabPane>
          <TabPane tab="应用的规则" key="2">
            <Table
              dataSource={applied_rules}
              rowKey="name"
              columns={[
                { title: '规则名称', dataIndex: 'name' },
                { title: '类型', dataIndex: 'type' },
                { title: '优先级', dataIndex: 'priority' },
                { title: '描述', dataIndex: 'description' }
              ]}
              pagination={false}
            />
          </TabPane>
        </Tabs>
      </Card>
    );
  };

  return (
    <div>
      {error && (
        <Alert
          message="错误"
          description={error}
          type="error"
          closable
          style={{ marginBottom: 16 }}
        />
      )}

      <Tabs defaultActiveKey="1">
        <TabPane tab="清洗规则管理" key="1">
          <div style={{ display: 'flex' }}>
            <div style={{ width: 300, marginRight: 16 }}>
              <Card title="规则列表" loading={loading}>
                {rules.map((rule) => (
                  <div
                    key={rule.name}
                    style={{
                      padding: '8px 16px',
                      borderRadius: 4,
                      marginBottom: 8,
                      cursor: 'pointer',
                      background: selectedRule?.name === rule.name ? '#e6f7ff' : '#fff',
                      border: selectedRule?.name === rule.name ? '1px solid #1890ff' : '1px solid #d9d9d9'
                    }}
                    onClick={() => handleRuleSelect(rule)}
                  >
                    <div>{rule.name}</div>
                    <div>
                      <Tag color="blue">{rule.type}</Tag>
                      <Tag color="green">优先级: {rule.priority}</Tag>
                    </div>
                  </div>
                ))}
              </Card>
            </div>

            <div style={{ flex: 1 }}>
              <Card
                title={selectedRule ? `编辑规则: ${selectedRule.name}` : "选择一个规则来编辑"}
                loading={loading}
              >
                {selectedRule ? (
                  <Form
                    form={editForm}
                    layout="vertical"
                    onFinish={handleUpdateRule}
                  >
                    <Form.Item
                      name="name"
                      label="规则名称"
                      rules={[{ required: true, message: '请输入规则名称' }]}
                    >
                      <Input placeholder="规则名称" />
                    </Form.Item>

                    <Form.Item
                      name="description"
                      label="规则描述"
                    >
                      <Input.TextArea placeholder="规则描述" rows={2} />
                    </Form.Item>

                    <Form.Item
                      name="priority"
                      label="规则优先级"
                      rules={[{ required: true, message: '请输入优先级' }]}
                      help="数字越大优先级越高"
                    >
                      <InputNumber placeholder="优先级" style={{ width: '100%' }} />
                    </Form.Item>

                    <Divider>规则特定配置</Divider>

                    {renderRuleForm()}

                    <Form.Item>
                      <Button type="primary" htmlType="submit" loading={loading}>
                        <SaveOutlined /> 保存规则
                      </Button>
                    </Form.Item>
                  </Form>
                ) : (
                  <Empty description="请选择要编辑的规则" />
                )}
              </Card>

              {renderPreviewComparison()}
            </div>
          </div>
        </TabPane>

        <TabPane tab="应用清洗规则" key="2">
          <Card title="批量应用清洗规则">
            <Form layout="vertical">
              <Form.Item
                label="选择项目"
                required
                tooltip="选择要应用清洗规则的项目"
              >
                <Select
                  placeholder="请选择项目"
                  style={{ width: '100%' }}
                  onChange={setSelectedProject}
                  value={selectedProject}
                >
                  {projects.map(project => (
                    <Option key={project.project_id} value={project.project_id}>
                      {project.project_name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                label="选择账户（可选）"
                tooltip="可以选择特定账户进行清洗，不选择则清洗项目下所有账户"
              >
                <Select
                  placeholder="请选择账户（可选）"
                  style={{ width: '100%' }}
                  onChange={setSelectedAccount}
                  value={selectedAccount}
                  allowClear
                  disabled={!selectedProject}
                >
                  {accounts.map(account => (
                    <Option key={account.account_id} value={account.account_id}>
                      {account.account_name || account.card_number} ({account.person_name})
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Alert
                message="清洗说明"
                description={
                  <div>
                    <p>点击【开始清洗】按钮后，系统将对选定的项目（和可选的账户）应用当前的清洗规则。</p>
                    <p>清洗过程会对原始交易数据进行处理，生成标准化的交易记录。</p>
                    <p>清洗不会修改原始数据，可以随时重新清洗。</p>
                  </div>
                }
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />

              <Space>
                <Button
                  type="primary"
                  icon={<ClearOutlined />}
                  onClick={handleApplyCleaning}
                  loading={applyLoading}
                  disabled={!selectedProject}
                >
                  开始清洗
                </Button>
                <Button
                  icon={<SyncOutlined />}
                  onClick={handlePreview}
                  loading={previewLoading}
                  disabled={!selectedProject}
                >
                  预览效果
                </Button>
              </Space>
            </Form>

            {applyResult && (
              <Alert
                message="清洗完成"
                description={`成功清洗了 ${applyResult.cleaned_count} 条交易记录`}
                type="success"
                showIcon
                style={{ marginTop: 16 }}
              />
            )}
          </Card>

          <Card title="清洗规则说明" style={{ marginTop: 16 }}>
            <Collapse>
              <Panel header="日期时间清洗规则" key="1">
                <p>将原始日期和时间字段标准化为YYYY-MM-DD HH:MM:SS格式。</p>
                <p>处理多种日期格式，如中文日期、特殊分隔符等。</p>
                <p>可以合并单独的日期和时间字段。</p>
              </Panel>
              <Panel header="金额清洗规则" key="2">
                <p>统一金额格式并确保正负号一致性。</p>
                <p>支持多种金额表示方式：</p>
                <ul>
                  <li>单列金额+借贷标志</li>
                  <li>借方/贷方分列</li>
                  <li>带有正负号的单列金额</li>
                </ul>
                <p>标准化为"负数表示支出，正数表示收入"的一致格式。</p>
              </Panel>
              <Panel header="文本清洗规则" key="3">
                <p>处理文本字段，去除特殊字符，统一格式。</p>
                <p>去除多余空格、标点符号。</p>
                <p>统一中英文符号。</p>
              </Panel>
            </Collapse>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default BankStatementsClean; 