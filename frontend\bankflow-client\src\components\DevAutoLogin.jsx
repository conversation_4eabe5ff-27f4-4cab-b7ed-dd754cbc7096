import React, { useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { validateUser } from '../utils/userDataSync';
import { message } from 'antd';
import { devConfig, isProduction } from '../config/environment';

/**
 * 开发环境自动登录组件
 * 在开发环境下自动使用测试账户登录，跳过登录页面
 */
const DevAutoLogin = ({ children }) => {
  const { login, isAuthenticated, loading } = useAuth();

  useEffect(() => {
    // 🔒 生产环境安全检查：强制禁用自动登录
    if (isProduction()) {
      console.log('🔒 生产环境：自动登录功能已禁用');
      return;
    }
    
    // 检查是否启用自动登录
    if (devConfig.autoLogin.enabled && !isAuthenticated && !loading) {
      console.log('🔧 开发环境检测到未登录状态，执行自动登录...');
      
      // 使用配置的测试账户
      const testUsername = devConfig.autoLogin.username;
      const testPassword = devConfig.autoLogin.password;
      
      try {
        // 使用统一的用户验证函数
        const userInfo = validateUser(testUsername, testPassword);
        
        if (userInfo) {
          // 设置登录状态
          localStorage.setItem('userInfo', JSON.stringify(userInfo));
          localStorage.setItem('isLoggedIn', 'true');
          localStorage.setItem('currentUser', testUsername);
          localStorage.setItem('rememberedUsername', testUsername);
          
          // 调用认证上下文的登录方法
          login(userInfo);
          
          console.log('✅ 开发环境自动登录成功:', {
            用户名: testUsername,
            登录时间: userInfo.loginTime,
            是否管理员: userInfo.isAdmin
          });
          
          message.success({
            content: `🔧 开发环境自动登录成功！用户：${testUsername}`,
            duration: 2,
            style: {
              marginTop: '20vh',
            }
          });
        } else {
          console.error('❌ 开发环境自动登录失败：用户验证失败');
        }
      } catch (error) {
        console.error('❌ 开发环境自动登录异常:', error);
      }
    }
  }, [isAuthenticated, loading, login]);

  // 渲染子组件
  return children;
};

export default DevAutoLogin; 