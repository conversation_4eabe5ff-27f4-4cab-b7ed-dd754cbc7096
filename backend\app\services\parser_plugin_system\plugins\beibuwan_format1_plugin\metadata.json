{"name": "beibuwan_format1_plugin", "version": "1.0.0", "description": "北部湾银行Format1解析器插件，支持黄宪文1.xls格式的银行流水解析", "author": "银行流水系统团队", "license": "MIT", "homepage": "https://github.com/your-org/bank-parser", "supported_formats": ["Excel (.xls)"], "supported_banks": ["北部湾银行"], "dependencies": ["pandas>=1.3.0", "openpyxl>=3.0.0", "xlrd>=2.0.0"], "entry_point": "plugin.Plugin", "confidence_threshold": 0.8, "keywords": ["北部湾银行", "银行流水", "解析器", "插件", "Format1"], "format_features": {"multi_sheet": true, "time_format": "YYYY-MM-DD HH:MM:SS", "amount_format": "negative_for_outflow", "header_info": true, "transaction_sheet": "工作表2"}, "changelog": {"1.0.0": "初始版本，支持黄宪文1.xls格式解析，包含时间拆分、负数金额处理、多表结构支持"}}