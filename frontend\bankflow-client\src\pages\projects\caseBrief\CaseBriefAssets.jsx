import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Card, Table, Button, Space, Modal, Form, Input, InputNumber, Select, message, Tag, Row, Col, Divider } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, DollarOutlined, HomeOutlined, CarOutlined, BankOutlined } from '@ant-design/icons';
import { assetAPI } from '../../../services/api';

const { Option } = Select;
const { TextArea } = Input;

/**
 * 被调查人财产信息管理组件
 * 支持自定义输入栏，灵活的财产信息录入
 */
const CaseBriefAssets = () => {
  const { projectId } = useParams();
  const [assets, setAssets] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editingId, setEditingId] = useState(null);

  // 组件挂载时获取数据
  useEffect(() => {
    if (projectId) {
      fetchAssets();
    }
  }, [projectId]);

  // 获取财产信息列表
  const fetchAssets = async () => {
    try {
      setLoading(true);
      const response = await assetAPI.getAssets(projectId);
      
      const formattedAssets = response.data.map(asset => ({
        id: asset.asset_id,
        name: asset.name,
        type: asset.type,
        owner: asset.owner,
        value: asset.value,
        status: asset.status,
        location: asset.location,
        identificationNumber: asset.identification_number,
        acquisitionDate: asset.acquisition_date,
        acquisitionMethod: asset.acquisition_method,
        customField1: asset.custom_field1,
        customField2: asset.custom_field2,
        customField3: asset.custom_field3,
        detail: asset.detail,
        notes: asset.notes,
        createTime: new Date(asset.created_at).toLocaleString('zh-CN'),
        updateTime: asset.updated_at ? new Date(asset.updated_at).toLocaleString('zh-CN') : null
      }));
      setAssets(formattedAssets);
      message.success('获取财产信息成功');
    } catch (error) {
      console.error('获取财产信息列表出错:', error);
      message.error('获取财产信息列表失败，请检查网络连接');
      setAssets([]);
    } finally {
      setLoading(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '财产名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text) => <span style={{ fontWeight: 'bold' }}>{text}</span>
    },
    {
      title: '财产类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (text) => {
        const colorMap = {
          '房产': 'red',
          '车辆': 'blue',
          '银行账户': 'green',
          '现金': 'orange',
          '股票': 'purple',
          '债券': 'cyan',
          '基金': 'magenta',
          '保险': 'lime',
          '其他': 'default'
        };
        const iconMap = {
          '房产': <HomeOutlined />,
          '车辆': <CarOutlined />,
          '银行账户': <BankOutlined />,
          '现金': <DollarOutlined />,
          '股票': '📈',
          '债券': '📊',
          '基金': '💰',
          '保险': '🛡️',
          '其他': '📋'
        };
        return (
          <Tag color={colorMap[text] || 'default'} icon={iconMap[text]}>
            {text || '未分类'}
          </Tag>
        );
      }
    },
    {
      title: '所有人',
      dataIndex: 'owner',
      key: 'owner',
      width: 120,
    },
    {
      title: '价值(元)',
      dataIndex: 'value',
      key: 'value',
      width: 150,
      render: (value) => {
        if (!value) return <Tag color="default">未评估</Tag>;
        return (
          <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
            ¥{value.toLocaleString()}
          </span>
        );
      },
      sorter: (a, b) => (a.value || 0) - (b.value || 0),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (text) => {
        const colorMap = {
          '已查封': 'red',
          '已冻结': 'orange',
          '已扣押': 'purple',
          '未处理': 'default',
          '已处置': 'green',
          '其他': 'blue'
        };
        return <Tag color={colorMap[text] || 'default'}>{text || '未知'}</Tag>;
      }
    },
    {
      title: '位置/归属',
      dataIndex: 'location',
      key: 'location',
      width: 150,
      ellipsis: true,
      render: (text) => text || '未填写'
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="text" 
            size="small"
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record)}
          />
          <Button 
            type="text" 
            size="small"
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDelete(record.id)}
          />
        </Space>
      ),
    },
  ];

  // 处理添加或编辑
  const handleAddOrEdit = () => {
    setIsModalVisible(true);
    if (editingId === null) {
      form.resetFields();
    }
  };

  // 处理编辑
  const handleEdit = (record) => {
    setEditingId(record.id);
    form.setFieldsValue(record);
    setIsModalVisible(true);
  };

  // 处理删除财产
  const handleDelete = async (id) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条财产信息吗？删除后无法恢复。',
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await assetAPI.deleteAsset(id);
          message.success('财产信息已删除');
          // 重新获取数据
          await fetchAssets();
        } catch (error) {
          console.error('删除财产信息失败:', error);
          message.error('删除失败，请重试');
        }
      },
    });
  };

  // 处理模态框确认
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      
      const assetData = {
        project_id: projectId,
        name: values.name,
        type: values.type,
        owner: values.owner,
        value: values.value || null,
        status: values.status || null,
        location: values.location || null,
        identification_number: values.identificationNumber || null,
        acquisition_date: values.acquisitionDate || null,
        acquisition_method: values.acquisitionMethod || null,
        custom_field1: values.customField1 || null,
        custom_field2: values.customField2 || null,
        custom_field3: values.customField3 || null,
        detail: values.detail || null,
        notes: values.notes || null
      };

      if (editingId === null) {
        // 新增财产
        await assetAPI.createAsset(assetData);
        message.success('财产信息已添加');
      } else {
        // 更新财产
        const updateData = { ...assetData };
        delete updateData.project_id; // 更新时不需要project_id
        
        await assetAPI.updateAsset(editingId, updateData);
        message.success('财产信息已更新');
      }

      setIsModalVisible(false);
      setEditingId(null);
      form.resetFields();
      // 重新获取数据
      await fetchAssets();
    } catch (error) {
      console.error('表单验证或提交失败:', error);
      message.error('操作失败，请重试');
    }
  };

  // 处理模态框取消
  const handleCancel = () => {
    setIsModalVisible(false);
    setEditingId(null);
    form.resetFields();
  };

  // 计算总价值
  const totalValue = assets.reduce((sum, asset) => sum + (asset.value || 0), 0);

  return (
    <div>
      <Card 
        title="财产信息管理" 
        extra={
          <Space>
            <Button 
              loading={loading}
              onClick={fetchAssets}
            >
              刷新
            </Button>
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={handleAddOrEdit}
          >
              添加财产
          </Button>
          </Space>
        }
      >
        {/* 统计信息 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={8}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                  {assets.length}
                </div>
                <div style={{ color: '#666' }}>财产总数</div>
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#52c41a' }}>
                  ¥{totalValue.toLocaleString()}
                </div>
                <div style={{ color: '#666' }}>总价值</div>
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fa8c16' }}>
                  {assets.filter(asset => asset.status && asset.status !== '未处理').length}
                </div>
                <div style={{ color: '#666' }}>已处理</div>
              </div>
            </Card>
          </Col>
        </Row>

        <Table 
          columns={columns} 
          dataSource={assets} 
          rowKey="id" 
          loading={loading}
          pagination={{ 
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      <Modal
        title={editingId === null ? "添加财产信息" : "编辑财产信息"}
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={900}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          requiredMark={false}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="财产名称"
                rules={[{ required: true, message: '请输入财产名称' }]}
              >
                <Input placeholder="请输入财产名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="财产类型"
                rules={[{ required: true, message: '请选择财产类型' }]}
              >
                <Select placeholder="请选择财产类型">
                  <Option value="房产">房产</Option>
                  <Option value="车辆">车辆</Option>
                  <Option value="银行账户">银行账户</Option>
                  <Option value="现金">现金</Option>
                  <Option value="股票">股票</Option>
                  <Option value="债券">债券</Option>
                  <Option value="基金">基金</Option>
                  <Option value="保险">保险</Option>
                  <Option value="其他">其他</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="owner"
                label="所有人"
                rules={[{ required: true, message: '请输入所有人' }]}
              >
                <Input placeholder="请输入所有人姓名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="value"
                label="价值(元)"
              >
                <InputNumber 
                  style={{ width: '100%' }} 
                  placeholder="请输入财产价值"
                  min={0}
                  formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => value.replace(/¥\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
              >
                <Select placeholder="请选择状态">
                  <Option value="未处理">未处理</Option>
                  <Option value="已查封">已查封</Option>
                  <Option value="已冻结">已冻结</Option>
                  <Option value="已扣押">已扣押</Option>
                  <Option value="已处置">已处置</Option>
                  <Option value="其他">其他</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="location"
                label="位置/归属"
              >
                <Input placeholder="请输入位置或归属信息" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
          <Form.Item
            name="identificationNumber"
                label="证件号码"
                extra="如房产证号、车牌号、账户号等"
          >
                <Input placeholder="请输入相关证件号码" />
          </Form.Item>
            </Col>
            <Col span={12}>
          <Form.Item
            name="acquisitionDate"
            label="取得时间"
          >
                <Input placeholder="请输入取得时间" />
          </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="acquisitionMethod"
            label="取得方式"
          >
            <Input placeholder="如购买、继承、赠与等" />
          </Form.Item>

          <Divider orientation="left">自定义字段</Divider>

          <Row gutter={16}>
            <Col span={8}>
          <Form.Item
            name="customField1"
            label="自定义字段1"
          >
                <Input placeholder="可自定义内容" />
          </Form.Item>
            </Col>
            <Col span={8}>
          <Form.Item
            name="customField2"
            label="自定义字段2"
          >
                <Input placeholder="可自定义内容" />
          </Form.Item>
            </Col>
            <Col span={8}>
          <Form.Item
            name="customField3"
            label="自定义字段3"
          >
                <Input placeholder="可自定义内容" />
          </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="detail"
            label="详细信息"
          >
            <TextArea 
              rows={4} 
              placeholder="请详细描述财产信息..."
              showCount
              maxLength={1000}
            />
          </Form.Item>

          <Form.Item
            name="notes"
            label="备注"
          >
            <TextArea 
              rows={3} 
              placeholder="其他需要说明的信息..."
              showCount
              maxLength={500}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CaseBriefAssets; 