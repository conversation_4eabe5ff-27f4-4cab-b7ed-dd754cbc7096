"""
兴业银行Format1解析器插件
支持xls与xlsx，基于模板1.xls与2.xlsx字段规则：
- 账户名称=持卡人姓名
- 交易时间 拆分：前半部映射 交易日期，后半部映射 交易时间
- 账户代号=账号（卡号同账号）
- 借贷标记=收支符号：1=收入，0=支出
- 对方客户名称=交易对象
- 对方金融机构名称=对方银行
- 摘要=交易方式
- 用途与备注=备注1
"""

import pandas as pd
import logging
from typing import Dict, List, Any, Tuple
from datetime import datetime
import re
import os
import time
import traceback
import json

# 修复导入路径
try:
    from ...core.plugin_interface import BasePlugin
except ImportError:
    class BasePlugin:
        def __init__(self):
            self.name = "cib_format1_plugin"
            self.version = "1.0.0"
            self.description = "兴业银行Format1解析器插件"
            self.bank_name = "兴业银行"

logger = logging.getLogger(__name__)


class Plugin(BasePlugin):
    """兴业银行格式1解析器插件"""

    def __init__(self, file_path: str = None):
        super().__init__()
        self.name = "cib_format1_plugin"
        self.version = "1.0.0"
        self.description = "兴业银行Format1解析器插件"
        self.bank_name = "兴业银行"
        self.format_type = "xls/xlsx"
        self.file_path = file_path

        self.accounts: List[Dict[str, Any]] = []
        self.transactions: List[Dict[str, Any]] = []

        self.start_time = time.time()
        self.error_count = 0
        self.last_error = None

        self.config = self._load_config()

        logger.info("兴业银行Format1解析器插件初始化完成")

    def _load_config(self) -> Dict[str, Any]:
        try:
            config_path = os.path.join(os.path.dirname(__file__), 'config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"加载配置文件失败: {e}")
        return {
            "parsing": {
                "header_rows": 5,
                "date_time_column": "交易时间",
                "data_required_columns": [
                    "序号", "账户名称", "账户代号", "交易时间", "借贷标记", "交易金额", "交易后余额"
                ],
                "date_formats": [
                    "%Y-%m-%d %H:%M:%S",
                    "%Y-%m-%d %H:%M",
                    "%Y/%m/%d %H:%M:%S",
                    "%Y/%m/%d %H:%M"
                ]
            },
            "field_mapping": {
                "holder_name_source": "账户名称",
                "account_number_source": "账户代号",
                "card_number_source": "账户代号",
                "transaction_datetime_source": "交易时间",
                "debit_credit_flag_source": "借贷标记",
                "amount_source": "交易金额",
                "balance_source": "交易后余额",
                "counterparty_name_source": "对方客户名称",
                "counterparty_bank_source": "对方金融机构名称",
                "summary_source": "摘要",
                "remark1_source": "用途与备注"
            }
        }

    def get_metadata(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "bank_name": self.bank_name,
            "supported_formats": ["兴业银行标准格式"],
            "confidence_threshold": 0.8,
            "created_at": self.start_time
        }

    def get_supported_formats(self) -> List[str]:
        return ["兴业银行标准格式", "CIB Format 1"]

    def validate_file(self, file_path: str) -> bool:
        try:
            if not os.path.exists(file_path):
                return False
            if not file_path.lower().endswith((".xls", ".xlsx")):
                return False
            df = pd.read_excel(file_path, nrows=10, header=None)
            content = df.to_string()
            # 关键字：根据实际模板特征保守判断
            keywords = ["账户名称", "账户代号", "借贷标记", "对方客户名称", "对方金融机构名称", "用途与备注"]
            found = sum(1 for k in keywords if k in content)
            return found >= 3
        except Exception:
            return False

    def calculate_confidence(self, file_path: str) -> float:
        try:
            if not self.validate_file(file_path):
                return 0.0
            df = pd.read_excel(file_path, nrows=30, header=None)
            content = df.to_string()
            score = 0.0
            if "兴业银行" in content:
                score += 0.4
            for k in ["账户名称", "账户代号", "借贷标记", "交易时间", "对方客户名称"]:
                if k in content:
                    score += 0.1
            return min(score, 1.0)
        except Exception:
            return 0.0

    def parse(self, file_path: str) -> Dict[str, Any]:
        try:
            self.file_path = file_path
            self.accounts = []
            self.transactions = []

            logger.info(f"开始解析兴业银行文件: {file_path}")

            # 读取列名行索引：尝试自动定位含列标题的行
            header_row = self._find_header_row(file_path)

            df = pd.read_excel(file_path, header=header_row, dtype=str)
            df.columns = df.columns.astype(str).str.strip()

            # 必需列校验，若存在同义列名，做兼容映射
            df = self._normalize_columns(df)

            # 逐行解析
            serial_counter = 0
            for _, row in df.iterrows():
                # 跳过明显空行
                if all((str(row.get(c, '')).strip() == '' or str(row.get(c, '')).lower() == 'nan') for c in df.columns):
                    continue

                serial_counter += 1

                holder_name = str(row.get(self.config['field_mapping']['holder_name_source'], '')).strip()
                account_number = str(row.get(self.config['field_mapping']['account_number_source'], '')).strip()
                account_number = self._format_account_number(account_number)

                dt_str = str(row.get(self.config['field_mapping']['transaction_datetime_source'], '')).strip()
                t_date, t_time = self._split_datetime(dt_str)

                raw_amount_str = str(row.get(self.config['field_mapping']['amount_source'], '')).strip()
                amount = self._parse_amount(raw_amount_str)
                balance = self._parse_amount(str(row.get(self.config['field_mapping']['balance_source'], '')).strip())

                # 先读取摘要与备注，供收支符号推断使用
                summary = str(row.get(self.config['field_mapping']['summary_source'], '')).strip()
                remark1 = str(row.get(self.config['field_mapping']['remark1_source'], '')).strip()

                flag = str(row.get(self.config['field_mapping']['debit_credit_flag_source'], '')).strip()
                income_expense = self._parse_income_expense(
                    flag,
                    raw_amount_str,
                    amount,
                    summary
                )
                if income_expense == "收入" and amount < 0:
                    amount = abs(amount)
                elif income_expense == "支出" and amount > 0:
                    amount = abs(amount)

                counterpart_name = str(row.get(self.config['field_mapping']['counterparty_name_source'], '')).strip()
                # 兼容“对方账号/对方账户/对方卡号”等同义列
                counterparty_account = str(row.get(self.config['field_mapping'].get('counterparty_account_source', '对方账号'), '')).strip()
                counterparty_bank = str(row.get(self.config['field_mapping']['counterparty_bank_source'], '')).strip()

                # 统一前端判色需要：dr_cr_flag 用“收/支”
                dr_flag = "收" if income_expense == "收入" else ("支" if income_expense == "支出" else "未知")

                transaction = {
                    "序号": str(serial_counter),
                    "持卡人姓名": holder_name,
                    "银行名称": self.bank_name,
                    "账号": account_number,
                    "卡号": account_number,
                    "交易日期": t_date,
                    "交易时间": t_time,
                    "交易方式": summary,
                    "交易金额": amount,
                    "交易余额": balance,
                    "收支符号": income_expense,
                    "交易对象": counterpart_name,
                    "对方户名": counterpart_name,
                    "对方账号": counterparty_account,
                    "对方银行": counterparty_bank,
                    "备注1": remark1,
                    "备注2": "",
                    # 兼容后端内部字段
                    "serial_number": str(serial_counter),
                    "cardholder_name": holder_name,
                    "holder_name": holder_name,
                    "bank_name": self.bank_name,
                    "account_number": account_number,
                    "card_number": account_number,
                    "transaction_date": t_date,
                    "transaction_time": t_time,
                    "transaction_type": summary,
                    "income_expense": income_expense,
                    "amount": amount,
                    "balance": balance,
                    # 兼容两种命名：counterpart_* 与 counterparty_*
                    "counterpart_name": counterpart_name,
                    "counterparty_name": counterpart_name,
                    "counterparty_account": counterparty_account,
                    "counterparty_account_name": counterpart_name,
                    "counterparty_bank": counterparty_bank,
                    "remark": summary,
                    "transaction_amount": amount,
                    "balance_amount": balance,
                    # 前端采用该字段做颜色判定
                    "dr_cr_flag": dr_flag,
                    "sequence_number": str(serial_counter),
                    "transaction_method": summary,
                    "remark1": remark1,
                    "remark2": ""
                }

                self.transactions.append(transaction)

            self._create_accounts()

            stats = self._calculate_statistics()
            return {
                "success": True,
                "message": f"成功解析兴业银行文件，共{len(self.accounts)}个账户，{len(self.transactions)}条交易记录",
                "accounts": self.accounts,
                "transactions": self.transactions,
                "statistics": stats,
                "metadata": {
                    "parser_name": self.name,
                    "parser_version": self.version,
                    "bank_name": self.bank_name,
                    "file_path": file_path,
                    "parse_time": datetime.now().isoformat()
                }
            }

        except Exception as e:
            self.error_count += 1
            self.last_error = str(e)
            logger.error(f"兴业银行文件解析失败: {e}\n{traceback.format_exc()}")
            return {
                "success": False,
                "message": f"兴业银行文件解析失败: {str(e)}",
                "accounts": [],
                "transactions": [],
                "error": str(e)
            }

    def _find_header_row(self, file_path: str) -> int:
        try:
            peek = pd.read_excel(file_path, nrows=15, header=None)
            for i in range(len(peek)):
                row_text = ' '.join(peek.iloc[i].fillna('').astype(str).values)
                if "交易时间" in row_text and ("借贷标记" in row_text or "借贷标志" in row_text):
                    return i
            return 0
        except Exception:
            return 0

    def _normalize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        mapping_variants = {
            "账户名称": ["账户名称", "户名", "账户名"],
            "账户代号": ["账户代号", "账号", "主账户账号"],
            "交易时间": ["交易时间", "时间"],
            "借贷标记": ["借贷标记", "借贷标志", "借贷标识"],
            "交易金额": ["交易金额", "金额"],
            "交易后余额": ["交易后余额", "账户余额", "余额"],
            "对方客户名称": ["对方客户名称", "交易对象", "对方户名", "收/付方名称"],
            # 新增：对方账号同义识别
            "对方账号": ["对方账号", "对方账户", "对方卡号", "收/付方账号", "对方账号/卡号"],
            "对方金融机构名称": ["对方金融机构名称", "对方银行", "对方账户开户行名"],
            "摘要": ["摘要", "交易摘要", "交易类型描述"],
            "用途与备注": ["用途与备注", "附言", "备注", "用途"]
        }
        rename_map = {}
        for std, variants in mapping_variants.items():
            for v in variants:
                if v in df.columns:
                    rename_map[v] = std
                    break
        if rename_map:
            df = df.rename(columns=rename_map)
        return df

    def _split_datetime(self, dt: str) -> Tuple[str, str]:
        if not dt or dt.lower() == 'nan':
            return "", ""
        s = str(dt).strip()
        # 优先匹配“YYYY-MM-DD HH:MM[:SS]”或“YYYY/MM/DD HH:MM[:SS]”
        m = re.match(r"(\d{4}[/-]\d{1,2}[/-]\d{1,2})\s+(\d{1,2}:\d{1,2}(?::\d{1,2})?)", s)
        if m:
            date_part = m.group(1).replace('/', '-')
            time_part = m.group(2)
            if len(time_part.split(':')) == 2:
                time_part += ":00"
            return date_part, time_part
        # 退化处理：仅日期或仅时间
        try:
            for fmt in self.config["parsing"]["date_formats"]:
                try:
                    dtp = datetime.strptime(s, fmt)
                    return dtp.strftime("%Y-%m-%d"), dtp.strftime("%H:%M:%S")
                except ValueError:
                    continue
        except Exception:
            pass
        return s, ""

    def _parse_amount(self, val: str) -> float:
        try:
            if val is None:
                return 0.0
            t = str(val).strip()
            if not t or t.lower() == 'nan':
                return 0.0
            t = re.sub(r'[￥¥,\s]', '', t)
            negative = False
            if t.startswith('-'):
                negative = True
                t = t[1:]
            elif t.startswith('+'):
                t = t[1:]
            num = float(t)
            return -num if negative else num
        except Exception:
            return 0.0

    def _parse_income_expense(self, flag: str, raw_amount_str: str = "", numeric_amount: float = 0.0, transaction_type: str = "") -> str:
        f = (flag or "").strip()
        f_lower = f.lower()
        # 兼容数值类 '1.0' / '0.0'
        try:
            if f and re.fullmatch(r"[-+]?\d+(?:\.\d+)?", f):
                fv = float(f)
                if abs(fv - 1.0) < 1e-6:
                    return "收入"
                if abs(fv - 0.0) < 1e-6:
                    return "支出"
        except Exception:
            pass
        income_aliases = {
            '1', '收', '入', '收入', '贷', 'c', 'credit', '贷方', '贷记', '贷入', '贷方发生额', '进', '+', '存', '转入'
        }
        expense_aliases = {
            '0', '支', '付', '出', '支出', '借', 'd', 'debit', '借方', '借记', '借出', '借方发生额', '转出', '-', '取'
        }
        if f in {'1', '0'}:
            return "收入" if f == '1' else "支出"
        if f_lower in income_aliases:
            return "收入"
        if f_lower in expense_aliases:
            return "支出"
        # 依据交易方式/摘要关键词判断
        t = (transaction_type or "").strip()
        income_kw = [
            '代发', '汇入', '转入', '退款', '存款利息', '工资', '来账', '入账', '转存', '回款'
        ]
        expense_kw = [
            '快捷支付', '取款', '消费', '汇出', '扣款', '缴费', '还款', '提现', '转出', '网上汇款'
        ]
        if t:
            if any(k in t for k in income_kw):
                return "收入"
            if any(k in t for k in expense_kw):
                return "支出"
        # 兜底：根据原始金额文本符号
        s = (raw_amount_str or "").strip()
        if s.startswith('-') or s.startswith('－'):
            return "支出"
        if s.startswith('+'):
            return "收入"
        # 再兜底：根据数值正负
        try:
            if float(numeric_amount) < 0:
                return "支出"
            if float(numeric_amount) > 0:
                return "收入"
        except Exception:
            pass
        return "未知"

    def _format_account_number(self, account_number: str) -> str:
        try:
            s = str(account_number)
            if 'e' in s.lower():
                return str(int(float(s)))
            return s.split('.')[0]
        except Exception:
            return str(account_number)

    def _create_accounts(self):
        account_numbers = {}
        for t in self.transactions:
            acc = t.get("account_number") or t.get("账号")
            holder = t.get("holder_name") or t.get("持卡人姓名") or "未知"
            if not acc:
                continue
            acc = self._format_account_number(acc)
            stats = account_numbers.setdefault(acc, {
                "holder_name": holder,
                "transactions": [],
                "total_inflow": 0.0,
                "total_outflow": 0.0
            })
            stats["transactions"].append(t)
            if t.get("income_expense") == "收入" and t.get("amount", 0) > 0:
                stats["total_inflow"] += t.get("amount", 0)
            if t.get("income_expense") == "支出":
                stats["total_outflow"] += abs(t.get("amount", 0))

        for acc, stats in account_numbers.items():
            txs = sorted(stats["transactions"], key=lambda x: (x.get("transaction_date", ""), x.get("transaction_time", "")))
            balance = txs[-1].get("balance", 0.0) if txs else 0.0
            dates = [t.get("transaction_date") for t in txs if t.get("transaction_date")]
            if dates:
                date_range = f"{min(dates)} 至 {max(dates)}" if min(dates) != max(dates) else min(dates)
            else:
                date_range = "未知"
            self.accounts.append({
                "holder_name": stats["holder_name"],
                "cardholder_name": stats["holder_name"],
                "account_number": acc,
                "card_number": acc,
                "bank_name": self.bank_name,
                "account_type": "个人账户",
                "currency": "人民币",
                "transactions_count": len(txs),
                "total_inflow": stats["total_inflow"],
                "total_outflow": stats["total_outflow"],
                "account_balance": balance,
                "date_range": date_range,
                "transactions": txs
            })

    def _calculate_statistics(self) -> Dict[str, Any]:
        if not self.transactions:
            return {
                "total_inflow": 0.0,
                "total_outflow": 0.0,
                "net_flow": 0.0,
                "transactions_count": 0,
                "date_range": "无数据"
            }
        total_inflow = sum(t["amount"] for t in self.transactions if t.get("income_expense") == "收入")
        total_outflow = sum(abs(t["amount"]) for t in self.transactions if t.get("income_expense") == "支出")
        dates = [t.get("transaction_date") for t in self.transactions if t.get("transaction_date")]
        date_range = f"{min(dates)} 至 {max(dates)}" if dates else "无数据"
        return {
            "total_inflow": total_inflow,
            "total_outflow": total_outflow,
            "net_flow": total_inflow - total_outflow,
            "transactions_count": len(self.transactions),
            "date_range": date_range
        }

    def get_health_status(self) -> Dict[str, Any]:
        return {
            "healthy": self.error_count < 5,
            "last_check": time.time(),
            "memory_usage": "正常",
            "error_count": self.error_count,
            "last_error": self.last_error,
            "uptime": time.time() - self.start_time
        }




    def extract_sample(self, file_path: str = None, limit: int = 10) -> Dict[str, Any]:
        """
        提取样本数据用于快速置信度评估 - 兴业银行Format1精准识别版本
        参考工商银行成功模式：数据特征识别 + 固定列位置 + 严格验证
        
        Args:
            file_path: 文件路径
            limit: 样本数量限制
            
        Returns:
            Dict: 包含样本账户和交易的字典
        """
        try:
            target_file = file_path or self.file_path
            if not target_file:
                raise ValueError("未设置文件路径")
                
            logger.info(f"🏦 兴业银行Format1样本提取开始: {target_file}")
            
            # 1. 数据特征识别：使用实际解析器的表头查找逻辑
            header_row = self._find_header_row(target_file)
            if header_row == -1:
                raise ValueError("未找到有效的表头行")
            
            # 2. 固定列位置：读取样本数据，使用实际解析器的逻辑
            df = pd.read_excel(target_file, header=header_row, nrows=limit + 5)
            
            if df.empty:
                raise ValueError("数据表为空")
            
            sample_accounts = []
            sample_transactions = []
            
            # 3. 严格验证：提取账户信息，使用实际解析器的字段映射
            # 从第一行数据中提取账户信息
            if len(df) > 0:
                first_row = df.iloc[0]
                
                # 使用实际解析器的字段识别逻辑
                holder_name = "未知持卡人"
                account_number = "Unknown"
                
                # 姓名识别：查找可能的姓名字段
                for col in df.columns:
                    col_str = str(col).strip()
                    if any(keyword in col_str for keyword in ['持卡人', '户名', '姓名', '客户']):
                        value = str(first_row.get(col, "")).strip()
                        if value and value != "nan" and len(value) >= 2 and len(value) <= 10:
                            # 验证是否为有效姓名（2-10个字符，包含中文）
                            if any('\u4e00' <= char <= '\u9fff' for char in value):
                                holder_name = value
                                break
                
                # 账号识别：查找可能的账号字段
                for col in df.columns:
                    col_str = str(col).strip()
                    if any(keyword in col_str for keyword in ['账号', '账户', '卡号']):
                        value = str(first_row.get(col, "")).strip()
                        if value and value != "nan" and len(value) >= 10:
                            # 验证是否为有效账号（至少10位数字或字母数字组合）
                            if value.replace(' ', '').replace('-', '').isalnum():
                                account_number = value
                                break
                
                account = {
                    "person_name": holder_name,
                    "bank_name": "兴业银行",
                    "account_number": account_number,
                    "card_number": account_number,
                    "account_name": f"{holder_name}的账户"
                }
                sample_accounts.append(account)
            
            # 4. 提取交易数据：使用实际解析器的字段映射逻辑
            for idx, row in df.iterrows():
                if len(sample_transactions) >= limit:
                    break
                    
                try:
                    # 提取必要字段
                    transaction_date = ""
                    transaction_amount = 0.0
                    balance = 0.0
                    dr_cr_flag = ""
                    summary = ""
                    
                    # 字段映射（基于列名匹配）
                    for col_name in df.columns:
                        col_str = str(col_name).strip()
                        cell_value = row.get(col_name, "")
                        
                        if pd.notna(cell_value):
                            # 交易日期
                            if any(keyword in col_str for keyword in ['交易日期', '日期', '记账日期']):
                                date_str = str(cell_value).strip()
                                if date_str and date_str != "nan":
                                    # 标准化日期格式
                                    try:
                                        if '-' in date_str or '/' in date_str:
                                            transaction_date = date_str[:10]  # 取前10位
                                        else:
                                            # 处理YYYYMMDD格式
                                            date_clean = date_str.replace(' ', '')[:8]
                                            if len(date_clean) == 8 and date_clean.isdigit():
                                                transaction_date = f"{date_clean[:4]}-{date_clean[4:6]}-{date_clean[6:8]}"
                                    except:
                                        pass
                            
                            # 交易金额
                            elif any(keyword in col_str for keyword in ['交易金额', '金额', '发生额']):
                                try:
                                    amount_val = float(str(cell_value).replace(',', '').replace('￥', ''))
                                    if amount_val != 0:
                                        transaction_amount = abs(amount_val)
                                        dr_cr_flag = "收" if amount_val > 0 else "支"
                                except:
                                    pass
                            
                            # 账户余额
                            elif any(keyword in col_str for keyword in ['余额', '账户余额', '结余']):
                                try:
                                    balance = float(str(cell_value).replace(',', '').replace('￥', ''))
                                except:
                                    pass
                            
                            # 交易摘要
                            elif any(keyword in col_str for keyword in ['摘要', '用途', '备注', '说明']):
                                summary = str(cell_value).strip()
                    
                    # 数据验证：确保4维度数据完整性
                    name_check = sample_accounts[0]["person_name"] if sample_accounts else "未知持卡人"
                    account_check = sample_accounts[0]["account_number"] if sample_accounts else "Unknown"
                    
                    # 验证必要字段完整性
                    if (name_check != "未知持卡人" and account_check != "Unknown" and 
                        transaction_date and transaction_amount > 0):
                        
                        transaction = {
                            'cardholder_name': name_check,  # 🔧 4维度姓名识别需要
                            'account_number': account_check,
                            'transaction_date': transaction_date,  # 🔧 4维度时间格式需要
                            'transaction_amount': transaction_amount,
                            'balance': balance,  # 🔧 4维度金额解析需要
                            'dr_cr_flag': dr_cr_flag,
                            'card_number': account_check,
                            'currency': 'CNY',
                            'transaction_method': summary or '兴业银行交易',
                            'bank_name': '兴业银行',
                            'summary': summary
                        }
                        sample_transactions.append(transaction)
                        
                except Exception as e:
                    logger.debug(f"跳过第{idx+1}行: {str(e)}")
                    continue
            
            logger.info(f"兴业银行Format1样本提取完成: {len(sample_accounts)}个账户, {len(sample_transactions)}条交易")
            
            return {
                'accounts': sample_accounts,
                'transactions': sample_transactions,
                'success': True,
                'metadata': {
                    'sample_size': len(sample_transactions),
                    'plugin_name': self.name,
                    'parser_version': 'v3.0-feature_recognition'
                }
            }
            
        except Exception as e:
            logger.error(f"兴业银行Format1样本提取失败: {str(e)}")
            return {
                'accounts': [],
                'transactions': [],
                'success': False,
                'error': str(e),
                'metadata': {
                    'sample_size': 0,
                    'plugin_name': self.name,
                    'error_details': str(e)
                }
            }
