"""
自动解析器选择系统控制器 - 插件系统版本
协调插件系统解析和结果验证的整体流程
"""
import os
import logging
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session

from .cleaner.data_cleaner import DataCleaner
from .parser_service import ParserService

logger = logging.getLogger(__name__)

class AutoParserController:
    """
    自动解析控制器 - 使用插件系统进行解析
    """
    
    def __init__(self):
        """初始化控制器"""
        self.parser_service = ParserService()
    
    def auto_parse(self, file_path: str, bank_name: str = None, db: Session = None) -> Dict[str, Any]:
        """
        自动解析银行流水文件
        
        Args:
            file_path: 文件路径
            bank_name: 银行名称，如果为None则尝试自动检测
            db: 数据库会话
            
        Returns:
            Dict[str, Any]: 解析结果和相关信息
        """
        result = {
            "file_path": file_path,
            "bank_name": bank_name,
            "auto_detected_bank": None,
            "selected_parser": None,
            "parsing_successful": False,
            "parsing_result": None,
            "validation_result": None,
            "consistency_check": None,
            "errors": [],
            "warnings": []
        }
        
        try:
            # 步骤1：使用插件系统进行解析
            logger.info(f"使用插件系统解析文件: {file_path}")
            if bank_name:
                logger.info(f"指定银行: {bank_name}")
            
            parsing_result = self.parser_service.parse_file(file_path, db)
            
            if not parsing_result:
                error_msg = "解析失败，插件系统未能提取数据"
                logger.error(error_msg)
                result["errors"].append(error_msg)
                return result
            
            result["parsing_result"] = parsing_result
            result["parsing_successful"] = True
            result["selected_parser"] = parsing_result.get('metadata', {}).get('plugin_name', 'unknown')
            
            # 步骤2：基本验证（简化版）
            logger.info("验证解析结果")
            accounts = parsing_result.get('accounts', [])
            transactions = parsing_result.get('transactions', [])
            
            validation_result = {
                "is_valid": len(accounts) > 0 and len(transactions) > 0,
                "errors": [],
                "warnings": []
            }
            
            if not accounts:
                validation_result["errors"].append("未找到账户信息")
            if not transactions:
                validation_result["errors"].append("未找到交易记录")
            
            result["validation_result"] = validation_result
            
            if not validation_result["is_valid"]:
                result["warnings"].append("解析结果验证未通过，数据可能不完整")
                result["errors"].extend(validation_result["errors"])
            
            # 简化的一致性检查
            consistency_check = {
                "is_consistent": True,
                "issues": []
            }
            result["consistency_check"] = consistency_check
            
            return result
        except Exception as e:
            logger.exception(f"自动解析过程出错: {str(e)}")
            result["errors"].append(f"解析过程出错: {str(e)}")
            return result
    
    def register_parser_feedback(self, file_path: str, bank_name: str, 
                               parser_class: str, success: bool, feedback: str = None,
                               db: Session = None) -> bool:
        """
        注册解析器反馈，用于改进解析器选择
        
        Args:
            file_path: 文件路径
            bank_name: 银行名称
            parser_class: 解析器类名（插件名称）
            success: 解析是否成功
            feedback: 反馈信息
            db: 数据库会话
            
        Returns:
            bool: 是否成功注册反馈
        """
        try:
            # 简化的反馈记录（插件系统版本）
            logger.info(f"解析器反馈: 文件={os.path.basename(file_path)}, 银行={bank_name}, " +
                       f"插件={parser_class}, 成功={success}, 反馈={feedback}")
            
            # 未来可以扩展为将反馈信息发送给插件管理器
            return True
        except Exception as e:
            logger.error(f"注册解析器反馈时出错: {str(e)}")
            return False 