@echo off
chcp 65001 > nul 2>&1
echo ========================================
echo 银行流水分析系统 - 生产环境构建脚本
echo ========================================

echo [1/5] 环境检查...
node --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not installed
    echo 错误：Node.js 未安装
    pause
    exit /b 1
)

npm --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm not available  
    echo 错误：npm 不可用
    pause
    exit /b 1
)

echo [2/5] 清理开发环境残留...
cd frontend/bankflow-client
if exist "build" (
    echo 删除旧的构建文件...
    rmdir /s /q build
)

echo [3/5] 安装生产环境依赖...
npm ci --only=production

echo [4/5] 设置生产环境变量...
set NODE_ENV=production
set REACT_APP_AUTO_LOGIN=false
set REACT_APP_SHOW_DEV_TOOLS=false
set REACT_APP_DEBUG_MODE=false
set GENERATE_SOURCEMAP=false
set INLINE_RUNTIME_CHUNK=false

echo [5/5] 构建生产版本...
npm run build

if %errorlevel% equ 0 (
    echo ========================================
    echo 🎉 生产环境构建成功！
    echo 📁 构建文件位置: frontend/bankflow-client/build/
    echo 🔒 所有开发功能已禁用
    echo ========================================
    echo 生产环境特性:
    echo - ✅ 自动登录功能已禁用
    echo - ✅ 开发工具条已禁用  
    echo - ✅ 调试模式已禁用
    echo - ✅ 源码映射已禁用
    echo - ✅ 代码已压缩优化
    echo ========================================
) else (
    echo ERROR: 构建失败
    echo 错误：生产环境构建失败
    pause
    exit /b 1
)

echo 按任意键退出...
pause > nul 