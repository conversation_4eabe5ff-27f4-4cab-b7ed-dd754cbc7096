# 4维度评分系统保护机制

## ⚠️ 重要警告
**禁止删除、修改或重构以下文件，除非有明确的用户授权！**

## 核心保护文件
1. `backend/app/services/parser/confidence_evaluator_v2.py` - 4维度评估核心
2. `frontend/.../BankStatementsImport.jsx` 中的4维度显示逻辑
3. `backend/app/api/parser.py` 中的4维度评估调用

## 数据结构规范
**后端输出格式（ConfidenceEvaluatorV2）：**
```json
{
  "details": {
    "cardholder_recognition": {"score": 25, "percentage": 25},
    "time_format_accuracy": {"score": 25, "percentage": 25}, 
    "account_recognition": {"score": 25, "percentage": 25},
    "amount_parsing": {"score": 25, "percentage": 25}
  }
}
```

**前端期望格式：**
```javascript
fourDMetrics: {
  cardholder_name_score: {percentage: 25},
  time_format_score: {percentage: 25},
  account_number_score: {percentage: 25},
  amount_parsing_score: {percentage: 25}
}
```

## 修改规则
1. 修改4维度评估系统前必须先备份
2. 任何结构性变更都要同时更新前后端
3. 修改后必须进行端到端测试验证
4. 禁止在未授权情况下删除ConfidenceEvaluator相关代码

## 历史问题
- 曾多次因为重构而误删4维度显示
- 数据结构不匹配导致前端显示失效
- import路径错误导致系统崩溃

## 测试验证清单
- [ ] 4维度评分是否正常显示
- [ ] 每个维度是否为0或25分的二元评分
- [ ] 总分是否为4个维度分数之和
- [ ] 前端模板选择界面是否显示4维度详情

---
**最后更新：2025-01-27**
**创建原因：防止4维度评分系统再次被误删或破坏**


