"""
交通银行Format1解析器插件
支持交通银行标准Excel格式的银行流水解析
特殊处理：1.借贷标志识别(借=支出,贷=收入) 2.特殊时间格式(6位数/5位数) 3.业务摘要映射
"""

import pandas as pd
import time
import os
import traceback
from datetime import datetime
from typing import Dict, Any, List, Optional
import logging
import json
from pathlib import Path
import re

# 导入基础插件接口
try:
    from ...core.plugin_interface import BasePlugin
except ImportError:
    # 如果在测试环境中，创建一个简单的基础类
    class BasePlugin:
        def __init__(self):
            self.name = "default_plugin"
            self.version = "1.0.0"
            self.description = "默认解析器插件"
            self.bank_name = "通用银行"
            self.format_type = "standard"

logger = logging.getLogger(__name__)


class Plugin(BasePlugin):
    """交通银行Format1解析器插件"""
    
    def __init__(self, file_path: str = None):
        super().__init__()
        self.name = "bocm_format1_plugin"
        self.version = "1.0.0"
        self.description = "交通银行Format1解析器插件"
        self.bank_name = "交通银行"
        self.format_type = "format1"
        self.start_time = time.time()
        self.error_count = 0
        self.file_path = file_path
        
        # 加载配置
        self.config = self._load_config()
        
        # 解析结果
        self.accounts = []
        self.transactions = []
        
        # 交通银行特定的字段映射（基于实际分析的Excel列结构）
        self.field_mapping = {
            'account_field': '账号',                    # A列
            'record_account_field': '记账账号',         # B列
            'account_name_field': '户名',               # C列
            'currency_field': '币种',                   # D列
            'amount_field': '交易金额',                 # E列
            'balance_field': '余额',                    # F列
            'direction_field': '借贷标志',              # G列 - 关键：D-借/C-贷
            'account_date_field': '会计日期',           # H列
            'transaction_date_field': '交易日期',       # I列
            'transaction_time_field': '交易时间',       # J列 - 关键：特殊格式
            'network_field': '交易网点',                # K列
            'serial_number_field': '会计流水号',        # L列
            'opposite_bank_flag_field': '对方行标志',   # M列
            'opposite_account_field': '对方账号',       # N列
            'opposite_name_field': '对方户名',          # O列
            'opposite_bank_number_field': '对方行号',   # P列
            'opposite_bank_name_field': '对方行名',     # Q列
            'business_code_name_field': '业务摘要码名称', # R列 - 交易方式
            'business_summary_field': '业务摘要'        # S列 - 备注1
        }
        
        logger.info(f"✅ {self.name} v{self.version} 初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载插件配置"""
        try:
            config_path = Path(__file__).parent / "config.json"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"加载配置失败: {e}")
        
        # 默认配置
        return {
            "enabled": True,
            "confidence_threshold": 0.7,
            "plugin_settings": {
                "max_file_size": "100MB",
                "supported_extensions": [".xlsx", ".xls"]
            }
        }
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取插件元信息"""
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "supported_formats": ["交通银行Excel格式"],
            "confidence_threshold": self.config.get("confidence_threshold", 0.7),
            "bank_name": self.bank_name,
            "format_type": self.format_type
        }
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的格式列表"""
        return ["交通银行Excel格式", "BOCM_Format1"]
    
    def validate_file(self, file_path: str) -> bool:
        """验证文件是否适用于此解析器"""
        try:
            if not os.path.exists(file_path):
                return False
            
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in ['.xlsx', '.xls']:
                return False
            
            # 尝试读取文件并检查是否包含交通银行特征
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            
            # 检查工作表内容是否包含交通银行特征
            for sheet_name in sheet_names:
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=20, dtype={'账号': str})
                    if df.empty:
                        continue
                    
                    # 转换为字符串进行特征检测
                    content = df.to_string()
                    
                    # 交通银行特征关键词
                    bocm_keywords = ["交通银行", "借贷标志", "业务摘要码名称", "对方行标志", "会计流水号"]
                    matches = sum(1 for keyword in bocm_keywords if keyword in content)
                    
                    if matches >= 3:
                        logger.info(f"发现交通银行特征关键词匹配: {matches}/5")
                        return True
                        
                except Exception:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"文件验证失败: {e}")
            return False
    
    def calculate_confidence(self, file_path: str) -> float:
        """计算解析置信度"""
        try:
            if not self.validate_file(file_path):
                return 0.0
            
            confidence = 0.0
            
            # 文件扩展名检查
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext in ['.xlsx', '.xls']:
                confidence += 20.0
            
            # 内容特征检查
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            
            max_sheet_confidence = 0.0
            
            for sheet_name in sheet_names:
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=50, dtype={'账号': str})
                    if df.empty:
                        continue
                    
                    sheet_confidence = 0.0
                    content = df.to_string()
                    
                    # 交通银行特征词汇
                    if "交通银行" in content:
                        sheet_confidence += 25.0
                    
                    # 必要字段检查 - 交通银行特有字段
                    required_fields = ["借贷标志", "业务摘要码名称", "交易时间"]
                    field_matches = sum(1 for field in required_fields if field in content)
                    sheet_confidence += (field_matches / len(required_fields)) * 30.0
                    
                    # 可选字段检查
                    optional_fields = ["对方行标志", "会计流水号", "对方户名", "对方行名"]
                    optional_matches = sum(1 for field in optional_fields if field in content)
                    sheet_confidence += (optional_matches / len(optional_fields)) * 15.0
                    
                    # 借贷标志格式检查（交通银行特有的"D - 借"/"C - 贷"格式）
                    if "D - 借" in content or "C - 贷" in content:
                        sheet_confidence += 10.0
                    
                    max_sheet_confidence = max(max_sheet_confidence, sheet_confidence)
                    
                except Exception:
                    continue
            
            confidence += max_sheet_confidence
            
            logger.info(f"交通银行解析器置信度: {confidence:.1f}%")
            return min(confidence, 100.0)
            
        except Exception as e:
            logger.error(f"置信度计算失败: {e}")
            return 0.0    
    def _clean_field(self, value) -> str:
        """清理字段值"""
        if value is None or pd.isna(value):
            return ""
        return str(value).strip().replace('\t', '').replace('\n', '').replace('\r', '')
    
    def _clean_amount_string(self, value) -> str:
        """清理金额字符串"""
        if value is None:
            return ""
        
        clean_value = str(value).replace(" ", "").replace(",", "").replace("，", "").replace("\t", "").replace("\n", "").replace("\r", "").strip()
        clean_value = clean_value.replace("¥", "").replace("$", "").replace("€", "").replace("￥", "")
        
        return clean_value
    
    def _clean_card_number(self, card_number: Any) -> str:
        """清理卡号，将无效卡号设为空字符串"""
        if card_number is None:
            return ""
        
        card_str = str(card_number).strip()
        invalid_patterns = ["****************00", "****************", "", "nan", "None", "NaN", "null", "NULL"]
        
        # 检查是否为无效卡号
        if card_str.lower() in [p.lower() for p in invalid_patterns] or card_str.startswith('0000'):
            return ""
        return card_str
    
    def _parse_amount(self, amount_str: str, direction_flag: str = "") -> float:
        """解析金额 - 交通银行特殊处理：根据借贷标志确定正负"""
        if not amount_str:
            return 0.0

        clean_amount = self._clean_amount_string(amount_str)

        try:
            amount = float(clean_amount)

            # 交通银行借贷标志处理：借=支出（负数），贷=收入（正数）
            if "借" in direction_flag:  # D - 借 = 支出
                return -abs(amount)
            elif "贷" in direction_flag:  # C - 贷 = 收入
                return abs(amount)
            else:
                # 如果没有明确标志，根据金额本身的正负判断
                return amount
        except (ValueError, TypeError):
            logger.warning(f"无法解析金额: {amount_str}")
            return 0.0

    def _parse_balance(self, balance_str: str) -> float:
        """专门解析余额 - 余额不受借贷标志影响，始终为正值"""
        if not balance_str:
            return 0.0

        # 清理余额字符串
        clean_balance = self._clean_amount_string(balance_str)

        try:
            balance = float(clean_balance)
            # 余额始终为绝对值
            return abs(balance)
        except (ValueError, TypeError):
            logger.warning(f"🔧 无法解析余额: {balance_str}")
            return 0.0
    
    def _convert_bocm_time_format(self, time_str: str) -> str:
        """
        转换交通银行特殊时间格式
        特殊规则：
        1. 6位数：222238 = 22:22:38
        2. 5位数：81944 = 08:19:44 (早上0-9点用单数字表示小时)
        """
        if not time_str:
            return "00:00:00"
        
        time_clean = self._clean_field(time_str)
        
        try:
            # 如果已经是标准格式，直接返回
            if ':' in time_clean:
                return time_clean
            
            # 处理纯数字格式
            if time_clean.isdigit():
                if len(time_clean) == 6:
                    # 6位数格式：222238 = 22:22:38
                    hour = time_clean[:2]
                    minute = time_clean[2:4]
                    second = time_clean[4:6]
                    return f"{hour}:{minute}:{second}"
                elif len(time_clean) == 5:
                    # 5位数格式：81944 = 08:19:44 (早上时间)
                    hour = time_clean[0].zfill(2)  # 单数字小时补零
                    minute = time_clean[1:3]
                    second = time_clean[3:5]
                    return f"{hour}:{minute}:{second}"
                elif len(time_clean) == 4:
                    # 4位数格式：1234 = 12:34:00
                    hour = time_clean[:2]
                    minute = time_clean[2:4]
                    return f"{hour}:{minute}:00"
            
            # 如果无法解析，返回默认值
            logger.warning(f"无法解析时间格式: {time_str}")
            return "00:00:00"
            
        except Exception as e:
            logger.warning(f"时间格式转换失败: {time_str}, 错误: {str(e)}")
            return "00:00:00"
    
    def _standardize_time_format(self, date_str: str, time_str: str = "") -> str:
        """标准化时间格式 - 交通银行特殊处理"""
        if not date_str:
            return ""
        
        try:
            date_clean = self._clean_field(date_str)
            time_clean = self._clean_field(time_str) if time_str else ""
            
            # 处理日期部分
            if date_clean:
                # 尝试多种日期格式
                for date_format in ['%Y%m%d', '%Y-%m-%d', '%Y/%m/%d']:
                    try:
                        date_obj = datetime.strptime(date_clean, date_format).date()
                        formatted_date = date_obj.strftime("%Y-%m-%d")
                        break
                    except ValueError:
                        continue
                else:
                    # 如果都失败了，尝试pandas解析
                    date_obj = pd.to_datetime(date_clean).date()
                    formatted_date = date_obj.strftime("%Y-%m-%d")
            else:
                formatted_date = ""
            
            # 处理时间部分 - 使用交通银行特殊转换
            if time_clean:
                formatted_time = self._convert_bocm_time_format(time_clean)
            else:
                formatted_time = "00:00:00"
            
            return f"{formatted_date} {formatted_time}"
            
        except Exception as e:
            logger.warning(f"时间格式化失败: {date_str} {time_str}, 错误: {str(e)}")
            return date_str
    
    def _format_date_to_standard(self, date_str: str) -> str:
        """
        将日期格式化为YYYY-MM-DD标准格式
        根据插件化解析器架构设计方案中的规范：date_format: "YYYY-MM-DD"
        """
        if not date_str:
            return ""
        
        try:
            date_clean = str(date_str).strip()
            logger.info(f"🔧 日期转换：'{date_str}' -> 清理后：'{date_clean}'")
            
            # 交通银行主要使用YYYYMMDD格式
            if len(date_clean) == 8 and date_clean.isdigit():
                try:
                    year = date_clean[:4]
                    month = date_clean[4:6] 
                    day = date_clean[6:8]
                    formatted = f"{year}-{month}-{day}"
                    logger.info(f"🔧 日期转换成功：'{date_str}' -> '{formatted}'")
                    return formatted
                except Exception as e:
                    logger.warning(f"YYYYMMDD格式解析失败: {date_str}, 错误: {str(e)}")
            
            # 尝试其他格式
            for date_format in ['%Y-%m-%d', '%Y/%m/%d']:
                try:
                    date_obj = datetime.strptime(date_clean, date_format).date()
                    result = date_obj.strftime("%Y-%m-%d")
                    logger.info(f"🔧 日期转换成功：'{date_str}' -> '{result}'")
                    return result
                except ValueError:
                    continue
            
            logger.warning(f"⚠️ 日期格式无法识别，保持原样: {date_str}")
            return date_str
            
        except Exception as e:
            logger.error(f"❌ 日期格式化严重失败: {date_str}, 错误: {str(e)}")
            return date_str
    
    def _find_data_start_row(self, df: pd.DataFrame) -> int:
        """查找数据开始行"""
        for i, row in df.iterrows():
            row_str = ' '.join([str(cell) for cell in row if pd.notna(cell)])
            # 寻找包含交通银行特征字段的行
            if '账号' in row_str and '借贷标志' in row_str and '交易金额' in row_str:
                logger.info(f"在第{i+1}行找到交通银行数据表头")
                return i
        
        logger.warning("未找到数据表头，使用默认第0行")
        return 0
    
    def _extract_account_info(self, df: pd.DataFrame) -> tuple:
        """从DataFrame中提取账户信息"""
        account_name = ""
        account_number = ""
        card_number = ""
        
        if not df.empty:
            # 提取户名
            if self.field_mapping['account_name_field'] in df.columns:
                first_valid_name = df[self.field_mapping['account_name_field']].dropna().iloc[0] if not df[self.field_mapping['account_name_field']].dropna().empty else ""
                account_name = self._clean_field(first_valid_name)
            
            # 提取账号
            if self.field_mapping['account_field'] in df.columns:
                first_valid_account = df[self.field_mapping['account_field']].dropna().iloc[0] if not df[self.field_mapping['account_field']].dropna().empty else ""
                account_number = self._clean_field(first_valid_account)
            
            # 交通银行数据中没有独立的卡号字段，使用账号
            card_number = account_number
            
            # 🔧 修复卡号显示：如果卡号为无效值或全0，则显示空值
            if card_number in ["****************00", "****************", "", "nan", "None"]:
                card_number = ""
        
        return account_name, account_number, card_number    
    def _process_transaction_data(self, df: pd.DataFrame, account_name: str, account_number: str, card_number: str) -> None:
        """处理交易数据 - 交通银行特殊格式处理"""
        transactions_added = 0
        
        for _, row in df.iterrows():
            # 提取交易数据 - 根据交通银行实际字段结构
            account = self._clean_field(row.get(self.field_mapping['account_field'], ''))
            account_name_current = self._clean_field(row.get(self.field_mapping['account_name_field'], ''))
            currency = self._clean_field(row.get(self.field_mapping['currency_field'], ''))
            amount_str = self._clean_field(row.get(self.field_mapping['amount_field'], ''))
            balance_str = self._clean_field(row.get(self.field_mapping['balance_field'], ''))
            direction_flag = self._clean_field(row.get(self.field_mapping['direction_field'], ''))

            # 🔧 增强余额字段提取：如果主字段为空，尝试其他可能的余额字段
            if not balance_str:
                # 尝试其他可能的余额字段名称
                alternative_balance_fields = ['余额', 'Balance', '账户余额', '当前余额']
                for alt_field in alternative_balance_fields:
                    if alt_field in row and row[alt_field]:
                        balance_str = self._clean_field(row[alt_field])
                        logger.info(f"🔧 使用备用余额字段 '{alt_field}': {balance_str}")
                        break

            # 🔧 添加调试：输出前几行的余额数据
            if transactions_added < 5:  # 增加调试行数
                logger.info(f"🔧 调试交易{transactions_added+1}: amount_str='{amount_str}', balance_str='{balance_str}', direction_flag='{direction_flag}'")
                logger.info(f"🔧 原始行数据余额字段: {row.get(self.field_mapping['balance_field'])}")
            account_date = self._clean_field(row.get(self.field_mapping['account_date_field'], ''))
            trade_date = self._clean_field(row.get(self.field_mapping['transaction_date_field'], ''))
            trade_time = self._clean_field(row.get(self.field_mapping['transaction_time_field'], ''))
            network = self._clean_field(row.get(self.field_mapping['network_field'], ''))
            serial_number = self._clean_field(row.get(self.field_mapping['serial_number_field'], ''))
            opposite_bank_flag = self._clean_field(row.get(self.field_mapping['opposite_bank_flag_field'], ''))
            opposite_account = self._clean_field(row.get(self.field_mapping['opposite_account_field'], ''))
            opposite_name = self._clean_field(row.get(self.field_mapping['opposite_name_field'], ''))
            opposite_bank_number = self._clean_field(row.get(self.field_mapping['opposite_bank_number_field'], ''))
            opposite_bank_name = self._clean_field(row.get(self.field_mapping['opposite_bank_name_field'], ''))
            business_code_name = self._clean_field(row.get(self.field_mapping['business_code_name_field'], ''))  # 交易方式
            business_summary = self._clean_field(row.get(self.field_mapping['business_summary_field'], ''))    # 备注1
            
            # 使用当前行的账户信息，如果为空则使用全局信息
            actual_cardholder = account_name_current if account_name_current else account_name
            actual_account = account if account else account_number
            
            # 跳过无效行（必须有日期和金额）
            if not trade_date or not amount_str:
                continue
            
            # 解析金额和余额 - 使用交通银行借贷标志逻辑
            amount = self._parse_amount(amount_str, direction_flag)
            balance = self._parse_balance(balance_str)  # 🔧 使用专门的余额解析方法

            # 🔧 调试：记录余额解析过程
            if balance_str:
                logger.info(f"🔧 余额解析：原始='{balance_str}' -> 解析后={balance}")
            else:
                logger.warning(f"⚠️ 余额字段为空：行{idx+1}")
            
            # 处理收支符号 - 根据借贷标志
            if "借" in direction_flag:  # D - 借 = 支出
                dr_cr_flag = "支"
            elif "贷" in direction_flag:  # C - 贷 = 收入
                dr_cr_flag = "收"
            else:
                # 备用逻辑：根据金额正负判断
                dr_cr_flag = "收" if amount >= 0 else "支"
            
            # 标准化时间格式 - 使用交通银行特殊时间转换
            standardized_datetime = self._standardize_time_format(trade_date, trade_time)
            # 🔧 修复日期格式：将********转换为2015-02-22标准格式
            formatted_date = self._format_date_to_standard(trade_date)
            formatted_time = self._convert_bocm_time_format(trade_time)
            
            # 交易方式 = 业务摘要码名称，备注1 = 业务摘要（按用户要求）
            transaction_method = business_code_name  # 交易方式来源
            remark1 = business_summary               # 备注1来源
            
            # 构建交易记录
            transaction = {
                'sequence_number': len(self.transactions) + 1,
                'transaction_datetime': standardized_datetime,
                'transaction_date': formatted_date,
                'transaction_time': formatted_time,
                'transaction_amount': amount,
                'amount': amount,
                'balance_amount': balance,
                'balance': balance,
                'transaction_method': transaction_method,    # 来自业务摘要码名称
                'summary': transaction_method,
                'remark1': remark1,                         # 来自业务摘要
                'remark': remark1,
                'dr_cr_flag': dr_cr_flag,
                'direction': direction_flag,
                'direction_original': direction_flag,       # 保留原始借贷标志
                'counterparty_account': opposite_account,
                'opposite_account': opposite_account,
                'counterparty_name': opposite_name,
                'opposite_name': opposite_name,
                'holder_name': actual_cardholder,
                'cardholder_name': actual_cardholder,
                'account_number': actual_account,
                'card_number': self._clean_card_number(card_number),
                'bank_name': '交通银行',
                'currency': currency or 'CNY',
                'network': network,
                'serial_number': serial_number,
                'opposite_bank_flag': opposite_bank_flag,
                'opposite_bank_number': opposite_bank_number,
                'opposite_bank_name': opposite_bank_name,
                'account_date': account_date
            }
            
            self.transactions.append(transaction)
            transactions_added += 1
        
        logger.info(f"成功处理 {transactions_added} 条交通银行交易记录")
    
    def _process_sheet(self, sheet_name: str) -> None:
        """处理单个工作表"""
        try:
            logger.info(f"分析交通银行工作表: {sheet_name}")
            
            df = pd.read_excel(self.file_path, sheet_name=sheet_name, dtype={'账号': str})
            
            if df.empty:
                logger.warning(f"工作表 {sheet_name} 为空")
                return
            
            # 查找数据开始行
            data_start_row = self._find_data_start_row(df)
            
            # 重新读取，从数据开始行作为表头，强制账号字段为字符串类型
            df = pd.read_excel(self.file_path, sheet_name=sheet_name, header=data_start_row, dtype={'账号': str})
            
            # 🔧 调试：输出实际Excel列名
            logger.info(f"🔧 Excel实际列名: {list(df.columns)}")
            logger.info(f"🔧 寻找余额字段'{self.field_mapping['balance_field']}'，是否存在: {self.field_mapping['balance_field'] in df.columns}")
            
            logger.info(f"从交通银行工作表'{sheet_name}'提取到{len(df)}行数据")
            
            # 验证必要字段 - 交通银行特有字段
            required_fields = [
                self.field_mapping['transaction_date_field'],  # 交易日期
                self.field_mapping['amount_field'],            # 交易金额
                self.field_mapping['direction_field']          # 借贷标志
            ]
            missing_fields = [field for field in required_fields if field not in df.columns]
            
            if missing_fields:
                logger.error(f"交通银行工作表 {sheet_name} 缺少必需字段: {missing_fields}")
                return
            
            # 提取账户信息
            account_name, account_number, card_number = self._extract_account_info(df)
            
            # 添加账户记录
            if account_name:
                account = {
                    'holder_name': account_name,
                    'cardholder_name': account_name,
                    'account_number': account_number,
                    'card_number': "" if card_number in ["****************00", "****************", "", "nan", "None"] else card_number,
                    'bank_name': '交通银行',
                    'account_type': '企业账户'  # 根据示例数据判断
                }
                self.accounts.append(account)
                logger.info(f"找到交通银行账户: {account_name} - {account_number}")
            
            # 处理交易数据
            self._process_transaction_data(df, account_name, account_number, card_number)
            
        except Exception as e:
            logger.error(f"处理交通银行工作表 {sheet_name} 时出错: {str(e)}")
            logger.error(traceback.format_exc())    
    def parse(self, file_path: str = None) -> Dict[str, Any]:
        """执行解析 - 交通银行Format1"""
        try:
            # 使用传入的文件路径或实例的文件路径
            if file_path:
                self.file_path = file_path
            elif not self.file_path:
                raise ValueError("未设置文件路径")
            
            logger.info(f"🔍 开始解析交通银行Format1文件: {os.path.basename(self.file_path)}")
            
            # 验证文件
            if not self.validate_file(self.file_path):
                raise ValueError("文件验证失败，不是有效的交通银行格式文件")
            
            # 重置状态
            self.accounts = []
            self.transactions = []
            
            if not os.path.exists(self.file_path):
                return {
                    'success': False,
                    'error': f'文件不存在: {self.file_path}',
                    'accounts': [],
                    'transactions_by_account': {}
                }
            
            # 获取所有工作表
            excel_file = pd.ExcelFile(self.file_path)
            sheet_names = excel_file.sheet_names
            logger.info(f"发现交通银行工作表: {sheet_names}")
            
            # 处理每个工作表
            for sheet_name in sheet_names:
                self._process_sheet(sheet_name)
            
            # 构建解析结果
            result = self._build_result()
            
            logger.info(f"✅ 交通银行解析完成: 共 {len(self.accounts)} 个账户, {len(self.transactions)} 条交易")
            return result
            
        except Exception as e:
            logger.error(f"交通银行文件解析失败: {str(e)}")
            logger.error(traceback.format_exc())
            self.handle_error(e)
            return {
                'success': False,
                'error': str(e),
                'accounts': [],
                'transactions_by_account': {},
                'summary': {'total_accounts': 0, 'total_transactions': 0},
                'metadata': {
                    'plugin_name': self.name,
                    'plugin_version': self.version,
                    'error_details': str(e)
                }
            }
    
    def _calculate_confidence_score(self, accounts: List[Dict], transactions: List[Dict]) -> float:
        """计算置信度分数"""
        score = 0.0
        
        if accounts:
            score += 20
        if transactions:
            score += 20
        
        # 检查账户信息完整性
        if accounts:
            account = accounts[0]
            if account.get('holder_name'):
                score += 15
            if account.get('account_number'):
                score += 10
        
        # 检查交易数据完整性
        if transactions:
            valid_transactions = 0
            time_format_correct = 0
            direction_correct = 0
            
            for trans in transactions[:10]:  # 检查前10条
                if trans.get('transaction_date') and trans.get('amount'):
                    valid_transactions += 1
                
                # 检查时间格式是否正确转换
                if trans.get('transaction_time') and ':' in trans.get('transaction_time', ''):
                    time_format_correct += 1
                
                # 检查借贷标志是否正确处理
                if trans.get('direction_original') and trans.get('dr_cr_flag'):
                    if ("借" in trans['direction_original'] and trans['dr_cr_flag'] == "支") or \
                       ("贷" in trans['direction_original'] and trans['dr_cr_flag'] == "收"):
                        direction_correct += 1
            
            # 基础有效性
            if valid_transactions >= 8:
                score += 15
            elif valid_transactions >= 5:
                score += 10
            else:
                score += 5
            
            # 时间格式处理加分
            if time_format_correct >= 5:
                score += 10
            
            # 借贷标志处理加分
            if direction_correct >= 5:
                score += 10
        
        return min(score, 100.0)
    
    def _build_result(self) -> Dict[str, Any]:
        """构建返回结果"""
        confidence_score = self._calculate_confidence_score(self.accounts, self.transactions)
        
        # 按账户分组交易记录
        transactions_by_account = {}
        corrected_accounts = []
        
        # 为每条交易分配序号并按账户分组
        for index, transaction in enumerate(self.transactions, 1):
            transaction['sequence_number'] = index
            
            cardholder_name = transaction['cardholder_name']
            account_number = transaction['account_number']
            group_key = f"{cardholder_name}_{account_number}" if account_number else cardholder_name
            
            if group_key not in transactions_by_account:
                transactions_by_account[group_key] = []
                
                # 🔧 修复卡号显示：使用统一的卡号清理函数
                raw_card_number = transaction['card_number']
                clean_card_number = self._clean_card_number(raw_card_number)
                logger.info(f"🔧 卡号处理：原始='{raw_card_number}' -> 清理后='{clean_card_number}'")
                
                account_data = {
                    'account_number': account_number,
                    'card_number': clean_card_number,
                    'holder_name': cardholder_name,
                    'cardholder_name': cardholder_name,
                    'bank_name': '交通银行',
                    'account_type': '企业账户',
                    'transactions_count': 0,
                    'total_inflow': 0.0,
                    'total_outflow': 0.0,
                    'date_range': '',
                    'account_id': account_number,
                    'account_name': cardholder_name,
                    'currency': transaction.get('currency', 'CNY'),
                    'is_primary': len(corrected_accounts) == 0
                }
                corrected_accounts.append(account_data)
                logger.info(f"创建交通银行账户记录: {cardholder_name} - {account_number}")
            
            transactions_by_account[group_key].append(transaction)
        
        # 计算每个账户的统计信息
        for account in corrected_accounts:
            cardholder_name = account['cardholder_name']
            account_number = account['account_number']
            group_key = f"{cardholder_name}_{account_number}" if account_number else cardholder_name
            account_transactions = transactions_by_account.get(group_key, [])
            
            account['transactions_count'] = len(account_transactions)
            
            total_inflow = sum(t['transaction_amount'] for t in account_transactions if t['dr_cr_flag'] == '收')
            total_outflow = abs(sum(t['transaction_amount'] for t in account_transactions if t['dr_cr_flag'] == '支'))
            
            account['total_inflow'] = total_inflow
            account['total_outflow'] = total_outflow
            
            # 🔧 余额就是取时间上最后一条交易的交易余额 - 简单直接！
            account_balance = 0.0
            final_balance = 0.0
            
            if account_transactions:
                logger.info(f"🔧 处理账户 {cardholder_name} ({account_number})，共 {len(account_transactions)} 笔交易")
                
                # 🔧 修复排序逻辑：确保正确的日期时间排序
                def sort_key(transaction):
                    date_str = str(transaction.get('transaction_date', '1900-01-01'))
                    time_str = str(transaction.get('transaction_time', '00:00:00'))

                    # 标准化日期格式为YYYY-MM-DD
                    if len(date_str) == 8 and date_str.isdigit():
                        # ******** -> 2015-02-22
                        date_str = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
                    elif len(date_str) == 10 and '-' in date_str:
                        # 已经是YYYY-MM-DD格式
                        pass
                    else:
                        # 其他格式，尝试解析
                        try:
                            from datetime import datetime
                            parsed_date = datetime.strptime(date_str, '%Y-%m-%d')
                            date_str = parsed_date.strftime('%Y-%m-%d')
                        except:
                            date_str = '1900-01-01'

                    # 标准化时间格式为HH:MM:SS
                    if len(time_str) < 8:
                        time_str = time_str.ljust(8, '0')[:8]
                        if ':' not in time_str:
                            # 如果是纯数字，转换为HH:MM:SS格式
                            if len(time_str) >= 6:
                                time_str = f"{time_str[:2]}:{time_str[2:4]}:{time_str[4:6]}"

                    return (date_str, time_str)
                
                sorted_transactions = sorted(account_transactions, key=sort_key)

                # 🔧 调试：显示排序前后的最后几笔交易
                logger.info(f"🔧 账户 {cardholder_name} 交易总数：{len(sorted_transactions)}")
                logger.info(f"🔧 排序前最后3笔交易:")
                for i, tx in enumerate(account_transactions[-3:]):
                    logger.info(f"   原始{i+1}: {tx.get('transaction_date')} {tx.get('transaction_time')} 余额={tx.get('balance')}")

                logger.info(f"🔧 排序后最后3笔交易:")
                for i, tx in enumerate(sorted_transactions[-3:]):
                    logger.info(f"   排序{i+1}: {tx.get('transaction_date')} {tx.get('transaction_time')} 余额={tx.get('balance')}")

                # 调试：检查所有可能的余额字段
                last_transaction = sorted_transactions[-1]

                logger.info(f"🔧 最后交易日期时间：{last_transaction.get('transaction_date')} {last_transaction.get('transaction_time')}")
                logger.info(f"🔧 最后交易所有字段：balance={last_transaction.get('balance')}, balance_amount={last_transaction.get('balance_amount')}")
                
                # 🔧 改进余额提取逻辑：优先寻找非零余额
                balance_candidates = [
                    ('balance', last_transaction.get('balance')),
                    ('balance_amount', last_transaction.get('balance_amount')),
                    ('余额', last_transaction.get('余额'))
                ]

                balance_value = None
                balance_source = None

                # 首先尝试从最后一条交易获取非零余额
                for field_name, candidate in balance_candidates:
                    if candidate is not None and candidate != 0.0:
                        balance_value = candidate
                        balance_source = f"最后交易的{field_name}"
                        logger.info(f"🔧 找到有效余额：{balance_source}={candidate}")
                        break

                # 🔧 如果最后一条交易余额为0或None，搜索最后一条有效余额的交易
                if balance_value is None or balance_value == 0.0:
                    logger.warning(f"⚠️ 最后交易余额无效({balance_value})，搜索最后一条有效余额交易")
                    for i, transaction in enumerate(reversed(sorted_transactions)):
                        for field_name, candidate in [('balance', transaction.get('balance')),
                                                     ('balance_amount', transaction.get('balance_amount'))]:
                            if candidate is not None and candidate > 0.0:
                                balance_value = candidate
                                balance_source = f"倒数第{i+1}条交易的{field_name}"
                                logger.info(f"🔧 找到有效余额：{balance_source}={candidate}")
                                break
                        if balance_value and balance_value > 0.0:
                            break

                if balance_value is None:
                    balance_value = 0.0
                    balance_source = "默认值"

                logger.info(f"🔧 最终余额来源：{balance_source}，值：{balance_value}")
                
                # 确保余额为数字
                try:
                    account_balance = float(balance_value)
                    final_balance = account_balance
                    logger.info(f"🔧 成功获取账户 {cardholder_name} 余额：¥{account_balance}")
                except (ValueError, TypeError):
                    logger.warning(f"⚠️ 无法转换余额值：{balance_value}")
                    account_balance = 0.0
                    final_balance = 0.0
                
                # 🔧 设置日期范围 - 强化处理逻辑，输出更多调试信息
                valid_dates = []
                logger.info(f"🔧 开始处理账户 {cardholder_name} 的日期范围，总交易数：{len(account_transactions)}")
                
                for i, t in enumerate(account_transactions[:5]):  # 检查前5笔交易
                    date_str = t.get('transaction_date')
                    logger.info(f"🔧 交易{i+1}原始日期：'{date_str}', 类型：{type(date_str)}")
                
                for t in account_transactions:
                    date_str = t.get('transaction_date')
                    if date_str and str(date_str).strip() and str(date_str) != 'nan':
                        # 确保日期格式统一
                        clean_date = str(date_str).strip()
                        if len(clean_date) == 8 and clean_date.isdigit():
                            # ******** -> 2015-02-22
                            formatted_date = f"{clean_date[:4]}-{clean_date[4:6]}-{clean_date[6:8]}"
                            valid_dates.append(formatted_date)
                        elif '-' in clean_date and len(clean_date) >= 8:
                            # 已经是YYYY-MM-DD格式
                            valid_dates.append(clean_date)
                        elif len(clean_date) >= 6:
                            # 其他可能的日期格式
                            try:
                                # 尝试解析各种日期格式
                                if '/' in clean_date:
                                    parts = clean_date.split('/')
                                    if len(parts) == 3:
                                        formatted_date = f"{parts[0]}-{parts[1].zfill(2)}-{parts[2].zfill(2)}"
                                        valid_dates.append(formatted_date)
                                else:
                                    logger.debug(f"🔧 跳过未识别的日期格式：{clean_date}")
                            except Exception as e:
                                logger.debug(f"🔧 日期解析失败：{clean_date}, 错误：{e}")
                
                logger.info(f"🔧 账户 {cardholder_name} 找到有效日期：{len(valid_dates)}个")
                if valid_dates and len(valid_dates) > 0:
                    min_date = min(valid_dates)
                    max_date = max(valid_dates)
                    account['date_range'] = f"{min_date} 至 {max_date}"
                    logger.info(f"🔧 账户 {cardholder_name} 时间范围：{min_date} 至 {max_date} (共{len(valid_dates)}个有效日期)")
                else:
                    account['date_range'] = ""  # 显示空而不是"未知"
                    logger.warning(f"⚠️ 账户 {cardholder_name} 没有找到有效的交易日期！交易数：{len(account_transactions)}")
                    # 输出前几笔交易的详细信息用于调试
                    for i, t in enumerate(account_transactions[:3]):
                        logger.warning(f"⚠️ 调试交易{i+1}：{t.get('transaction_date')} - {t.get('transaction_time')}")
                
                logger.info(f"🔧 账户 {cardholder_name} 最终设置：余额=¥{account_balance}, 时间范围={account['date_range']}")
            else:
                # 没有交易记录的情况
                account_balance = 0.0
                final_balance = 0.0
                account['date_range'] = ""
                
            account['account_balance'] = account_balance
            account['final_balance'] = final_balance
            
            logger.info(f"交通银行账户 {cardholder_name} ({account_number}): {account['transactions_count']}笔交易")
        
        # 构建汇总信息
        summary = {
            'total_accounts': len(corrected_accounts),
            'total_transactions': len(self.transactions),
            'confidence_score': confidence_score,
            'parser_type': 'BOCM_Format1_Plugin'
        }
        
        if corrected_accounts:
            first_account = corrected_accounts[0]
            summary.update({
                'account_name': first_account.get('cardholder_name', ''),
                'account_number': first_account.get('account_number', ''),
                'card_number': first_account.get('card_number', ''),
                'total_inflow': first_account.get('total_inflow', 0.0),
                'total_outflow': first_account.get('total_outflow', 0.0),
                'account_balance': first_account.get('account_balance', 0.0),  # 🔧 添加账户余额
                'final_balance': first_account.get('final_balance', 0.0),      # 🔧 添加最终余额
                'date_range': first_account.get('date_range', '')
            })
        
        logger.info(f"交通银行解析完成: {len(corrected_accounts)}个账户，{len(self.transactions)}条交易记录")
        
        return {
            'success': True,
            'message': f'成功解析交通银行 {len(corrected_accounts)} 个账户，{len(self.transactions)} 条交易记录',
            'summary': summary,
            'accounts': corrected_accounts,
            'transactions': self.transactions,
            'transactions_by_account': transactions_by_account,
            'confidence': confidence_score,
            'metadata': {
                'plugin_name': self.name,
                'plugin_version': self.version,
                'parser_type': 'BOCM_Format1_Plugin',
                'bank_name': '交通银行',
                'total_accounts': len(corrected_accounts),
                'total_transactions': len(self.transactions),
                'confidence_score': confidence_score,
                'file_path': self.file_path,
                'parse_time': datetime.now().isoformat(),
                'special_features': {
                    'time_format_converted': True,
                    'direction_flag_processed': True,
                    'business_summary_mapped': True
                }
            }
        }
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取插件健康状态"""
        return {
            "healthy": self.error_count < 10,
            "last_check": time.time(),
            "memory_usage": "normal",
            "error_count": self.error_count,
            "last_error": getattr(self, 'last_error', None),
            "uptime": time.time() - self.start_time,
            "plugin_specific": {
                "time_format_handler": "active",
                "direction_flag_handler": "active",
                "business_summary_mapper": "active"
            }
        }
    
    def handle_error(self, error: Exception, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理错误"""
        logger.error(f"交通银行Format1插件错误: {str(error)}")
        if context:
            logger.error(f"错误上下文: {context}")
        
        self.error_count += 1
        self.last_error = str(error)
        
        return {
            'success': False,
            'error': f"交通银行Format1插件处理失败: {str(error)}",
            'plugin_name': self.name,
            'context': context or {}
        }
    
    def extract_sample(self, file_path: str = None, limit: int = 10) -> Dict[str, Any]:
        """
        提取样本数据用于快速置信度评估 - 交通银行精准识别版本
        参考工商银行成功模式：数据特征识别 + 固定列位置 + 严格验证
        
        Args:
            file_path: 文件路径
            limit: 样本数量限制
            
        Returns:
            Dict: 包含样本账户和交易的字典
        """
        try:
            target_file = file_path or self.file_path
            if not target_file:
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
            
            logger.info(f"🔍 交通银行解析器开始精准样本提取，限制条数: {limit}")
            
            # 🔧 第一步：精准数据特征识别 - 交通银行特征：第0列账号(16-25位数字) + 第1列户名(中文2-4字)
            df = pd.read_excel(target_file, header=None, nrows=50)
            if df.empty:
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
            
            logger.info(f"精准识别模式：读取交通银行文件前50行")
            
            # 🔧 第二步：边界检测 - 寻找交通银行数据特征
            data_start_idx = None
            cardholder_name = "交通银行测试用户"
            account_number = "****************"

            # 检查第1行是否为表头
            if len(df) > 0:
                first_row = df.iloc[0]
                if len(first_row) > 1:
                    first_cell = str(first_row.iloc[0]).strip() if pd.notna(first_row.iloc[0]) else ""
                    second_cell = str(first_row.iloc[1]).strip() if pd.notna(first_row.iloc[1]) else ""

                    # 如果第1行是表头（账号、户名），则数据从第2行开始
                    if first_cell == "账号" and second_cell == "户名":
                        logger.info("检测到交通银行表头，数据从第2行开始")
                        # 检查第2行是否有有效数据
                        if len(df) > 1:
                            second_row = df.iloc[1]
                            if len(second_row) > 1:
                                account_cell = str(second_row.iloc[0]).strip() if pd.notna(second_row.iloc[0]) else ""
                                name_cell = str(second_row.iloc[1]).strip() if pd.notna(second_row.iloc[1]) else ""

                                # 交通银行数据特征：第0列账号(18-25位数字) + 第1列户名(中文2-4字)
                                if (re.match(r'^\d{18,25}$', account_cell) and
                                    re.match(r'^[\u4e00-\u9fa5]{2,4}$', name_cell)):
                                    data_start_idx = 1  # 从第2行开始（索引为1）
                                    cardholder_name = name_cell
                                    account_number = account_cell
                                    logger.info(f"精准检测到交通银行数据起始行 {data_start_idx+1}: 户名={cardholder_name}, 账号={account_number}")

            # 如果没有找到表头，则逐行搜索
            if data_start_idx is None:
                for idx in range(min(30, len(df))):
                    row = df.iloc[idx]
                    if len(row) > 1:
                        account_cell = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else ""
                        name_cell = str(row.iloc[1]).strip() if pd.notna(row.iloc[1]) else ""

                        # 交通银行数据特征：第0列账号(18-25位数字) + 第1列户名(中文2-4字)
                        if (re.match(r'^\d{18,25}$', account_cell) and
                            re.match(r'^[\u4e00-\u9fa5]{2,4}$', name_cell)):
                            data_start_idx = idx
                            cardholder_name = name_cell
                            account_number = account_cell
                            logger.info(f"精准检测到交通银行数据起始行 {idx+1}: 户名={cardholder_name}, 账号={account_number}")
                            break

            if data_start_idx is None:
                logger.warning("精准识别：未找到交通银行数据特征")
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
            
            # 🔧 第三步：构建账户信息
            sample_accounts = []
            if cardholder_name and account_number:
                account = {
                    'cardholder_name': cardholder_name,  # 🔧 4维度姓名识别需要
                    'holder_name': cardholder_name,      # 🔧 前端显示需要
                    'person_name': cardholder_name,      # 🔧 兼容性字段
                    'account_number': account_number,    # 🔧 4维度账号识别需要
                    'card_number': account_number,       # 🔧 前端显示需要
                    'bank_name': '交通银行',
                    'account_name': f"{cardholder_name}的账户",
                    'currency': 'CNY',
                    'account_type': '企业账户'
                }
                sample_accounts.append(account)

            # 🔧 第四步：基于识别结果的固定列位置提取交易数据
            sample_transactions = []

            for idx in range(data_start_idx, min(data_start_idx + limit * 2, len(df))):
                if len(sample_transactions) >= limit:
                    break

                row = df.iloc[idx]
                if row.isna().all():
                    continue

                try:
                    # 🔧 第五步：严格验证 - 交通银行固定列位置验证
                    # 交通银行格式：账号(0) 户名(1) 币种(2) 交易金额(3) 余额(4) 借贷标志(5) 会计日期(6) 交易日期(7) 交易时间(8)
                    if len(row) < 9:
                        continue

                    account_check = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else ""
                    name_check = str(row.iloc[1]).strip() if pd.notna(row.iloc[1]) else ""
                    currency = str(row.iloc[2]).strip() if pd.notna(row.iloc[2]) else "CNY"
                    amount_raw = str(row.iloc[3]).strip() if pd.notna(row.iloc[3]) else ""
                    balance_raw = str(row.iloc[4]).strip() if pd.notna(row.iloc[4]) else ""
                    dr_cr_raw = str(row.iloc[5]).strip() if pd.notna(row.iloc[5]) else ""
                    account_date = str(row.iloc[6]).strip() if pd.notna(row.iloc[6]) else ""
                    transaction_date = str(row.iloc[7]).strip() if pd.notna(row.iloc[7]) else ""
                    transaction_time = str(row.iloc[8]).strip() if pd.notna(row.iloc[8]) else "0"

                    # 严格验证：必须符合交通银行数据特征
                    if not (re.match(r'^\d{18,25}$', account_check) and
                            re.match(r'^[\u4e00-\u9fa5]{2,4}$', name_check)):
                        continue

                    # 验证交易日期格式 (YYYYMMDD)
                    if not re.match(r'^\d{8}$', transaction_date):
                        continue

                    # 解析交易金额和余额
                    try:
                        transaction_amount = float(amount_raw)
                        balance_value = float(balance_raw) if balance_raw else 0.0
                    except:
                        continue

                    # 解析交易时间（6位数格式：HHMMSS）
                    formatted_time = "00:00:00"
                    if transaction_time and transaction_time != "0":
                        try:
                            if len(transaction_time) == 6:
                                formatted_time = f"{transaction_time[:2]}:{transaction_time[2:4]}:{transaction_time[4:6]}"
                            elif len(transaction_time) == 5:
                                formatted_time = f"0{transaction_time[0]}:{transaction_time[1:3]}:{transaction_time[3:5]}"
                        except:
                            formatted_time = "00:00:00"

                    # 解析借贷方向：D=借(支出)，C=贷(收入)
                    dr_cr_flag = ""
                    direction = ""
                    if "D" in dr_cr_raw or "借" in dr_cr_raw:
                        dr_cr_flag = "支"
                        direction = "支"
                    elif "C" in dr_cr_raw or "贷" in dr_cr_raw:
                        dr_cr_flag = "收"
                        direction = "收"
                    else:
                        dr_cr_flag = "收"  # 默认收入
                        direction = "收"

                    # 验证必要字段完整性
                    if account_check and name_check and transaction_date and transaction_amount > 0:
                        transaction = {
                            'cardholder_name': name_check,    # 🔧 4维度姓名识别需要
                            'holder_name': name_check,        # 🔧 前端显示需要
                            'person_name': name_check,        # 🔧 兼容性字段
                            'account_number': account_check,  # 🔧 4维度账号识别需要
                            'card_number': account_check,     # 🔧 前端显示需要
                            'transaction_date': f"{transaction_date[:4]}-{transaction_date[4:6]}-{transaction_date[6:8]}",  # 🔧 4维度时间格式需要
                            'transaction_time': formatted_time,  # 🔧 4维度时间格式需要
                            'transaction_amount': transaction_amount,  # 🔧 4维度金额解析需要
                            'balance': balance_value,         # 🔧 4维度金额解析需要
                            'balance_amount': balance_value,  # 🔧 前端显示需要
                            'dr_cr_flag': dr_cr_flag,
                            'direction': direction,           # 🔧 前端显示需要
                            'currency': currency,
                            'transaction_method': '交通银行交易',
                            'bank_name': '交通银行',
                            'summary': '交通银行交易',         # 备注信息
                            'remark1': '交通银行交易',         # 备注1
                            'remark2': '',                    # 备注2
                            'remark3': '',                    # 备注3
                            'counterparty_name': '',          # 对方户名
                            'counterparty_account': '',       # 对方账号
                            'sequence_number': len(sample_transactions) + 1  # 序号
                        }
                        sample_transactions.append(transaction)

                except Exception as e:
                    logger.debug(f"跳过第{idx+1}行: {str(e)}")
                    continue
            
            logger.info(f"交通银行Format1样本提取完成: {len(sample_accounts)}个账户, {len(sample_transactions)}条交易")
            
            return {
                'accounts': sample_accounts,
                'transactions': sample_transactions,
                'success': True,
                'metadata': {
                    'sample_size': len(sample_transactions),
                    'plugin_name': self.name,
                    'parser_version': 'v3.0-feature_recognition'
                }
            }
            
        except Exception as e:
            logger.error(f"交通银行extract_sample方法失败: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'accounts': [],
                'transactions': [],
                'success': False,
                'error': str(e),
                'metadata': {
                    'sample_size': 0,
                    'plugin_name': self.name,
                    'error_details': str(e)
                }
            }