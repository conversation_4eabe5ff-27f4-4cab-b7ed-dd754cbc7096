"""
财产信息管理API
"""
import logging
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from fastapi import APIRouter, Depends, HTTPException, status, Query

from ..middleware.auth_middleware import get_current_user, get_user_db
from ..models.duckdb_models import DuckDBAsset as Asset
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter()

class AssetCreate(BaseModel):
    """创建财产信息的请求模式"""
    project_id: str
    name: str
    type: str  # 房产、车辆、银行账户等
    owner: str
    value: Optional[float] = None
    status: Optional[str] = None  # 已查封、已冻结等
    location: Optional[str] = None
    identification_number: Optional[str] = None  # 房产证号、车牌号等
    acquisition_date: Optional[str] = None
    acquisition_method: Optional[str] = None
    custom_field1: Optional[str] = None
    custom_field2: Optional[str] = None
    custom_field3: Optional[str] = None
    detail: Optional[str] = None
    notes: Optional[str] = None

class AssetUpdate(BaseModel):
    """更新财产信息的请求模式"""
    name: Optional[str] = None
    type: Optional[str] = None
    owner: Optional[str] = None
    value: Optional[float] = None
    status: Optional[str] = None
    location: Optional[str] = None
    identification_number: Optional[str] = None
    acquisition_date: Optional[str] = None
    acquisition_method: Optional[str] = None
    custom_field1: Optional[str] = None
    custom_field2: Optional[str] = None
    custom_field3: Optional[str] = None
    detail: Optional[str] = None
    notes: Optional[str] = None

class AssetResponse(BaseModel):
    """财产信息响应模式"""
    asset_id: str
    project_id: str
    name: str
    type: str
    owner: str
    value: Optional[float] = None
    status: Optional[str] = None
    location: Optional[str] = None
    identification_number: Optional[str] = None
    acquisition_date: Optional[str] = None
    acquisition_method: Optional[str] = None
    custom_field1: Optional[str] = None
    custom_field2: Optional[str] = None
    custom_field3: Optional[str] = None
    detail: Optional[str] = None
    notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

@router.get("/")
async def get_assets(
    project_id: str = Query(..., description="项目ID"),
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> List[Dict[str, Any]]:
    """获取项目的财产信息列表"""
    try:
        logger.info(f"用户 '{current_user}' 获取项目 {project_id} 的财产信息列表")
        
        assets = db.query(Asset).filter(
            Asset.project_id == project_id
        ).order_by(Asset.created_at.desc()).all()
        
        result = []
        for asset in assets:
            result.append({
                "asset_id": asset.asset_id,
                "project_id": asset.project_id,
                "name": asset.name,
                "type": asset.type,
                "owner": asset.owner,
                "value": asset.value,
                "status": asset.status,
                "location": asset.location,
                "identification_number": asset.identification_number,
                "acquisition_date": asset.acquisition_date,
                "acquisition_method": asset.acquisition_method,
                "custom_field1": asset.custom_field1,
                "custom_field2": asset.custom_field2,
                "custom_field3": asset.custom_field3,
                "detail": asset.detail,
                "notes": asset.notes,
                "created_at": asset.created_at.isoformat() if asset.created_at else None,
                "updated_at": asset.updated_at.isoformat() if asset.updated_at else None
            })
        
        logger.info(f"用户 '{current_user}' 获取到 {len(result)} 条财产信息")
        return result
        
    except Exception as e:
        logger.error(f"用户 '{current_user}' 获取财产信息列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取财产信息列表失败: {str(e)}"
        )

@router.post("/", response_model=AssetResponse, status_code=status.HTTP_201_CREATED)
def create_asset(asset_data: AssetCreate, current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)):
    """创建财产信息"""
    try:
        new_asset = Asset(
            asset_id=str(uuid.uuid4()),
            **asset_data.dict()
        )
        
        db.add(new_asset)
        db.commit()
        db.refresh(new_asset)
        
        logger.info(f"用户 \'{current_user}\' 成功创建财产信息: {new_asset.asset_id}")
        return new_asset
        
    except Exception as e:
        db.rollback()
        logger.error(f"用户 \'{current_user}\' 创建财产信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建财产信息失败: {str(e)}"
        )

@router.put("/{asset_id}", response_model=AssetResponse)
def update_asset(
    asset_id: str,
    asset_data: AssetUpdate,
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
):
    """更新财产信息"""
    try:
        asset = db.query(Asset).filter(Asset.asset_id == asset_id).first()
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"财产信息 {asset_id} 不存在"
            )
        
        # 更新字段
        update_data = asset_data.dict(exclude_unset=True)
        update_data['updated_at'] = datetime.utcnow()
        
        for field, value in update_data.items():
            setattr(asset, field, value)
        
        db.commit()
        db.refresh(asset)
        
        logger.info(f"用户 \'{current_user}\' 成功更新财产信息: {asset.asset_id}")
        return asset
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"用户 \'{current_user}\' 更新财产信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新财产信息失败: {str(e)}"
        )

@router.delete("/{asset_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_asset(asset_id: str, current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)):
    """删除财产信息"""
    try:
        asset = db.query(Asset).filter(Asset.asset_id == asset_id).first()
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"财产信息 {asset_id} 不存在"
            )
        
        db.delete(asset)
        db.commit()
        
        logger.info(f"用户 \'{current_user}\' 成功删除财产信息: {asset.asset_id}")
        return None
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"用户 \'{current_user}\' 删除财产信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除财产信息失败: {str(e)}"
        ) 