import React, { useState } from 'react';
import { Card, Table, Button, Space, Upload, Modal, Form, Input, Select, Tag, Divider, message, Typography, Alert } from 'antd';
import { PlusOutlined, UploadOutlined, EditOutlined, DeleteOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';

const { Option } = Select;
const { Title, Paragraph, Text } = Typography;
const { Dragger } = Upload;

const BankStatementsParsers = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [form] = Form.useForm();
  const [parsers, setParsers] = useState([
    {
      id: '1',
      name: '工商银行标准模板',
      bankCode: 'ICBC',
      fileType: 'excel',
      format: 'format1',
      createdAt: '2023-01-10',
      updatedAt: '2023-05-15',
      status: 'active',
    },
    {
      id: '2',
      name: '建设银行标准模板',
      bankCode: 'CCB',
      fileType: 'excel',
      format: 'format1',
      createdAt: '2023-01-15',
      updatedAt: '2023-04-10',
      status: 'active',
    },
    {
      id: '3',
      name: '农业银行PDF模板',
      bankCode: 'ABC',
      fileType: 'pdf',
      format: 'format1',
      createdAt: '2023-02-20',
      updatedAt: '2023-05-20',
      status: 'testing',
    },
  ]);

  // 表格列定义
  const columns = [
    {
      title: '解析器名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '银行代码',
      dataIndex: 'bankCode',
      key: 'bankCode',
    },
    {
      title: '文件类型',
      dataIndex: 'fileType',
      key: 'fileType',
      render: (text) => {
        if (text === 'excel') {
          return <Tag color="green">Excel</Tag>;
        } else if (text === 'pdf') {
          return <Tag color="blue">PDF</Tag>;
        } else if (text === 'image') {
          return <Tag color="purple">图片</Tag>;
        }
        return <Tag color="default">{text}</Tag>;
      },
    },
    {
      title: '格式版本',
      dataIndex: 'format',
      key: 'format',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => {
        if (text === 'active') {
          return <Tag icon={<CheckCircleOutlined />} color="success">已启用</Tag>;
        } else if (text === 'inactive') {
          return <Tag icon={<CloseCircleOutlined />} color="default">已禁用</Tag>;
        } else if (text === 'testing') {
          return <Tag color="warning">测试中</Tag>;
        }
        return <Tag color="default">{text}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record)}
          />
          <Button 
            type="text" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDelete(record.id)}
          />
        </Space>
      ),
    },
  ];

  // 处理添加
  const handleAdd = () => {
    form.resetFields();
    setEditingId(null);
    setIsModalVisible(true);
  };

  // 处理编辑
  const handleEdit = (record) => {
    form.setFieldsValue(record);
    setEditingId(record.id);
    setIsModalVisible(true);
  };

  // 处理删除
  const handleDelete = (id) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个解析器吗？删除后无法恢复。',
      onOk() {
        setParsers(parsers.filter(parser => parser.id !== id));
        message.success('解析器已删除');
      },
    });
  };

  // 处理上传模板
  const handleUploadTemplate = () => {
    message.info('上传模板功能开发中');
  };

  // 处理模态框确认
  const handleOk = () => {
    form.validateFields()
      .then(values => {
        if (editingId === null) {
          // 新增
          const newParser = {
            id: Date.now().toString(),
            ...values,
            createdAt: new Date().toISOString().split('T')[0],
            updatedAt: new Date().toISOString().split('T')[0],
          };
          setParsers([...parsers, newParser]);
          message.success('解析器已添加');
        } else {
          // 编辑
          setParsers(parsers.map(parser => 
            parser.id === editingId ? { 
              ...parser, 
              ...values,
              updatedAt: new Date().toISOString().split('T')[0],
            } : parser
          ));
          message.success('解析器已更新');
        }
        setIsModalVisible(false);
        setEditingId(null);
      })
      .catch(info => {
        console.log('表单验证失败:', info);
      });
  };

  // 处理模态框取消
  const handleCancel = () => {
    setIsModalVisible(false);
    setEditingId(null);
  };

  return (
    <div>
      <Card
        title="银行流水解析器管理"
        extra={
          <Space>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={handleAdd}
            >
              添加解析器
            </Button>
            <Button 
              icon={<UploadOutlined />} 
              onClick={handleUploadTemplate}
            >
              上传模板
            </Button>
          </Space>
        }
      >
        <Alert
          message="解析器说明"
          description="解析器用于处理不同银行和不同格式的银行流水文件，确保流水数据能正确导入系统。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Table 
          columns={columns} 
          dataSource={parsers} 
          rowKey="id" 
          pagination={{ pageSize: 10 }}
        />
      </Card>

      <Card style={{ marginTop: 16 }}>
        <Title level={4}>模板上传区</Title>
        <Paragraph>上传银行流水模板文件，系统将自动分析文件结构并生成解析器。</Paragraph>
        
        <Dragger 
          multiple={false}
          beforeUpload={() => false}
          onChange={(info) => {
            message.info(`准备处理文件: ${info.file.name}`);
          }}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">支持单个或批量上传。支持 Excel、PDF 格式的银行流水文件。</p>
        </Dragger>
      </Card>

      <Modal
        title={editingId === null ? "添加解析器" : "编辑解析器"}
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="解析器名称"
            rules={[{ required: true, message: '请输入解析器名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="bankCode"
            label="银行代码"
            rules={[{ required: true, message: '请选择银行代码' }]}
          >
            <Select>
              <Option value="ICBC">中国工商银行(ICBC)</Option>
              <Option value="CCB">中国建设银行(CCB)</Option>
              <Option value="ABC">中国农业银行(ABC)</Option>
              <Option value="BOC">中国银行(BOC)</Option>
              <Option value="COMM">交通银行(COMM)</Option>
              <Option value="PSBC">中国邮政储蓄银行(PSBC)</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="fileType"
            label="文件类型"
            rules={[{ required: true, message: '请选择文件类型' }]}
          >
            <Select>
              <Option value="excel">Excel</Option>
              <Option value="pdf">PDF</Option>
              <Option value="image">图片</Option>
              <Option value="txt">文本文件</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="format"
            label="格式版本"
            rules={[{ required: true, message: '请输入格式版本' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select>
              <Option value="active">已启用</Option>
              <Option value="inactive">已禁用</Option>
              <Option value="testing">测试中</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default BankStatementsParsers; 