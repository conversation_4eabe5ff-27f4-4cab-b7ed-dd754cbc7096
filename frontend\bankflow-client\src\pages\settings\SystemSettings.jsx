import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Button, Tabs, Switch, Select, message, Divider, Space, Typography, Radio, InputNumber, Alert } from 'antd';
import { SaveOutlined, ReloadOutlined, ApiOutlined, SettingOutlined, LockOutlined, UserOutlined } from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';
import { API_BASE_URL } from '../../config/api';

const { TabPane } = Tabs;
const { Title, Paragraph, Text } = Typography;
const { Option } = Select;

/**
 * 系统设置页面
 * 
 * @returns {JSX.Element} 系统设置页面
 */
const SystemSettings = () => {
  const [apiForm] = Form.useForm();
  const [preferencesForm] = Form.useForm();
  const [saving, setSaving] = useState(false);
  const [loading, setLoading] = useState(true);
  const { getUsername } = useAuth();
  const [passwordForm] = Form.useForm();

  useEffect(() => {
    // 模拟从API获取设置
    setTimeout(() => {
      // 模拟API配置
      apiForm.setFieldsValue({
        api_base_url: API_BASE_URL,
        api_timeout: 30000,
        api_version: 'v1',
        use_https: true
      });
      
      // 模拟用户偏好设置
      preferencesForm.setFieldsValue({
        theme: 'light',
        table_page_size: 10,
        date_format: 'YYYY-MM-DD',
        default_bank: 'icbc',
        auto_save: true,
        notification_enabled: true
      });
      
      setLoading(false);
    }, 1000);
  }, [apiForm, preferencesForm]);
  
  const handleApiSubmit = async (values) => {
    setSaving(true);
    
    try {
      // 模拟API保存
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('API设置保存成功');
      
      // 实际项目中应该调用API保存设置
      // await saveAPISettings(values);
    } catch (error) {
      message.error('保存失败，请重试');
      console.error('保存API设置失败:', error);
    } finally {
      setSaving(false);
    }
  };
  
  const handlePreferencesSubmit = async (values) => {
    setSaving(true);
    
    try {
      // 模拟API保存
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('用户偏好设置保存成功');
      
      // 实际项目中应该调用API保存设置
      // await savePreferences(values);
    } catch (error) {
      message.error('保存失败，请重试');
      console.error('保存用户偏好失败:', error);
    } finally {
      setSaving(false);
    }
  };
  
  const resetApiForm = () => {
    apiForm.resetFields();
    message.info('已重置为上次保存的设置');
  };
  
  const resetPreferencesForm = () => {
    preferencesForm.resetFields();
    message.info('已重置为上次保存的设置');
  };

  /**
   * 处理密码修改
   */
  const handlePasswordChange = async (values) => {
    setLoading(true);
    
    try {
      const { currentPassword, newPassword } = values;
      const currentUser = getUsername();
      
      if (!currentUser) {
        message.error('用户信息获取失败，请重新登录');
        return;
      }

      // 获取当前用户信息
      const registeredUsers = JSON.parse(localStorage.getItem('registeredUsers') || '[]');
      const userIndex = registeredUsers.findIndex(u => u.username === currentUser);
      
      // 检查是否是管理员用户（使用默认密码登录的）
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      const isAdmin = userInfo.isAdmin;
      
      if (isAdmin) {
        // 管理员用户验证默认密码
        if (currentPassword !== 'lgmis2012%') {
          message.error('当前密码错误');
          return;
        }
        
        // 为管理员创建新的用户记录
        const newAdminUser = {
          username: currentUser,
          password: newPassword,
          securityQuestion: '默认管理员安全问题：您的工号是？',
          securityAnswer: 'admin',
          registerTime: new Date().toISOString(),
          isAdmin: true
        };
        
        registeredUsers.push(newAdminUser);
        localStorage.setItem('registeredUsers', JSON.stringify(registeredUsers));
        
        // 更新用户信息，标记为非管理员（现在有自己的密码了）
        const updatedUserInfo = { ...userInfo, isAdmin: false };
        localStorage.setItem('userInfo', JSON.stringify(updatedUserInfo));
        
      } else {
        // 普通用户验证当前密码
        if (userIndex === -1) {
          message.error('用户不存在');
          return;
        }
        
        if (registeredUsers[userIndex].password !== currentPassword) {
          message.error('当前密码错误');
          return;
        }
        
        // 更新密码
        registeredUsers[userIndex].password = newPassword;
        localStorage.setItem('registeredUsers', JSON.stringify(registeredUsers));
      }
      
      message.success('密码修改成功！');
      passwordForm.resetFields();
      
    } catch (error) {
      console.error('密码修改失败:', error);
      message.error('密码修改失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ maxWidth: 800, margin: '0 auto', padding: '24px' }}>
      <Card>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div style={{ textAlign: 'center' }}>
            <SettingOutlined style={{ fontSize: '48px', color: '#0066cc', marginBottom: '16px' }} />
            <Title level={2}>系统设置</Title>
            <Text type="secondary">管理您的账户设置和系统偏好</Text>
          </div>

          <Divider />

          {/* 用户信息显示 */}
          <Card size="small" style={{ backgroundColor: '#f8f9fa' }}>
            <Space>
              <UserOutlined style={{ color: '#0066cc' }} />
              <Text strong>当前用户：</Text>
              <Text>{getUsername() || '未知用户'}</Text>
            </Space>
          </Card>

          <Divider orientation="left">
            <Space>
              <LockOutlined />
              <span>密码管理</span>
            </Space>
          </Divider>

          {/* 密码修改表单 */}
          <Card title="修改密码" size="small">
            <Form
              form={passwordForm}
              layout="vertical"
              onFinish={handlePasswordChange}
              requiredMark={false}
            >
              <Form.Item
                name="currentPassword"
                label="当前密码"
                rules={[
                  { required: true, message: '请输入当前密码' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="请输入当前密码"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                name="newPassword"
                label="新密码"
                rules={[
                  { required: true, message: '请输入新密码' },
                  { min: 6, message: '密码至少6个字符' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="请输入新密码（至少6位）"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                name="confirmPassword"
                label="确认新密码"
                dependencies={['newPassword']}
                rules={[
                  { required: true, message: '请确认新密码' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('newPassword') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('两次输入的密码不一致'));
                    },
                  }),
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="请再次输入新密码"
                  size="large"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  size="large"
                  style={{
                    background: 'linear-gradient(135deg, #0066cc 0%, #003d7a 100%)',
                    border: 'none',
                    borderRadius: '8px',
                    height: '48px',
                    fontWeight: '600'
                  }}
                >
                  修改密码
                </Button>
              </Form.Item>
            </Form>
          </Card>

          <Divider />

          {/* 系统信息 */}
          <Card title="系统信息" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text>系统名称：</Text>
                <Text strong>柳州供电局纪委流水分析软件</Text>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text>版本号：</Text>
                <Text strong>v2.0.0</Text>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text>开发单位：</Text>
                <Text strong>中国南方电网有限责任公司</Text>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text>技术支持：</Text>
                <Text strong>柳州供电局纪委</Text>
              </div>
            </Space>
          </Card>

          {/* 安全提示 */}
          <Card size="small" style={{ backgroundColor: '#e6f3ff', border: '1px solid #0066cc' }}>
            <Space direction="vertical">
              <Text strong style={{ color: '#0066cc' }}>🔒 安全提示</Text>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                • 请定期更换密码，建议使用包含字母、数字的复杂密码<br/>
                • 不要在公共场所或他人面前输入密码<br/>
                • 如发现账户异常，请立即联系系统管理员<br/>
                • 系统会自动保存您的操作记录，请规范使用
              </Text>
            </Space>
          </Card>
        </Space>
      </Card>
    </div>
  );
};

export default SystemSettings; 