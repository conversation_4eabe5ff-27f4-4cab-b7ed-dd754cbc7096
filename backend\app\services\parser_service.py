"""
银行流水解析服务 - 统一插件系统架构
"""
import os
import logging
import traceback
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session

from .data_storage_service import DataStorageService
from .parser_plugin_system.core.plugin_manager import PluginManager

logger = logging.getLogger(__name__)

class ParserService:
    """
    解析服务类 - 统一插件系统架构
    """
    
    def __init__(self, template_dir: str = None):
        """
        初始化解析服务
        
        Args:
            template_dir: 模板目录路径（已弃用，保持兼容性）
        """
        self.data_storage = DataStorageService()
        self.plugin_manager = None
        self._initialize_plugin_manager()
    
    def _initialize_plugin_manager(self):
        """初始化插件管理器"""
        try:
            logger.info("初始化插件管理器...")
            self.plugin_manager = PluginManager()
            self.plugin_manager.start()
            
            loaded_plugins = self.plugin_manager.list_loaded_plugins()
            logger.info(f"插件管理器启动成功，已加载插件: {loaded_plugins}")
            
        except Exception as e:
            logger.error(f"插件管理器初始化失败: {e}")
            self.plugin_manager = None
    
    def parse_file(self, file_path: str, db: Session = None) -> Optional[Dict[str, Any]]:
        """
        解析银行流水文件 - 使用插件系统
        
        Args:
            file_path: 文件路径
            db: 数据库会话
            
        Returns:
            Optional[Dict[str, Any]]: 解析结果
        """
        try:
            logger.info(f"开始解析文件: {os.path.basename(file_path)}")
            
            # 检查插件管理器是否可用
            if not self.plugin_manager:
                logger.error("插件管理器未初始化，无法解析文件")
                return None
            
            # 使用插件管理器选择最佳解析器
            best_plugin = self.plugin_manager.get_best_plugin_for_file(file_path)
            
            if not best_plugin:
                logger.error("未找到合适的解析器插件")
                return None
            
            logger.info(f"选择解析器插件: {best_plugin}")
            
            # 使用插件执行解析
            result = self.plugin_manager.execute_plugin(best_plugin, file_path)
            
            if result and result.get('success'):
                total_transactions = result.get('summary', {}).get('total_transactions', 0) or len(result.get('transactions', []))
                logger.info(f"解析成功: {total_transactions}条交易")
                return result
            else:
                logger.warning(f"插件{best_plugin}解析失败: {result.get('message', '未知错误')}")
                return None
            
        except Exception as e:
            logger.error(f"解析文件时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return None
    
    def get_supported_banks(self) -> List[str]:
        """
        获取支持的银行列表
            
        Returns:
            List[str]: 支持的银行名称列表
        """
        banks = set()
        
        if self.plugin_manager:
            # 从插件系统获取支持的银行
            available_plugins = self.plugin_manager.registry.list_plugins()
            for plugin_name in available_plugins:
                plugin_info = self.plugin_manager.registry.get_plugin(plugin_name)
                if plugin_info:
                    # 根据插件名称推断银行
                    if 'icbc' in plugin_name.lower():
                        banks.add("中国工商银行")
                    elif 'ccb' in plugin_name.lower():
                        banks.add("中国建设银行")
                    elif 'beibuwan' in plugin_name.lower():
                        banks.add("北部湾银行")
        
        return list(banks) if banks else ["中国工商银行"]  # 保持向后兼容
    
    def validate_file_format(self, file_path: str) -> bool:
        """
        验证文件格式是否受支持
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 文件格式是否受支持
        """
        try:
            if not os.path.exists(file_path):
                return False
            
            # 检查文件扩展名
            _, ext = os.path.splitext(file_path.lower())
            return ext in ['.xlsx', '.xls']
            
        except Exception as e:
            logger.error(f"验证文件格式时出错: {str(e)}")
            return False 