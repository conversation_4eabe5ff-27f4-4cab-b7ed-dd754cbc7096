{"name": "spdb_format1_plugin", "display_name": "上海浦东发展银行Format1解析器插件", "version": "1.0.0", "description": "解析上海浦东发展银行流水文件，支持XLS和XLSX格式，自动适应字段名差异", "author": "银行流水分析系统开发团队", "bank_name": "上海浦东发展银行", "bank_aliases": ["浦发银行", "SPDB"], "supported_formats": ["XLS", "XLSX"], "supported_file_extensions": [".xls", ".xlsx"], "confidence_threshold": 0.8, "priority": 10, "category": "bank_parser", "tags": ["浦发银行", "上海浦东发展银行", "SPDB", "银行流水", "解析器"], "requirements": ["pandas>=1.3.0", "openpyxl>=3.0.0"], "field_mappings": {"cardholder_name": ["账户中文名", "户名"], "card_number": "", "account_number": "账号", "dr_cr_flag": {"0": "支出", "1": "收入"}, "transaction_method": ["摘要代码", "摘要"], "remark1": "附言", "counterpart_bank": "对方行名"}, "detection_keywords": ["账户中文名", "户名", "账号", "借贷标志", "摘要代码", "摘要", "附言", "对方行名", "浦发银行", "上海浦东发展银行", "SPDB"], "created_date": "2024-01-15", "last_modified": "2024-01-15", "license": "MIT", "documentation": "README.md", "test_files": ["1.xls", "2.xlsx"]}