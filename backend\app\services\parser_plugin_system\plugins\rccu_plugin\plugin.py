#!/usr/bin/env python3
"""
农村信用社解析器插件（RCCU）

要求与规则：
- 兼容 .xls/.xlsx
- 多子表：挑选包含“交易金额”的表；若多张，优先有效行数多者
- 字段映射：
  - “存/取” => 收支（存=收，取=支）
  - “现/转” => 交易方式
  - “交易摘要” => 备注1
  - 对方银行字段不存在 => 留空
  - 交易时间形如"(12:45:23:572272)"，仅保留时分秒：12:45:23
"""

import os
import logging
import pandas as pd
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from collections import Counter
import time

# 兼容导入
try:
    from ...core.plugin_interface import BasePlugin
except ImportError:
    class BasePlugin:  # 简化占位
        def __init__(self):
            self.name = "rccu_plugin"
            self.version = "1.0.0"
            self.description = "农村信用社解析器插件"
            self.bank_name = "农村信用社"

logger = logging.getLogger(__name__)


class RCCUPlugin(BasePlugin):
    """农村信用社解析器插件"""

    def __init__(self, file_path: str = None):
        super().__init__()
        self.plugin_id = "rccu_plugin"
        self.plugin_name = "农村信用社解析器插件"
        self.supported_banks = ["农村信用社"]
        self.version = "1.0.0"
        self.description = "解析农村信用社流水，按用户规则映射字段"
        self.file_path = file_path

        logger.info(f"🚀 RCCU_PLUGIN 初始化 {self.plugin_name} v{self.version}")

    # ---------- 入口与能力 ----------
    def can_parse(self, file_path: str) -> float:
        """通过关键字段快速判断可解析性：交易金额 + （存/取 或 现/转 或 交易摘要）"""
        try:
            if not os.path.exists(file_path):
                return 0.0
            if not file_path.lower().endswith((".xls", ".xlsx")):
                return 0.0
            excel_data = pd.read_excel(file_path, sheet_name=None, header=None, nrows=12, dtype=str)
            for _, df in excel_data.items():
                if df is None or df.empty:
                    continue
                head = df.astype(str)
                hit_amount = (head.apply(lambda s: s.str.contains('交易金额', na=False)).any()).any()
                hit_sd = (head.apply(lambda s: s.str.contains('存/取|存取', na=False)).any()).any()
                hit_xz = (head.apply(lambda s: s.str.contains('现/转|现转', na=False)).any()).any()
                hit_zy = (head.apply(lambda s: s.str.contains('交易摘要', na=False)).any()).any()
                if hit_amount and (hit_sd or hit_xz or hit_zy):
                    return 0.92
            return 0.0
        except Exception:
            return 0.0

    def parse(self, file_path: str) -> Dict[str, Any]:
        return self.parse_file(file_path)

    def parse_file(self, file_path: str) -> Dict[str, Any]:
        try:
            logger.info(f"🚀 RCCU_PLUGIN 开始解析: {file_path}")
            sheets = self._find_valid_sheets(file_path)
            if not sheets:
                raise ValueError("未找到包含‘交易金额’字段的工作表")

            merged_accounts: Dict[str, Dict[str, Any]] = {}
            all_transactions: List[Dict[str, Any]] = []

            for sheet in sheets:
                raw_df = self._read_sheet_no_header(file_path, sheet)
                header_row = self._detect_header_row(raw_df)
                df = self._read_sheet_with_header(file_path, sheet, header_row)
                if df is None or df.empty:
                    continue

                accounts, transactions, _diag = self._parse_sheet(df, sheet)
                # 合并账户
                for a in accounts:
                    key = f"{a.get('holder_name','')}_{a.get('account_number','')}_{a.get('data_source','')}"
                    if key not in merged_accounts:
                        merged_accounts[key] = a
                    else:
                        merged_accounts[key]['total_inflow'] += a.get('total_inflow', 0.0)
                        merged_accounts[key]['total_outflow'] += a.get('total_outflow', 0.0)
                        merged_accounts[key]['transaction_count'] += a.get('transaction_count', 0)
                all_transactions.extend(transactions)

            result = {
                'success': True,
                'accounts': list(merged_accounts.values()),
                'transactions': all_transactions,
                'summary': {
                    'total_accounts': len(merged_accounts),
                    'total_transactions': len(all_transactions),
                    'file_path': file_path,
                    'parsed_at': datetime.now().isoformat(),
                    'sheets': sheets
                }
            }
            logger.info(f"✅ RCCU_PLUGIN 解析完成: {len(result['accounts'])}个账户, {len(all_transactions)}条交易")
            return result

        except Exception as e:
            logger.error(f"❌ RCCU_PLUGIN 解析失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'accounts': [],
                'transactions': []
            }

    # ---------- Sheet与列识别 ----------
    def _find_valid_sheets(self, file_path: str) -> List[str]:
        """选择包含‘交易金额’的子表，按有效行数降序"""
        candidates: List[Tuple[str, int]] = []
        is_xls = file_path.lower().endswith('.xls') and not file_path.lower().endswith('.xlsx')
        try:
            if is_xls:
                import xlrd
                book = xlrd.open_workbook(file_path, formatting_info=False)
                sheet_names = book.sheet_names()
            else:
                xfile = pd.ExcelFile(file_path)
                sheet_names = xfile.sheet_names
        except Exception:
            return []

        for sheet_name in sheet_names:
            try:
                df_raw = self._read_sheet_no_header(file_path, sheet_name)
                # 扫描前30行以提高命中率
                scan_rows = min(30, df_raw.shape[0])
                head = df_raw.head(scan_rows).astype(str)
                # 放宽金额关键词：优先“交易金额”，其次“金额”
                has_amount_kw = (head.apply(lambda s: s.str.contains('交易金额|金额', na=False)).any()).any()
                if not has_amount_kw:
                    continue
                header_row = self._detect_header_row(df_raw)
                df = self._read_sheet_with_header(file_path, sheet_name, header_row)
                mapping = self._find_column_mapping(df)
                if 'transaction_amount' not in mapping:
                    continue
                amt_col = mapping['transaction_amount']
                series = df.iloc[:, amt_col].astype(str)
                valid_rows = int(series.map(lambda x: str(x).strip() not in ('', 'nan')).sum())
                candidates.append((sheet_name, valid_rows))
            except Exception:
                continue
        candidates.sort(key=lambda x: -x[1])
        return [c[0] for c in candidates]

    def _read_sheet_no_header(self, file_path: str, sheet_name: str) -> pd.DataFrame:
        """读取原始子表为DataFrame（不设表头），兼容xls/xlsx"""
        if file_path.lower().endswith('.xls') and not file_path.lower().endswith('.xlsx'):
            try:
                import xlrd
                book = xlrd.open_workbook(file_path, formatting_info=False)
                sh = book.sheet_by_name(sheet_name)
                rows = []
                for r in range(sh.nrows):
                    row_vals = [sh.cell_value(r, c) for c in range(sh.ncols)]
                    rows.append(row_vals)
                return pd.DataFrame(rows)
            except Exception:
                return pd.DataFrame()
        else:
            return pd.read_excel(file_path, sheet_name=sheet_name, header=None, dtype=str)

    def _read_sheet_with_header(self, file_path: str, sheet_name: str, header_row: int) -> pd.DataFrame:
        """按指定表头行读取为DataFrame，兼容xls/xlsx"""
        if file_path.lower().endswith('.xls') and not file_path.lower().endswith('.xlsx'):
            try:
                import xlrd
                book = xlrd.open_workbook(file_path, formatting_info=False)
                sh = book.sheet_by_name(sheet_name)
                if sh.nrows == 0:
                    return pd.DataFrame()
                headers = [str(sh.cell_value(header_row, c)) for c in range(sh.ncols)]
                data_rows = []
                for r in range(header_row + 1, sh.nrows):
                    data_rows.append([sh.cell_value(r, c) for c in range(sh.ncols)])
                df = pd.DataFrame(data_rows, columns=headers)
                return df
            except Exception:
                return pd.DataFrame()
        else:
            return pd.read_excel(file_path, sheet_name=sheet_name, header=header_row, dtype=str)

    def _detect_header_row(self, df_no_header: pd.DataFrame) -> int:
        try:
            max_hits = -1
            candidate_row = 0
            keywords = ['交易金额', '余额', '交易摘要', '存/取', '现/转', '交易日期', '交易时间', '账号']
            limit = min(20, df_no_header.shape[0])
            for i in range(limit):
                row = df_no_header.iloc[i].astype(str)
                hits = sum(1 for v in row if any(k in v for k in keywords))
                if hits > max_hits:
                    max_hits = hits
                    candidate_row = i
            return candidate_row
        except Exception:
            return 0

    def _find_column_mapping(self, df: pd.DataFrame) -> Dict[str, int]:
        mapping: Dict[str, int] = {}
        # 优先用列名精确匹配（防止把数据内容误当表头）
        try:
            if isinstance(df.columns, pd.MultiIndex):
                df = df.copy()
                df.columns = [''.join([str(x) for x in col if str(x) != 'nan']) for col in df.columns]

            for j, col in enumerate(list(df.columns)):
                col_str = str(col)
                if any(k in col_str for k in ['户名', '姓名', '客户名称', '持卡人']):
                    mapping.setdefault('cardholder_name', j)
                if any(k in col_str for k in ['账号', '账户', '主账户账号', '银行卡号']):
                    mapping.setdefault('account_number', j)
                if any(k in col_str for k in ['卡号', '流水卡号']):
                    mapping.setdefault('card_number', j)
                if any(k in col_str for k in ['交易日期', '日期']):
                    mapping.setdefault('transaction_date', j)
                if any(k in col_str for k in ['交易时间', '时间']):
                    mapping.setdefault('transaction_time', j)
                if ('交易金额' in col_str) or (('金额' in col_str or '发生额' in col_str or '交易金额(元)' in col_str) and ('余' not in col_str)):
                    mapping.setdefault('transaction_amount', j)
                if any(k in col_str for k in ['余额', '交易后余额', '账户余额', '余额(元)', '可用余额']):
                    mapping.setdefault('account_balance', j)
                if re.search(r'存/取|存取', col_str):
                    mapping.setdefault('dr_cr_flag', j)
                if re.search(r'现/转|现转', col_str):
                    mapping.setdefault('transaction_method', j)
                if '交易摘要' in col_str:
                    mapping.setdefault('remark1', j)
                if any(k in col_str for k in ['对方账户', '对方账号']):
                    mapping.setdefault('counterpart_account', j)
                if any(k in col_str for k in ['对方户名', '对方名称']):
                    mapping.setdefault('counterpart_name', j)

        except Exception:
            pass

        # 若仍有缺失，再用内容特征兜底
        mapping = self._auto_map_by_content(df, mapping)
        return mapping

    def _auto_map_by_content(self, df: pd.DataFrame, mapping: Dict[str, int]) -> Dict[str, int]:
        """当表头匹配失败时，通过内容特征自动推断关键列"""
        try:
            sample = df.head(200)
            col_count = sample.shape[1]

            # 检测“存/取”列
            if 'dr_cr_flag' not in mapping:
                for j in range(col_count):
                    vals = sample.iloc[:, j].astype(str).str.strip()
                    hits = vals.isin(['存', '取']).sum()
                    if hits >= max(5, int(0.1 * len(sample))):
                        mapping['dr_cr_flag'] = j
                        break

            # 检测“现/转”列
            if 'transaction_method' not in mapping:
                for j in range(col_count):
                    vals = sample.iloc[:, j].astype(str).str.strip()
                    hits = vals.isin(['现', '转']).sum()
                    if hits >= max(5, int(0.1 * len(sample))):
                        mapping['transaction_method'] = j
                        break

            # 若缺少“交易日期”，按日期正则自动识别（yyyy-mm-dd / yyyy/mm/dd / yyyymmdd / yyyy.mm.dd / 中文日期）
            if 'transaction_date' not in mapping:
                date_patterns = [
                    r"\b\d{4}-\d{1,2}-\d{1,2}\b",
                    r"\b\d{4}/\d{1,2}/\d{1,2}\b",
                    r"\b\d{8}\b",
                    r"\b\d{4}\.\d{1,2}\.\d{1,2}\b",
                    r"\d{4}年\d{1,2}月\d{1,2}日"
                ]
                for j in range(col_count):
                    vals = sample.iloc[:, j].astype(str)
                    hits = 0
                    for p in date_patterns:
                        hits += vals.str.contains(p, regex=True, na=False).sum()
                    if hits >= max(5, int(0.1 * len(sample))):
                        mapping['transaction_date'] = j
                        break

            # 若缺少“交易时间”，按时间正则自动识别（HH:MM(:SS) 或 (HH:MM:SS:ms)）
            if 'transaction_time' not in mapping:
                time_patterns = [
                    r"\b\d{1,2}:\d{1,2}:\d{1,2}\b",
                    r"\b\d{1,2}:\d{1,2}\b",
                    r"\(\d{1,2}:\d{1,2}:\d{1,2}:[^\)]*\)"
                ]
                for j in range(col_count):
                    vals = sample.iloc[:, j].astype(str)
                    hits = 0
                    for p in time_patterns:
                        hits += vals.str.contains(p, regex=True, na=False).sum()
                    if hits >= max(5, int(0.1 * len(sample))):
                        mapping['transaction_time'] = j
                        break

            # 数值列检测
            def parse_num(x: str) -> Optional[float]:
                try:
                    s = re.sub(r'[¥$,，,\s]', '', str(x))
                    if s in ['', 'nan', 'None']:
                        return None
                    return float(s)
                except Exception:
                    return None

            numeric_info = []  # (col_index, non_null_count, variance, name_hint)
            for j in range(col_count):
                nums = sample.iloc[:, j].apply(parse_num)
                vals = [v for v in nums if v is not None]
                if len(vals) >= max(5, int(0.2 * len(sample))):
                    var = float(pd.Series(vals).var()) if len(vals) > 1 else 0.0
                    name = str(df.columns[j])
                    numeric_info.append((j, len(vals), var, name))

            # 余额优先按列名命中“余”，否则选数值列中非空最多且方差较小者
            if 'account_balance' not in mapping and numeric_info:
                by_name = [t for t in numeric_info if ('余' in t[3] or '余额' in t[3])]
                if by_name:
                    mapping['account_balance'] = by_name[0][0]
                else:
                    # 选择非空多且方差较小的列作为余额
                    candidate = sorted(numeric_info, key=lambda x: (-x[1], x[2]))[0]
                    mapping['account_balance'] = candidate[0]

            # 金额列：不同于余额的另一数值列，优先方差较大的
            if 'transaction_amount' not in mapping and numeric_info:
                avoid = mapping.get('account_balance')
                others = [t for t in numeric_info if t[0] != avoid]
                if others:
                    amt = sorted(others, key=lambda x: (-x[2], -x[1]))[0]
                    mapping['transaction_amount'] = amt[0]

            # 备注列：找包含“摘要”的列名，否则选择文本列长度较长的一列
            if 'remark1' not in mapping:
                # 按列名
                for j in range(col_count):
                    if '摘要' in str(df.columns[j]):
                        mapping['remark1'] = j
                        break
                if 'remark1' not in mapping:
                    # 内容长度较长
                    text_scores = []
                    for j in range(col_count):
                        vals = sample.iloc[:, j].astype(str)
                        avg_len = vals.str.len().mean()
                        text_scores.append((avg_len, j))
                    if text_scores:
                        mapping['remark1'] = sorted(text_scores, reverse=True)[0][1]

            return mapping
        except Exception:
            return mapping

    # ---------- 单元提取 ----------
    def _extract_cardholder_name(self, row: pd.Series, m: Dict[str, int]) -> str:
        if 'cardholder_name' not in m:
            return ""
        val = row.iloc[m['cardholder_name']]
        return "" if pd.isna(val) else str(val).strip()

    def _extract_account_number(self, row: pd.Series, m: Dict[str, int]) -> str:
        if 'account_number' not in m:
            return ""
        val = row.iloc[m['account_number']]
        return "" if pd.isna(val) else str(val).strip()

    def _extract_card_number(self, row: pd.Series, m: Dict[str, int]) -> str:
        if 'card_number' not in m:
            return ""
        val = row.iloc[m['card_number']]
        s = "" if pd.isna(val) else str(val).strip()
        # 去除小数与科学计数
        if '.' in s and s.replace('.', '').isdigit():
            s = s.split('.')[0]
        return s

    def _extract_transaction_date(self, row: pd.Series, m: Dict[str, int]) -> Optional[datetime]:
        if 'transaction_date' not in m:
            return None
        raw = row.iloc[m['transaction_date']]
        if pd.isna(raw):
            return None
        s = str(raw).strip()
        # 去掉可能的时间
        s = s.split(' ')[0]
        for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%Y%m%d', '%Y.%m.%d', '%Y年%m月%d日']:
            try:
                return datetime.strptime(s, fmt)
            except ValueError:
                continue
        try:
            return pd.to_datetime(s)
        except Exception:
            return None

    def _extract_transaction_time(self, row: pd.Series, m: Dict[str, int]) -> str:
        if 'transaction_time' not in m:
            return "00:00:00"
        raw = row.iloc[m['transaction_time']]
        s = "" if pd.isna(raw) else str(raw).strip()
        return self._format_rccu_time(s)

    def _format_rccu_time(self, s: str) -> str:
        """将 (12:45:23:572272) 规范化为 12:45:23（仅保留时分秒）"""
        if not s:
            return "00:00:00"
        s = s.strip()
        s = s[1:-1] if (s.startswith('(') and s.endswith(')')) else s
        parts = s.split(':')
        if len(parts) >= 3:
            hh, mm, ss = parts[0], parts[1], parts[2]
            return f"{hh}:{mm}:{ss}"
        # 纯数字如 124523 -> HHMMSS
        if re.fullmatch(r"\d{6}", s):
            return f"{s[:2]}:{s[2:4]}:{s[4:6]}"
        return "00:00:00"

    def _extract_transaction_amount(self, row: pd.Series, m: Dict[str, int]) -> float:
        if 'transaction_amount' not in m:
            return 0.0
        raw = row.iloc[m['transaction_amount']]
        return self._parse_amount(str(raw))

    def _extract_account_balance(self, row: pd.Series, m: Dict[str, int]) -> float:
        if 'account_balance' not in m:
            return 0.0
        raw = row.iloc[m['account_balance']]
        return self._parse_amount(str(raw))

    def _extract_method(self, row: pd.Series, m: Dict[str, int]) -> str:
        if 'transaction_method' not in m:
            return ""
        raw = row.iloc[m['transaction_method']]
        return "" if pd.isna(raw) else str(raw).strip()

    def _extract_drcr(self, row: pd.Series, m: Dict[str, int], amount: float) -> str:
        """存=收，取=支；若无则按金额正负"""
        if 'dr_cr_flag' in m:
            raw = row.iloc[m['dr_cr_flag']]
            if not pd.isna(raw):
                s = str(raw).strip()
                if '存' in s:
                    return '收'
                if '取' in s:
                    return '支'
        if amount > 0:
            return '收'
        if amount < 0:
            return '支'
        return '未知'

    def _extract_remarks(self, row: pd.Series, m: Dict[str, int]) -> Dict[str, str]:
        r1 = ''
        if 'remark1' in m:
            raw = row.iloc[m['remark1']]
            if not pd.isna(raw):
                r1 = str(raw).strip()
        return {'remark1': r1, 'remark2': '', 'remark3': ''}

    def _clean_id_string(self, s: str) -> str:
        """清洗账号/对方账号等字段，去除小数与空白，保留原始数字字符串"""
        if s is None:
            return ''
        text = str(s).strip()
        # 处理像 6228... 或 1.23456789E+17 等
        if re.fullmatch(r"\d+\.0+", text):
            text = text.split('.')[0]
        if re.search(r"e\+|E\+", text):
            try:
                num = int(float(text))
                return str(num)
            except Exception:
                pass
        return text

    def _extract_counterparty_account(self, row: pd.Series, m: Dict[str, int]) -> str:
        if 'counterpart_account' not in m:
            return ''
        raw = row.iloc[m['counterpart_account']]
        return '' if pd.isna(raw) else self._clean_id_string(raw)

    def _extract_counterparty_name(self, row: pd.Series, m: Dict[str, int]) -> str:
        if 'counterpart_name' not in m:
            return ''
        raw = row.iloc[m['counterpart_name']]
        return '' if pd.isna(raw) else str(raw).strip()

    def _parse_amount(self, s: str) -> float:
        try:
            if not s or s in ['nan', 'None']:
                return 0.0
            s = re.sub(r'[¥$,，,\s]', '', str(s))
            neg = s.startswith('-')
            if neg:
                s = s[1:]
            val = float(s)
            return -val if neg else val
        except Exception:
            return 0.0

    # ---------- 表解析 ----------
    def _parse_sheet(self, df: pd.DataFrame, sheet_name: str) -> tuple[List[Dict], List[Dict], Dict[str, Any]]:
        m = self._find_column_mapping(df)
        if 'transaction_amount' not in m:
            return [], [], {'sheet': sheet_name, 'reason': 'no transaction_amount'}

        accounts: Dict[str, Dict[str, Any]] = {}
        transactions: List[Dict[str, Any]] = []
        seq = 0
        last_holder = ''
        last_account = ''
        last_date: Optional[datetime] = None

        for idx, row in df.iterrows():
            try:
                holder = self._extract_cardholder_name(row, m) or last_holder or sheet_name
                last_holder = holder or last_holder

                account = self._extract_account_number(row, m) or last_account
                last_account = account or last_account
                # 账号兜底：若无账号但有卡号，则以卡号作为账号；再无则用子表名+行号占位，避免整表被丢弃
                card_no = self._extract_card_number(row, m)
                if not account:
                    if card_no:
                        account = card_no
                    else:
                        account = f"{sheet_name}_ACC"
                if not account:
                    continue

                # card_no 已在上方提取
                dt = self._extract_transaction_date(row, m)
                if not dt and last_date is not None:
                    dt = last_date
                if not dt:
                    continue
                else:
                    last_date = dt

                tm = self._extract_transaction_time(row, m)
                amt = self._extract_transaction_amount(row, m)
                bal = self._extract_account_balance(row, m)
                method = self._extract_method(row, m)
                drcr = self._extract_drcr(row, m, amt)
                remarks = self._extract_remarks(row, m)
                cp_name = self._extract_counterparty_name(row, m)
                cp_acc = self._extract_counterparty_account(row, m)

                acc_key = f"{holder}|{account}|{sheet_name}"
                if acc_key not in accounts:
                    accounts[acc_key] = {
                        'cardholder_name': holder,
                        'holder_name': holder,
                        'bank_name': '农村信用社',
                        'account_number': account,
                        'card_number': '',
                        'total_inflow': 0.0,
                        'total_outflow': 0.0,
                        'transaction_count': 0,
                        'data_source': sheet_name,
                        'earliest_date': dt,
                        'latest_date': dt,
                        'latest_datetime': None,
                        'latest_balance': None,
                        'latest_seq': None,
                        'card_numbers_freq': Counter(),
                    }

                if drcr == '收':
                    accounts[acc_key]['total_inflow'] += abs(amt)
                elif drcr == '支':
                    accounts[acc_key]['total_outflow'] += abs(amt)
                accounts[acc_key]['transaction_count'] += 1

                if dt < accounts[acc_key]['earliest_date']:
                    accounts[acc_key]['earliest_date'] = dt
                if dt > accounts[acc_key]['latest_date']:
                    accounts[acc_key]['latest_date'] = dt

                # 账户期末余额追踪
                try:
                    tm_base = tm if tm and len(tm) >= 5 else '00:00:00'
                    current_dt = datetime.strptime(f"{dt.strftime('%Y-%m-%d')} {tm_base[:8]}", '%Y-%m-%d %H:%M:%S')
                except Exception:
                    current_dt = None
                prev_dt = accounts[acc_key]['latest_datetime']
                if prev_dt is None or (current_dt and current_dt > prev_dt):
                    accounts[acc_key]['latest_datetime'] = current_dt
                    accounts[acc_key]['latest_balance'] = bal
                    accounts[acc_key]['latest_seq'] = seq
                elif current_dt and prev_dt and current_dt == prev_dt and seq > (accounts[acc_key]['latest_seq'] or -1):
                    accounts[acc_key]['latest_balance'] = bal
                    accounts[acc_key]['latest_seq'] = seq

                if card_no:
                    accounts[acc_key]['card_numbers_freq'][card_no] += 1

                seq += 1
                transactions.append({
                    'sequence_number': seq,
                    'cardholder_name': holder,
                    'holder_name': holder,
                    'bank_name': '农村信用社',
                    'account_number': account,
                    'card_number': card_no,
                    'transaction_date': dt.strftime('%Y-%m-%d'),
                    'transaction_time': tm,
                    'transaction_type': method,
                    'transaction_method': method,
                    'transaction_amount': abs(amt),
                    'amount': abs(amt),
                    'account_balance': bal,
                    'balance_after': bal,
                    'balance_amount': bal,
                    'debit_credit_indicator': drcr,
                    'income_expense_flag': drcr,
                    'dr_cr_flag': drcr,
                    'counterpart_name': cp_name,
                    'counterpart_account': cp_acc,
                    'counterpart_bank': '',  # 留空
                    'counterparty_name': cp_name,
                    'counterparty_account': cp_acc,
                    'counterparty_bank': '',
                    'remark1': remarks['remark1'],  # 交易摘要
                    'remark2': remarks.get('remark2', ''),
                    'remark3': remarks.get('remark3', ''),
                    'sheet_source': sheet_name
                })
            except Exception:
                continue

        # 输出账户列表与清理
        account_list = list(accounts.values())
        for a in account_list:
            # 账户层面的展示卡号：取频次最高
            if a.get('card_numbers_freq'):
                try:
                    a['card_number'] = a['card_numbers_freq'].most_common(1)[0][0]
                except Exception:
                    pass
            # 期末余额
            final_bal = a.get('latest_balance')
            final_bal = 0.0 if final_bal is None else final_bal
            a['account_balance'] = final_bal
            a['balance'] = final_bal
            # 日期范围
            try:
                a['date_range'] = f"{a['earliest_date'].strftime('%Y-%m-%d')} 至 {a['latest_date'].strftime('%Y-%m-%d')}"
            except Exception:
                a['date_range'] = '未知'
            # 清理内部字段
            a.pop('latest_datetime', None)
            a.pop('latest_balance', None)
            a.pop('latest_seq', None)
            a.pop('card_numbers_freq', None)

        account_list.sort(key=lambda x: (str(x.get('holder_name','')), str(x.get('account_number','')), str(x.get('card_number',''))))
        return account_list, transactions, {'sheet': sheet_name, 'columns': list(df.columns)}

    # ---------- 抽象方法实现 ----------
    def calculate_confidence(self, file_path: str) -> float:
        """用于快速评估：返回0-1之间分值，供选择器比较"""
        try:
            score = self.can_parse(file_path)
            # can_parse 已返回 0.0~1.0
            return float(score)
        except Exception:
            return 0.0

    def get_health_status(self) -> Dict[str, Any]:
        return {
            'healthy': True,
            'last_check': time.time(),
            'error_count': 0,
            'plugin_version': self.version
        }



    def extract_sample(self, file_path: str = None, limit: int = 10) -> Dict[str, Any]:
        """
        提取样本数据用于快速置信度评估
        农信社专用版本 - 支持4维度评估
        
        Args:
            file_path: 文件路径
            limit: 样本数量限制
            
        Returns:
            Dict: 包含样本账户和交易的字典
        """
        try:
            target_file = file_path or self.file_path
            if not target_file:
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
            
            logger.info(f"农信社解析器开始提取样本数据，限制条数: {limit}")
            
            # 快速读取Excel文件前几行
            try:
                if target_file.endswith('.xlsx'):
                    df = pd.read_excel(target_file, nrows=limit * 2)
                else:
                    df = pd.read_excel(target_file, nrows=limit * 2)
                
                if df.empty:
                    return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
                
                # 提取样本账户信息
                sample_accounts = []
                sample_transactions = []
                
                # 从第一行提取基本信息
                if len(df) > 0:
                    first_row = df.iloc[0]
                    
                    # 尝试多种可能的字段名
                    holder_name_fields = ['持卡人姓名', '户名', '姓名', '账户名称', '持卡人']
                    account_fields = ['账号', '账户号', '卡号', '帐号']
                    
                    holder_name = ""
                    account_number = ""
                    
                    # 查找持卡人姓名
                    for field in holder_name_fields:
                        if field in first_row and pd.notna(first_row[field]):
                            holder_name = str(first_row[field]).strip()
                            break
                    
                    # 查找账号
                    for field in account_fields:
                        if field in first_row and pd.notna(first_row[field]):
                            account_number = str(first_row[field]).strip()
                            break
                    
                    # 如果没有找到有效数据，使用默认值
                    if not holder_name:
                        holder_name = "农信社测试用户"
                    if not account_number:
                        account_number = "****************"
                    
                    sample_account = {
                        'cardholder_name': holder_name,  # 🔧 4维度姓名识别需要
                        'holder_name': holder_name,
                        'account_number': account_number,
                        'card_number': "",
                        'bank_name': '农村信用合作社',
                        'account_type': '个人账户' if len(holder_name) <= 4 else '企业账户'
                    }
                    sample_accounts.append(sample_account)
                
                # 提取样本交易数据
                transaction_count = 0
                for idx, row in df.iterrows():
                    if transaction_count >= limit:
                        break
                    
                    try:
                        # 尝试多种可能的字段名
                        date_fields = ['交易日期', '日期', '交易时间', '记账日期']
                        amount_fields = ['交易金额', '金额', '发生额', '交易额']
                        balance_fields = ['余额', '账户余额', '当前余额', '结余']
                        
                        transaction_date = ""
                        amount = 0.0
                        balance = 0.0
                        
                        # 查找交易日期
                        for field in date_fields:
                            if field in row and pd.notna(row[field]):
                                transaction_date = str(row[field]).strip()
                                break
                        
                        # 查找交易金额
                        for field in amount_fields:
                            if field in row and pd.notna(row[field]):
                                try:
                                    amount = float(row[field])
                                    break
                                except:
                                    continue
                        
                        # 查找余额
                        for field in balance_fields:
                            if field in row and pd.notna(row[field]):
                                try:
                                    balance = float(row[field])
                                    break
                                except:
                                    continue
                        
                        # 基本验证
                        if not transaction_date or amount == 0:
                            continue
                        
                        # 构建样本交易
                        transaction = {
                            'cardholder_name': holder_name,  # 🔧 4维度姓名识别需要
                            'holder_name': holder_name,
                            'account_number': account_number,
                            'transaction_date': transaction_date,
                            'transaction_amount': abs(amount),
                            'balance': balance,  # 🔧 4维度金额解析需要
                            'dr_cr_flag': '收' if amount >= 0 else '支',
                            'currency': 'CNY',
                            'transaction_method': '农信社交易',
                            'bank_name': '农村信用合作社'
                        }
                        
                        sample_transactions.append(transaction)
                        transaction_count += 1
                        
                    except Exception as e:
                        logger.debug(f"跳过第{idx+1}行: {str(e)}")
                        continue
                
                return {
                    'accounts': sample_accounts,
                    'transactions': sample_transactions[:limit],
                    'metadata': {
                        'sample_size': len(sample_transactions),
                        'evaluation_mode': 'extract_sample',
                        'plugin_name': self.name,
                        'bank_name': '农村信用合作社'
                    }
                }
                
            except Exception as e:
                logger.error(f"农信社解析器样本提取失败: {str(e)}")
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0, 'error': str(e)}}
            
        except Exception as e:
            logger.error(f"农信社解析器extract_sample方法失败: {str(e)}")
            return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0, 'error': str(e)}}

# 兼容插件系统的入口命名
Plugin = RCCUPlugin


