{"name": "universal_parser_plugin", "version": "1.0.0", "description": "通用解析器插件，支持用户按标准模板整理的银行流水文件解析", "author": "银行流水系统团队", "license": "MIT", "supported_formats": ["通用标准格式", "手工整理格式", "标准模板格式"], "dependencies": ["pandas>=1.3.0", "openpyxl>=3.0.0", "uuid"], "entry_point": "plugin.Plugin", "tags": ["通用", "标准格式", "模板", "万能解析器"], "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "min_python_version": "3.8", "metadata": {"supported_banks": ["通用", "所有银行"], "priority": 100, "universal": true}, "changelog": {"1.0.0": "初始版本，支持通用标准格式解析，包含银行名称标准化功能"}}