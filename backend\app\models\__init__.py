"""
数据库模型包
"""
from .duckdb_models import (
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>an<PERSON>,
    <PERSON><PERSON>BP<PERSON>ject,
    DuckDB<PERSON>arsingTemplate,
    DuckDBAccount,
    DuckD<PERSON><PERSON>ransaction,
    Duck<PERSON><PERSON>awTransaction,
    <PERSON><PERSON><PERSON><PERSON>,
    DuckDBSubject,
    <PERSON><PERSON><PERSON><PERSON>ted<PERSON>erson,
    <PERSON><PERSON><PERSON>sse<PERSON>,
    Duck<PERSON>BRelatedUnit,
    Duck<PERSON>BRelationship
)

# 为了向后兼容，提供别名
Account = DuckDBAccount
Transaction = DuckDBTransaction
Project = DuckDBProject
Bank = DuckDBBank
ParsingTemplate = DuckDBParsingTemplate
RawTransaction = DuckDBRawTransaction
Clue = DuckDBClue
Subject = DuckDBSubject
RelatedPerson = DuckDBRelatedPerson
Asset = DuckDBAsset
RelatedUnit = DuckDBRelatedUnit
Relationship = DuckDBRelationship

__all__ = [
    'DuckDBBase',
    'DuckDBBank',
    'DuckDBProject', 
    'DuckDBParsingTemplate',
    'DuckDBAccount',
    'DuckDBTransaction',
    'DuckD<PERSON>awTransaction',
    '<PERSON><PERSON><PERSON><PERSON>',
    'DuckDBSubject',
    'Duck<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    'DuckDBAsset',
    'Duck<PERSON><PERSON>elatedUnit',
    'DuckDBRelationship',
    'Account',
    'Transaction',
    'Project',
    'Bank',
    'ParsingTemplate',
    'RawTransaction',
    'Clue',
    'Subject',
    'RelatedPerson',
    'Asset',
    'RelatedUnit',
    'Relationship'
] 