/* 现代化登录页面样式 */
.modern-login-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 18px;
  line-height: 1.6;
}

/* 动态背景层 */
.login-background-modern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('../assets/images/background.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 1;
}

.login-background-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(10, 22, 40, 0.7) 0%, rgba(0, 21, 41, 0.8) 25%, rgba(0, 61, 122, 0.6) 75%, rgba(0, 102, 204, 0.5) 100%);
  z-index: 1;
}

.background-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.6;
}

.layer-1 {
  background: 
    radial-gradient(circle at 20% 20%, rgba(0, 102, 204, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(0, 61, 122, 0.3) 0%, transparent 50%);
  animation: float1 20s ease-in-out infinite;
}

.layer-2 {
  background: 
    linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.03) 50%, transparent 70%),
    linear-gradient(-45deg, transparent 30%, rgba(255, 255, 255, 0.03) 50%, transparent 70%);
  animation: float2 25s ease-in-out infinite reverse;
}

.layer-3 {
  background: 
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 100px,
      rgba(255, 255, 255, 0.02) 101px,
      rgba(255, 255, 255, 0.02) 102px
    ),
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 100px,
      rgba(255, 255, 255, 0.02) 101px,
      rgba(255, 255, 255, 0.02) 102px
    );
  animation: float3 30s linear infinite;
}

/* 浮动元素 */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-element {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: floatUp 15s linear infinite;
}

.element-1 {
  width: 4px;
  height: 4px;
  left: 10%;
  animation-delay: 0s;
  animation-duration: 20s;
}

.element-2 {
  width: 6px;
  height: 6px;
  left: 30%;
  animation-delay: 5s;
  animation-duration: 25s;
}

.element-3 {
  width: 3px;
  height: 3px;
  left: 60%;
  animation-delay: 10s;
  animation-duration: 18s;
}

.element-4 {
  width: 5px;
  height: 5px;
  left: 80%;
  animation-delay: 15s;
  animation-duration: 22s;
}

.element-5 {
  width: 4px;
  height: 4px;
  left: 90%;
  animation-delay: 8s;
  animation-duration: 28s;
}

/* 动画定义 */
@keyframes float1 {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes float2 {
  0%, 100% { transform: translateX(0) scale(1); }
  50% { transform: translateX(20px) scale(1.1); }
}

@keyframes float3 {
  0% { transform: translateY(0); }
  100% { transform: translateY(-100px); }
}

@keyframes floatUp {
  0% {
    bottom: -10px;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    bottom: 100vh;
    opacity: 0;
  }
}

/* 主要内容区域 */
.login-content-wrapper {
  position: relative;
  z-index: 3;
  min-height: 100vh;
  display: flex;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 30px;
}

/* 左侧品牌区域 */
.brand-section {
  flex: 1;
  padding: 60px 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  text-align: center;
}

.brand-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 800px;
  width: 100%;
}

.brand-logo {
  margin-bottom: 40px;
}

.logo-icon {
  animation: logoFloat 6s ease-in-out infinite;
}

@keyframes logoFloat {
  0%, 100% { 
    transform: translateY(0) scale(1);
    filter: drop-shadow(0 16px 32px rgba(0, 102, 204, 0.4));
  }
  50% { 
    transform: translateY(-12px) scale(1.02);
    filter: drop-shadow(0 24px 48px rgba(0, 102, 204, 0.6));
  }
}

.brand-text {
  text-align: center;
}

.brand-title {
  font-size: 4.2rem !important;
  font-weight: 700;
  margin-bottom: 2rem !important;
  color: #ffffff !important;
  text-shadow: 0 6px 16px rgba(0, 0, 0, 0.6);
  line-height: 1.1;
  letter-spacing: 0.02em;
  white-space: nowrap;
  word-break: keep-all;
}

/* 品牌描述区域样式 */
.brand-description {
  margin-top: 1rem;
  white-space: nowrap;
}

.company-name {
  display: inline-block;
  font-size: 1.6rem !important;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9) !important;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
  line-height: 1.4;
  white-space: nowrap;
  word-break: keep-all;
}

/* 右侧登录区域 */
.login-section {
  flex: 0 0 480px;
  padding: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-login-card {
  width: 100%;
  max-width: 450px;
  min-height: auto;
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(20px);
  border-radius: 12px !important;
  box-shadow: 
    0 16px 32px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  border: none !important;
  position: relative;
}

.modern-login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #0066cc, #003d7a, #0066cc);
  background-size: 200% 100%;
  animation: gradientMove 3s ease-in-out infinite;
}

.modern-login-card .ant-card-body {
  padding: 36px 32px 32px 32px !important;
}

/* 登录头部 */
.login-header-modern {
  text-align: center;
  margin-bottom: 36px;
}

.login-title {
  font-size: 2.6rem !important;
  font-weight: 700 !important;
  margin-bottom: 12px !important;
  background: linear-gradient(135deg, #001529 0%, #0066cc 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-subtitle {
  font-size: 1.4rem !important;
  font-weight: 400 !important;
  color: #595959 !important;
  margin-bottom: 8px !important;
}

/* 现代化表单 - 强力重置Ant Design样式，彻底解决双重边框 */
.modern-form .ant-form-item {
  margin-bottom: 24px;
}

.modern-form .ant-form-item:last-child {
  margin-bottom: 0;
}

/* 注册表单紧凑布局 */
.modern-form[data-form-type="register"] .ant-form-item {
  margin-bottom: 16px;
}

.modern-form[data-form-type="register"] .ant-form-item:last-child {
  margin-bottom: 0;
}

/* 仅调整注册表单输入框高度 */
.modern-form[data-form-type="register"] .modern-input,
.modern-form[data-form-type="register"] .ant-input-password .ant-input {
  height: 44px !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.modern-form[data-form-type="register"] .ant-input-affix-wrapper {
  height: 44px !important;
}

/* 强力重置 - 移除所有Ant Design默认样式 */
.modern-form .ant-input,
.modern-form .ant-input-password,
.modern-form .ant-input-affix-wrapper,
.modern-form .ant-input-affix-wrapper-focused,
.modern-form .ant-input-focused,
.modern-input {
  /* 强制重置所有可能的样式 */
  all: unset !important;
  
  /* 重新定义基础样式 */
  display: flex !important;
  align-items: center !important;
  width: 100% !important;
  height: 52px !important;
  padding: 0 18px !important;
  
  /* 边框和背景 - 只保留一个边框 */
  border: 2px solid #e8e8e8 !important;
  border-radius: 8px !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px);
  
  /* 文字样式 */
  font-size: 17px !important;
  color: #262626 !important;
  line-height: 1.5 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif !important;
  
  /* 移除所有阴影和轮廓 */
  box-shadow: none !important;
  outline: none !important;
  
  /* 过渡效果 */
  transition: all 0.3s ease !important;
  
  /* 确保盒模型 */
  box-sizing: border-box !important;
}

/* 悬停状态 - 统一处理 */
.modern-form .ant-input:hover,
.modern-form .ant-input-password:hover,
.modern-form .ant-input-affix-wrapper:hover,
.modern-input:hover {
  border: 2px solid #0066cc !important;
  background: rgba(255, 255, 255, 0.98) !important;
  box-shadow: none !important;
}

/* 聚焦状态 - 统一处理 */
.modern-form .ant-input:focus,
.modern-form .ant-input-focused,
.modern-form .ant-input-password:focus,
.modern-form .ant-input-affix-wrapper:focus,
.modern-form .ant-input-affix-wrapper-focused,
.modern-input:focus {
  border: 2px solid #0066cc !important;
  box-shadow: 0 0 0 4px rgba(0, 102, 204, 0.1) !important;
  background: rgba(255, 255, 255, 1) !important;
  outline: none !important;
}

/* 占位符样式 */
.modern-form .ant-input::placeholder,
.modern-input::placeholder {
  color: #bfbfbf !important;
  font-size: 16px !important;
  opacity: 1 !important;
}

/* 密码输入框特殊处理 */
.modern-form .ant-input-password.modern-input {
  padding: 0 !important;
}

.modern-form .ant-input-password.modern-input .ant-input {
  all: unset !important;
  flex: 1 !important;
  height: 48px !important;
  padding: 0 18px !important;
  font-size: 17px !important;
  color: #262626 !important;
  background: transparent !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 密码输入框后缀图标 */
.modern-form .ant-input-password.modern-input .ant-input-suffix {
  margin-right: 18px !important;
  display: flex !important;
  align-items: center !important;
}

/* 图标对齐修正 */
.modern-form .ant-input-prefix {
  margin-right: 10px !important;
  display: flex !important;
  align-items: center !important;
  height: 100%;
}

.modern-form .ant-input-prefix .anticon {
  display: flex !important;
  align-items: center !important;
}

.input-icon {
  color: #0066cc !important;
  font-size: 18px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  vertical-align: middle !important;
  line-height: 1 !important;
}

/* 强制移除所有可能的伪元素和额外样式 */
.modern-form .ant-input::before,
.modern-form .ant-input::after,
.modern-form .ant-input-password::before,
.modern-form .ant-input-password::after,
.modern-form .ant-input-affix-wrapper::before,
.modern-form .ant-input-affix-wrapper::after {
  display: none !important;
  content: none !important;
}

/* 确保没有额外的容器样式 */
.modern-form .ant-form-item-control-input,
.modern-form .ant-form-item-control-input-content {
  display: block !important;
}

/* 移除任何可能的边框重复 */
.modern-form * {
  box-shadow: none !important;
}

.modern-form .ant-input-affix-wrapper > .ant-input {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
  padding: 0 !important;
}

/* 登录选项 */
.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.remember-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 17px;
  color: #595959;
  user-select: none;
}

.remember-checkbox input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 22px;
  height: 22px;
  border: 2px solid #d9d9d9;
  border-radius: 5px;
  margin-right: 12px;
  position: relative;
  transition: all 0.3s ease;
}

.remember-checkbox input[type="checkbox"]:checked + .checkmark {
  background: #0066cc;
  border-color: #0066cc;
}

.remember-checkbox input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.forgot-link {
  color: #0066cc !important;
  font-weight: 500 !important;
  padding: 0 !important;
  height: auto !important;
  font-size: 17px !important;
}

.forgot-link:hover {
  color: #003d7a !important;
}

/* 现代化按钮 */
.modern-login-button {
  height: 52px !important;
  border-radius: 8px !important;
  font-size: 19px !important;
  font-weight: 600 !important;
  background: linear-gradient(135deg, #0066cc 0%, #003d7a 100%) !important;
  border: none !important;
  box-shadow: 0 4px 12px rgba(0, 102, 204, 0.3) !important;
  transition: all 0.3s ease !important;
  position: relative;
  overflow: hidden;
}

.modern-login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.modern-login-button:hover::before {
  left: 100%;
}

.modern-login-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 12px 32px rgba(0, 102, 204, 0.4) !important;
  background: linear-gradient(135deg, #0052a3 0%, #002d5c 100%) !important;
}

.modern-login-button:active {
  transform: translateY(0) !important;
}

/* 分割线 */
.login-divider {
  margin: 28px 0 !important;
}

.login-divider .ant-divider-inner-text {
  color: #595959 !important;
  font-size: 17px !important;
}

/* 页脚 */
.login-footer-modern {
  text-align: center;
  margin-top: 24px !important;
  padding-top: 24px !important;
  padding-bottom: 24px !important;
  border-top: 1px solid #f0f0f0;
  white-space: nowrap;
  overflow: hidden;
}

.footer-text {
  color: #595959 !important;
  font-size: 17px !important;
  white-space: nowrap !important;
  word-break: keep-all !important;
  display: inline-block;
}

/* 模态框样式 */
.modern-modal .ant-modal-header {
  background: linear-gradient(135deg, #0066cc 0%, #003d7a 100%) !important;
  border-bottom: none !important;
  border-radius: 12px 12px 0 0 !important;
  padding: 20px 24px !important;
}

.modern-modal .ant-modal-title {
  color: white !important;
  font-weight: 600 !important;
  font-size: 18px !important;
}

.modern-modal .ant-modal-close {
  color: white !important;
  top: 16px !important;
  right: 16px !important;
}

.modern-modal .ant-modal-close:hover {
  color: rgba(255, 255, 255, 0.8) !important;
}

.modern-modal .ant-modal-body {
  padding: 36px 28px !important;
}

.modern-modal .ant-modal-content {
  border-radius: 12px !important;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
}

/* 加载动画 */
.modern-login-button.ant-btn-loading {
  background: linear-gradient(135deg, #0066cc 0%, #003d7a 100%) !important;
}

/* 表单验证错误样式 */
.modern-form .ant-form-item-has-error .modern-input {
  border-color: #ff4d4f !important;
}

.modern-form .ant-form-item-has-error .modern-input:focus {
  box-shadow: 0 0 0 4px rgba(255, 77, 79, 0.1) !important;
}

/* 成功状态样式 */
.modern-form .ant-form-item-has-success .modern-input {
  border-color: #52c41a !important;
}

/* 禁用状态样式 */
.modern-input:disabled {
  background-color: #f5f5f5 !important;
  color: #999 !important;
  cursor: not-allowed;
}

/* 动画优化 */
@keyframes gradientMove {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .modern-login-card {
    background: rgba(255, 255, 255, 0.95) !important;
  }
}

/* 高对比度支持 */
@media (prefers-contrast: high) {
  .modern-input {
    border-width: 3px !important;
  }
  
  .modern-login-button {
    border: 2px solid #003d7a !important;
  }
}

/* 减少动画支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 现代化标签页 */
.modern-tabs .ant-tabs-nav {
  margin-bottom: 32px !important;
}

.modern-tabs .ant-tabs-tab {
  font-size: 17px !important;
  font-weight: 600 !important;
  color: #595959 !important;
  padding: 14px 28px !important;
  border-radius: 8px 8px 0 0 !important;
  transition: all 0.3s ease !important;
  position: relative;
  overflow: hidden;
}

.modern-tabs .ant-tabs-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 102, 204, 0.05) 0%, rgba(0, 61, 122, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modern-tabs .ant-tabs-tab:hover::before {
  opacity: 1;
}

.modern-tabs .ant-tabs-tab-active {
  color: #0066cc !important;
  background: linear-gradient(135deg, rgba(0, 102, 204, 0.1) 0%, rgba(0, 61, 122, 0.1) 100%) !important;
}

.modern-tabs .ant-tabs-ink-bar {
  background: linear-gradient(90deg, #0066cc, #003d7a) !important;
  height: 3px !important;
  border-radius: 2px !important;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 针对注册表单的特定调整，确保按钮下方无多余空白 */
.modern-form[data-form-type="register"] {
  padding-bottom: 0px !important;
}

.modern-form[data-form-type="register"] .ant-form-item:last-child {
  margin-bottom: 0 !important;
}