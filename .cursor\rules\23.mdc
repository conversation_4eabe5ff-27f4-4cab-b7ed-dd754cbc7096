---
description: 
globs: 
alwaysApply: true
---
RULE 1: 端到端验证强制要求（无例外）
禁止声称"问题解决"，除非完成以下验证：
1. 后端API测试通过
2. 前端实际操作测试通过，包括测试前端UI交互  
3. 用户完整业务流程验证通过
4.禁止以"技术限制"为理由跳过前端实际操作测试
5.必须使用所有可用工具进行完整验证，如果Playwright工具暂时连不上不可用要明确做出说明
6.如违反端到端测试规则，必须立即停止并重新执行，不得继续进行其他任务直到测试完成，如经2次测试发现确无用具可用，在结束任务前注明情况，并给出用户下一步操作的建议。

RULE 2: 状态报告诚实化
必须使用准确的状态描述：
- 禁止："问题完全解决"
- 改为："后端API修复完成，前端集成待验证"
- 禁止："系统正常运行" 
- 改为："部分功能修复，整体功能需用户验证"

RULE 3: 测试证据强制提供
每次声称修复时必须提供：
1. 具体测试了哪些场景
2. 具体没有测试哪些场景  
3. 用户需要验证的具体步骤
4. 可能仍存在的问题点

RULE 4 强化版：
- 任何涉及解析器测试的任务，必须包含以下强制步骤：
  1. 启动前后端服务
  2. 使用浏览器工具实际访问前端界面
  3. 上传测试文件并查看解析结果
  4. 逐项对比前端显示数据与后端解析数据
  5. 检查交易流水的个明细字段是否完整显示，严禁以总条数与原表相同就下结论，要点击“明细查询”将每一条字段都能准确映射原表的内容。
  6. 如发现数据不一致或显示异常，必须作为BUG修复
  7. 禁止声称"测试完成"除非以上步骤全部通过

违反此规则的后果：立即停止当前任务，重新执行完整验证流程。

任何时候都不要修改前端3000端口和后端的8000段，如果端口无法打开就修正，但禁止改换其他端口。


