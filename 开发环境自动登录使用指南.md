# 开发环境自动登录使用指南

## 🎯 解决的问题

开发过程中每次启动前后端都需要手动登录，影响开发效率。现在系统支持开发环境自动登录功能。

## ✨ 功能特性

### 1. 自动登录机制
- **触发条件**：开发环境 + 未登录状态
- **默认账户**：樊迪/123456
- **自动操作**：页面加载时自动完成登录
- **跳过步骤**：无需手动输入用户名密码

### 2. 开发工具条
- **位置**：页面右上角
- **显示内容**：当前登录状态
- **快速操作**：登出、重新登录按钮
- **仅开发环境显示**

### 3. 环境变量控制
```bash
# 可选环境变量（不设置使用默认值）
REACT_APP_AUTO_LOGIN=true          # 启用自动登录（默认true）
REACT_APP_DEV_USERNAME=樊迪        # 测试用户名（默认樊迪）
REACT_APP_DEV_PASSWORD=123456      # 测试密码（默认123456）
```

## 🚀 使用方法

### 启动系统
```bash
# 使用可靠启动脚本（推荐）
.\reliable_startup.bat

# 或手动启动前后端
cd backend && python -m uvicorn app.main:app --host 127.0.0.1 --port 8000
cd frontend/bankflow-client && npm start
```

### 验证功能
1. 访问 http://localhost:3000
2. 开发环境会自动检测登录状态
3. 如未登录，自动使用测试账户登录
4. 页面会显示成功消息："🔧 开发环境自动登录成功！用户：樊迪"
5. 右上角显示开发工具条

## 🔧 自定义配置

### 使用不同测试账户
如需使用其他测试账户，修改 `DevAutoLogin.jsx` 中的默认值：
```javascript
const testUsername = '张三';  // 改为其他用户
const testPassword = '123456';
```

### 禁用自动登录
在 `DevAutoLogin.jsx` 中设置：
```javascript
const autoLoginEnabled = false; // 禁用自动登录
```

### 临时禁用
开发工具条中点击"登出"按钮，然后刷新页面不会自动登录。

## 📋 可用测试账户

系统预设了以下测试账户（在 `userDataSync.js` 中定义）：

| 用户名 | 密码 | 权限 |
|--------|------|------|
| 樊迪 | 123456 | 普通用户 |
| admin | lgmis2012% | 管理员 |
| 张三 | 123456 | 普通用户 |
| 李四 | 123456 | 普通用户 |

## ⚠️ 注意事项

1. **仅开发环境**：此功能只在开发环境下启用，生产环境无效
2. **账户要求**：确保使用的测试账户在 `userDataSync.js` 中已定义
3. **浏览器缓存**：如遇问题可清除 localStorage 重新测试
4. **端口要求**：前端必须运行在3000端口，后端8000端口

## 🐛 故障排除

### 自动登录失败
1. 检查控制台错误信息
2. 确认用户名密码在默认用户列表中
3. 清除浏览器缓存和localStorage
4. 重启前端服务

### 开发工具条不显示
1. 确认是开发环境（npm start方式启动）
2. 检查 `NODE_ENV` 环境变量
3. 查看浏览器控制台错误

### 登录后仍跳转到登录页
1. 检查 `AuthContext` 状态
2. 确认 localStorage 中的登录信息
3. 查看网络请求是否正常

## 🎉 效果对比

**修复前**：
- ❌ 每次启动需手动登录
- ❌ 经常忘记正确的测试账户
- ❌ 验证功能时需重复登录步骤

**修复后**：
- ✅ 自动登录，无需手动操作
- ✅ 固定使用正确的测试账户（樊迪/123456）
- ✅ 开发工具条显示状态，便于调试
- ✅ 专注于功能验证，提升开发效率

## 📖 相关文件

- `frontend/bankflow-client/src/components/DevAutoLogin.jsx` - 自动登录组件
- `frontend/bankflow-client/src/components/DevToolbar.jsx` - 开发工具条
- `frontend/bankflow-client/src/utils/userDataSync.js` - 用户数据管理
- `frontend/bankflow-client/src/contexts/AuthContext.jsx` - 认证上下文
- `frontend/bankflow-client/src/App.js` - 主应用配置

---
*此功能彻底解决了开发环境下的登录困扰，让AI和开发者都能专注于功能验证和问题解决！* 