#!/usr/bin/env python3
import os, sys, json

sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from app.services.parser_plugin_system.core.plugin_manager import PluginManager

file_path = r"F:\\流水清洗\\银行流水数据参考\\8平安银行\\**************-司法个人查询杨蕊瑜.xlsx"

pm = PluginManager()
pm.start()

res = pm.execute_plugin('pingan_format1_plugin', file_path)
print('success:', res.get('success'))
print('accounts:', len(res.get('accounts', [])))
print('transactions:', len(res.get('transactions', [])))
if res.get('accounts'):
    print('first account:', json.dumps(res['accounts'][0], ensure_ascii=False)[:300])
if res.get('transactions'):
    print('first tx:', json.dumps(res['transactions'][0], ensure_ascii=False)[:300])



