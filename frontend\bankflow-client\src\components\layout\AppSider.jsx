import React, { useState } from 'react';
import { Layout, Menu } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import CustomExportOutlined from '../icons/CustomExportOutlined';
import {
  HomeOutlined,
  ProjectOutlined,
  BankOutlined,
  SettingOutlined,
  FileTextOutlined,
  UploadOutlined,
  BarChartOutlined,
  CommentOutlined,
  RobotOutlined,
  LogoutOutlined,
  UserOutlined,
  TeamOutlined,
  BuildOutlined,
  SearchOutlined,
  // ExportOutlined, // 替换为自定义组件以修复fill-rule警告
  ClearOutlined,
  ToolOutlined,
  QuestionCircleOutlined,
  ContactsOutlined,
  ApartmentOutlined,
  DollarOutlined,
  ShareAltOutlined
} from '@ant-design/icons';

const { Sider } = Layout;

/**
 * 应用侧边栏组件
 * 
 * @returns {JSX.Element} 侧边栏组件
 */
const AppSider = () => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  
  // 获取当前路径的第一级路径
  const selectedKey = '/' + (location.pathname.split('/')[1] || '');
  const projectId = location.pathname.split('/projects/')[1]?.split('/')[0];
  
  // 检查是否在项目详情页面内
  const isInProjectDetail = location.pathname.includes('/projects/') && projectId;
  
  // 主应用层级的菜单项
  const appLevelMenuItems = [
    {
      key: '/projects',
      icon: <ProjectOutlined />,
      label: '项目管理',
      onClick: () => navigate('/projects')
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
      onClick: () => navigate('/settings')
    }
  ];
  
  // 主应用层级的菜单
  const renderAppLevelMenu = () => (
    <Menu
      mode="inline"
      selectedKeys={[location.pathname]}
      style={{ height: '100%', borderRight: 0 }}
      theme="dark"
      items={appLevelMenuItems}
    />
  );

  // 项目工作区的菜单项
  const projectWorkspaceMenuItems = [
    {
      key: `/projects/${projectId}`,
      icon: <HomeOutlined />,
      label: '项目概览',
      onClick: () => navigate(`/projects/${projectId}`)
    },
    {
      key: 'case-brief',
      icon: <FileTextOutlined />,
      label: '案情简要',
      children: [
        {
          key: `/projects/${projectId}/case-brief/clues`,
          icon: <QuestionCircleOutlined />,
          label: '问题线索',
          onClick: () => navigate(`/projects/${projectId}/case-brief/clues`)
        },
        {
          key: `/projects/${projectId}/case-brief/subject`,
          icon: <UserOutlined />,
          label: '被反映人资料',
          onClick: () => navigate(`/projects/${projectId}/case-brief/subject`)
        },
        {
          key: `/projects/${projectId}/case-brief/related-persons`,
          icon: <TeamOutlined />,
          label: '相关人员资料',
          onClick: () => navigate(`/projects/${projectId}/case-brief/related-persons`)
        },
        {
          key: `/projects/${projectId}/case-brief/related-units`,
          icon: <BuildOutlined />,
          label: '相关单位情况',
          onClick: () => navigate(`/projects/${projectId}/case-brief/related-units`)
        },
        {
          key: `/projects/${projectId}/case-brief/assets`,
          icon: <DollarOutlined />,
          label: '财产信息',
          onClick: () => navigate(`/projects/${projectId}/case-brief/assets`)
        },
        {
          key: `/projects/${projectId}/case-brief/relationship`,
          icon: <ShareAltOutlined />,
          label: '人物关系图谱',
          onClick: () => navigate(`/projects/${projectId}/case-brief/relationship`)
        }
      ]
    },
    {
      key: 'bank-statements',
      icon: <BankOutlined />,
      label: '银行流水',
      children: [
        {
          key: `/projects/${projectId}/bankStatements/import`,
          icon: <UploadOutlined />,
          label: '银行流水导入',
          onClick: () => navigate(`/projects/${projectId}/bankStatements/import`)
        },
        {
          key: `/projects/${projectId}/bankStatements/clean`,
          icon: <ClearOutlined />,
          label: '数据清洗',
          onClick: () => navigate(`/projects/${projectId}/bankStatements/clean`)
        },
        {
          key: `/projects/${projectId}/bankStatements/parsers`,
          icon: <ToolOutlined />,
          label: '解析器管理',
          onClick: () => navigate(`/projects/${projectId}/bankStatements/parsers`)
        },
        {
          key: `/projects/${projectId}/bankStatements/query`,
          icon: <SearchOutlined />,
          label: '查询整理',
          onClick: () => navigate(`/projects/${projectId}/bankStatements/query`)
        },
        {
          key: `/projects/${projectId}/bankStatements/export`,
          icon: <CustomExportOutlined />,
          label: '导出模块',
          onClick: () => navigate(`/projects/${projectId}/bankStatements/export`)
        }
      ]
    },
    {
      key: 'analysis',
      icon: <BarChartOutlined />,
      label: '银行流水分析',
      children: [
        {
          key: `/projects/${projectId}/analysis/tactics`,
          icon: <BarChartOutlined />,
          label: '常用战法分析',
          onClick: () => navigate(`/projects/${projectId}/analysis/tactics`)
        },
        {
          key: `/projects/${projectId}/analysis/ai`,
          icon: <RobotOutlined />,
          label: 'AI智能分析',
          onClick: () => navigate(`/projects/${projectId}/analysis/ai`)
        }
      ]
    },
    {
      key: `/projects/${projectId}/transcripts`,
      icon: <CommentOutlined />,
      label: '谈话笔录',
      onClick: () => navigate(`/projects/${projectId}/transcripts`)
    },
    {
      key: `/projects/${projectId}/aiQuery`,
      icon: <RobotOutlined />,
      label: '全局智能查询',
      onClick: () => navigate(`/projects/${projectId}/aiQuery`)
    },
    {
      type: 'divider'
    },
    {
      key: 'exit-project',
      icon: <LogoutOutlined />,
      label: '退出项目',
      onClick: () => navigate('/projects'),
      style: { color: '#ff7875' }
    }
  ];

  // 项目工作区的菜单
  const renderProjectWorkspaceMenu = () => (
    <Menu
      mode="inline"
      selectedKeys={[location.pathname]}
      defaultOpenKeys={[
        'case-brief',
        'bank-statements', 
        'analysis'
      ]}
      style={{ height: '100%', borderRight: 0 }}
      theme="dark"
      items={projectWorkspaceMenuItems}
    />
  );

  return (
    <Sider
      collapsible
      collapsed={collapsed}
      onCollapse={value => setCollapsed(value)}
      width={240} // 从200增大到240
      style={{ background: '#001529' }}
    >
      {isInProjectDetail ? renderProjectWorkspaceMenu() : renderAppLevelMenu()}
    </Sider>
  );
};

export default AppSider; 