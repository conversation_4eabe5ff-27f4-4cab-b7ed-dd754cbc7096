"""
工商银行格式4增强解析器
专门处理4.xlsx文件的企业多账户结构：
- 客户信息: 企业基本信息
- 开户信息: 账户开户信息
- 多个企业工作表: 每个企业的交易流水数据
"""
import pandas as pd
import logging
from typing import Dict, List, Any, Tuple
from datetime import datetime
import re
import os

logger = logging.getLogger(__name__)

class ICBCFormat4EnhancedParser:
    """工商银行格式4增强解析器"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.customer_info = {}
        self.account_info = {}
        self.transactions = []
    
    def _clean_amount_string(self, value) -> str:
        """
        清理金额字符串，移除空格、逗号等格式字符
        
        Args:
            value: 原始金额值
            
        Returns:
            str: 清理后的金额字符串
        """
        if value is None:
            return ""
        
        # 转换为字符串并清理各种格式字符
        clean_value = str(value).replace(" ", "").replace(",", "").replace("，", "").replace("\t", "").replace("\n", "").replace("\r", "").strip()
        
        # 移除可能的货币符号
        clean_value = clean_value.replace("¥", "").replace("$", "").replace("€", "").replace("￥", "")
        
        return clean_value
        
    def parse(self) -> Dict[str, Any]:
        """解析文件"""
        try:
            logger.info(f"开始解析工商银行格式4文件: {self.file_path}")
            
            # 检查文件是否存在
            if not os.path.exists(self.file_path):
                error_msg = f"文件不存在: {self.file_path}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg,
                    'accounts': [],
                    'transactions': [],
                    'summary': {'total_accounts': 0, 'total_transactions': 0}
                }
            
            # 读取所有工作表
            try:
                all_sheets = pd.read_excel(self.file_path, sheet_name=None)
                logger.info(f"成功读取Excel文件，包含工作表: {list(all_sheets.keys())}")
            except Exception as e:
                error_msg = f"读取Excel文件失败: {str(e)}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg,
                    'accounts': [],
                    'transactions': [],
                    'summary': {'total_accounts': 0, 'total_transactions': 0}
                }
            
            # 解析客户信息
            if '客户信息' in all_sheets:
                try:
                    self._parse_customer_info(all_sheets['客户信息'])
                    logger.info("客户信息解析完成")
                except Exception as e:
                    logger.warning(f"客户信息解析失败: {str(e)}")
            
            # 解析开户信息
            if '开户信息' in all_sheets:
                try:
                    self._parse_account_info(all_sheets['开户信息'])
                    logger.info("开户信息解析完成")
                except Exception as e:
                    logger.warning(f"开户信息解析失败: {str(e)}")
            
            # 解析企业交易数据
            enterprise_sheets = [name for name in all_sheets.keys() 
                                if name not in ['客户信息', '开户信息']]
            
            logger.info(f"找到企业交易工作表: {enterprise_sheets}")
            
            for sheet_name in enterprise_sheets:
                try:
                    self._parse_enterprise_transactions(all_sheets[sheet_name], sheet_name)
                    logger.info(f"工作表 '{sheet_name}' 解析完成")
                except Exception as e:
                    logger.warning(f"工作表 '{sheet_name}' 解析失败: {str(e)}")
            
            result = self._build_result()
            logger.info(f"解析完成，共解析 {result.get('summary', {}).get('total_accounts', 0)} 个账户，{result.get('summary', {}).get('total_transactions', 0)} 条交易")
            return result
            
        except Exception as e:
            error_msg = f"解析工商银行格式4文件失败: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': error_msg,
                'accounts': [],
                'transactions': [],
                'summary': {'total_accounts': 0, 'total_transactions': 0}
            }
    
    def _parse_customer_info(self, df: pd.DataFrame) -> None:
        """解析客户信息"""
        logger.info("解析客户信息...")
        
        # 第一行是表头
        if len(df) < 1:
            return
        
        for _, row in df.iterrows():
            account_number = str(row.get('卡号或帐号', '')).strip().replace('\t', '')
            customer_id = str(row.get('客户号', '')).strip()
            company_name = str(row.get('姓名', '')).strip().replace('\t', '')
            cert_type = str(row.get('证件类型', '')).strip().replace('\t', '')
            cert_number = str(row.get('证件号码', '')).strip().replace('\t', '')
            
            if account_number and company_name:
                self.customer_info[account_number] = {
                    'customer_id': customer_id,
                    'company_name': company_name,
                    'cert_type': cert_type,
                    'cert_number': cert_number
                }
                logger.info(f"提取客户信息: {company_name} - {account_number}")
    
    def _parse_account_info(self, df: pd.DataFrame) -> None:
        """解析开户信息"""
        logger.info("解析开户信息...")
        
        for _, row in df.iterrows():
            account_number = str(row.get('账号', '')).strip().replace('\t', '')
            company_name = str(row.get('公司名称', '')).strip().replace('\t', '')
            account_type = str(row.get('账号类型', '')).strip().replace('\t', '')
            account_name = str(row.get('账户名称', '')).strip().replace('\t', '')
            currency = str(row.get('币种', '')).strip().replace('\t', '')
            status = str(row.get('账户状态', '')).strip().replace('\t', '')
            open_date = str(row.get('开户日期', '')).strip().replace('\t', '')
            
            if account_number and company_name:
                self.account_info[account_number] = {
                    'company_name': company_name,
                    'account_type': account_type,
                    'account_name': account_name,
                    'currency': currency,
                    'status': status,
                    'open_date': open_date
                }
                logger.info(f"提取账户信息: {company_name} - {account_number}")
    
    def _parse_enterprise_transactions(self, df: pd.DataFrame, enterprise_name: str) -> None:
        """解析企业交易数据"""
        logger.info(f"解析企业交易数据: {enterprise_name}")
        
        if len(df) < 2:
            logger.warning(f"企业 {enterprise_name} 交易数据表格行数不足")
            return
        
        # 第一行是企业名称，第二行是表头
        real_headers = df.iloc[1].tolist()
        logger.info(f"企业 {enterprise_name} 表头: {real_headers[:5]}...")  # 只显示前5个
        
        # 重新构建数据：从第二行开始都是数据
        data_rows = []
        
        # 第二行的数据（被pandas当作表头的那一行）
        second_row_data = real_headers
        data_rows.append(second_row_data)
        
        # 从第三行开始的数据
        for i in range(2, len(df)):
            row_data = df.iloc[i].tolist()
            data_rows.append(row_data)
        
        # 使用标准的列名作为表头
        column_names = ['帐号', '工作日期', '入帐时间', '借贷标志', '发生额', '余额', '币种', '交易物理机构号', '渠道', '柜员号',
                       '对方帐号', '凭证种类', '网银业务种类', '对方行号', '备注1', '对方户名', '对方行名', '附言', '摘要', '凭证号']
        
        # 确保列数匹配
        if len(column_names) > len(real_headers):
            column_names = column_names[:len(real_headers)]
        elif len(column_names) < len(real_headers):
            for i in range(len(column_names), len(real_headers)):
                column_names.append(f'未知列{i+1}')
        
        # 创建新的DataFrame
        data_df = pd.DataFrame(data_rows, columns=column_names)
        
        logger.info(f"企业 {enterprise_name} 重构后数据行数: {len(data_df)}")
        
        # 处理交易记录
        transaction_count = 0
        for _, row in data_df.iterrows():
            try:
                transaction = self._process_enterprise_transaction_row(row, enterprise_name)
                if transaction:
                    self.transactions.append(transaction)
                    transaction_count += 1
            except Exception as e:
                logger.warning(f"处理企业 {enterprise_name} 交易记录失败: {str(e)}")
                continue
        
        logger.info(f"企业 {enterprise_name} 成功解析 {transaction_count} 条交易记录")
    
    def _process_enterprise_transaction_row(self, row: pd.Series, enterprise_name: str) -> Dict[str, Any]:
        """处理单条企业交易记录 - 按照PARSER_DEVELOPMENT_RULES.md的17个必需字段规范"""
        # 提取账号
        account_number = str(row.get('帐号', '')).strip().replace('\t', '')
        if not account_number or len(account_number) < 15:
            return None
        
        # 提取交易日期和时间
        work_date = str(row.get('工作日期', '')).strip().replace('\t', '')
        entry_time = str(row.get('入帐时间', '')).strip().replace('\t', '')
        
        transaction_date = work_date if work_date != 'nan' else ""
        transaction_time = self._format_transaction_time(entry_time if entry_time != 'nan' else "")
        
        # 提取金额 - 使用增强的清理方法
        amount_str = str(row.get('发生额', '')).strip().replace('\t', '')
        amount = 0.0
        if amount_str and amount_str != 'nan':
            try:
                # 使用增强的金额清理方法，处理空格、制表符、逗号等
                clean_amount = self._clean_amount_string(amount_str)
                if clean_amount and clean_amount not in ['', '0', '0.0', '0.00']:
                    amount = float(clean_amount)
            except (ValueError, TypeError):
                logger.warning(f"金额转换失败: {amount_str}")
                pass
        
        # 提取余额 - 使用增强的清理方法
        balance_str = str(row.get('余额', '')).strip().replace('\t', '')
        balance = 0.0
        if balance_str and balance_str != 'nan':
            try:
                clean_balance = self._clean_amount_string(balance_str)
                if clean_balance and clean_balance not in ['', '0', '0.0', '0.00']:
                    balance = float(clean_balance)
            except (ValueError, TypeError):
                logger.warning(f"余额转换失败: {balance_str}")
                pass
        
        # 提取借贷标志
        dr_cr_flag_raw = str(row.get('借贷标志', '')).strip().replace('\t', '')
        dr_cr_flag = "收" if dr_cr_flag_raw == "贷" else "支"
        
        # 根据借贷标志调整金额符号
        if dr_cr_flag_raw == "借":  # 借方表示支出
            amount = -abs(amount)
        else:  # 贷方表示收入
            amount = abs(amount)
        
        # 提取其他信息
        currency = str(row.get('币种', '')).strip().replace('\t', '')
        counterparty_account = str(row.get('对方帐号', '')).strip().replace('\t', '')
        counterparty_name = str(row.get('对方户名', '')).strip().replace('\t', '')
        counterparty_bank = str(row.get('对方行名', '')).strip().replace('\t', '')
        remarks1 = str(row.get('备注1', '')).strip().replace('\t', '')
        remarks2 = str(row.get('附言', '')).strip().replace('\t', '')
        remarks3 = str(row.get('摘要', '')).strip().replace('\t', '')
        transaction_method = str(row.get('渠道', '')).strip().replace('\t', '')  # 交易方式
        
        # 处理空值或NaN
        def clean_field(value):
            return value if value and value != 'nan' else ""
        
        # 返回符合PARSER_DEVELOPMENT_RULES.md规范的17个必需字段
        return {
            # 1. sequence_number - Integer - 序号 (将在构建结果时添加)
            'sequence_number': 0,  # 临时值，后续会更新
            # 2. holder_name - String - 持卡人姓名
            'holder_name': enterprise_name,
            # 3. bank_name - String - 银行名称
            'bank_name': '中国工商银行',
            # 4. account_number - String - 账户号
            'account_number': account_number,
            # 5. card_number - String - 卡号 - 保持原始数据，不强制生成
            'card_number': "",  # 企业账户通常没有卡号
            # 6. transaction_date - String - 交易日期
            'transaction_date': transaction_date,
            # 7. transaction_time - String - 交易时间 (HH:MM:SS格式)
            'transaction_time': transaction_time,
            # 8. transaction_method - String - 交易方式
            'transaction_method': clean_field(transaction_method),
            # 9. transaction_amount - Float - 交易金额
            'transaction_amount': amount,
            # 10. balance_amount - Float - 交易余额
            'balance_amount': balance,
            # 11. dr_cr_flag - String - 收支符号
            'dr_cr_flag': dr_cr_flag,
            # 12. counterparty_name - String - 对方户名
            'counterparty_name': clean_field(counterparty_name),
            # 13. counterparty_account - String - 对方账号
            'counterparty_account': clean_field(counterparty_account),
            # 14. counterparty_bank - String - 对方银行
            'counterparty_bank': clean_field(counterparty_bank),
            # 15. remark1 - String - 备注1
            'remark1': clean_field(remarks1),
            # 16. remark2 - String - 备注2(摘要)
            'remark2': clean_field(remarks3),  # 摘要
            # 17. remark3 - String - 备注3(附言)
            'remark3': clean_field(remarks2),  # 附言
            
            # 附加字段（向后兼容）
            'currency': currency if currency != 'nan' else "人民币",
            'enterprise_name': enterprise_name,
            'transaction_datetime': f"{transaction_date} {transaction_time}" if transaction_date and transaction_time else "",
            'raw_data': row.to_dict()
        }
    
    def _calculate_confidence_score(self, accounts: List[Dict], transactions: List[Dict]) -> float:
        """
        计算解析置信度
        
        Returns:
            float: 置信度分数 (0-100)
        """
        if not accounts or not transactions:
            return 0.0
        
        total_score = 0
        max_score = 100
        
        # 1. 账户信息完整性评估 (25分)
        account_score = 0
        valid_accounts = 0
        for account in accounts:
            # 账号或卡号有其一即可得分 - 按照用户要求修正
            account_number = account.get('account_number', '')
            card_number = account.get('card_number', '')
            if (account_number and len(account_number) >= 10) or card_number:
                account_score += 15  # 账号或卡号有效得15分
            if account.get('holder_name') and len(account['holder_name']) >= 2:
                account_score += 10  # 持卡人姓名有效得10分
            valid_accounts += 1
        
        if valid_accounts > 0:
            account_score = min(25, account_score / valid_accounts)
        
        # 2. 交易数据完整性评估 (25分)  
        transaction_score = 0
        valid_transactions = 0
        for transaction in transactions:
            score = 0
            if transaction.get('transaction_date'):
                score += 5
            if transaction.get('transaction_amount') != 0:
                score += 10
            if transaction.get('dr_cr_flag'):
                score += 5
            if transaction.get('counterparty_name') or transaction.get('remark1'):
                score += 5
            
            transaction_score += score
            valid_transactions += 1
        
        if valid_transactions > 0:
            transaction_score = min(25, transaction_score / valid_transactions)
        
        # 3. 数据格式一致性评估 (25分)
        format_score = 25  # 基础分
        
        # 检查日期格式一致性
        date_formats = set()
        for transaction in transactions[:10]:  # 只检查前10条
            date = transaction.get('transaction_date', '')
            if date:
                if re.match(r'^\d{4}-\d{2}-\d{2}$', date):
                    date_formats.add('standard')
                else:
                    date_formats.add('non_standard')
        
        if len(date_formats) > 1:
            format_score -= 10  # 日期格式不一致扣分
        
        # 4. 数据量合理性评估 (25分)
        volume_score = 25
        
        # 检查账户数量是否合理（1-50个账户比较正常）
        account_count = len(accounts)
        if account_count == 0:
            volume_score = 0
        elif account_count > 100:
            volume_score -= 10  # 账户过多可能是解析错误
        
        # 检查交易数量是否合理
        transaction_count = len(transactions)
        if transaction_count == 0:
            volume_score = 0
        elif transaction_count < 10:
            volume_score -= 5  # 交易过少可能不完整
        
        total_score = account_score + transaction_score + format_score + volume_score
        return min(100.0, max(0.0, total_score))

    def _build_result(self) -> Dict[str, Any]:
        """构建解析结果 - 按照PARSER_DEVELOPMENT_RULES.md规范"""
        # 按账户分组交易
        transactions_by_account = {}
        accounts = []
        
        # 为每条交易分配序号
        for index, transaction in enumerate(self.transactions, 1):
            transaction['sequence_number'] = index
            
            account_number = transaction['account_number']
            
            if account_number not in transactions_by_account:
                transactions_by_account[account_number] = []
                
                # 获取账户相关信息
                customer_info = self.customer_info.get(account_number, {})
                account_info = self.account_info.get(account_number, {})
                
                # 创建符合规范的账户信息
                account_data = {
                    'account_number': account_number,
                    'card_number': transaction['card_number'],
                    'holder_name': (customer_info.get('company_name') or 
                                   account_info.get('company_name') or 
                                   transaction['enterprise_name']),
                    'bank_name': '中国工商银行',
                    'account_type': account_info.get('account_type', '企业账户'),
                    
                    # 统计信息
                    'transactions_count': 0,  # 将在后续计算
                    'total_inflow': 0.0,
                    'total_outflow': 0.0,
                    'date_range': "",
                    
                    # 兼容字段
                    'account_id': account_number,
                    'account_name': (customer_info.get('company_name') or 
                                   account_info.get('company_name') or 
                                   transaction['enterprise_name']),
                    'currency': transaction['currency'],
                    'status': account_info.get('status', ''),
                    'is_primary': len(accounts) == 0
                }
                accounts.append(account_data)
            
            transactions_by_account[account_number].append(transaction)
        
        # 计算每个账户的统计信息
        for account in accounts:
            account_number = account['account_number']
            account_transactions = transactions_by_account[account_number]
            
            # 计算交易笔数
            account['transactions_count'] = len(account_transactions)

            # 🔧 修复：添加交易数据到账户信息中
            account['transactions'] = account_transactions
            
            # 计算收支统计
            total_inflow = sum(t['transaction_amount'] for t in account_transactions if t['transaction_amount'] > 0)
            total_outflow = sum(abs(t['transaction_amount']) for t in account_transactions if t['transaction_amount'] < 0)
            
            account['total_inflow'] = total_inflow
            account['total_outflow'] = total_outflow
            
            # 计算时间范围
            dates = []
            for t in account_transactions:
                if t.get('transaction_date'):
                    try:
                        date_obj = datetime.strptime(t['transaction_date'], '%Y-%m-%d')
                        dates.append(date_obj)
                    except ValueError:
                        continue
            
            if dates:
                min_date = min(dates).strftime('%Y-%m-%d')
                max_date = max(dates).strftime('%Y-%m-%d')
                if min_date == max_date:
                    account['date_range'] = min_date
                else:
                    account['date_range'] = f"{min_date} 至 {max_date}"
            else:
                account['date_range'] = "未知"

            # 🔧 修复：计算账户余额 - 从最后一个时间段的交易记录获取余额
            account_balance = 0.0
            if account_transactions:
                # 按交易日期和时间排序，获取最新的交易记录
                # 对于同一时间的交易，取原始顺序的最后一条（余额最终状态）
                def get_datetime_key(transaction):
                    date = transaction.get('transaction_date', '')
                    time = transaction.get('transaction_time', '')
                    # 添加原始索引确保同一时间的交易按原始顺序排列
                    original_index = account_transactions.index(transaction)
                    return f"{date} {time}", original_index

                sorted_transactions = sorted(account_transactions, key=get_datetime_key, reverse=True)
                latest_transaction = sorted_transactions[0]  # 最新的交易

                # 从最新交易的balance_amount字段获取账户余额
                if 'balance_amount' in latest_transaction and latest_transaction['balance_amount'] is not None:
                    try:
                        account_balance = float(latest_transaction['balance_amount'])
                        datetime_key, _ = get_datetime_key(latest_transaction)
                        logger.info(f"账户 {account['account_number']} 余额: ¥{account_balance:,.2f} (来自最新交易: {datetime_key})")
                    except (ValueError, TypeError):
                        logger.warning(f"账户 {account['account_number']} 余额转换失败: {latest_transaction['balance_amount']}")
                        account_balance = 0.0
                else:
                    logger.warning(f"账户 {account['account_number']} 最新交易缺少余额信息")

            account['account_balance'] = account_balance
        
        # 计算全局统计信息
        total_transactions = len(self.transactions)
        total_accounts = len(accounts)
        total_amount = sum(abs(t['transaction_amount']) for t in self.transactions)
        
        # 计算置信度
        all_transactions = []
        for acc_trans in transactions_by_account.values():
            all_transactions.extend(acc_trans)
        confidence_score = self._calculate_confidence_score(accounts, all_transactions)
        
        return {
            'success': True,
            'file_info': {
                'filename': self.file_path.split('/')[-1],
                'file_type': 'XLSX',
                'parser_used': 'ICBC_Format4_Enhanced'
            },
            'summary': {
                'total_accounts': total_accounts,
                'total_transactions': total_transactions,
                'date_range': self._get_date_range(),
                'total_amount': total_amount
            },
            'accounts': accounts,
            'transactions': self.transactions,  # 添加交易列表
            'transactions_by_account': transactions_by_account,
            'confidence_score': confidence_score,  # 添加置信度到顶层
            'metadata': {
                'customer_info': self.customer_info,
                'account_info': self.account_info,
                'parser_version': '2.0.0',
                'fields_compliance': '17_required_fields_standard',
                'parse_time': datetime.now().isoformat(),
                'confidence_score': confidence_score
            }
        }
    
    def _get_date_range(self) -> Dict[str, str]:
        """获取交易日期范围"""
        dates = []
        for transaction in self.transactions:
            if transaction.get('transaction_date'):
                try:
                    date_obj = datetime.strptime(transaction['transaction_date'], '%Y-%m-%d')
                    dates.append(date_obj)
                except ValueError:
                    continue
        
        if dates:
            min_date = min(dates).strftime('%Y-%m-%d')
            max_date = max(dates).strftime('%Y-%m-%d')
            return {'start_date': min_date, 'end_date': max_date}
        
        return {'start_date': '', 'end_date': ''}
    
    def _format_transaction_time(self, time_str: str) -> str:
        """
        格式化交易时间为标准HH:MM:SS格式
        
        Args:
            time_str: 原始时间字符串
            
        Returns:
            str: 标准格式的时间字符串
        """
        if not time_str or time_str in ['-', '未知时间', 'nan']:
            return '-'
        
        # 标准格式验证
        if re.match(r'^\d{2}:\d{2}:\d{2}$', time_str):
            return time_str
        
        # HH:MM格式补充秒数
        if re.match(r'^\d{2}:\d{2}$', time_str):
            return time_str + ':00'
        
        # H:M:S格式补充前导零
        if re.match(r'^\d{1,2}:\d{1,2}:\d{1,2}$', time_str):
            parts = time_str.split(':')
            return ':'.join(part.zfill(2) for part in parts)
        
        # H:M格式补充前导零和秒数
        if re.match(r'^\d{1,2}:\d{1,2}$', time_str):
            parts = time_str.split(':')
            return ':'.join(part.zfill(2) for part in parts) + ':00'
        
        return time_str
    
    def _format_amount_with_separator(self, amount: float) -> str:
        """
        格式化金额显示，添加千位分隔符
        
        Args:
            amount: 金额数值
            
        Returns:
            str: 格式化后的金额字符串
        """
        if amount is None or amount == 0:
            return "0.00"
        
        return f"{amount:,.2f}" 

    def extract_sample(self, limit: int = 10) -> Dict[str, Any]:
        """
        提取样本数据用于快速置信度评估
        
        Args:
            limit: 样本数据限制数量
            
        Returns:
            Dict: 样本数据
        """
        try:
            # 🔧 快速检查文件是否为4号格式的企业文件
            with pd.ExcelFile(self.file_path) as xls:
                sheet_names = xls.sheet_names
                
                # 检查是否包含企业格式的关键工作表
                has_customer_info = '客户信息' in sheet_names
                has_account_info = '开户信息' in sheet_names
                
                # 如果没有企业格式的关键工作表，置信度为0
                if not (has_customer_info or has_account_info):
                    logger.warning("快速评估：未找到企业格式的关键工作表（客户信息/开户信息）")
                    return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0, 'format_mismatch': True}}
                
                # 获取企业工作表列表（排除客户信息和开户信息）
                enterprise_sheets = [name for name in sheet_names 
                                   if name not in ['客户信息', '开户信息']]
                
                if not enterprise_sheets:
                    logger.warning("快速评估：未找到企业交易工作表")
                    return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
                
                # 使用第一个企业工作表进行快速评估
                target_sheet = enterprise_sheets[0]
                logger.info(f"快速评估：使用企业工作表 {target_sheet}")
                
                # 读取企业交易工作表的前20行
                df = pd.read_excel(xls, sheet_name=target_sheet, header=None, nrows=20)
                
                # 检查是否有企业交易数据格式
                # 企业格式应该有：帐号、工作日期、入帐时间、借贷标志、发生额、余额等字段
                header_found = False
                data_start_row = 1
                
                for idx in range(min(5, len(df))):
                    row_text = ' '.join(str(cell) for cell in df.iloc[idx] if pd.notna(cell))
                    if '帐号' in row_text and '发生额' in row_text and '余额' in row_text:
                        header_found = True
                        data_start_row = idx + 1
                        break
                
                if not header_found:
                    logger.warning("快速评估：未找到企业交易表头格式")
                    return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0, 'header_not_found': True}}
                
                # 构建企业格式的字段映射（基于表头）
                header_row = df.iloc[data_start_row - 1]
                field_mapping = {}
                
                for col_idx, cell_value in enumerate(header_row):
                    if pd.isna(cell_value):
                        continue
                    cell_text = str(cell_value).strip()
                    
                    if '帐号' in cell_text:
                        field_mapping['帐号'] = col_idx
                    elif '工作日期' in cell_text:
                        field_mapping['工作日期'] = col_idx
                    elif '入帐时间' in cell_text:
                        field_mapping['入帐时间'] = col_idx
                    elif '借贷标志' in cell_text:
                        field_mapping['借贷标志'] = col_idx
                    elif '发生额' in cell_text:
                        field_mapping['发生额'] = col_idx
                    elif '余额' in cell_text:
                        field_mapping['余额'] = col_idx
                
                # 检查必要字段是否存在
                required_fields = ['帐号', '发生额', '借贷标志']
                missing_fields = [field for field in required_fields if field not in field_mapping]
                
                if missing_fields:
                    logger.warning(f"快速评估：缺少必要字段 {missing_fields}")
                    return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0, 'missing_fields': missing_fields}}
                
                # 快速处理样本数据
                sample_accounts = []
                sample_transactions = []
                enterprise_name = target_sheet  # 使用工作表名作为企业名
                
                for idx in range(data_start_row, min(data_start_row + limit, len(df))):
                    if idx >= len(df):
                        break
                    
                    row = df.iloc[idx]
                    if row.isna().all():
                        continue
                    
                    try:
                        # 提取企业交易记录字段
                        account_number = str(row.iloc[field_mapping['帐号']]).strip().replace('\t', '') if '帐号' in field_mapping else ""
                        work_date = str(row.iloc[field_mapping['工作日期']]).strip().replace('\t', '') if '工作日期' in field_mapping else ""
                        amount_str = str(row.iloc[field_mapping['发生额']]).strip().replace('\t', '') if '发生额' in field_mapping else ""
                        dr_cr_flag = str(row.iloc[field_mapping['借贷标志']]).strip().replace('\t', '') if '借贷标志' in field_mapping else ""
                        
                        # 基本验证
                        if not account_number or len(account_number) < 10:
                            continue
                        if not amount_str or amount_str in ['nan', '']:
                            continue
                        
                        # 解析金额
                        try:
                            amount = float(self._clean_amount_string(amount_str))
                        except (ValueError, TypeError):
                            continue
                        
                        # 调整金额符号
                        if dr_cr_flag == "借":
                            amount = -abs(amount)
                        else:
                            amount = abs(amount)
                        
                        # 添加账户（如果还未添加）
                        if account_number not in [acc.get('account_number') for acc in sample_accounts]:
                            sample_accounts.append({
                                'account_number': account_number,
                                'holder_name': enterprise_name,
                                'card_number': "",  # 企业账户通常没有卡号
                                'bank_name': '中国工商银行',
                                'account_type': '企业账户'
                            })
                        
                        # 构建交易记录
                        transaction = {
                            'holder_name': enterprise_name,
                            'account_number': account_number,
                            'card_number': "",
                            'transaction_date': work_date if work_date != 'nan' else "",
                            'transaction_amount': amount,
                            'dr_cr_flag': "收" if dr_cr_flag == "贷" else "支",
                            'currency': "人民币"
                        }
                        
                        sample_transactions.append(transaction)
                        
                    except Exception as e:
                        logger.debug(f"跳过第{idx+1}行: {str(e)}")
                        continue
                
                logger.info(f"快速评估完成：提取{len(sample_transactions)}条样本交易，{len(sample_accounts)}个账户")
                
                return {
                    'accounts': sample_accounts,
                    'transactions': sample_transactions[:limit],
                    'metadata': {
                        'sample_size': len(sample_transactions),
                        'enterprise_sheets_count': len(enterprise_sheets),
                        'evaluation_mode': 'quick_enterprise_sample',
                        'target_sheet': target_sheet,
                        'field_mapping': field_mapping
                    }
                }
                
        except Exception as e:
            logger.error(f"企业格式快速样本数据提取失败: {str(e)}")
            return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0, 'error': str(e)}} 