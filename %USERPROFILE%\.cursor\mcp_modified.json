{"mcpServers": {"browser-tools": {"command": "npx", "args": ["-y", "@agentdeskai/browser-tools-mcp@1.2.0"], "enabled": true}, "playwright-mcp": {"command": "node", "args": ["D:/playwright-mcp/cli.js", "--headless", "--isolated", "--output-dir", "D:/playwright-mcp/output", "--browser", "chrome"]}, "interactive-feedback-mcp": {"command": "uv", "args": ["--directory", "D:/interactive-feedback-mcp", "run", "server.py"], "timeout": 600, "autoApprove": ["interactive_feedback"]}, "server-sequential-thinking": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@smithery-ai/server-sequential-thinking", "--key", "d581cc25-e461-491a-96a0-6efb57c1179c"]}, "desktop-commander": {"command": "node", "args": ["D:/DesktopCommander/dist/index.js"]}, "excel-mcp-server": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@negokaz/excel-mcp-server", "--key", "d581cc25-e461-491a-96a0-6efb57c1179c"]}, "task-master-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"OPENROUTER_API_KEY": "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"}}}}