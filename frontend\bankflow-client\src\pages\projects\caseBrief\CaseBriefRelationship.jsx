import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Card, Table, Button, Space, Modal, Form, Input, Select, message, Tag, Row, Col, Typography } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SyncOutlined } from '@ant-design/icons';
import { relationshipAPI } from '../../../services/api';

const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;

/**
 * 人物关系图谱管理组件
 * 支持人物关系录入、可视化展示等功能
 */
const CaseBriefRelationship = () => {
  const { projectId } = useParams();
  const [relationships, setRelationships] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editingId, setEditingId] = useState(null);

  // 组件挂载时获取数据
  useEffect(() => {
    if (projectId) {
      fetchRelationships();
    }
  }, [projectId]);

  // 获取人物关系列表
  const fetchRelationships = async () => {
    try {
      setLoading(true);
      const response = await relationshipAPI.getRelationships(projectId);
      
      const formattedRelationships = response.data.map(relationship => ({
        id: relationship.relationship_id,
        personA: relationship.person_a,
        personB: relationship.person_b,
        relationship: relationship.relationship_type,
        status: relationship.status,
        description: relationship.description,
        evidence: relationship.evidence,
        notes: relationship.notes,
        createTime: new Date(relationship.created_at).toLocaleString('zh-CN'),
        updateTime: relationship.updated_at ? new Date(relationship.updated_at).toLocaleString('zh-CN') : null
      }));
      setRelationships(formattedRelationships);
      message.success('获取人物关系列表成功');
    } catch (error) {
      console.error('获取人物关系列表出错:', error);
      message.error('获取人物关系列表失败，请检查网络连接');
      setRelationships([]);
    } finally {
      setLoading(false);
    }
  };

  // 关系类型配置
  const relationshipTypes = [
    { value: '父母', color: 'red' },
    { value: '儿女', color: 'pink' },
    { value: '夫妻', color: 'magenta' },
    { value: '兄弟姐妹', color: 'orange' },
    { value: '朋友', color: 'green' },
    { value: '同事', color: 'cyan' },
    { value: '上下级', color: 'blue' },
    { value: '其他', color: 'default' }
  ];

  // 表格列定义
  const columns = [
    {
      title: '人员A',
      dataIndex: 'personA',
      key: 'personA',
      width: 150,
    },
    {
      title: '关系',
      dataIndex: 'relationship',
      key: 'relationship',
      width: 120,
      render: (text) => {
        const relationConfig = relationshipTypes.find(r => r.value === text);
        return <Tag color={relationConfig?.color || 'default'}>{text}</Tag>;
      }
    },
    {
      title: '人员B',
      dataIndex: 'personB',
      key: 'personB',
      width: 150,
    },
    {
      title: '关系描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (text) => text || '未填写'
    },
    {
      title: '确认状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (text) => {
        const colorMap = {
          '已确认': 'green',
          '疑似': 'orange',
          '待核实': 'blue'
        };
        return <Tag color={colorMap[text] || 'default'}>{text || '待核实'}</Tag>;
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="text" 
            size="small"
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record)}
          />
          <Button 
            type="text" 
            size="small"
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDelete(record.id)}
          />
        </Space>
      ),
    },
  ];

  // 处理添加或编辑关系
  const handleAddOrEdit = () => {
    setIsModalVisible(true);
    if (editingId === null) {
      form.resetFields();
    }
  };

  // 处理编辑关系
  const handleEdit = (record) => {
    setEditingId(record.id);
    form.setFieldsValue({
      personA: record.personA,
      personB: record.personB,
      relationship: record.relationship,
      status: record.status,
      description: record.description,
      evidence: record.evidence,
      notes: record.notes,
    });
    setIsModalVisible(true);
  };

  // 处理删除关系
  const handleDelete = async (id) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条人物关系吗？删除后无法恢复。',
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await relationshipAPI.deleteRelationship(id);
          message.success('人物关系已删除');
          // 重新获取数据
          await fetchRelationships();
        } catch (error) {
          console.error('删除人物关系失败:', error);
          message.error('删除失败，请重试');
        }
      },
    });
  };

  // 处理模态框确认
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      
      const relationshipData = {
        project_id: projectId,
        person_a: values.personA,
        person_b: values.personB,
        relationship_type: values.relationship,
        status: values.status || null,
        description: values.description || null,
        evidence: values.evidence || null,
        notes: values.notes || null
      };

      if (editingId === null) {
        // 新增关系
        await relationshipAPI.createRelationship(relationshipData);
        message.success('人物关系已添加');
      } else {
        // 更新关系
        const updateData = { ...relationshipData };
        delete updateData.project_id; // 更新时不需要project_id
        
        await relationshipAPI.updateRelationship(editingId, updateData);
        message.success('人物关系已更新');
      }

      setIsModalVisible(false);
      setEditingId(null);
      form.resetFields();
      // 重新获取数据
      await fetchRelationships();
    } catch (error) {
      console.error('表单验证或提交失败:', error);
      message.error('操作失败，请重试');
    }
  };

  // 处理模态框取消
  const handleCancel = () => {
    setIsModalVisible(false);
    setEditingId(null);
    form.resetFields();
  };

  return (
    <div>
      <Card 
        title="人物关系图谱" 
        extra={
          <Space>
            <Button 
              loading={loading}
              onClick={fetchRelationships}
            >
              刷新
            </Button>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={handleAddOrEdit}
            >
              添加关系
            </Button>
          </Space>
        }
      >
        <Table 
          columns={columns} 
          dataSource={relationships} 
          rowKey="id" 
          loading={loading}
          pagination={{ 
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }}
          scroll={{ x: 1000 }}
        />
      </Card>

      <Modal
        title={editingId === null ? "添加人物关系" : "编辑人物关系"}
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={700}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          requiredMark={false}
        >
          <Form.Item
            name="personA"
            label="人员A"
            rules={[{ required: true, message: '请输入人员A姓名' }]}
          >
            <Input placeholder="请输入人员A姓名" />
          </Form.Item>

          <Form.Item
            name="relationship"
            label="关系类型"
            rules={[{ required: true, message: '请选择关系类型' }]}
          >
            <Select placeholder="请选择关系类型">
              {relationshipTypes.map(type => (
                <Option key={type.value} value={type.value}>
                  {type.value}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="personB"
            label="人员B"
            rules={[{ required: true, message: '请输入人员B姓名' }]}
          >
            <Input placeholder="请输入人员B姓名" />
          </Form.Item>

          <Form.Item
            name="status"
            label="确认状态"
          >
            <Select placeholder="请选择确认状态">
              <Option value="已确认">已确认</Option>
              <Option value="疑似">疑似</Option>
              <Option value="待核实">待核实</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="关系描述"
          >
            <TextArea 
              rows={3} 
              placeholder="请详细描述两人之间的关系..."
              showCount
              maxLength={500}
            />
          </Form.Item>

          <Form.Item
            name="evidence"
            label="证据材料"
          >
            <TextArea 
              rows={3} 
              placeholder="请描述支持此关系的证据材料..."
              showCount
              maxLength={500}
            />
          </Form.Item>

          <Form.Item
            name="notes"
            label="备注"
          >
            <TextArea 
              rows={2} 
              placeholder="其他需要说明的信息..."
              showCount
              maxLength={300}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CaseBriefRelationship; 