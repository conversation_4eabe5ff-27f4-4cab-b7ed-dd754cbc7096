# 银行解析器开发通用指南

## 📋 核心开发原则

### 1. 字段映射理解
**❌ 常见错误**：混淆"卡号"和"账号"的概念
**✅ 正确理解**：
- **卡号** = 原表的"客户账号"（银行卡号）
- **账号** = 原表的"账户账号"（银行内部账户编号）
- **必须严格按照用户定义的字段映射关系**

### 2. 工作表处理策略
**❌ 常见错误**：自动合并相同账号的工作表
**✅ 正确处理**：
- **每个工作表代表独立的账户记录**
- **即使账号相同，也应独立显示**
- **不要擅自进行数据合并**
- **工作表名称应包含在账户ID中以确保唯一性**

### 3. 统计数据计算
**❌ 常见错误**：统计数据显示为0或"未知"
**✅ 正确计算**：
- **收入总额**：所有收入交易的金额总和
- **支出总额**：所有支出交易的金额总和
- **时间范围**：从最早到最晚的交易日期
- **必须在解析过程中实时计算统计数据**

## 🔍 开发流程检查点

### 阶段1：需求理解
- [ ] 明确字段映射关系（卡号vs账号）
- [ ] 确认工作表处理策略（独立vs合并）
- [ ] 理解数据结构和业务逻辑
- [ ] 确认用户的具体要求

### 阶段2：代码实现
- [ ] 正确解析表头信息
- [ ] 实现字段映射逻辑
- [ ] 处理工作表独立性
- [ ] 实现统计数据计算
- [ ] 生成唯一账户ID

### 阶段3：功能验证
- [ ] 启动前后端服务
- [ ] 上传测试文件
- [ ] 验证解析结果
- [ ] 检查账户汇总表
- [ ] 验证交易明细

## ⚠️ 易错点总结

### 1. 字段映射错误
**问题描述**：将客户账号映射到account_number，账户账号没有正确解析
**解决方案**：
```python
# 错误示例
header_info["account_number"] = 客户账号  # ❌

# 正确示例  
header_info["card_number"] = 客户账号     # ✅ 卡号
header_info["account_number"] = 账户账号  # ✅ 账号
```

### 2. 工作表合并错误
**问题描述**：自动合并相同账号的不同工作表
**解决方案**：
```python
# 错误：合并相同账号
if account_number in existing_accounts:
    merge_accounts()  # ❌

# 正确：保持独立
account_id = f"{account_number}_{cardholder_name}_{sheet_name}"  # ✅
```

### 3. 统计数据计算错误
**问题描述**：收入、支出显示为¥0.00，时间范围显示"未知"
**解决方案**：
```python
# 在解析过程中实时计算
for transaction in transactions:
    if transaction['amount'] > 0:
        total_income += transaction['amount']
    else:
        total_expense += abs(transaction['amount'])
    
    # 更新时间范围
    update_date_range(transaction['date'])
```

### 4. 收支符号错误
**问题描述**：收支符号显示为"未知"
**解决方案**：
```python
# 根据交易类型和金额正确判断
if transaction_type in ['存款', '转入'] or amount > 0:
    income_expense = '收入'
elif transaction_type in ['取款', '转出'] or amount < 0:
    income_expense = '支出'
```

## 🧪 验证清单

### 必须验证的内容

#### 1. 账户汇总表验证
- [ ] **字段映射正确**：卡号显示客户账号，账号显示账户账号
- [ ] **账户数量正确**：每个工作表对应一个独立账户
- [ ] **统计数据正确**：收入、支出、时间范围都不为空
- [ ] **交易笔数正确**：与实际交易数量一致

#### 2. 交易明细验证
- [ ] **所有字段完整**：序号、持卡人、银行、账号、卡号、日期、时间等
- [ ] **收支符号正确**：显示"收入"/"支出"而不是"未知"
- [ ] **金额计算正确**：交易金额和余额计算准确
- [ ] **分页功能正常**：能正确显示所有交易记录

#### 3. 数据一致性验证
- [ ] **汇总与明细一致**：账户汇总的统计数据与明细计算结果一致
- [ ] **工作表独立性**：相同卡号的不同工作表独立显示
- [ ] **字段映射一致**：前端显示与后端解析的字段映射一致

## 🚨 强制验证规则

### RULE 1: 完整测试流程
**每次修改解析器后必须执行**：
1. 重启后端服务
2. 重新上传测试文件
3. 完整验证解析结果
4. 检查交易明细的每个字段

### RULE 2: 字段映射验证
**必须确认**：
- 卡号字段显示的是客户账号
- 账号字段显示的是账户账号
- 不能混淆或颠倒

### RULE 3: 统计数据验证
**必须检查**：
- 收入总额不为¥0.00
- 支出总额不为¥0.00  
- 时间范围不为"未知"
- 净流水计算正确

### RULE 4: 工作表独立性验证
**必须确认**：
- 每个工作表生成独立账户
- 相同卡号的不同工作表不合并
- 账户ID包含工作表标识

## 📝 开发检查清单

### 开发前
- [ ] 仔细阅读用户需求
- [ ] 明确字段映射关系
- [ ] 了解数据结构特点
- [ ] 确认处理策略

### 开发中
- [ ] 正确实现字段解析
- [ ] 保持工作表独立性
- [ ] 实时计算统计数据
- [ ] 处理异常情况

### 开发后
- [ ] 执行完整测试流程
- [ ] 验证所有必检项目
- [ ] 确认数据一致性
- [ ] 记录测试结果

## 💡 最佳实践

### 1. 代码结构
```python
def parse_sheet(sheet_data, sheet_name):
    # 1. 解析表头信息
    header_info = parse_header(sheet_data)
    
    # 2. 生成唯一账户ID
    account_id = generate_unique_id(header_info, sheet_name)
    
    # 3. 解析交易数据
    transactions = parse_transactions(sheet_data, header_info)
    
    # 4. 计算统计数据
    statistics = calculate_statistics(transactions)
    
    return create_account(account_id, header_info, transactions, statistics)
```

### 2. 字段映射
```python
# 明确的字段映射
FIELD_MAPPING = {
    '客户账号': 'card_number',      # 卡号
    '账户账号': 'account_number',   # 账号
    '持卡人': 'cardholder_name',
    # ... 其他字段
}
```

### 3. 统计计算
```python
def calculate_statistics(transactions):
    total_income = sum(t['amount'] for t in transactions if t['amount'] > 0)
    total_expense = sum(abs(t['amount']) for t in transactions if t['amount'] < 0)
    date_range = f"{min_date} 至 {max_date}"
    return {
        'total_income': total_income,
        'total_expense': total_expense,
        'date_range': date_range
    }
```

## 🎯 成功标准

解析器开发成功的标准：
1. **字段映射100%正确**
2. **统计数据100%准确**
3. **工作表独立性100%保持**
4. **交易明细100%完整**
5. **前端显示100%正常**

只有满足以上所有标准，才能认为解析器开发完成。

## 🔧 调试技巧

### 1. 日志分析
```python
# 添加详细日志
logger.info(f"解析工作表: {sheet_name}")
logger.info(f"提取客户账号（卡号）: {card_number}")
logger.info(f"提取账户账号: {account_number}")
logger.info(f"生成账户ID: {account_id}")
logger.info(f"解析交易数量: {len(transactions)}")
```

### 2. 数据验证
```python
# 验证关键数据
assert card_number != "Unknown", "客户账号未正确解析"
assert account_number != "Unknown", "账户账号未正确解析"
assert len(transactions) > 0, "交易数据为空"
assert total_income > 0 or total_expense > 0, "统计数据异常"
```

### 3. 前端验证步骤
1. **检查账户汇总表**：字段、数量、统计数据
2. **点击"查看交易"**：验证明细数据
3. **检查分页功能**：确认所有数据可访问
4. **验证字段映射**：卡号vs账号显示正确

## 📊 常见数据结构处理

### 1. Excel多工作表
```python
for sheet_name in workbook.sheet_names:
    sheet_data = workbook[sheet_name]
    # 每个工作表独立处理
    account = parse_sheet(sheet_data, sheet_name)
    accounts.append(account)
```

### 2. 表头信息提取
```python
def extract_header_info(sheet_data):
    header_info = {}
    for row in sheet_data.iter_rows():
        row_text = ' '.join([str(cell.value) for cell in row if cell.value])

        # 客户账号（卡号）
        if "客户账号" in row_text:
            header_info["card_number"] = extract_value(row, "客户账号")

        # 账户账号
        if "账户账号" in row_text:
            header_info["account_number"] = extract_value(row, "账户账号")

    return header_info
```

### 3. 交易数据解析
```python
def parse_transactions(sheet_data, header_info):
    transactions = []
    for row in transaction_rows:
        transaction = {
            'cardholder_name': header_info['cardholder_name'],
            'bank_name': header_info['bank_name'],
            'account_number': header_info['account_number'],
            'card_number': header_info['card_number'],
            # ... 其他字段
        }

        # 计算收支符号
        if transaction['amount'] > 0:
            transaction['income_expense'] = '收入'
        else:
            transaction['income_expense'] = '支出'

        transactions.append(transaction)

    return transactions
```

## 🚨 紧急修复指南

### 当发现字段映射错误时
1. **立即停止测试**
2. **检查字段映射逻辑**
3. **修复代码中的字段赋值**
4. **重启后端服务**
5. **重新完整测试**

### 当发现统计数据为0时
1. **检查统计计算逻辑**
2. **验证交易金额解析**
3. **确认收支判断逻辑**
4. **重新计算并验证**

### 当发现工作表合并时
1. **检查账户ID生成逻辑**
2. **确保包含工作表标识**
3. **移除合并逻辑**
4. **验证独立性**

## 📋 测试用例模板

### 基础测试用例
```
测试文件：[银行名称]测试文件.xlsx
预期结果：
- 账户数量：X个
- 总交易数：X条
- 字段映射：卡号=客户账号，账号=账户账号
- 统计数据：收入>0，支出>0，时间范围正确
```

### 边界测试用例
```
测试场景：
1. 单工作表文件
2. 多工作表文件
3. 相同卡号不同账户
4. 空交易记录
5. 特殊字符处理
```

## 🎓 学习要点

### 从民生银行开发中学到的教训
1. **不要假设用户需求**：必须明确字段映射关系
2. **不要自作主张合并数据**：保持原始数据结构
3. **不要忽略统计计算**：实时计算并验证
4. **不要跳过完整测试**：每次修改都要完整验证

### 开发心态
- **谨慎理解需求**：多次确认用户要求
- **保守处理数据**：不擅自修改数据结构
- **完整验证功能**：不遗漏任何验证步骤
- **及时记录问题**：为后续开发积累经验

## 📚 参考资料

### 相关文档
- 银行解析器插件开发规范
- 前端交易明细显示规范
- 数据库字段映射标准

### 工具和命令
```bash
# 重启后端服务
cd backend && python main.py

# 查看解析日志
tail -f backend/logs/parser.log

# 前端访问地址
http://localhost:3000
```

---

**记住：每个银行解析器都是独特的，但这些通用原则和检查点适用于所有银行解析器的开发。严格遵循这个指南，可以避免重复犯错，提高开发效率和质量。**
