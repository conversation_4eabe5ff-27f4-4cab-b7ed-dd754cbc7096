import React, { useState, useEffect } from 'react';
import { Table, Card, Button, Space, Modal, Form, Input, Switch, message, Tooltip } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { bankApi } from '../../services/api';

/**
 * 银行设置页面组件
 * 
 * @returns {JSX.Element} 银行设置页面
 */
const BankSettings = () => {
  const [loading, setLoading] = useState(true);
  const [banks, setBanks] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [currentBank, setCurrentBank] = useState(null);

  useEffect(() => {
    // 模拟数据加载
    setTimeout(() => {
      setBanks([
        {
          bank_id: '1',
          bank_name: '中国工商银行',
          bank_code: 'ICBC',
          is_enabled: true,
          template_count: 2
        },
        {
          bank_id: '2',
          bank_name: '中国农业银行',
          bank_code: 'ABC',
          is_enabled: true,
          template_count: 3
        },
        {
          bank_id: '3',
          bank_name: '中国银行',
          bank_code: 'BOC',
          is_enabled: true,
          template_count: 1
        },
        {
          bank_id: '4',
          bank_name: '中国建设银行',
          bank_code: 'CCB',
          is_enabled: true,
          template_count: 2
        },
        {
          bank_id: '5',
          bank_name: '交通银行',
          bank_code: 'COMM',
          is_enabled: false,
          template_count: 0
        }
      ]);
      setLoading(false);
    }, 1000);
    
    // 实际代码应该调用API
    // bankApi.getBanks().then(data => {
    //   setBanks(data);
    //   setLoading(false);
    // });
  }, []);

  const showModal = (bank = null) => {
    setCurrentBank(bank);
    setIsModalVisible(true);
    
    if (bank) {
      form.setFieldsValue({
        bank_name: bank.bank_name,
        bank_code: bank.bank_code,
        is_enabled: bank.is_enabled
      });
    } else {
      form.resetFields();
      form.setFieldsValue({
        is_enabled: true
      });
    }
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (currentBank) {
        // 编辑现有银行
        // 实际代码应该调用API
        // await bankApi.updateBank(currentBank.bank_id, values);
        setBanks(banks.map(b => 
          b.bank_id === currentBank.bank_id 
            ? { ...b, ...values } 
            : b
        ));
        message.success('银行信息更新成功');
      } else {
        // 创建新银行
        // 实际代码应该调用API
        // const newBank = await bankApi.createBank(values);
        const newBank = {
          bank_id: Date.now().toString(),
          ...values,
          template_count: 0
        };
        setBanks([...banks, newBank]);
        message.success('银行添加成功');
      }
      
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('表单错误:', error);
    }
  };

  const handleDelete = (bankId) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除此银行吗？此操作不可恢复。',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        // 实际代码应该调用API
        // await bankApi.deleteBank(bankId);
        setBanks(banks.filter(b => b.bank_id !== bankId));
        message.success('银行已删除');
      }
    });
  };

  const toggleStatus = async (bankId, status) => {
    // 实际代码应该调用API
    // await bankApi.updateBank(bankId, { is_enabled: status });
    setBanks(banks.map(b => 
      b.bank_id === bankId 
        ? { ...b, is_enabled: status } 
        : b
    ));
    message.success(`银行已${status ? '启用' : '禁用'}`);
  };

  const columns = [
    {
      title: '银行名称',
      dataIndex: 'bank_name',
      key: 'bank_name',
      sorter: (a, b) => a.bank_name.localeCompare(b.bank_name)
    },
    {
      title: '银行代码',
      dataIndex: 'bank_code',
      key: 'bank_code',
    },
    {
      title: '模板数量',
      dataIndex: 'template_count',
      key: 'template_count',
      sorter: (a, b) => a.template_count - b.template_count
    },
    {
      title: '状态',
      dataIndex: 'is_enabled',
      key: 'is_enabled',
      render: (isEnabled, record) => (
        <Switch 
          checked={isEnabled} 
          onChange={(checked) => toggleStatus(record.bank_id, checked)}
        />
      ),
      filters: [
        { text: '已启用', value: true },
        { text: '已禁用', value: false }
      ],
      onFilter: (value, record) => record.is_enabled === value
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="编辑">
            <Button 
              icon={<EditOutlined />} 
              onClick={() => showModal(record)} 
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button 
              danger 
              icon={<DeleteOutlined />} 
              onClick={() => handleDelete(record.bank_id)} 
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card 
        title="银行设置" 
        extra={
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={() => showModal()}
          >
            添加银行
          </Button>
        }
      >
        <Table 
          columns={columns} 
          dataSource={banks} 
          rowKey="bank_id" 
          loading={loading}
        />
      </Card>

      <Modal
        title={currentBank ? "编辑银行" : "添加银行"}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="bank_name"
            label="银行名称"
            rules={[{ required: true, message: '请输入银行名称' }]}
          >
            <Input placeholder="请输入银行名称" />
          </Form.Item>
          <Form.Item
            name="bank_code"
            label="银行代码"
            rules={[{ required: true, message: '请输入银行代码' }]}
          >
            <Input placeholder="请输入银行代码" />
          </Form.Item>
          <Form.Item
            name="is_enabled"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default BankSettings; 