"""
平安银行Format1解析器插件

开发要点（按用户强制规则实现）：
1. 户名（持卡人）、卡号、账号仅在表头出现，明细行不再重复 → 解析时必须从表头区域提取并回填到每条明细，严禁为空或未知。
2. 字段映射：
   - 可用余额 → 交易余额（balance_amount/account_balance）
   - 摘要 → 交易方式（transaction_method/transaction_type）
   - 备注 → 备注1（remark/remark1）
   - 其他列按语义就近匹配（交易对手名称/账号/开户行等）
3. D/C 列（+ 收入，- 支出）→ dr_cr_flag：收/支；transaction_amount统一为正数；transaction_direction：收入/支出。

插件遵循统一插件接口，不在API层直接导入解析器类，所有调用通过插件管理器完成。
"""

import os
import re
import uuid
import time
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

import pandas as pd

# 兼容导入基础插件接口
try:
    from ...core.plugin_interface import BasePlugin
except ImportError:  # 测试环境兜底
    class BasePlugin:
        def __init__(self):
            self.name = "pingan_format1_plugin"
            self.version = "1.0.0"
            self.description = "平安银行Format1解析器插件"


logger = logging.getLogger(__name__)


class Plugin(BasePlugin):
    """平安银行Format1解析器插件"""

    def __init__(self, file_path: str | None = None):
        super().__init__()
        self.name = "pingan_format1_plugin"
        self.version = "1.0.0"
        self.description = "平安银行流水（个人交易明细）解析器"
        self.bank_name = "平安银行"
        self.file_path = file_path

        # 结果容器
        self.accounts: List[Dict[str, Any]] = []
        self.transactions: List[Dict[str, Any]] = []
        self.transactions_by_account: Dict[str, List[Dict[str, Any]]] = {}
        self.start_time = time.time()

        # 关键列关键词（用于探测表头行与列映射）
        self.header_keywords = [
            "D/C", "交易日期", "交易时间", "交易金额", "可用余额", "摘要",
            "备注", "交易对手", "交易对手账号", "交易对手开户行"
        ]

    # ===== 公共接口 =====
    def get_supported_formats(self) -> List[str]:
        return ["pingan_format1"]

    def validate_file(self, file_path: str) -> bool:
        try:
            if not os.path.exists(file_path):
                return False
            if not file_path.lower().endswith((".xlsx", ".xls")):
                return False

            xls = pd.ExcelFile(file_path)
            for sheet in xls.sheet_names:
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet, header=None, dtype=str, nrows=25)
                    if df is None or df.empty:
                        continue
                    content = df.astype(str)
                    hits = 0
                    for kw in ["交易日期", "交易金额", "可用余额", "摘要"]:
                        if (content.apply(lambda s: s.str.contains(kw, na=False)).any()).any():
                            hits += 1
                    # 同时出现4个关键字段基本可判定
                    if hits >= 4:
                        return True
                except Exception:
                    continue
            return False
        except Exception:
            return False

    def calculate_confidence(self, file_path: str) -> float:
        try:
            return 0.95 if self.validate_file(file_path) else 0.0
        except Exception:
            return 0.0

    def parse(self, file_path: str | None = None) -> Dict[str, Any]:
        try:
            if file_path:
                self.file_path = file_path
            if not self.file_path:
                raise ValueError("未指定文件路径")

            self.accounts = []
            self.transactions = []
            self.transactions_by_account = {}

            xls = pd.ExcelFile(self.file_path)

            # 选择包含关键列的工作表
            target_sheet, header_row = self._select_target_sheet_and_header(xls)
            if target_sheet is None:
                return self._failed_result("未找到包含交易明细的工作表")

            df = pd.read_excel(self.file_path, sheet_name=target_sheet, header=header_row, dtype=str)
            if df is None or df.empty:
                return self._failed_result("工作表为空")

            # 列映射
            column_map = self._build_column_mapping(df)
            logger.info(f"🔧 平安银行列映射: {column_map}")

            # 表头区域信息（持卡人/账号/卡号/币种）
            header_info = self._extract_header_info(self.file_path, target_sheet, header_row)
            holder_name = header_info.get("holder_name", "")
            account_number = header_info.get("account_number", "")
            card_number = header_info.get("card_number", "")
            currency = header_info.get("currency", "CNY")

            # 遍历明细
            seq = 0
            parsed_dates: List[str] = []
            total_inflow = 0.0
            total_outflow = 0.0
            latest_balance: Optional[float] = None

            for _, row in df.iterrows():
                # 过滤无日期或无金额/余额的行
                date_val = self._get_cell(row, column_map, "date")
                amount_val = self._get_cell(row, column_map, "amount")
                balance_val = self._get_cell(row, column_map, "balance")

                if not (date_val and (amount_val is not None) and (balance_val is not None)):
                    continue

                seq += 1

                # D/C 方向
                dc_symbol = self._get_cell(row, column_map, "dc") or ""
                dc_symbol = str(dc_symbol).strip()
                if dc_symbol == "+":
                    dr_cr_flag = "收"
                    direction = "收入"
                elif dc_symbol == "-":
                    dr_cr_flag = "支"
                    direction = "支出"
                else:
                    # 兜底：通过金额正负判断，但平安文件通常金额为正，方向靠D/C
                    dr_cr_flag = "收" if float(amount_val) >= 0 else "支"
                    direction = "收入" if dr_cr_flag == "收" else "支出"

                # 交易方式/备注
                summary = self._get_cell(row, column_map, "summary") or ""
                remark1 = self._get_cell(row, column_map, "remark") or ""

                # 对手信息
                cp_name = self._get_cell(row, column_map, "cp_name") or ""
                cp_account = self._get_cell(row, column_map, "cp_account") or ""
                cp_bank = self._get_cell(row, column_map, "cp_bank") or ""

                tx = {
                    "transaction_id": str(uuid.uuid4()),
                    "sequence_number": seq,
                    "holder_name": holder_name,
                    "cardholder_name": holder_name,
                    "bank_name": self.bank_name,
                    "account_number": account_number,
                    "card_number": card_number,
                    "currency": currency,
                    "transaction_date": self._format_date(date_val),
                    "transaction_time": self._format_time(self._get_cell(row, column_map, "time")),
                    "transaction_method": summary,
                    "transaction_type": summary,
                    "transaction_amount": abs(float(amount_val)),
                    "transaction_direction": direction,
                    "dr_cr_flag": dr_cr_flag,
                    "account_balance": float(balance_val),
                    "balance_amount": float(balance_val),
                    "counterparty_name": cp_name,
                    "counterparty_account": cp_account,
                    "counterparty_bank": cp_bank,
                    "remark": remark1,
                    "remark1": remark1,
                }

                # 统计
                if direction == "收入":
                    total_inflow += abs(float(amount_val))
                else:
                    total_outflow += abs(float(amount_val))

                latest_balance = float(balance_val)
                if tx["transaction_date"]:
                    parsed_dates.append(tx["transaction_date"])

                self.transactions.append(tx)

            # 账户汇总
            date_range = ""
            if parsed_dates:
                start_date = min(parsed_dates)
                end_date = max(parsed_dates)
                date_range = start_date if start_date == end_date else f"{start_date} 到 {end_date}"

            account_id = str(uuid.uuid4())
            account = {
                "account_id": account_id,
                "person_name": holder_name,
                "holder_name": holder_name,
                "cardholder_name": holder_name,
                "bank_name": self.bank_name,
                "account_name": holder_name,
                "account_number": account_number,
                "card_number": card_number,
                "currency": currency,
                "date_range": date_range,
                "transactions_count": len(self.transactions),
                "total_inflow": total_inflow,
                "total_outflow": total_outflow,
                "account_balance": latest_balance or 0.0,
            }

            self.accounts.append(account)

            # 组织交易按账户分组
            self.transactions_by_account = {f"{holder_name}_{account_number}": self.transactions}

            # 结果
            result = {
                "success": True,
                "accounts": self.accounts,
                "transactions": self.transactions,
                "transactions_by_account": self.transactions_by_account,
                "parser_type": self.name,
                "confidence_score": self.calculate_confidence(self.file_path),
                "summary": {
                    "bank_name": self.bank_name,
                    "account_name": holder_name,
                    "account_number": account_number,
                    "card_number": card_number,
                    "currency": currency,
                    "total_transactions": len(self.transactions),
                    "total_inflow": total_inflow,
                    "total_outflow": total_outflow,
                    "date_range": date_range,
                },
            }
            logger.info(
                f"✅ 平安解析成功: 账户={account_number}, 持卡人={holder_name}, 笔数={len(self.transactions)}"
            )
            return result

        except Exception as e:
            logger.error(f"❌ 平安银行解析失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return self._failed_result(str(e))

    # ===== 内部工具方法 =====
    def _failed_result(self, msg: str) -> Dict[str, Any]:
        return {
            "success": False,
            "error": msg,
            "accounts": [],
            "transactions": [],
            "transactions_by_account": {},
        }

    def _select_target_sheet_and_header(self, xls: pd.ExcelFile) -> Tuple[Optional[str], Optional[int]]:
        best_sheet = None
        best_header_row = None
        best_score = -1
        for sheet in xls.sheet_names:
            try:
                df_head = pd.read_excel(xls, sheet_name=sheet, header=None, dtype=str, nrows=30)
                if df_head is None or df_head.empty:
                    continue
                # 找到最可能的表头行
                header_row = self._detect_header_row(df_head)
                row = df_head.iloc[header_row].astype(str).tolist()
                score = sum(1 for kw in self.header_keywords if any(kw in str(c) for c in row))
                if score > best_score:
                    best_score = score
                    best_sheet = sheet
                    best_header_row = header_row
            except Exception:
                continue
        return best_sheet, best_header_row

    def _detect_header_row(self, df_no_header: pd.DataFrame) -> int:
        try:
            max_hits = -1
            candidate = 0
            limit = min(25, df_no_header.shape[0])
            for i in range(limit):
                row = df_no_header.iloc[i].astype(str)
                hits = 0
                for kw in self.header_keywords:
                    if row.str.contains(kw, na=False).any():
                        hits += 1
                if hits > max_hits:
                    max_hits = hits
                    candidate = i
            return candidate
        except Exception:
            return 0

    def _build_column_mapping(self, df_with_header: pd.DataFrame) -> Dict[str, int]:
        mapping: Dict[str, int] = {}

        # 多级表头扁平化
        if isinstance(df_with_header.columns, pd.MultiIndex):
            flat = []
            for col in df_with_header.columns:
                flat.append("".join([str(x) for x in col if str(x) != "nan"]))
            df_with_header = df_with_header.copy()
            df_with_header.columns = flat

        for idx, col in enumerate(df_with_header.columns):
            name = str(col).strip()
            if name in ("D/C", "D／C", "DC"):
                mapping["dc"] = idx
            elif "交易日期" in name:
                mapping["date"] = idx
            elif "交易时间" in name:
                mapping["time"] = idx
            elif "交易金额" in name:
                mapping["amount"] = idx
            elif "可用余额" in name:
                mapping["balance"] = idx
            elif name == "摘要" or "摘要" in name:
                mapping["summary"] = idx
            elif name == "备注" or "备注" in name:
                mapping["remark"] = idx
            elif "交易对手" in name and "名称" in name:
                mapping["cp_name"] = idx
            elif ("交易对手账号" in name) or ("对方账号" in name):
                mapping["cp_account"] = idx
            elif ("交易对手开户行" in name) or ("对方开户行" in name):
                mapping["cp_bank"] = idx

        return mapping

    def _extract_header_info(self, file_path: str, sheet_name: str, header_row: int) -> Dict[str, str]:
        """从数据表头上方区域提取：户名/账号/卡号/币种。"""
        info = {"holder_name": "", "account_number": "", "card_number": "", "currency": "CNY"}
        try:
            # 读取表头上方若干行
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=None, dtype=str, nrows=max(5, header_row))
            if df is None or df.empty:
                return info
            head = df.astype(str)

            def find_value(label: str) -> Optional[str]:
                for i in range(head.shape[0]):
                    for j in range(head.shape[1] - 1):
                        cell = str(head.iloc[i, j]).strip()
                        if label in cell:
                            # 优先取右侧单元格；如为空再尝试下方
                            right = str(head.iloc[i, j + 1]).strip() if j + 1 < head.shape[1] else ""
                            if right and right != "nan":
                                return right
                            down = str(head.iloc[i + 1, j]).strip() if i + 1 < head.shape[0] else ""
                            if down and down != "nan":
                                return down
                return None

            holder = find_value("户名") or find_value("户名：")
            account = find_value("账号") or find_value("账户")
            card = find_value("卡号")
            curr = find_value("货币") or find_value("币别")

            if holder:
                info["holder_name"] = holder
            if account:
                info["account_number"] = account
            if card:
                info["card_number"] = self._format_card_number(card)
            if curr:
                info["currency"] = curr if curr else "CNY"

            return info
        except Exception:
            return info

    def _get_cell(self, row: pd.Series, mapping: Dict[str, int], key: str):
        if key not in mapping:
            return None
        try:
            val = row.iloc[mapping[key]]
            if pd.isna(val):
                return None
            text = str(val).strip()
            if key in ("amount", "balance"):
                return self._parse_amount(text)
            return text
        except Exception:
            return None

    @staticmethod
    def _format_card_number(raw: str) -> str:
        if not raw:
            return ""
        s = str(raw).strip()
        # 去小数点科学计数
        if "." in s and s.replace(".", "").isdigit():
            try:
                s = s.split(".")[0]
            except Exception:
                pass
        return s if re.fullmatch(r"\d{13,19}", s or "") else s

    @staticmethod
    def _parse_amount(s: str) -> Optional[float]:
        try:
            if s is None or s == "" or s == "nan":
                return None
            s = re.sub(r"[¥$,，\s]", "", str(s))
            return float(s)
        except Exception:
            return None

    @staticmethod
    def _format_date(s: Optional[str]) -> str:
        if not s:
            return ""
        try:
            # 支持 20180329 / 2018-03-29 / 2018/03/29 / 2018.03.29
            s = str(s).strip()
            candidates = ["%Y%m%d", "%Y-%m-%d", "%Y/%m/%d", "%Y.%m.%d"]
            for fmt in candidates:
                try:
                    return datetime.strptime(s, fmt).strftime("%Y-%m-%d")
                except Exception:
                    continue
            # 兜底：pandas 解析
            return pd.to_datetime(s).strftime("%Y-%m-%d")
        except Exception:
            return s

    @staticmethod
    def _format_time(s: Optional[str]) -> str:
        if not s or str(s).strip() in ("nan", ""):
            return ""
        s = str(s).strip()
        try:
            if re.fullmatch(r"\d{2}:\d{2}:\d{2}", s):
                return s
            if re.fullmatch(r"\d{2}:\d{2}", s):
                return f"{s}:00"
            if re.fullmatch(r"\d{6}", s):
                return f"{s[:2]}:{s[2:4]}:{s[4:6]}"
            if "." in s:
                return s.replace(".", ":")
            return s
        except Exception:
            return s





    def extract_sample(self, file_path: str = None, limit: int = 10) -> Dict[str, Any]:
        """
        提取样本数据用于快速置信度评估 - 平安银行Format1精准识别版本
        参考工商银行成功模式：数据特征识别 + 固定列位置 + 严格验证
        
        Args:
            file_path: 文件路径
            limit: 样本数量限制
            
        Returns:
            Dict: 包含样本账户和交易的字典
        """
        try:
            target_file = file_path or self.file_path
            if not target_file:
                raise ValueError("未设置文件路径")
                
            logger.info(f"🏦 平安银行Format1样本提取开始: {target_file}")
            
            # 1. 数据特征识别：使用实际解析器的逻辑
            # 平安银行通常有固定的表头结构
            df_raw = pd.read_excel(target_file, header=None, nrows=20)
            
            # 寻找表头行（包含"交易日期"、"交易金额"等关键字段）
            header_row = 0
            for row_idx in range(min(10, len(df_raw))):
                row_values = [str(val).strip() for val in df_raw.iloc[row_idx] if pd.notna(val)]
                row_text = ' '.join(row_values)
                
                # 检查是否包含平安银行特征字段
                if any(keyword in row_text for keyword in ['交易日期', '交易时间', '交易金额', '余额']):
                    header_row = row_idx
                    logger.info(f"找到平安银行表头行: {row_idx + 1}")
                    break
            
            # 2. 固定列位置：读取样本数据
            df = pd.read_excel(target_file, header=header_row, nrows=limit + 10)
            
            if df.empty:
                raise ValueError("数据表为空")
            
            sample_accounts = []
            sample_transactions = []
            
            # 3. 严格验证：提取账户信息
            try:
                cardholder_name = "未知持卡人"
                account_number = "Unknown"
                
                # 从数据中提取账户信息
                if len(df) > 0:
                    for idx, row in df.head(5).iterrows():  # 检查前5行
                        for col_name in df.columns:
                            col_str = str(col_name).strip()
                            cell_value = str(row.get(col_name, "")).strip()
                            
                            if cell_value and cell_value != "nan":
                                # 姓名识别：2-10个字符，包含中文
                                if (any(keyword in col_str for keyword in ['持卡人', '户名', '姓名', '客户']) and
                                    len(cell_value) >= 2 and len(cell_value) <= 10 and
                                    any('\u4e00' <= char <= '\u9fff' for char in cell_value)):
                                    cardholder_name = cell_value
                                
                                # 账号识别：10-25位数字或字母数字组合
                                elif (any(keyword in col_str for keyword in ['账号', '账户', '卡号']) and
                                      len(cell_value) >= 10 and len(cell_value) <= 25 and
                                      cell_value.replace(' ', '').replace('-', '').isalnum()):
                                    account_number = cell_value
                
                account = {
                    "person_name": cardholder_name,
                    "bank_name": "平安银行",
                    "account_number": account_number,
                    "card_number": account_number,
                    "account_name": f"{cardholder_name}的账户"
                }
                sample_accounts.append(account)
                
            except Exception as e:
                logger.debug(f"提取账户信息失败，使用默认值: {e}")
                sample_accounts.append({
                    "person_name": "未知持卡人",
                    "bank_name": "平安银行", 
                    "account_number": "Unknown",
                    "card_number": "Unknown",
                    "account_name": "默认账户"
                })
            
            # 4. 严格验证：提取交易数据，确保4维度字段完整
            for idx, row in df.iterrows():
                if len(sample_transactions) >= limit:
                    break
                    
                try:
                    # 提取必要字段
                    transaction_date = ""
                    transaction_amount = 0.0
                    balance = 0.0
                    dr_cr_flag = ""
                    summary = ""
                    
                    # 字段映射（基于列名匹配）
                    for col_name in df.columns:
                        col_str = str(col_name).strip()
                        cell_value = row.get(col_name, "")
                        
                        if pd.notna(cell_value):
                            # 交易日期
                            if any(keyword in col_str for keyword in ['交易日期', '日期', '记账日期']):
                                date_str = str(cell_value).strip()
                                if date_str and date_str != "nan":
                                    # 标准化日期格式
                                    try:
                                        if '-' in date_str or '/' in date_str:
                                            transaction_date = date_str[:10]  # 取前10位
                                        else:
                                            # 处理YYYYMMDD格式
                                            date_clean = date_str.replace(' ', '')[:8]
                                            if len(date_clean) == 8 and date_clean.isdigit():
                                                transaction_date = f"{date_clean[:4]}-{date_clean[4:6]}-{date_clean[6:8]}"
                                    except:
                                        pass
                            
                            # 交易金额
                            elif any(keyword in col_str for keyword in ['交易金额', '金额', '发生额']):
                                try:
                                    amount_val = float(str(cell_value).replace(',', '').replace('￥', ''))
                                    if amount_val != 0:
                                        transaction_amount = abs(amount_val)
                                        dr_cr_flag = "收" if amount_val > 0 else "支"
                                except:
                                    pass
                            
                            # 账户余额
                            elif any(keyword in col_str for keyword in ['余额', '账户余额', '结余']):
                                try:
                                    balance = float(str(cell_value).replace(',', '').replace('￥', ''))
                                except:
                                    pass
                            
                            # 交易摘要
                            elif any(keyword in col_str for keyword in ['摘要', '用途', '备注', '说明']):
                                summary = str(cell_value).strip()
                    
                    # 数据验证：确保4维度数据完整性
                    name_check = sample_accounts[0]["person_name"] if sample_accounts else "未知持卡人"
                    account_check = sample_accounts[0]["account_number"] if sample_accounts else "Unknown"
                    
                    # 验证必要字段完整性
                    if (name_check != "未知持卡人" and account_check != "Unknown" and 
                        transaction_date and transaction_amount > 0):
                        
                        transaction = {
                            'cardholder_name': name_check,  # 🔧 4维度姓名识别需要
                            'account_number': account_check,
                            'transaction_date': transaction_date,  # 🔧 4维度时间格式需要
                            'transaction_amount': transaction_amount,
                            'balance': balance,  # 🔧 4维度金额解析需要
                            'dr_cr_flag': dr_cr_flag,
                            'card_number': account_check,
                            'currency': 'CNY',
                            'transaction_method': summary or '平安银行交易',
                            'bank_name': '平安银行',
                            'summary': summary
                        }
                        sample_transactions.append(transaction)
                        
                except Exception as e:
                    logger.debug(f"跳过第{idx+1}行: {str(e)}")
                    continue
            
            logger.info(f"平安银行Format1样本提取完成: {len(sample_accounts)}个账户, {len(sample_transactions)}条交易")
            
            return {
                'accounts': sample_accounts,
                'transactions': sample_transactions,
                'success': True,
                'metadata': {
                    'sample_size': len(sample_transactions),
                    'plugin_name': self.name,
                    'parser_version': 'v3.0-feature_recognition'
                }
            }
            
        except Exception as e:
            logger.error(f"平安银行Format1样本提取失败: {str(e)}")
            return {
                'accounts': [],
                'transactions': [],
                'success': False,
                'error': str(e),
                'metadata': {
                    'sample_size': 0,
                    'plugin_name': self.name,
                    'error_details': str(e)
                }
            }
