"""
银行数据模式定义
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field


class BankBase(BaseModel):
    """银行基本信息模式"""
    bank_name: str
    is_enabled: bool = True


class BankCreate(BankBase):
    """创建银行的请求模式"""
    pass


class BankUpdate(BaseModel):
    """更新银行的请求模式"""
    bank_name: Optional[str] = None
    is_enabled: Optional[bool] = None


class BankResponse(BankBase):
    """银行响应模式"""
    bank_id: str
    is_predefined: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True 