/**
 * API配置文件
 * 统一管理后端服务地址和相关配置
 */

// 后端服务基础URL配置
export const API_CONFIG = {
  // 开发环境配置
  development: {
    baseURL: 'http://127.0.0.1:8000',  // 🔧 修复：使用正确的8000端口
    timeout: 30000,
  },

  // 生产环境配置
  production: {
    baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',  // 标准后端端口
    timeout: 30000,
  }
};

// 获取当前环境的API配置
const getCurrentConfig = () => {
  const env = process.env.NODE_ENV || 'development';
  return API_CONFIG[env] || API_CONFIG.development;
};

// 导出当前配置
const config = getCurrentConfig();
export const API_BASE_URL = config.baseURL;
export const API_TIMEOUT = config.timeout;

// API端点配置
export const API_ENDPOINTS = {
  // 项目相关
  projects: '/api/projects/',  // 🔧 修复：添加斜杠避免307重定向

  // 账户相关
  accounts: '/api/accounts',
  
  // 解析器相关
  parser: {
    root: '/api/parser',
    preAnalyze: '/api/parser/pre-analyze',
    analyze: '/api/parser/analyze',
    save: '/api/parser/save-optimized',
    checkDuplicates: '/api/parser/check-duplicates'
  },
  
  // 模板相关 - 修复路径
  templates: '/api/parser/v2/templates'
};

// 构建完整的API URL
export const buildApiUrl = (endpoint) => {
  return `${API_BASE_URL}${endpoint}`;
};

// 导出默认配置
export default {
  API_BASE_URL,
  API_TIMEOUT,
  API_ENDPOINTS,
  buildApiUrl
}; 