import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Card, Descriptions, Button, Modal, Form, Input, Select, message, Upload, Row, Col, List, Avatar, Space, Popconfirm, Typography } from 'antd';
import { EditOutlined, UploadOutlined, RobotOutlined, PlusOutlined, UserOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import moment from 'moment';
import { subjectAPI } from '../../../services/api';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

/**
 * 被反映人资料管理组件
 * 支持多个被反映人的完整管理功能
 */
const CaseBriefSubject = () => {
  const { projectId } = useParams();
  const [subjects, setSubjects] = useState([]);
  const [currentSubject, setCurrentSubject] = useState(null);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDetailVisible, setIsDetailVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [form] = Form.useForm();

  // 组件挂载时获取数据
  useEffect(() => {
    if (projectId) {
      fetchSubjects();
    }
  }, [projectId]);

  // 获取被反映人资料列表
  const fetchSubjects = async () => {
    try {
      setLoading(true);
      const response = await subjectAPI.getSubjects(projectId);
      setSubjects(response.data);
      message.success('获取被反映人资料成功');
    } catch (error) {
      console.error('获取被反映人资料出错:', error);
      message.error('获取被反映人资料失败，请检查网络连接');
      setSubjects([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理新增被反映人
  const handleAdd = () => {
    setCurrentSubject(null);
    setIsEditing(false);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 处理编辑被反映人
  const handleEdit = (subject) => {
    setCurrentSubject(subject);
    setIsEditing(true);
    
    // 设置表单值，处理日期格式
    const formValues = {
      ...subject,
      birth_date: subject.birth_date ? moment(subject.birth_date) : null,
      work_start_date: subject.work_start_date ? moment(subject.work_start_date) : null,
    };
    form.setFieldsValue(formValues);
    setIsModalVisible(true);
  };

  // 处理查看详情
  const handleViewDetail = (subject) => {
    setCurrentSubject(subject);
    setIsDetailVisible(true);
  };

  // 处理删除被反映人
  const handleDelete = async (subjectId) => {
    try {
      await subjectAPI.deleteSubject(subjectId);
      message.success('删除成功');
      await fetchSubjects();
    } catch (error) {
      console.error('删除被反映人失败:', error);
      message.error('删除失败，请重试');
    }
  };

  // 处理模态框确认
  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      // 格式化日期字段
      const formattedValues = {
        ...values,
        birth_date: values.birth_date ? values.birth_date.format('YYYY-MM-DD') : '',
        work_start_date: values.work_start_date ? values.work_start_date.format('YYYY-MM-DD') : '',
      };

      if (isEditing && currentSubject) {
        // 更新现有被反映人资料
        await subjectAPI.updateSubject(currentSubject.subject_id, formattedValues);
        message.success('更新成功');
      } else {
        // 创建新的被反映人资料
        await subjectAPI.createSubject({
          project_id: projectId,
          ...formattedValues
        });
        message.success('创建成功');
      }

      setIsModalVisible(false);
      await fetchSubjects();
    } catch (error) {
      console.error('表单验证或提交失败:', error);
      message.error('保存失败，请重试');
    }
  };

  // 处理模态框取消
  const handleCancel = () => {
    setIsModalVisible(false);
    setIsDetailVisible(false);
    form.resetFields();
    setCurrentSubject(null);
  };

  // 处理简历上传识别（预留功能）
  const handleResumeUpload = () => {
    message.info('简历上传识别功能正在开发中，敬请期待！');
  };

  // 渲染被反映人列表项
  const renderSubjectItem = (subject) => (
    <List.Item
      key={subject.subject_id}
      actions={[
        <Button 
          type="link" 
          icon={<EyeOutlined />} 
          onClick={() => handleViewDetail(subject)}
        >
          查看
        </Button>,
        <Button 
          type="link" 
          icon={<EditOutlined />} 
          onClick={() => handleEdit(subject)}
        >
          编辑
        </Button>,
        <Popconfirm
          title="确定要删除这个被反映人吗？"
          onConfirm={() => handleDelete(subject.subject_id)}
          okText="确定"
          cancelText="取消"
        >
          <Button 
            type="link" 
            danger 
            icon={<DeleteOutlined />}
          >
            删除
          </Button>
        </Popconfirm>
      ]}
    >
      <List.Item.Meta
        avatar={<Avatar icon={<UserOutlined />} />}
        title={
          <Space>
            <Text strong>{subject.name}</Text>
            {subject.gender && <Text type="secondary">({subject.gender})</Text>}
          </Space>
        }
        description={
          <div>
            <div>
              <Text type="secondary">职务：</Text>
              <Text>{subject.current_position || '未填写'}</Text>
            </div>
            <div>
              <Text type="secondary">工作单位：</Text>
              <Text>{subject.work_unit || '未填写'}</Text>
            </div>
            {subject.id_number && (
              <div>
                <Text type="secondary">身份证号：</Text>
                <Text>{subject.id_number}</Text>
              </div>
            )}
          </div>
        }
      />
    </List.Item>
  );

  return (
    <div>
      <Card 
        title="被反映人资料管理" 
        extra={
          <Space>
            <Button 
              icon={<RobotOutlined />} 
              onClick={handleResumeUpload}
              disabled={true}
            >
              上传简历识别
            </Button>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={handleAdd}
            >
              新增被反映人
            </Button>
          </Space>
        }
        loading={loading}
      >
        {subjects.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Text type="secondary">暂无被反映人资料，请点击"新增被反映人"添加</Text>
          </div>
        ) : (
          <List
            itemLayout="vertical"
            dataSource={subjects}
            renderItem={renderSubjectItem}
            pagination={subjects.length > 5 ? { pageSize: 5 } : false}
          />
        )}
      </Card>

      {/* 详情查看模态框 */}
      <Modal
        title={`被反映人详细资料 - ${currentSubject?.name || ''}`}
        open={isDetailVisible}
        onCancel={handleCancel}
        footer={[
          <Button key="close" onClick={handleCancel}>
            关闭
          </Button>,
          <Button 
            key="edit" 
            type="primary" 
            onClick={() => {
              setIsDetailVisible(false);
              handleEdit(currentSubject);
            }}
          >
            编辑资料
          </Button>
        ]}
        width={900}
      >
        {currentSubject && (
          <Descriptions bordered column={2} size="small">
          <Descriptions.Item label="姓名" span={1}>
              {currentSubject.name}
          </Descriptions.Item>
          <Descriptions.Item label="性别" span={1}>
              {currentSubject.gender || '未填写'}
          </Descriptions.Item>
          <Descriptions.Item label="出生年月" span={1}>
              {currentSubject.birth_date || '未填写'}
          </Descriptions.Item>
          <Descriptions.Item label="民族" span={1}>
              {currentSubject.nationality || '未填写'}
          </Descriptions.Item>
          <Descriptions.Item label="籍贯" span={1}>
              {currentSubject.native_place || '未填写'}
          </Descriptions.Item>
          <Descriptions.Item label="出生地" span={1}>
              {currentSubject.birth_place || '未填写'}
          </Descriptions.Item>
          <Descriptions.Item label="身份证号" span={2}>
              {currentSubject.id_number || '未填写'}
          </Descriptions.Item>
          <Descriptions.Item label="参加工作时间" span={1}>
              {currentSubject.work_start_date || '未填写'}
          </Descriptions.Item>
          <Descriptions.Item label="学历" span={1}>
              {currentSubject.education || '未填写'}
          </Descriptions.Item>
          <Descriptions.Item label="专业" span={1}>
              {currentSubject.major || '未填写'}
          </Descriptions.Item>
          <Descriptions.Item label="学位" span={1}>
              {currentSubject.degree || '未填写'}
          </Descriptions.Item>
          <Descriptions.Item label="职称" span={1}>
              {currentSubject.professional_title || '未填写'}
          </Descriptions.Item>
          <Descriptions.Item label="现职务" span={1}>
              {currentSubject.current_position || '未填写'}
          </Descriptions.Item>
          <Descriptions.Item label="工作单位" span={2}>
              {currentSubject.work_unit || '未填写'}
          </Descriptions.Item>
          <Descriptions.Item label="政治面貌" span={1}>
              {currentSubject.political_status || '未填写'}
          </Descriptions.Item>
          <Descriptions.Item label="婚姻状况" span={1}>
              {currentSubject.marital_status || '未填写'}
          </Descriptions.Item>
          <Descriptions.Item label="联系电话" span={1}>
              {currentSubject.phone || '未填写'}
          </Descriptions.Item>
          <Descriptions.Item label="现住址" span={1}>
              {currentSubject.address || '未填写'}
          </Descriptions.Item>
          <Descriptions.Item label="个人简历" span={2}>
            <div style={{ 
                minHeight: '120px', 
                maxHeight: '300px',
                overflowY: 'auto',
              whiteSpace: 'pre-wrap',
              backgroundColor: '#fafafa',
              padding: '12px',
              borderRadius: '4px',
              border: '1px solid #d9d9d9'
            }}>
                {currentSubject.resume || '未填写个人简历信息'}
            </div>
          </Descriptions.Item>
            {currentSubject.notes && (
            <Descriptions.Item label="备注" span={2}>
                <div style={{ 
                  minHeight: '60px', 
                  maxHeight: '200px',
                  overflowY: 'auto',
                  whiteSpace: 'pre-wrap',
                  backgroundColor: '#fafafa',
                  padding: '12px',
                  borderRadius: '4px',
                  border: '1px solid #d9d9d9'
                }}>
                  {currentSubject.notes}
                </div>
            </Descriptions.Item>
          )}
        </Descriptions>
        )}
      </Modal>

      {/* 编辑/新增模态框 */}
      <Modal
        title={isEditing ? "编辑被反映人资料" : "新增被反映人资料"}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleCancel}
        width={900}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            gender: '',
            education: '',
            political_status: '',
            marital_status: ''
          }}
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="name"
                label="姓名"
                rules={[{ required: true, message: '请输入姓名' }]}
              >
                <Input placeholder="请输入姓名" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="gender"
                label="性别"
                rules={[{ required: true, message: '请选择性别' }]}
              >
                <Select placeholder="请选择性别">
                  <Option value="男">男</Option>
                  <Option value="女">女</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="birth_date"
                label="出生年月"
              >
                <Space>
                  <Form.Item name="birth_year" style={{ marginBottom: 0 }}>
                    <Select 
                      placeholder="选择年份" 
                      style={{ width: 100 }}
                    >
                      {Array.from({ length: 80 }, (_, i) => {
                        const year = new Date().getFullYear() - i;
                        return (
                          <Option key={year} value={year}>
                            {year}年
                          </Option>
                        );
                      })}
                    </Select>
                  </Form.Item>
                  <Form.Item name="birth_month" style={{ marginBottom: 0 }}>
                    <Select 
                      placeholder="选择月份" 
                      style={{ width: 80 }}
                    >
                      {Array.from({ length: 12 }, (_, i) => {
                        const month = i + 1;
                        return (
                          <Option key={month} value={month}>
                            {month.toString().padStart(2, '0')}月
                          </Option>
                        );
                      })}
                    </Select>
                  </Form.Item>
                </Space>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="nationality"
                label="民族"
              >
                <Input placeholder="请输入民族" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="native_place"
                label="籍贯"
              >
                <Input placeholder="请输入籍贯" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="birth_place"
                label="出生地"
              >
                <Input placeholder="请输入出生地" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="id_number"
                label="身份证号"
              >
                <Input placeholder="请输入身份证号" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="work_start_date"
                label="参加工作时间"
              >
                <Space>
                  <Form.Item name="work_start_year" style={{ marginBottom: 0 }}>
                    <Select 
                      placeholder="选择年份" 
                      style={{ width: 100 }}
                    >
                      {Array.from({ length: 50 }, (_, i) => {
                        const year = new Date().getFullYear() - i;
                        return (
                          <Option key={year} value={year}>
                            {year}年
                          </Option>
                        );
                      })}
                    </Select>
                  </Form.Item>
                  <Form.Item name="work_start_month" style={{ marginBottom: 0 }}>
                    <Select 
                      placeholder="选择月份" 
                      style={{ width: 80 }}
                    >
                      {Array.from({ length: 12 }, (_, i) => {
                        const month = i + 1;
                        return (
                          <Option key={month} value={month}>
                            {month.toString().padStart(2, '0')}月
                          </Option>
                        );
                      })}
                    </Select>
                  </Form.Item>
                </Space>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="education"
                label="学历"
              >
                <Select placeholder="请选择学历">
                  <Option value="小学">小学</Option>
                  <Option value="初中">初中</Option>
                  <Option value="高中">高中</Option>
                  <Option value="中专">中专</Option>
                  <Option value="大专">大专</Option>
                  <Option value="本科">本科</Option>
                  <Option value="硕士">硕士</Option>
                  <Option value="博士">博士</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="major"
                label="专业"
              >
                <Input placeholder="请输入专业" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="degree"
                label="学位"
              >
                <Input placeholder="请输入学位" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="professional_title"
                label="职称"
              >
                <Input placeholder="请输入职称" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="current_position"
                label="现职务"
              >
                <Input placeholder="请输入现职务" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="political_status"
                label="政治面貌"
              >
                <Select placeholder="请选择政治面貌">
                  <Option value="中共党员">中共党员</Option>
                  <Option value="中共预备党员">中共预备党员</Option>
                  <Option value="共青团员">共青团员</Option>
                  <Option value="民革党员">民革党员</Option>
                  <Option value="民盟盟员">民盟盟员</Option>
                  <Option value="民建会员">民建会员</Option>
                  <Option value="民进会员">民进会员</Option>
                  <Option value="农工党党员">农工党党员</Option>
                  <Option value="致公党党员">致公党党员</Option>
                  <Option value="九三学社社员">九三学社社员</Option>
                  <Option value="台盟盟员">台盟盟员</Option>
                  <Option value="无党派人士">无党派人士</Option>
                  <Option value="群众">群众</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="work_unit"
                label="工作单位"
              >
                <Input placeholder="请输入工作单位" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="marital_status"
                label="婚姻状况"
              >
                <Select placeholder="请选择婚姻状况">
                  <Option value="未婚">未婚</Option>
                  <Option value="已婚">已婚</Option>
                  <Option value="离异">离异</Option>
                  <Option value="丧偶">丧偶</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="联系电话"
              >
                <Input placeholder="请输入联系电话" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="address"
                label="现住址"
              >
                <Input placeholder="请输入现住址" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="resume"
            label="个人简历"
          >
            <TextArea 
              placeholder="请输入个人简历信息，包括工作经历、教育背景等"
              autoSize={{ minRows: 12, maxRows: 20 }}
              style={{ minHeight: '200px' }}
            />
          </Form.Item>

          <Form.Item
            name="notes"
            label="备注"
          >
            <TextArea 
              placeholder="请输入备注信息"
              autoSize={{ minRows: 6, maxRows: 12 }}
              style={{ minHeight: '120px' }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CaseBriefSubject; 