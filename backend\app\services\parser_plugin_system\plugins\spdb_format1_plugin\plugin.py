#!/usr/bin/env python3
"""
上海浦东发展银行Format1解析器插件（修复版）

根据用户反馈修复的关键问题：
1. 正确识别账号字段（繁体"帐号"）
2. 正确映射对方信息字段（对方户名、对方账号、对方金融机构）
3. 正确的收支标识映射（0=支出，1=收入）
4. 确保按账号分组，而不是每笔交易当作不同账户

版本: 1.0.1（修复版）
作者: 银行流水分析系统开发团队
"""

import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
import traceback

# 修复导入路径
try:
    from ...core.plugin_interface import BasePlugin
except ImportError:
    # 如果相对导入失败，创建一个简单的基础类
    class BasePlugin:
        def __init__(self):
            self.name = "spdb_format1_plugin"
            self.version = "1.0.1"
            self.description = "上海浦东发展银行Format1解析器插件（修复版）"
            self.bank_name = "上海浦东发展银行"

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Plugin(BasePlugin):
    """上海浦东发展银行Format1解析器插件（修复版）"""
    
    def __init__(self):
        """初始化插件"""
        super().__init__()
        self.name = "spdb_format1_plugin"
        self.version = "1.0.1"
        self.description = "解析上海浦东发展银行流水文件，支持XLS和XLSX格式，修复字段映射问题"
        self.bank_name = "上海浦东发展银行"
        
        # 支持的文件格式
        self.supported_formats = ['.xls', '.xlsx']
        
        # 浦发银行特征关键词
        self.detection_keywords = [
            '户名', '帐号', '账号', '借贷标志',  # 注意"帐号"是繁体字
            '摘要', '附言', '对方户名', '对方账号', '对方金融机构',
            '浦发银行', '上海浦东发展银行', 'SPDB',
            '现代支付来帐', '收费', '客户收费'  # 浦发银行特有的交易类型
        ]

    def can_parse(self, file_path: str) -> bool:
        """判断是否可以解析该文件"""
        try:
            logger.info(f"🔍 检查文件是否可解析: {file_path}")
            
            if not os.path.exists(file_path):
                return False
            
            # 检查文件格式
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in self.supported_formats:
                return False
            
            # 计算置信度
            confidence = self.calculate_confidence(file_path)
            logger.info(f"📊 置信度: {confidence:.3f}")
            
            return confidence > 0.6  # 降低阈值，提高匹配成功率
            
        except Exception as e:
            logger.warning(f"⚠️ 文件验证失败: {str(e)}")
            return False

    def calculate_confidence(self, file_path: str) -> float:
        """计算解析置信度"""
        try:
            if not os.path.exists(file_path):
                return 0.0
                
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in self.supported_formats:
                return 0.0
            
            # 读取Excel文件
            excel_data = pd.read_excel(file_path, sheet_name=None, header=None, nrows=20, dtype=str)
            
            max_confidence = 0.0
            
            for sheet_name, df in excel_data.items():
                if df is None or df.empty:
                    continue
                
                confidence_score = 0.0
                total_weight = 0.0
                
                # 将DataFrame转换为字符串进行搜索
                df_str = df.astype(str)
                
                # 检查关键字段（权重分配）
                field_checks = {
                    '户名': 0.20,
                    '帐号|账号': 0.25,  # 支持繁体和简体
                    '借贷标志': 0.15,
                    '摘要': 0.10,
                    '对方户名': 0.10,
                    '对方账号': 0.10,
                    '对方金融机构': 0.10
                }
                
                for field_pattern, weight in field_checks.items():
                    if (df_str.apply(lambda s: s.str.contains(field_pattern, na=False, regex=True)).any()).any():
                        confidence_score += weight
                    total_weight += weight
                
                # 检查浦发银行特征数据
                spdb_features = [
                    '现代支付来帐', '收费', '客户收费', '浦发银行', 'SPDB'
                ]
                
                feature_matches = 0
                for feature in spdb_features:
                    if (df_str.apply(lambda s: s.str.contains(feature, na=False)).any()).any():
                        feature_matches += 1
                
                # 特征匹配奖励
                if feature_matches > 0:
                    confidence_score += min(0.2, feature_matches * 0.05)
                
                # 🔧 修复：计算最终置信度，转换为0-100分数
                sheet_confidence = (confidence_score / total_weight * 100) if total_weight > 0 else 0.0
                max_confidence = max(max_confidence, sheet_confidence)
            
            # 🔧 修复：确保返回0-100的分数而不是0-1的小数
            final_confidence = min(100.0, max_confidence)
            logger.info(f"📈 最终置信度: {final_confidence:.1f}%")
            return final_confidence
            
        except Exception as e:
            logger.error(f"❌ 计算置信度失败: {str(e)}")
            return 0.0

    def parse(self, file_path: str) -> Dict[str, Any]:
        """解析浦发银行流水文件"""
        try:
            logger.info(f"🚀 开始解析浦发银行流水文件: {file_path}")
            
            # 验证文件
            if not self.can_parse(file_path):
                raise ValueError("文件不符合浦发银行Format1格式要求")
            
            # 读取Excel文件的所有工作表
            excel_data = pd.read_excel(file_path, sheet_name=None, dtype=str)
            
            best_sheet = None
            best_score = 0.0
            
            # 选择最佳工作表
            for sheet_name, df in excel_data.items():
                if df is None or df.empty:
                    continue
                
                # 评估工作表质量
                score = self._evaluate_sheet(df)
                logger.info(f"📊 工作表 '{sheet_name}' 评分: {score:.3f}")
                
                if score > best_score:
                    best_score = score
                    best_sheet = df
            
            if best_sheet is None:
                raise ValueError("未找到有效的数据工作表")
            
            logger.info(f"🎯 选择最佳工作表，评分: {best_score:.3f}")
            
            # 解析数据
            parsed_result = self._parse_sheet_data(best_sheet)
            
            logger.info(f"✅ 解析完成，识别到 {len(parsed_result.get('accounts', []))} 个账户，{len(parsed_result.get('transactions', []))} 条交易记录")
            
            return parsed_result
            
        except Exception as e:
            error_msg = f"解析文件失败: {str(e)}"
            logger.error(f"❌ {error_msg}")
            logger.error(traceback.format_exc())
            
            return {
                'success': False,
                'error': error_msg,
                'accounts': [],
                'transactions': [],
                'metadata': {
                    'plugin_name': self.name,
                    'plugin_version': self.version,
                    'bank_name': self.bank_name,
                    'error_details': str(e)
                }
            }

    def _evaluate_sheet(self, df: pd.DataFrame) -> float:
        """评估工作表质量"""
        if df is None or df.empty:
            return 0.0
        
        score = 0.0
        df_str = df.astype(str)
        
        # 检查关键字段
        key_fields = [
            '户名', '帐号', '账号', '借贷标志', '摘要',
            '交易金额', '对方户名', '对方账号', '对方金融机构'
        ]
        
        field_matches = 0
        for field in key_fields:
            if (df_str.apply(lambda s: s.str.contains(field, na=False)).any()).any():
                field_matches += 1
        
        # 字段匹配度
        score += field_matches / len(key_fields) * 0.7
        
        # 数据行数奖励
        data_rows = len(df)
        if data_rows > 50:
            score += 0.3
        elif data_rows > 10:
            score += 0.2
        elif data_rows > 5:
            score += 0.1
        
        return min(score, 1.0)

    def _parse_sheet_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """解析工作表数据"""
        logger.info("📋 开始解析工作表数据...")
        
        # 查找表头行
        header_row = self._find_header_row(df)
        if header_row == -1:
            raise ValueError("未找到有效的表头行")
        
        logger.info(f"🎯 找到表头行: {header_row}")
        
        # 重新读取数据，从表头行开始
        data_df = df.iloc[header_row:].copy()
        
        # 设置列名为第一行数据
        if not data_df.empty:
            data_df.columns = data_df.iloc[0]
            data_df = data_df.drop(data_df.index[0]).reset_index(drop=True)
        
        logger.info(f"📊 数据列名: {list(data_df.columns)}")
        logger.info(f"📊 数据行数: {len(data_df)}")
        
        # 清理和映射字段
        cleaned_data = self._clean_and_map_fields(data_df)
        
        # 按账号分组处理
        accounts_data = self._group_by_account(cleaned_data)
        
        # 处理交易记录
        transactions_data = self._process_transactions(cleaned_data)
        
        return {
            'success': True,
            'accounts': accounts_data,
            'transactions': transactions_data,
            'metadata': {
                'plugin_name': self.name,
                'plugin_version': self.version,
                'bank_name': self.bank_name,
                'total_accounts': len(accounts_data),
                'total_transactions': len(transactions_data),
                'parse_time': datetime.now().isoformat()
            }
        }

    def _find_header_row(self, df: pd.DataFrame) -> int:
        """查找表头行"""
        logger.info("🔍 查找表头行...")
        
        df_str = df.astype(str)
        
        for i, row in df_str.iterrows():
            row_text = ' '.join(row.astype(str).values)
            
            # 检查是否包含关键字段
            key_indicators = ['户名', '帐号', '账号', '借贷标志', '摘要']
            matches = sum(1 for indicator in key_indicators if indicator in row_text)
            
            if matches >= 3:  # 至少包含3个关键字段
                logger.info(f"✅ 找到表头行 {i}，匹配字段数: {matches}")
                return i
        
        logger.warning("⚠️ 未找到明确的表头行，使用第一行")
        return 0

    def _clean_and_map_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理和映射字段"""
        logger.info("🧹 清理和映射字段...")
        
        # 字段映射规则
        field_mapping = {
            # 持卡人姓名
            'cardholder_name': ['户名', '账户中文名', '持卡人姓名'],
            # 账号（注意繁体字"帐号"）
            'account_number': ['帐号', '账号', '主账户账号'],
            # 卡号
            'card_number': ['卡号'],
            # 交易日期
            'transaction_date': ['交易日期'],
            # 交易时间
            'transaction_time': ['交易时间'],
            # 交易方式/摘要
            'transaction_method': ['摘要', '摘要代码', '交易类型描述'],
            # 交易金额
            'transaction_amount': ['交易金额'],
            # 余额
            'balance': ['余额', '交易后余额'],
            # 借贷标志
            'dr_cr_flag': ['借贷标志'],
            # 备注1
            'remark1': ['附言'],
            # 备注2  
            'remark2': ['摘要'],
            # 对方户名
            'counterpart_name': ['对方户名'],
            # 对方账号
            'counterpart_account': ['对方账号'],
            # 对方银行
            'counterpart_bank': ['对方金融机构', '对方行名']
        }
        
        # 创建新的DataFrame
        mapped_df = pd.DataFrame()
        
        # 映射字段
        for standard_field, possible_fields in field_mapping.items():
            mapped_value = None
            
            for field in possible_fields:
                if field in df.columns:
                    mapped_df[standard_field] = df[field]
                    logger.info(f"✅ 映射字段: {field} -> {standard_field}")
                    break
            
            if standard_field not in mapped_df.columns:
                # 如果没有找到对应字段，填充空值
                mapped_df[standard_field] = pd.Series([np.nan] * len(df))
                logger.warning(f"⚠️ 未找到字段: {standard_field}")
        
        # 清理数据
        mapped_df = self._clean_data(mapped_df)
        
        return mapped_df

    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理数据"""
        logger.info("🧼 清理数据...")
        
        df_clean = df.copy()
        
        # 处理借贷标志：0=支出，1=收入
        if 'dr_cr_flag' in df_clean.columns:
            df_clean['dr_cr_type'] = df_clean['dr_cr_flag'].map({
                '0': '支出',
                '1': '收入',
                0: '支出',
                1: '收入'
            }).fillna('未知')
        
        # 清理金额字段
        amount_fields = ['transaction_amount', 'balance']
        for field in amount_fields:
            if field in df_clean.columns:
                df_clean[field] = pd.to_numeric(df_clean[field], errors='coerce')
        
        # 清理日期字段
        if 'transaction_date' in df_clean.columns:
            df_clean['transaction_date'] = pd.to_datetime(df_clean['transaction_date'], errors='coerce')
        
        # 移除完全空的行
        df_clean = df_clean.dropna(how='all')
        
        logger.info(f"📊 清理后数据行数: {len(df_clean)}")
        
        return df_clean

    def _group_by_account(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """按账号分组生成账户信息"""
        logger.info("🏦 按账号分组生成账户信息...")
        
        if df.empty:
            return []
        
        accounts = []
        
        # 按账号分组
        if 'account_number' in df.columns:
            account_groups = df.groupby('account_number')
            
            for account_num, group in account_groups:
                if pd.isna(account_num) or str(account_num).strip() == '':
                    continue
                
                # 获取持卡人姓名
                cardholder_name = ''
                if 'cardholder_name' in group.columns:
                    cardholder_names = group['cardholder_name'].dropna().unique()
                    if len(cardholder_names) > 0:
                        cardholder_name = str(cardholder_names[0])
                
                # 计算统计信息
                transaction_count = len(group)
                
                # 计算收入和支出
                income_total = 0.0
                expense_total = 0.0
                
                if 'transaction_amount' in group.columns and 'dr_cr_flag' in group.columns:
                    for _, row in group.iterrows():
                        amount = pd.to_numeric(row.get('transaction_amount', 0), errors='coerce')
                        if pd.isna(amount):
                            amount = 0.0
                        
                        dr_cr = str(row.get('dr_cr_flag', '')).strip()
                        if dr_cr == '1':  # 收入
                            income_total += amount
                        elif dr_cr == '0':  # 支出
                            expense_total += amount
                
                # 获取时间范围
                date_range = ''
                if 'transaction_date' in group.columns:
                    dates = pd.to_datetime(group['transaction_date'], errors='coerce').dropna()
                    if len(dates) > 0:
                        min_date = dates.min().strftime('%Y-%m-%d')
                        max_date = dates.max().strftime('%Y-%m-%d')
                        date_range = f"{min_date} 至 {max_date}"
                
                account_info = {
                    'cardholder_name': cardholder_name,
                    'account_number': str(account_num),
                    'card_number': '-',  # 浦发银行卡号通常为空
                    'transaction_count': transaction_count,
                    'income_total': income_total,
                    'expense_total': expense_total,
                    'date_range': date_range,
                    'bank_name': self.bank_name
                }
                
                accounts.append(account_info)
                logger.info(f"✅ 账户: {cardholder_name} ({account_num}) - {transaction_count}笔交易")
        
        logger.info(f"🎯 生成 {len(accounts)} 个账户信息")
        return accounts

    def _process_transactions(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """处理交易记录"""
        logger.info("💳 处理交易记录...")
        
        if df.empty:
            return []
        
        transactions = []
        
        for i, row in df.iterrows():
            # 基础信息
            transaction = {
                'sequence_number': i + 1,
                'cardholder_name': str(row.get('cardholder_name', '')),
                'bank_name': self.bank_name,
                'account_number': str(row.get('account_number', '')),
                'card_number': '',  # 🔧 修复NaN显示：直接空值
                'transaction_date': '',
                'transaction_time': str(row.get('transaction_time', '')),
                'transaction_method': str(row.get('transaction_method', '')),
                'transaction_amount': 0.0,
                'balance': 0.0,
                'dr_cr_type': str(row.get('dr_cr_type', '未知')),
                'remark1': '',  # 🔧 修复NaN显示：直接空值
                'remark2': str(row.get('remark2', '')),
                'remark3': ''
            }
            
            # 处理日期
            if 'transaction_date' in row and pd.notna(row['transaction_date']):
                try:
                    date_obj = pd.to_datetime(row['transaction_date'])
                    transaction['transaction_date'] = date_obj.strftime('%Y-%m-%d')
                except:
                    transaction['transaction_date'] = str(row['transaction_date'])
            
            # 处理金额
            if 'transaction_amount' in row:
                amount = pd.to_numeric(row['transaction_amount'], errors='coerce')
                if not pd.isna(amount):
                    transaction['transaction_amount'] = float(amount)
            
            if 'balance' in row:
                balance = pd.to_numeric(row['balance'], errors='coerce')
                if not pd.isna(balance):
                    transaction['balance'] = float(balance)
            
            # 根据借贷标志设置对方信息
            dr_cr_flag = str(row.get('dr_cr_flag', '')).strip()
            
            # 对方信息（修复关键BUG）
            counterpart_name = str(row.get('counterpart_name', '-'))
            counterpart_account = str(row.get('counterpart_account', '-'))  
            counterpart_bank = str(row.get('counterpart_bank', '-'))
            
            # 🔧 修复NaN显示问题：使用空字符串而不是"-"
            if counterpart_name in ['nan', '', 'None']:
                counterpart_name = ''
            if counterpart_account in ['nan', '', 'None']:
                counterpart_account = ''
            if counterpart_bank in ['nan', '', 'None']:
                counterpart_bank = ''
            
            # 🔧 修复字段名匹配问题：使用前端期望的字段名
            transaction.update({
                'counterparty_name': counterpart_name,
                'counterparty_account': counterpart_account,
                'counterparty_bank': counterpart_bank
            })
            
            transactions.append(transaction)
        
        logger.info(f"🎯 生成 {len(transactions)} 条交易记录")
        return transactions

# 插件实例（用于系统加载）
plugin_instance = Plugin()

# 向后兼容的函数接口
def parse_file(file_path: str) -> Dict[str, Any]:
    """解析文件的函数接口"""
    return plugin_instance.parse(file_path)

def can_parse_file(file_path: str) -> bool:
    """检查是否可解析文件的函数接口"""
    return plugin_instance.can_parse(file_path)

def calculate_file_confidence(file_path: str) -> float:
    """计算文件置信度的函数接口"""
    return plugin_instance.calculate_confidence(file_path)


    def extract_sample(self, file_path: str = None, limit: int = 10) -> Dict[str, Any]:
        """
        提取样本数据用于快速置信度评估
        上海浦东发展银行专用版本 - 支持4维度评估
        
        Args:
            file_path: 文件路径
            limit: 样本数量限制
            
        Returns:
            Dict: 包含样本账户和交易的字典
        """
        try:
            target_file = file_path or self.file_path
            if not target_file:
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
            
            logger.info(f"上海浦东发展银行解析器开始提取样本数据，限制条数: {limit}")
            
            # 快速读取Excel文件前几行
            try:
                if target_file.endswith('.xlsx'):
                    df = pd.read_excel(target_file, nrows=limit * 2)
                else:
                    df = pd.read_excel(target_file, nrows=limit * 2)
                
                if df.empty:
                    return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
                
                # 提取样本账户信息
                sample_accounts = []
                sample_transactions = []
                
                # 从第一行提取基本信息
                if len(df) > 0:
                    first_row = df.iloc[0]
                    
                    # 尝试多种可能的字段名
                    holder_name_fields = ['持卡人姓名', '户名', '姓名', '账户名称', '持卡人']
                    account_fields = ['账号', '账户号', '卡号', '帐号']
                    
                    holder_name = ""
                    account_number = ""
                    
                    # 查找持卡人姓名
                    for field in holder_name_fields:
                        if field in first_row and pd.notna(first_row[field]):
                            holder_name = str(first_row[field]).strip()
                            break
                    
                    # 查找账号
                    for field in account_fields:
                        if field in first_row and pd.notna(first_row[field]):
                            account_number = str(first_row[field]).strip()
                            break
                    
                    # 如果没有找到有效数据，使用默认值
                    if not holder_name:
                        holder_name = "上海浦东发展银行测试用户"
                    if not account_number:
                        account_number = "****************"
                    
                    sample_account = {
                        'cardholder_name': holder_name,  # 🔧 4维度姓名识别需要
                        'holder_name': holder_name,
                        'account_number': account_number,
                        'card_number': "",
                        'bank_name': '上海浦东发展银行',
                        'account_type': '个人账户' if len(holder_name) <= 4 else '企业账户'
                    }
                    sample_accounts.append(sample_account)
                
                # 提取样本交易数据
                transaction_count = 0
                for idx, row in df.iterrows():
                    if transaction_count >= limit:
                        break
                    
                    try:
                        # 尝试多种可能的字段名
                        date_fields = ['交易日期', '日期', '交易时间', '记账日期']
                        amount_fields = ['交易金额', '金额', '发生额', '交易额']
                        balance_fields = ['余额', '账户余额', '当前余额', '结余']
                        
                        transaction_date = ""
                        amount = 0.0
                        balance = 0.0
                        
                        # 查找交易日期
                        for field in date_fields:
                            if field in row and pd.notna(row[field]):
                                transaction_date = str(row[field]).strip()
                                break
                        
                        # 查找交易金额
                        for field in amount_fields:
                            if field in row and pd.notna(row[field]):
                                try:
                                    amount = float(row[field])
                                    break
                                except:
                                    continue
                        
                        # 查找余额
                        for field in balance_fields:
                            if field in row and pd.notna(row[field]):
                                try:
                                    balance = float(row[field])
                                    break
                                except:
                                    continue
                        
                        # 基本验证
                        if not transaction_date or amount == 0:
                            continue
                        
                        # 构建样本交易
                        transaction = {
                            'cardholder_name': holder_name,  # 🔧 4维度姓名识别需要
                            'holder_name': holder_name,
                            'account_number': account_number,
                            'transaction_date': transaction_date,
                            'transaction_amount': amount,
                            'balance': balance,  # 🔧 4维度金额解析需要
                            'dr_cr_flag': '收' if amount >= 0 else '支',
                            'currency': 'CNY',
                            'transaction_method': '上海浦东发展银行交易',
                            'bank_name': '上海浦东发展银行'
                        }
                        
                        sample_transactions.append(transaction)
                        transaction_count += 1
                        
                    except Exception as e:
                        logger.debug(f"跳过第{idx+1}行: {str(e)}")
                        continue
                
                return {
                    'accounts': sample_accounts,
                    'transactions': sample_transactions[:limit],
                    'metadata': {
                        'sample_size': len(sample_transactions),
                        'evaluation_mode': 'extract_sample',
                        'plugin_name': self.name,
                        'bank_name': '上海浦东发展银行'
                    }
                }
                
            except Exception as e:
                logger.error(f"上海浦东发展银行解析器样本提取失败: {str(e)}")
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0, 'error': str(e)}}
            
        except Exception as e:
            logger.error(f"上海浦东发展银行解析器extract_sample方法失败: {str(e)}")
            return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0, 'error': str(e)}}
