{"templateId": "ICBC_Personal_V1", "bankName": "中国工商银行", "accountType": "个人活期", "fileType": ["XLS", "XLSX"], "metaDataExtraction": [{"targetField": "extracted_account_name", "labelKeywords": ["户名", "账户名称"], "valueCellOffset": {"columns": 1}}, {"targetField": "extracted_card_number", "labelKeywords": ["账号", "卡号"], "valueRegex": "\\d{16,19}"}], "accountIdentifier": {"columns": ["card_number_in_transactions_column"], "useMetaData": true}, "processingSettings": {"skipRowsBeforeHeader": 5, "headerRowIdentifier": {"keywords": ["交易日期", "交易金额", "账号"], "rowIndex": null}, "dataStartOffset": 1, "removeEmptyRows": true}, "columnMapping": [{"targetField": "card_number_in_transactions_column", "sourceHeaders": ["账号", "卡号"], "dataType": "string"}, {"targetField": "transaction_date_raw", "sourceHeaders": ["交易日期", "日期"], "dataType": "date_string"}, {"targetField": "transaction_time_raw", "sourceHeaders": ["交易时间", "时间"], "dataType": "time_string", "required": false}, {"targetField": "transaction_method", "sourceHeaders": ["交易方式", "渠道", "交易类型简写"], "dataType": "string", "required": false}, {"targetField": "amount_raw_debit", "sourceHeaders": ["借方发生额", "支出金额", "取款"], "dataType": "amount_string", "required": false}, {"targetField": "amount_raw_credit", "sourceHeaders": ["贷方发生额", "存入金额", "存款"], "dataType": "amount_string", "required": false}, {"targetField": "balance", "sourceHeaders": ["余额", "账户余额"], "dataType": "amount_string", "required": false}, {"targetField": "counterparty_account_number", "sourceHeaders": ["对方账号", "对方账户"], "dataType": "string", "required": false}, {"targetField": "counterparty_account_name", "sourceHeaders": ["对方户名", "对方名称"], "dataType": "string", "required": false}, {"targetField": "counterparty_bank_name", "sourceHeaders": ["对方银行", "对方机构"], "dataType": "string", "required": false}, {"targetField": "remarks_base", "sourceHeaders": ["摘要", "备注", "交易说明"], "dataType": "string", "required": false}, {"targetField": "remarks_append_info1", "sourceHeaders": ["交易用途", "附言"], "dataType": "string", "required": false}], "amountProcessing": {"finalAmountTargetField": "transaction_amount", "drCrFlagTargetField": "dr_cr_flag", "methods": [{"type": "separate_columns", "creditColumnSourceField": "amount_raw_credit", "debitColumnSourceField": "amount_raw_debit", "creditFlagValue": "收", "debitFlagValue": "支"}], "postProcessing": {"ensureDebitIsNegative": true}}, "remarksProcessing": {"finalRemarksTargetField": "remarks", "baseRemarksSourceField": "remarks_base", "concatenationSources": [{"sourceField": "remarks_append_info1", "prefix": " [用途]: ", "ignoreIfEmpty": true}, {"sourceField": "counterparty_account_name", "prefix": " [对方]: ", "ignoreIfEmpty": true, "conditionField": "remarks_base", "conditionNotContains": ["对方户名", "对方名称"]}], "maxLength": 500}, "dateTimeProcessing": {"finalDateTimeTargetField": "transaction_datetime", "dateSourceField": "transaction_date_raw", "timeSourceField": "transaction_time_raw", "defaultTimeIfMissing": "00:00:00"}}