"""
银行流水分析工具API主入口
使用统一配置管理系统
"""
import sys
import io
import os

# 强制设置UTF-8编码，解决Windows下的乱码问题
if sys.platform.startswith('win'):
    # 设置环境变量
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    # 重新配置标准输出和错误输出
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

import logging
import json
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from starlette.responses import JSONResponse

# 🎯 导入统一配置管理
from .core.settings import settings, initialize_settings

# from .api import api_router
# from .api.v1 import api_v1_router

# 自定义JSON响应类，确保中文字符正确显示
class UTF8JSONResponse(JSONResponse):
    def render(self, content) -> bytes:
        return json.dumps(
            content,
            ensure_ascii=False,  # 🔧 关键修复：确保中文字符不被转义
            allow_nan=False,
            indent=None,
            separators=(",", ":"),
        ).encode("utf-8")

# 初始化配置
initialize_settings()

# 配置日志
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format=settings.log_format,
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description=settings.app_description,
    debug=settings.debug,
    docs_url="/docs" if settings.enable_docs else None,
    redoc_url="/redoc" if settings.enable_docs else None,
    default_response_class=UTF8JSONResponse
)

# 配置CORS
cors_settings = settings.get_cors_settings()
app.add_middleware(CORSMiddleware, **cors_settings)

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": settings.app_name,
        "version": settings.app_version,
        "docs": "/docs" if settings.enable_docs else "已禁用",
        "health": "/health" if settings.enable_health_check else "已禁用"
    }

@app.get("/health")
async def health_check():
    """基础健康检查"""
    if not settings.enable_health_check:
        return {"status": "disabled", "message": "健康检查已禁用"}
    
    return {
        "status": "healthy", 
        "message": "API服务正常运行",
        "version": settings.app_version,
        "environment": "production" if settings.is_production() else "development"
    }

@app.get("/config/summary")
async def config_summary():
    """配置摘要（调试用）"""
    if settings.is_production():
        return {"message": "生产环境不显示配置信息"}
    
    return {
        "app_name": settings.app_name,
        "version": settings.app_version,
        "debug": settings.debug,
        "server": f"{settings.server_host}:{settings.server_port}",
        "database_type": "duckdb" if "duckdb" in settings.database_url else "unknown",
        "upload_dir": settings.upload_dir,
        "max_file_size_mb": settings.max_file_size // (1024 * 1024)
    }

# 🚀 重要修复：添加插件管理器启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时初始化插件管理器"""
    try:
        logger.info("🚀 系统启动中...")
        logger.info("🔌 插件管理器已在parser.py中初始化")
        logger.info("✅ 系统启动完成")
    except Exception as e:
        logger.error(f"❌ 系统启动失败: {e}")

# 注册API路由 - 直接导入避免相对导入问题
from fastapi import APIRouter

# 创建主API路由
api_router = APIRouter()

# 直接导入并注册各模块路由
try:
    from .api.parser import router as parser_router
    api_router.include_router(parser_router, prefix="/parser", tags=["parser"])
    logger.info("✅ Parser路由注册成功")
except Exception as e:
    logger.error(f"❌ Parser路由注册失败: {e}")

try:
    from .api.projects import router as projects_router
    api_router.include_router(projects_router, prefix="/projects", tags=["projects"])
    logger.info("✅ Projects路由注册成功")
except Exception as e:
    logger.error(f"❌ Projects路由注册失败: {e}")

try:
    from .api.banks import router as banks_router
    api_router.include_router(banks_router, tags=["banks"])
    logger.info("✅ Banks路由注册成功")
except Exception as e:
    logger.error(f"❌ Banks路由注册失败: {e}")

# 注册主API路由
app.include_router(api_router, prefix="/api", tags=["api"])

if __name__ == "__main__":
    import uvicorn
    uvicorn_settings = settings.get_uvicorn_settings()
    logger.info(f"🚀 启动服务器: {uvicorn_settings}")
    uvicorn.run(app, **uvicorn_settings) 