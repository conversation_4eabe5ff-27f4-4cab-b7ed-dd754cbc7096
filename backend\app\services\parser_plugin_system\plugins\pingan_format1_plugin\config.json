{"plugin_id": "pingan_format1_plugin", "plugin_name": "平安银行Format1解析器插件", "bank_name": "平安银行", "version": "1.0.0", "description": "解析平安银行个人交易明细格式，支持表头信息回填（户名/账号/卡号），D/C方向，字段标准映射。", "parser_class": "Plugin", "confidence": 0.95, "supported_banks": ["平安银行"], "supported_formats": ["xlsx", "xls"], "keywords": ["平安银行", "个人交易明细", "D/C", "可用余额", "摘要", "备注"], "file_patterns": ["*平安*.xls*"], "author": "银行流水分析系统开发团队", "created_date": "2025-08-13", "features": ["表头信息回填", "D/C收支解析", "字段标准映射", "账户汇总与日期范围"], "requirements": ["pandas>=1.3.0", "openpyxl>=3.0.0"]}