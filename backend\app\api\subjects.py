"""
被反映人资料API
提供被反映人资料的CRUD操作
"""
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import List, Dict, Any
from datetime import datetime
import logging

from ..middleware.auth_middleware import get_current_user, get_user_db
from ..models.duckdb_models import DuckDBSubject as Subject

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/")
async def get_subjects(
    project_id: str,
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> List[Dict[str, Any]]:
    """获取项目的被反映人资料列表"""
    try:
        logger.info(f"用户 '{current_user}' 获取项目 {project_id} 的被反映人资料列表")
        
        subjects = db.query(Subject).filter(Subject.project_id == project_id).all()
        
        result = []
        for subject in subjects:
            result.append({
                "subject_id": subject.subject_id,
                "project_id": subject.project_id,
                "name": subject.name,
                "gender": subject.gender,
                "birth_date": subject.birth_date,
                "nationality": subject.nationality,
                "native_place": subject.native_place,
                "birth_place": subject.birth_place,
                "id_number": subject.id_number,
                "work_start_date": subject.work_start_date,
                "education": subject.education,
                "major": subject.major,
                "degree": subject.degree,
                "professional_title": subject.professional_title,
                "current_position": subject.current_position,
                "work_unit": subject.work_unit,
                "resume": subject.resume,
                "phone": subject.phone,
                "address": subject.address,
                "political_status": subject.political_status,
                "marital_status": subject.marital_status,
                "notes": subject.notes,
                "created_at": subject.created_at.isoformat() if subject.created_at else None,
                "updated_at": subject.updated_at.isoformat() if subject.updated_at else None
            })
        
        logger.info(f"用户 '{current_user}' 获取到 {len(result)} 条被反映人资料")
        return result
        
    except Exception as e:
        logger.error(f"用户 '{current_user}' 获取被反映人资料失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取被反映人资料失败: {str(e)}")

@router.post("/")
async def create_subject(
    subject_data: Dict[str, Any],
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> Dict[str, Any]:
    """创建新的被反映人资料"""
    try:
        logger.info(f"用户 '{current_user}' 创建被反映人资料: {subject_data.get('name', '未知')}")
        
        # 创建新的被反映人资料
        subject = Subject(
            project_id=subject_data["project_id"],
            name=subject_data.get("name", ""),
            gender=subject_data.get("gender", ""),
            birth_date=subject_data.get("birth_date", ""),
            nationality=subject_data.get("nationality", ""),
            native_place=subject_data.get("native_place", ""),
            birth_place=subject_data.get("birth_place", ""),
            id_number=subject_data.get("id_number", ""),
            work_start_date=subject_data.get("work_start_date", ""),
            education=subject_data.get("education", ""),
            major=subject_data.get("major", ""),
            degree=subject_data.get("degree", ""),
            professional_title=subject_data.get("professional_title", ""),
            current_position=subject_data.get("current_position", ""),
            work_unit=subject_data.get("work_unit", ""),
            resume=subject_data.get("resume", ""),
            phone=subject_data.get("phone", ""),
            address=subject_data.get("address", ""),
            political_status=subject_data.get("political_status", ""),
            marital_status=subject_data.get("marital_status", ""),
            notes=subject_data.get("notes", ""),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        db.add(subject)
        db.commit()
        db.refresh(subject)
        
        logger.info(f"用户 '{current_user}' 成功创建被反映人资料，ID: {subject.subject_id}")
        
        return {
            "subject_id": subject.subject_id,
            "project_id": subject.project_id,
            "name": subject.name,
            "gender": subject.gender,
            "birth_date": subject.birth_date,
            "nationality": subject.nationality,
            "native_place": subject.native_place,
            "birth_place": subject.birth_place,
            "id_number": subject.id_number,
            "work_start_date": subject.work_start_date,
            "education": subject.education,
            "major": subject.major,
            "degree": subject.degree,
            "professional_title": subject.professional_title,
            "current_position": subject.current_position,
            "work_unit": subject.work_unit,
            "resume": subject.resume,
            "phone": subject.phone,
            "address": subject.address,
            "political_status": subject.political_status,
            "marital_status": subject.marital_status,
            "notes": subject.notes,
            "created_at": subject.created_at.isoformat() if subject.created_at else None,
            "updated_at": subject.updated_at.isoformat() if subject.updated_at else None
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"用户 '{current_user}' 创建被反映人资料失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建被反映人资料失败: {str(e)}")

@router.put("/{subject_id}")
async def update_subject(
    subject_id: str,
    subject_data: Dict[str, Any],
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> Dict[str, Any]:
    """更新被反映人资料"""
    try:
        logger.info(f"用户 '{current_user}' 更新被反映人资料，ID: {subject_id}")
        
        subject = db.query(Subject).filter(Subject.subject_id == subject_id).first()
        if not subject:
            raise HTTPException(status_code=404, detail="被反映人资料不存在")
        
        # 更新字段
        for field, value in subject_data.items():
            if field != "subject_id" and hasattr(subject, field):
                setattr(subject, field, value)
        
        subject.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(subject)
        
        logger.info(f"用户 '{current_user}' 成功更新被反映人资料，ID: {subject_id}")
        
        return {
            "subject_id": subject.subject_id,
            "project_id": subject.project_id,
            "name": subject.name,
            "gender": subject.gender,
            "birth_date": subject.birth_date,
            "nationality": subject.nationality,
            "native_place": subject.native_place,
            "birth_place": subject.birth_place,
            "id_number": subject.id_number,
            "work_start_date": subject.work_start_date,
            "education": subject.education,
            "major": subject.major,
            "degree": subject.degree,
            "professional_title": subject.professional_title,
            "current_position": subject.current_position,
            "work_unit": subject.work_unit,
            "resume": subject.resume,
            "phone": subject.phone,
            "address": subject.address,
            "political_status": subject.political_status,
            "marital_status": subject.marital_status,
            "notes": subject.notes,
            "created_at": subject.created_at.isoformat() if subject.created_at else None,
            "updated_at": subject.updated_at.isoformat() if subject.updated_at else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"用户 '{current_user}' 更新被反映人资料失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新被反映人资料失败: {str(e)}")

@router.delete("/{subject_id}")
async def delete_subject(
    subject_id: str,
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> Dict[str, str]:
    """删除被反映人资料"""
    try:
        logger.info(f"用户 '{current_user}' 删除被反映人资料，ID: {subject_id}")
        
        subject = db.query(Subject).filter(Subject.subject_id == subject_id).first()
        if not subject:
            raise HTTPException(status_code=404, detail="被反映人资料不存在")
        
        db.delete(subject)
        db.commit()
        
        logger.info(f"用户 '{current_user}' 成功删除被反映人资料，ID: {subject_id}")
        
        return {"message": "被反映人资料删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"用户 '{current_user}' 删除被反映人资料失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除被反映人资料失败: {str(e)}") 