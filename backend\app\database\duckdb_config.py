"""
DuckDB数据库连接管理
支持用户级数据库隔离
"""
import os
from pathlib import Path
from sqlalchemy import create_engine, text
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.pool import NullPool
from typing import Optional
import urllib.parse

from ..config import get_env
from ..models.duckdb_models import DuckDBBase as Base

# 获取项目根目录的绝对路径
PROJECT_ROOT = Path(__file__).parent.parent.parent.parent  # 回到项目根目录
DEFAULT_DB_DIR = PROJECT_ROOT / "data"

def get_user_database_path(username: str) -> str:
    """
    根据用户名获取用户专属数据库路径
    
    Args:
        username: 用户名
        
    Returns:
        str: 用户专属数据库文件路径
    """
    # 确保数据库目录存在
    DEFAULT_DB_DIR.mkdir(parents=True, exist_ok=True)
    
    # 对用户名进行URL编码以处理特殊字符
    safe_username = urllib.parse.quote(username, safe='')
    user_db_path = DEFAULT_DB_DIR / f"{safe_username}_bankflow.duckdb"
    
    return str(user_db_path)

def get_duckdb_database_url(username: Optional[str] = None, database_path: Optional[str] = None) -> str:
    """
    获取DuckDB数据库连接URL
    
    Args:
        username: 用户名，用于创建用户专属数据库
        database_path: 数据库文件路径，如果为None则根据用户名创建
    
    Returns:
        str: DuckDB连接URL
    """
    if database_path is None:
        if username:
            database_path = get_user_database_path(username)
        else:
            # 如果没有指定用户名，使用默认共享数据库（用于系统管理）
            database_path = str(DEFAULT_DB_DIR / "system_bankflow.duckdb")
    
    # 如果是内存数据库
    if database_path == ":memory:":
        return "duckdb:///:memory:"
    
    # 确保数据库目录存在
    db_path = Path(database_path)
    db_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 返回文件数据库URL
    return f"duckdb:///{database_path}"

# 创建DuckDB数据库引擎
def create_duckdb_engine(username: Optional[str] = None, database_path: Optional[str] = None, **kwargs):
    """
    创建DuckDB数据库引擎
    
    Args:
        username: 用户名，用于创建用户专属数据库
        database_path: 数据库文件路径
        **kwargs: 传递给create_engine的其他参数
    
    Returns:
        Engine: SQLAlchemy引擎
    """
    url = get_duckdb_database_url(username, database_path)
    
    # DuckDB引擎配置 - 简化配置避免冲突
    engine_config = {
        "echo": False,
        "poolclass": NullPool,  # 使用NullPool避免连接池问题
    }
    
    # 合并用户提供的配置
    engine_config.update(kwargs)
    
    return create_engine(url, **engine_config)

# 默认系统引擎（用于系统级操作）
system_engine = None

def get_system_engine():
    """获取系统引擎，延迟初始化"""
    global system_engine
    if system_engine is None:
        system_engine = create_duckdb_engine()
    return system_engine

# 用户引擎缓存
user_engines = {}

def get_user_engine(username: str):
    """
    获取用户专属数据库引擎
    
    Args:
        username: 用户名
        
    Returns:
        Engine: 用户专属数据库引擎
    """
    if username not in user_engines:
        user_engines[username] = create_duckdb_engine(username=username)
        # 初始化用户数据库表结构
        init_user_database(user_engines[username], username)
    
    return user_engines[username]

def get_user_session(username: str):
    """
    获取用户专属数据库会话工厂
    
    Args:
        username: 用户名
        
    Returns:
        sessionmaker: 用户专属会话工厂
    """
    engine = get_user_engine(username)
    return sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db_by_user(username: str):
    """
    根据用户名获取数据库会话（用于FastAPI依赖注入）
    
    Args:
        username: 用户名
        
    Yields:
        Session: 用户专属数据库会话
    """
    SessionLocal = get_user_session(username)
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 兼容原有接口（使用系统数据库）
def get_db():
    """获取系统数据库会话（保持向后兼容）"""
    engine = get_system_engine()
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 创建基类（与PostgreSQL兼容）
DuckDBBase = declarative_base()

def init_user_database(engine, username: str):
    """
    初始化用户专属数据库，创建所有表
    
    Args:
        engine: 数据库引擎
        username: 用户名
    """
    # 导入所有模型以确保它们被注册到Base.metadata
    from ..models.duckdb_models import (
        DuckDBBank, DuckDBProject, DuckDBParsingTemplate, 
        DuckDBAccount, DuckDBRawTransaction, DuckDBTransaction, DuckDBClue,
        DuckDBSubject, DuckDBRelatedPerson, DuckDBAsset, DuckDBRelatedUnit, DuckDBRelationship
    )
    
    # 创建所有表
    from ..models.duckdb_models import DuckDBBase
    DuckDBBase.metadata.create_all(bind=engine)
    
    print(f"✅ 用户 '{username}' 的数据库已初始化: {engine.url}")

def init_duckdb_database(engine=None):
    """
    初始化系统数据库，创建所有表
    
    Args:
        engine: 数据库引擎，如果为None则使用默认引擎
    """
    if engine is None:
        engine = get_system_engine()
    
    # 确保临时目录存在
    temp_dir = Path("temp/duckdb")
    temp_dir.mkdir(parents=True, exist_ok=True)
    
    # 导入所有模型以确保它们被注册到Base.metadata
    from ..models.duckdb_models import (
        DuckDBBank, DuckDBProject, DuckDBParsingTemplate, 
        DuckDBAccount, DuckDBRawTransaction, DuckDBTransaction, DuckDBClue,
        DuckDBSubject, DuckDBRelatedPerson, DuckDBAsset, DuckDBRelatedUnit, DuckDBRelationship
    )
    
    # 创建所有表
    from ..models.duckdb_models import DuckDBBase
    DuckDBBase.metadata.create_all(bind=engine)
    
    print(f"✅ 系统数据库已初始化: {engine.url}")

# 兼容别名
init_duckdb = init_duckdb_database

def test_duckdb_connection(username: Optional[str] = None):
    """
    测试DuckDB连接
    
    Args:
        username: 用户名，如果为None则测试系统数据库
    """
    try:
        if username:
            engine = get_user_engine(username)
            message_prefix = f"用户 '{username}' 的"
        else:
            engine = get_system_engine()
            message_prefix = "系统"
            
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 'DuckDB连接测试成功!' as message"))
            message = result.fetchone()[0]
            print(f"✅ {message_prefix}{message}")
            return True
    except Exception as e:
        print(f"❌ {message_prefix}DuckDB连接测试失败: {e}")
        return False

def list_user_databases():
    """列出所有用户数据库"""
    db_files = list(DEFAULT_DB_DIR.glob("*_bankflow.duckdb"))
    users = []
    for db_file in db_files:
        # 解码用户名
        encoded_username = db_file.stem.replace("_bankflow", "")
        try:
            username = urllib.parse.unquote(encoded_username)
            users.append({
                "username": username,
                "database_file": str(db_file),
                "size": db_file.stat().st_size if db_file.exists() else 0
            })
        except Exception:
            continue
    return users

if __name__ == "__main__":
    # 测试连接
    test_duckdb_connection()
    
    # 初始化系统数据库
    init_duckdb_database() 