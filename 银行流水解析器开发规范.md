# 银行流水解析器开发规范

## 📋 概述

本文档定义了银行流水解析器插件的开发规范，旨在确保代码质量、减少开发过程中的问题，并提高开发效率。

## 🎯 核心原则

### 1. 一致性原则
- **字段命名**: 前后端必须使用统一的字段名称
- **数据格式**: 所有解析器输出格式必须保持一致
- **接口规范**: 严格遵循插件接口定义

### 2. 可靠性原则  
- **数据验证**: 每个解析步骤都必须进行数据验证
- **错误处理**: 完善的异常处理和错误恢复机制
- **测试覆盖**: 关键功能必须有完整的测试覆盖

### 3. 可维护性原则
- **代码规范**: 遵循统一的编码规范和最佳实践
- **文档完整**: 充分的代码注释和技术文档
- **模块化设计**: 清晰的模块划分和职责分离

## 🔧 开发规范

### 1. 字段命名规范

#### A. 标准字段名称
```python
# 必须使用的标准字段名称
REQUIRED_FIELDS = {
    # 账户基础信息
    "person_name": "持卡人姓名",
    "bank_name": "银行名称",
    "account_number": "账号", 
    "card_number": "卡号",
    
    # 交易核心信息
    "transaction_date": "交易日期",
    "transaction_time": "交易时间",
    "transaction_amount": "交易金额",
    "transaction_type": "交易方式",
    "transaction_balance": "交易余额",
    
    # 对方信息 (重要：必须使用counterparty而非counterpart)
    "counterparty_name": "对方户名",
    "counterparty_account": "对方账号", 
    "counterparty_bank": "对方银行",
    
    # 补充信息
    "remark1": "备注1",
    "remark2": "备注2",
    "remark3": "备注3",
    "currency": "货币"
}
```

#### B. 字段验证规则
```python
def validate_field_names(output_data: dict) -> List[str]:
    """验证输出字段名称"""
    errors = []
    
    for transaction in output_data.get('transactions', []):
        for field_name in transaction.keys():
            if field_name not in REQUIRED_FIELDS:
                errors.append(f"使用了非标准字段名: {field_name}")
                
            # 特别检查常见错误
            if field_name == "counterpart_bank":
                errors.append("错误字段名: counterpart_bank，应使用: counterparty_bank")
    
    return errors
```

### 2. 插件结构规范

#### A. 必需文件结构
```
plugin_name/
├── plugin.py          # 主插件文件
├── config.json        # 插件配置
├── metadata.json      # 插件元信息
├── requirements.txt   # 依赖列表
├── README.md          # 插件说明
└── tests/             # 测试文件
    ├── __init__.py
    ├── test_plugin.py
    └── test_data/
        └── sample.xlsx
```

#### B. 插件类实现规范
```python
from typing import Dict, List, Any, Optional
from abc import ABC, abstractmethod

class BankStatementParser(ABC):
    """银行流水解析器基类"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.config = self._load_config()
    
    @abstractmethod
    def get_metadata(self) -> Dict[str, Any]:
        """获取插件元信息"""
        pass
    
    @abstractmethod
    def validate_file(self, file_path: str) -> bool:
        """验证文件格式"""
        pass
    
    @abstractmethod
    def calculate_confidence(self, file_path: str) -> float:
        """计算置信度 (0.0-1.0)"""
        pass
    
    @abstractmethod
    def parse(self, file_path: str) -> Dict[str, Any]:
        """执行解析"""
        pass
    
    @abstractmethod
    def extract_sample(self, file_path: str = None, limit: int = 10) -> Dict[str, Any]:
        """
        提取样本数据用于快速评估解析器适用性
        
        Args:
            file_path: 文件路径，如果为None则使用实例的file_path
            limit: 最大提取样本数量，默认10条
            
        Returns:
            包含账户和交易样本的字典，格式为:
            {
                'accounts': [账户信息列表],
                'transactions': [交易记录列表],
                'metadata': {
                    'sample_size': 样本大小,
                    'target_sheet': 目标工作表,
                    'data_start_row': 数据起始行,
                    'parsing_method': 解析方法描述
                }
            }
        """
        pass
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        return {
            "status": "healthy",
            "last_check": datetime.now().isoformat(),
            "memory_usage": self._get_memory_usage(),
            "error_count": self._get_error_count()
        }
```

### 3. 数据验证规范

#### A. 输入验证
```python
def validate_input_file(file_path: str) -> bool:
    """验证输入文件"""
    try:
        # 检查文件存在性
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 检查文件大小
        file_size = os.path.getsize(file_path)
        if file_size > 100 * 1024 * 1024:  # 100MB限制
            raise ValueError(f"文件过大: {file_size} bytes")
        
        # 检查文件格式
        if not file_path.lower().endswith(('.xlsx', '.xls', '.csv')):
            raise ValueError(f"不支持的文件格式: {file_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"输入文件验证失败: {e}")
        return False
```

#### B. 输出验证
```python
def validate_output_data(result: Dict[str, Any]) -> List[str]:
    """验证输出数据"""
    errors = []
    
    # 验证基础结构
    required_keys = ['success', 'message', 'accounts', 'transactions']
    for key in required_keys:
        if key not in result:
            errors.append(f"缺少必需字段: {key}")
    
    # 验证账户数据
    for i, account in enumerate(result.get('accounts', [])):
        account_errors = validate_account_data(account, i)
        errors.extend(account_errors)
    
    # 验证交易数据
    for i, transaction in enumerate(result.get('transactions', [])):
        transaction_errors = validate_transaction_data(transaction, i)
        errors.extend(transaction_errors)
    
    return errors

def validate_account_data(account: Dict[str, Any], index: int) -> List[str]:
    """验证账户数据"""
    errors = []
    required_fields = ['person_name', 'account_number', 'bank_name']
    
    for field in required_fields:
        if field not in account or not account[field]:
            errors.append(f"账户{index}: 缺少必需字段 {field}")
    
    # 验证数值字段
    numeric_fields = ['total_inflow', 'total_outflow', 'transactions_count']
    for field in numeric_fields:
        if field in account and not isinstance(account[field], (int, float)):
            errors.append(f"账户{index}: {field} 应为数值类型")
    
    return errors

def validate_transaction_data(transaction: Dict[str, Any], index: int) -> List[str]:
    """验证交易数据"""
    errors = []
    required_fields = ['transaction_date', 'transaction_amount', 'account_number']
    
    for field in required_fields:
        if field not in transaction or transaction[field] is None:
            errors.append(f"交易{index}: 缺少必需字段 {field}")
    
    # 验证日期格式
    if 'transaction_date' in transaction:
        try:
            datetime.strptime(transaction['transaction_date'], '%Y-%m-%d')
        except ValueError:
            errors.append(f"交易{index}: 日期格式错误 {transaction['transaction_date']}")
    
    # 验证金额类型
    if 'transaction_amount' in transaction:
        if not isinstance(transaction['transaction_amount'], (int, float)):
            errors.append(f"交易{index}: 金额应为数值类型")
    
    return errors
```

### 4. 错误处理规范

#### A. 异常类定义
```python
class ParserBaseException(Exception):
    """解析器基础异常"""
    def __init__(self, message: str, error_code: str = None):
        super().__init__(message)
        self.error_code = error_code
        self.timestamp = datetime.now()

class FileValidationError(ParserBaseException):
    """文件验证错误"""
    pass

class DataParsingError(ParserBaseException):
    """数据解析错误"""
    pass

class FieldMappingError(ParserBaseException):
    """字段映射错误"""
    pass
```

#### B. 错误处理模式
```python
def safe_parse_with_recovery(self, file_path: str) -> Dict[str, Any]:
    """带恢复机制的安全解析"""
    try:
        # 主要解析逻辑
        return self._parse_main(file_path)
        
    except FileValidationError as e:
        self.logger.error(f"文件验证失败: {e}")
        return self._create_error_result("FILE_VALIDATION_ERROR", str(e))
        
    except DataParsingError as e:
        self.logger.error(f"数据解析失败: {e}")
        # 尝试降级解析
        return self._try_fallback_parsing(file_path, str(e))
        
    except Exception as e:
        self.logger.error(f"未知错误: {e}", exc_info=True)
        return self._create_error_result("UNKNOWN_ERROR", str(e))

def _create_error_result(self, error_code: str, message: str) -> Dict[str, Any]:
    """创建错误结果"""
    return {
        "success": False,
        "error_code": error_code,
        "message": message,
        "accounts": [],
        "transactions": [],
        "timestamp": datetime.now().isoformat()
    }
```

### 5. 测试规范

#### A. 单元测试
```python
import unittest
from unittest.mock import patch, MagicMock

class TestBankParser(unittest.TestCase):
    """银行解析器单元测试"""
    
    def setUp(self):
        self.parser = YourBankParser()
        self.test_file = "tests/test_data/sample.xlsx"
    
    def test_validate_file_success(self):
        """测试文件验证成功"""
        result = self.parser.validate_file(self.test_file)
        self.assertTrue(result)
    
    def test_validate_file_not_exists(self):
        """测试文件不存在"""
        result = self.parser.validate_file("nonexistent.xlsx")
        self.assertFalse(result)
    
    def test_calculate_confidence(self):
        """测试置信度计算"""
        confidence = self.parser.calculate_confidence(self.test_file)
        self.assertGreaterEqual(confidence, 0.0)
        self.assertLessEqual(confidence, 1.0)
    
    def test_parse_success(self):
        """测试解析成功"""
        result = self.parser.parse(self.test_file)
        
        # 验证基础结构
        self.assertIn('success', result)
        self.assertIn('accounts', result)
        self.assertIn('transactions', result)
        
        # 验证字段名称
        if result['transactions']:
            transaction = result['transactions'][0]
            self.assertIn('counterparty_bank', transaction)  # 确保使用正确字段名
    
    def test_field_mapping_consistency(self):
        """测试字段映射一致性"""
        result = self.parser.parse(self.test_file)
        errors = validate_field_names(result)
        self.assertEqual(len(errors), 0, f"字段映射错误: {errors}")
```

#### B. 集成测试
```python
def test_end_to_end_parsing():
    """端到端解析测试"""
    # 1. 准备测试数据
    test_file = create_test_excel_file()
    
    # 2. 执行解析
    parser = YourBankParser()
    result = parser.parse(test_file)
    
    # 3. 验证结果
    assert result['success'] == True
    assert len(result['accounts']) > 0
    assert len(result['transactions']) > 0
    
    # 4. 验证数据一致性
    validate_data_consistency(result)
    
    # 5. 验证字段映射
    validate_field_mapping(result)
    
    # 6. 清理测试数据
    cleanup_test_file(test_file)
```

### 6. 性能规范

#### A. 性能要求
- **解析速度**: > 1000条交易/秒
- **内存使用**: < 512MB (单个文件)
- **响应时间**: < 60秒 (100MB文件)
- **并发支持**: 支持10个并发解析任务

#### B. 性能监控
```python
import time
import psutil
from functools import wraps

def monitor_performance(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss
        
        try:
            result = func(*args, **kwargs)
            
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss
            
            # 记录性能指标
            performance_metrics = {
                "function": func.__name__,
                "execution_time": end_time - start_time,
                "memory_usage": (end_memory - start_memory) / 1024 / 1024,  # MB
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"性能指标: {performance_metrics}")
            return result
            
        except Exception as e:
            logger.error(f"性能监控异常: {e}")
            raise
    
    return wrapper
```

## 📋 开发检查清单

### 开发前检查
- [ ] 确认银行流水格式和字段映射
- [ ] 准备充足的测试数据
- [ ] 了解现有插件架构和接口
- [ ] 设置开发环境和调试工具

### 开发中检查
- [ ] 使用标准字段名称 (特别注意counterparty_bank)
- [ ] 实现完整的数据验证逻辑
- [ ] 添加充分的错误处理
- [ ] 实现预解析功能(extract_sample)，使用与实际解析相同的核心逻辑
- [ ] 确保预解析输出包含4维度评估所需的所有字段
- [ ] 编写单元测试和集成测试

### 开发后检查
- [ ] 运行完整的测试套件
- [ ] 验证字段映射一致性
- [ ] 测试预解析功能是否正常工作
- [ ] 验证4维度评估结果是否准确
- [ ] 检查前端显示是否完整
- [ ] 检查性能指标
- [ ] 更新文档和注释

### 部署前检查
- [ ] 代码审查通过
- [ ] 所有测试通过
- [ ] 性能测试达标
- [ ] 文档完整更新

---

*遵循本规范可以显著减少开发过程中的问题，提高代码质量和开发效率。*

*文档版本: v1.0*  
*最后更新: 2025年1月*
