"""
解析结果验证器
验证解析结果是否符合预期，检查数据质量
"""
import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import pandas as pd

logger = logging.getLogger(__name__)

class ParsingResultValidator:
    """
    解析结果验证器
    负责验证解析结果是否有效，检查数据质量和完整性
    """
    
    def __init__(self):
        """初始化验证器"""
        pass
    
    def validate_result(self, parsing_result: Dict[str, Any], validation_rules: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证解析结果
        
        Args:
            parsing_result: 解析结果
            validation_rules: 验证规则
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "account_stats": {},
            "transaction_stats": {}
        }
        
        try:
            # 检查解析结果是否包含必要字段
            if not parsing_result or not isinstance(parsing_result, dict):
                validation_result["is_valid"] = False
                validation_result["errors"].append("解析结果无效或为空")
                return validation_result
            
            # 检查metadata
            if "metadata" not in parsing_result:
                validation_result["errors"].append("缺少元数据(metadata)字段")
                validation_result["is_valid"] = False
            
            # 检查账户信息
            if "accounts" not in parsing_result or not parsing_result["accounts"]:
                validation_result["errors"].append("没有解析到账户信息")
                validation_result["is_valid"] = False
            else:
                account_validation = self._validate_accounts(parsing_result["accounts"], validation_rules)
                validation_result["account_stats"] = account_validation["stats"]
                validation_result["errors"].extend(account_validation["errors"])
                validation_result["warnings"].extend(account_validation["warnings"])
            
            # 检查交易数据
            if "transactions_by_account" not in parsing_result or not parsing_result["transactions_by_account"]:
                validation_result["errors"].append("没有解析到交易数据")
                validation_result["is_valid"] = False
            else:
                transaction_validation = self._validate_transactions(
                    parsing_result["transactions_by_account"], 
                    validation_rules
                )
                validation_result["transaction_stats"] = transaction_validation["stats"]
                validation_result["errors"].extend(transaction_validation["errors"])
                validation_result["warnings"].extend(transaction_validation["warnings"])
            
            # 更新验证结果状态
            if validation_result["errors"]:
                validation_result["is_valid"] = False
            
            return validation_result
        except Exception as e:
            logger.error(f"验证解析结果时出错: {str(e)}")
            validation_result["is_valid"] = False
            validation_result["errors"].append(f"验证过程出错: {str(e)}")
            return validation_result
    
    def _validate_accounts(self, accounts: List[Dict[str, Any]], rules: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证账户信息
        
        Args:
            accounts: 账户列表
            rules: 验证规则
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {
            "errors": [],
            "warnings": [],
            "stats": {
                "total_accounts": len(accounts),
                "valid_accounts": 0,
                "invalid_accounts": 0
            }
        }
        
        # 账号模式
        account_number_pattern = rules.get("accountNumberPattern", r"^\d+$")
        
        for i, account in enumerate(accounts):
            is_valid = True
            
            # 检查账号
            if "account_number" not in account or not account["account_number"]:
                result["errors"].append(f"账户 #{i+1} 缺少账号")
                is_valid = False
            elif not re.match(account_number_pattern, str(account["account_number"])):
                result["warnings"].append(f"账户 #{i+1} 账号格式不符合预期: {account['account_number']}")
            
            # 检查户名
            if "account_name" not in account or not account["account_name"]:
                result["warnings"].append(f"账户 #{i+1} 缺少户名")
            
            # 更新统计
            if is_valid:
                result["stats"]["valid_accounts"] += 1
            else:
                result["stats"]["invalid_accounts"] += 1
        
        return result
    
    def _validate_transactions(self, transactions_by_account: Dict[str, List[Dict[str, Any]]], 
                             rules: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证交易数据
        
        Args:
            transactions_by_account: 按账户分组的交易列表
            rules: 验证规则
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {
            "errors": [],
            "warnings": [],
            "stats": {
                "total_accounts": len(transactions_by_account),
                "total_transactions": 0,
                "accounts_with_too_few_transactions": 0,
                "missing_required_fields": 0,
                "date_format_issues": 0,
                "amount_format_issues": 0
            }
        }
        
        # 获取验证规则
        required_fields = rules.get("requiredFields", ["transaction_datetime", "transaction_amount"])
        min_transactions = rules.get("minTransactions", 1)
        date_format = rules.get("dateFormat", "%Y-%m-%d")
        amount_decimal_places = rules.get("amountDecimalPlaces", 2)
        
        # 遍历每个账户的交易
        for account_number, transactions in transactions_by_account.items():
            account_transaction_count = len(transactions)
            result["stats"]["total_transactions"] += account_transaction_count
            
            # 检查交易数量
            if account_transaction_count < min_transactions:
                result["warnings"].append(
                    f"账户 {account_number} 的交易数量({account_transaction_count})少于预期最小值({min_transactions})"
                )
                result["stats"]["accounts_with_too_few_transactions"] += 1
            
            # 检查每笔交易的必要字段和格式
            for i, transaction in enumerate(transactions):
                # 检查必要字段
                for field in required_fields:
                    if field not in transaction or transaction[field] is None:
                        result["errors"].append(
                            f"账户 {account_number} 的交易 #{i+1} 缺少必要字段: {field}"
                        )
                        result["stats"]["missing_required_fields"] += 1
                
                # 检查日期格式
                if "transaction_datetime" in transaction and transaction["transaction_datetime"]:
                    try:
                        # 尝试解析日期
                        if isinstance(transaction["transaction_datetime"], str):
                            datetime.strptime(transaction["transaction_datetime"], date_format)
                        # 如果已经是日期对象则跳过
                    except ValueError:
                        result["warnings"].append(
                            f"账户 {account_number} 的交易 #{i+1} 日期格式不符合预期: {transaction['transaction_datetime']}"
                        )
                        result["stats"]["date_format_issues"] += 1
                
                # 检查金额格式
                if "transaction_amount" in transaction and transaction["transaction_amount"] is not None:
                    amount = transaction["transaction_amount"]
                    # 如果是字符串，尝试转换为数字
                    if isinstance(amount, str):
                        try:
                            amount = float(amount.replace(',', ''))
                        except ValueError:
                            result["warnings"].append(
                                f"账户 {account_number} 的交易 #{i+1} 金额格式无效: {amount}"
                            )
                            result["stats"]["amount_format_issues"] += 1
                    
                    # 检查小数位数
                    if isinstance(amount, (int, float)):
                        decimal_str = str(amount).split('.')
                        if len(decimal_str) > 1 and len(decimal_str[1]) > amount_decimal_places:
                            result["warnings"].append(
                                f"账户 {account_number} 的交易 #{i+1} 金额小数位数({len(decimal_str[1])})超过预期({amount_decimal_places}): {amount}"
                            )
                            result["stats"]["amount_format_issues"] += 1
        
        return result
    
    def check_data_consistency(self, parsing_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        检查数据一致性
        
        Args:
            parsing_result: 解析结果
            
        Returns:
            Dict[str, Any]: 一致性检查结果
        """
        result = {
            "is_consistent": True,
            "issues": [],
            "stats": {}
        }
        
        try:
            accounts = parsing_result.get("accounts", [])
            transactions_by_account = parsing_result.get("transactions_by_account", {})
            
            # 检查每个账户是否都有交易数据
            account_numbers = set(acc["account_number"] for acc in accounts if "account_number" in acc)
            transaction_accounts = set(transactions_by_account.keys())
            
            # 找出有账户信息但没有交易的账户
            accounts_without_transactions = account_numbers - transaction_accounts
            if accounts_without_transactions:
                result["issues"].append(f"以下账户没有交易数据: {', '.join(accounts_without_transactions)}")
                result["is_consistent"] = False
            
            # 找出有交易但没有账户信息的账户
            transactions_without_account = transaction_accounts - account_numbers
            if transactions_without_account:
                result["issues"].append(f"以下账户有交易但缺少账户信息: {', '.join(transactions_without_account)}")
                result["is_consistent"] = False
            
            # 对每个账户的交易进行一致性检查
            for account_number, transactions in transactions_by_account.items():
                if not transactions:
                    continue
                
                # 检查交易日期是否按时间顺序排列
                dates = []
                for tx in transactions:
                    if "transaction_datetime" in tx and tx["transaction_datetime"]:
                        # 尝试转换为日期对象
                        if isinstance(tx["transaction_datetime"], str):
                            try:
                                # 尝试多种常见日期格式
                                date_formats = ["%Y-%m-%d", "%Y-%m-%d %H:%M:%S", "%Y/%m/%d", "%d/%m/%Y"]
                                date_obj = None
                                for fmt in date_formats:
                                    try:
                                        date_obj = datetime.strptime(tx["transaction_datetime"], fmt)
                                        break
                                    except ValueError:
                                        continue
                                
                                if date_obj:
                                    dates.append(date_obj)
                            except Exception:
                                pass
                        elif isinstance(tx["transaction_datetime"], datetime):
                            dates.append(tx["transaction_datetime"])
                
                # 检查日期是否有序
                if dates and len(dates) > 1:
                    is_ordered = all(dates[i] <= dates[i+1] for i in range(len(dates)-1)) or \
                                 all(dates[i] >= dates[i+1] for i in range(len(dates)-1))
                    
                    if not is_ordered:
                        result["issues"].append(f"账户 {account_number} 的交易日期不是按时间顺序排列的")
                        result["is_consistent"] = False
                
                # 检查借贷平衡（如果有余额字段）
                if all("balance" in tx for tx in transactions):
                    try:
                        # 尝试验证余额变化是否与交易金额一致
                        for i in range(len(transactions)-1):
                            current_balance = float(str(transactions[i]["balance"]).replace(',', ''))
                            next_balance = float(str(transactions[i+1]["balance"]).replace(',', ''))
                            
                            if "transaction_amount" in transactions[i+1]:
                                amount = float(str(transactions[i+1]["transaction_amount"]).replace(',', ''))
                                
                                # 判断借贷方向
                                is_debit = False
                                if "dr_cr_flag" in transactions[i+1]:
                                    is_debit = transactions[i+1]["dr_cr_flag"] in ["借", "支出", "D", "DR"]
                                elif next_balance < current_balance:
                                    is_debit = True
                                
                                expected_balance = current_balance - amount if is_debit else current_balance + amount
                                
                                # 允许小误差
                                if abs(expected_balance - next_balance) > 0.01:
                                    result["issues"].append(
                                        f"账户 {account_number} 的交易 #{i+2} 余额变化与交易金额不一致"
                                    )
                                    result["is_consistent"] = False
                                    break
                    except Exception as e:
                        logger.warning(f"检查账户 {account_number} 的余额一致性时出错: {str(e)}")
            
            return result
        except Exception as e:
            logger.error(f"检查数据一致性时出错: {str(e)}")
            result["is_consistent"] = False
            result["issues"].append(f"一致性检查过程出错: {str(e)}")
            return result 