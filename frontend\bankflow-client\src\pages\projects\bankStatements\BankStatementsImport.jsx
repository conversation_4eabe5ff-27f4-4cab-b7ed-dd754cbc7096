import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Card, 
  Steps, 
  Button, 
  Upload, 
  message, 
  Select, 
  Divider, 
  Space, 
  Table, 
  Typography, 
  Row, 
  Col,
  Alert,
  Radio,
  Tag,
  Progress,
  Spin,
  Result,
  Statistic,
  Badge,
  Descriptions,
  Modal,
  Empty,
  Tabs,
  Tooltip,
  Switch,
  Form,
  InputNumber
} from 'antd';
import { 
  InboxOutlined, 
  FileExcelOutlined, 
  FilePdfOutlined,
  FileImageOutlined,
  CheckCircleOutlined,
  LoadingOutlined,
  BankOutlined,
  FileTextOutlined,
  CloudUploadOutlined,
  SaveOutlined
} from '@ant-design/icons';

// 导入API配置
import { buildApiUrl, API_ENDPOINTS } from '../../../config/api';

const { Dragger } = Upload;
const { Title, Text } = Typography;
const { Option } = Select;

/**
 * 银行流水导入模块页面
 */
const BankStatementsImport = () => {
  const navigate = useNavigate();
  const { projectId: urlProjectId } = useParams();
  const [projectId, setProjectId] = useState(urlProjectId);
  
  // 当没有项目ID时，创建默认项目
  useEffect(() => {
    const createDefaultProjectIfNeeded = async () => {
      if (!projectId) {
        try {
          const response = await fetch(buildApiUrl(API_ENDPOINTS.projects), {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              project_name: '银行流水导入项目',
              person_name: '临时项目',
              description: '自动创建的银行流水导入项目'
            })
          });
          
          if (response.ok) {
            const newProject = await response.json();
            setProjectId(newProject.project_id);
            message.success('已自动创建项目用于数据导入');
            // 更新URL
            navigate(`/projects/${newProject.project_id}/bankStatements/import`, { replace: true });
          }
        } catch (error) {
          console.error('创建默认项目失败:', error);
          message.error('无法创建项目，请手动创建项目后再导入数据');
        }
      }
    };
    
    createDefaultProjectIfNeeded();
  }, [projectId, navigate]);

  const [currentStep, setCurrentStep] = useState(0);
  const [fileList, setFileList] = useState([]);
  const [selectedBank, setSelectedBank] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [fileType, setFileType] = useState('excel');
  const [parseStatus, setParseStatus] = useState('idle');
  const [parsingResult, setParsingResult] = useState(null);
  const [parseProgress, setParseProgress] = useState(0);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [availableTemplates, setAvailableTemplates] = useState([]);
  const [templateLoading, setTemplateLoading] = useState(false);
  
  // 交易详情显示状态 - 符合PARSER_DEVELOPMENT_RULES.md规范：原页面展示，不使用弹窗
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [accountTransactions, setAccountTransactions] = useState([]);
  const [showTransactionDetails, setShowTransactionDetails] = useState(false);
  
  // 保存进度状态
  const [saveProgress, setSaveProgress] = useState(0);
  const [isSaving, setIsSaving] = useState(false);
  
  // 银行列表状态
  const [bankOptions, setBankOptions] = useState([]);
  const [bankLoading, setBankLoading] = useState(true);
  
  // 🔧 新增：智能分析和4维度评分状态
  const [smartAnalysisLoading, setSmartAnalysisLoading] = useState(false);
  const [smartAnalysisResult, setSmartAnalysisResult] = useState('');
  
  // 获取银行图标
  const getBankIcon = (bankName) => {
    const iconMap = {
      '中国工商银行': '🏦',
      '中国农业银行': '🌾',
      '中国银行': '🏛️',
      '中国建设银行': '🏗️',
      '交通银行': '🚗',
      '中国邮政储蓄银行': '📮',
      '招商银行': '💼',
      '上海浦东发展银行': '🌅',
      '中信银行': '📞',
      '中国光大银行': '☀️',
      '中国民生银行': '👥',
      '兴业银行': '🌱',
      '平安银行': '🛡️',
      '北部湾银行': '🏖️',
      '农村信用社': '🌿',
      '其他银行': '🏦'
    };
    return iconMap[bankName] || '🏦';
  };

  // 获取银行对应的默认解析器
  const getDefaultParserForBank = (bankName) => {
    const bankParserMap = {
      '中国工商银行': 'icbc_format1_plugin',
      '中国农业银行': 'abc_format1_plugin',
      '中国银行': 'boc_format1_plugin',
      '中国建设银行': 'ccb_format1_plugin',
      '交通银行': 'bocm_format1_plugin',
      '中国邮政储蓄银行': 'psbc_format1_plugin',
      '招商银行': 'cmb_format1_plugin',
      '上海浦东发展银行': 'spdb_format1_plugin',
      '中信银行': 'citic_format1_plugin',
      '中国光大银行': 'ceb_format1_plugin',
      '中国民生银行': 'cmbc_format1_plugin',
      '兴业银行': 'cib_format1_plugin',
      '平安银行': 'pingan_format1_plugin',
      '北部湾银行': 'beibuwan_format1_plugin',
      '农村信用社': 'rccu_plugin'
    };
    return bankParserMap[bankName] || 'universal_parser_plugin';
  };
  
  // 加载银行列表
  useEffect(() => {
    const loadBankList = async () => {
      try {
        setBankLoading(true);
        const response = await fetch(buildApiUrl('/api/banks/'));
        if (!response.ok) {
          throw new Error('获取银行列表失败');
        }
        const banks = await response.json();

        // 使用后端唯一 bank_id 作为前端 Option key，且按 bank_name 去重，避免滚动时重复渲染
        const seenNames = new Set();
        const options = [];
        for (const bank of banks) {
          if (!bank || !bank.bank_name) continue;
          if (seenNames.has(bank.bank_name)) continue;
          seenNames.add(bank.bank_name);
          options.push({
            key: bank.bank_id || bank.bank_code || bank.bank_name,
            value: bank.bank_name,
            label: bank.bank_name,
            icon: getBankIcon(bank.bank_name)
          });
        }

        // 添加"其他银行"选项
        options.push({ key: 'other', value: '其他银行', label: '其他银行', icon: '🏦' });

        setBankOptions(options);
        console.log('✅ 银行列表加载成功:', options);
      } catch (error) {
        console.error('❌ 加载银行列表失败:', error);
        message.error('加载银行列表失败，请刷新页面重试');
        
        // 使用备用数据
        setBankOptions([
          { key: 'icbc', value: '中国工商银行', label: '中国工商银行', icon: '🏦' },
          { key: 'abc', value: '中国农业银行', label: '中国农业银行', icon: '🌾' },
          { key: 'boc', value: '中国银行', label: '中国银行', icon: '🏛️' },
          { key: 'ccb', value: '中国建设银行', label: '中国建设银行', icon: '🏗️' },
          { key: 'bocom', value: '交通银行', label: '交通银行', icon: '🚗' },
          { key: 'psbc', value: '中国邮政储蓄银行', label: '中国邮政储蓄银行', icon: '📮' },
          { key: 'cmb', value: '招商银行', label: '招商银行', icon: '💼' },
          { key: 'spdb', value: '上海浦东发展银行', label: '上海浦东发展银行', icon: '🌅' },
          { key: 'citic', value: '中信银行', label: '中信银行', icon: '📞' },
          { key: 'ceb', value: '中国光大银行', label: '中国光大银行', icon: '☀️' },
          { key: 'cmbc', value: '中国民生银行', label: '中国民生银行', icon: '👥' },
          { key: 'cib', value: '兴业银行', label: '兴业银行', icon: '🌱' },
          { key: 'pingan', value: '平安银行', label: '平安银行', icon: '🛡️' },
          { key: 'beibuwan', value: '北部湾银行', label: '北部湾银行', icon: '🏖️' },
          { key: 'other', value: '其他银行', label: '其他银行', icon: '🏦' }
        ]);
      } finally {
        setBankLoading(false);
      }
    };
    
    loadBankList();
  }, []);

  // 格式化上传进度 - 修复精度问题
  const formatUploadProgress = (percent) => {
    return Math.floor(percent);
  };

  // 时间格式化函数
  const formatTransactionTime = (timeString) => {
    if (!timeString || timeString === '-' || timeString === '未知时间') {
      return '-';
    }
    
    if (/^\d{2}:\d{2}:\d{2}$/.test(timeString)) {
      return timeString;
    }
    
    if (/^\d{2}:\d{2}$/.test(timeString)) {
      return timeString + ':00';
    }
    
    if (/^\d{1,2}:\d{1,2}:\d{1,2}$/.test(timeString)) {
      const parts = timeString.split(':');
      return parts.map(part => part.padStart(2, '0')).join(':');
    }
    
    if (/^\d{1,2}:\d{1,2}$/.test(timeString)) {
      const parts = timeString.split(':');
      return parts.map(part => part.padStart(2, '0')).join(':') + ':00';
    }
    
    return timeString || '-';
  };

  // 加载可用模板
  const loadAvailableTemplates = useCallback(async () => {
    if (!selectedBank) return;
    
      setTemplateLoading(true);
      
    try {
      console.log('🔄 开始加载解析器模板，银行:', selectedBank);
      
      // 如果有文件，使用智能分析
      if (fileList.length > 0) {
        console.log('📁 检测到已上传文件，启动智能分析');
        await performSmartAnalysis(fileList[0].originFileObj, selectedBank);
      } else {
        // 没有文件时，获取基础模板列表
        const response = await fetch(buildApiUrl(API_ENDPOINTS.templates) + `?bank_name=${encodeURIComponent(selectedBank)}`);
      
      if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.detail || '获取模板失败');
      }
      
        const result = await response.json();
        console.log('📋 获取基础模板列表成功:', result);

        // 兼容两种返回结构：
        // 1) v2: 直接返回数组
        // 2) v1: 返回 { templates: [] }
        const templateArray = Array.isArray(result) ? result : (result?.templates || []);
        // 转换为前端期望的格式
        const templates = templateArray.map((template, index) => ({
          value: template.templateId,
          label: template.templateName,
          description: template.description,
          confidence: template.confidence || 0,
          isDefault: template.isRecommended || index === 0,
          isSmartAnalyzed: false
        }));
        
        setAvailableTemplates(templates);
      
        // 自动选择默认模板
        const defaultTemplate = templates.find(t => t.isDefault) || templates[0];
      if (defaultTemplate) {
          setSelectedTemplate(defaultTemplate.value);
        }
      }
      
    } catch (error) {
      console.error('❌ 加载模板失败:', error);
      message.error(`加载解析器模板失败: ${error.message}`);
      setAvailableTemplates([]);
    } finally {
      setTemplateLoading(false);
    }
  }, [selectedBank, fileList]);

  // 加载可用模板
  useEffect(() => {
    if (selectedBank) {
      loadAvailableTemplates();
    }
  }, [selectedBank, loadAvailableTemplates]);

  // 页面加载时立即测试API连接
  useEffect(() => {
    const testApiConnection = async () => {
      try {
        console.log('🔗 测试API连接...');
        const response = await fetch(buildApiUrl(API_ENDPOINTS.templates));
        if (response.ok) {
          const result = await response.json();
          const templateArray = Array.isArray(result) ? result : (result?.templates || []);
          console.log('✅ API连接成功，模板数量:', templateArray.length);
          console.log('📋 模板详情:', result);

          // 如果有模板但界面显示为0，直接更新
          if (templateArray.length > 0) {
            setAvailableTemplates(templateArray.map((template, index) => ({
              value: template.templateId,
              label: template.templateName,
              description: template.description,
              confidence: template.confidence || 0,
              isDefault: template.isRecommended || index === 0,
              isSmartAnalyzed: false
            })));
          }
        } else {
          console.error('❌ API连接失败:', response.statusText);
        }
      } catch (error) {
        console.error('❌ API测试异常:', error);
      }
    };
    
    testApiConnection();
  }, []);

  // 🔧 新增：获取4维度评分详情
  const fetch4DDetails = async (file) => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(buildApiUrl('/api/parser/evaluate-4d'), {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        const result = await response.json();
        return result.candidates || [];
      } else {
        console.error('4维度评估失败:', response.statusText);
        return [];
      }
    } catch (error) {
      console.error('4维度评估异常:', error);
      return [];
    }
  };

  // 修改智能分析函数，增加4维度评估
  const performSmartAnalysis = async (file, bankName) => {
    try {
      setSmartAnalysisLoading(true);

      // 🔧 关键修复：清理所有可能影响置信度评估的旧状态
      setParsingResult(null);           // 清空解析结果缓存
      setAccountTransactions([]);       // 清空交易详情缓存
      setSelectedAccount(null);         // 清空选中账户
      setShowTransactionDetails(false); // 隐藏交易详情

      console.log('🔍 开始智能分析:', file.name, '银行:', bankName);
      console.log('🧹 已清理旧的解析结果缓存，确保置信度评估准确性');



      const formData = new FormData();
      formData.append('file', file);
      formData.append('bank_name', bankName);

      const response = await fetch(buildApiUrl(API_ENDPOINTS.parser.preAnalyze), {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        const result = await response.json();
        console.log('智能分析响应:', result);

        if (result.success && result.candidates && result.candidates.length > 0) {
          console.log('智能分析结果:', result.candidates);

          // 构建模板列表，直接从候选解析器中获取4维度数据
          const templates = result.candidates.map((candidate, index) => {
            // 🔧 修复：优先使用confidence_evaluation.details；若缺失，回退到core_metrics或evaluation_breakdown，确保4维度可见
            const confidenceEval = candidate.confidence_evaluation || {};
            const backendDetails = confidenceEval.details
              || confidenceEval.core_metrics
              || confidenceEval.evaluation_breakdown
              || {};

            console.log(`解析器 ${candidate.parser_id} 的4维度原始数据:`, backendDetails);

            // 🔧 关键修复：将后端数据结构转换为前端期望的格式
            const getP = (obj, k) => (obj && obj[k] && (obj[k].percentage ?? obj[k].score ?? 0)) || 0;
            const fourDMetrics = {
              cardholder_name_score: {
                percentage: getP(backendDetails,'cardholder_recognition'),
                score: backendDetails.cardholder_recognition?.score || backendDetails.cardholder_recognition?.percentage || 0
              },
              time_format_score: {
                percentage: getP(backendDetails,'time_format_accuracy'),
                score: backendDetails.time_format_accuracy?.score || backendDetails.time_format_accuracy?.percentage || 0
              },
              account_number_score: {
                percentage: getP(backendDetails,'account_recognition'),
                score: backendDetails.account_recognition?.score || backendDetails.account_recognition?.percentage || 0
              },
              amount_parsing_score: {
                percentage: getP(backendDetails,'amount_parsing'),
                score: backendDetails.amount_parsing?.score || backendDetails.amount_parsing?.percentage || 0
              }
            };

            console.log(`解析器 ${candidate.parser_id} 转换后的4维度数据:`, fourDMetrics);

            // 简化模板名称（去掉冗长描述，仅保留逗号/顿号/空格之前的短名）
            const rawLabel = candidate.parser_info?.templateName || `${candidate.parser_id}解析器`;
            const shortLabel = (rawLabel || '').split(/[，,。\s]/)[0] || rawLabel;

            return {
              value: candidate.parser_id,
              label: shortLabel,
              description: candidate.parser_info?.description || `${candidate.parser_id}解析器插件`,
              confidence: confidenceEval.confidence || 0,
              match_reason: confidenceEval.match_reason || '',
              isSmartAnalyzed: true,
              // 🔧 修复：使用转换后的4维度评分数据
              fourDMetrics: fourDMetrics,
              totalScore: confidenceEval.confidence || 0  // 使用置信度作为总分
            };
          });

          // 如果缺少4维度详情，尝试调用备用接口补全
          const need4D = templates.some(t => !t.fourDMetrics || Object.keys(t.fourDMetrics).length === 0);
          let finalTemplates = templates;
          if (need4D && fileList.length > 0) {
            try {
              const details = await fetch4DDetails(fileList[0].originFileObj);
              if (Array.isArray(details) && details.length > 0) {
                const detailsMap = {};
                details.forEach(d => { detailsMap[d.parser_id] = d.confidence_evaluation?.details || {}; });
                finalTemplates = templates.map(t => {
                  const backupMetrics = detailsMap[t.value] || {};
                  const convertedBackupMetrics = {
                    cardholder_name_score: {
                      percentage: backupMetrics.cardholder_recognition?.percentage || 0,
                      score: backupMetrics.cardholder_recognition?.score || 0
                    },
                    time_format_score: {
                      percentage: backupMetrics.time_format_accuracy?.percentage || 0,
                      score: backupMetrics.time_format_accuracy?.score || 0
                    },
                    account_number_score: {
                      percentage: backupMetrics.account_recognition?.percentage || 0,
                      score: backupMetrics.account_recognition?.score || 0
                    },
                    amount_parsing_score: {
                      percentage: backupMetrics.amount_parsing?.percentage || 0,
                      score: backupMetrics.amount_parsing?.score || 0
                    }
                  };

                  return {
                    ...t,
                    fourDMetrics: (t.fourDMetrics && Object.keys(t.fourDMetrics).length>0) ? t.fourDMetrics : convertedBackupMetrics
                  };
                });
              }
            } catch (e) {
              console.warn('获取4维度详情失败，继续使用原结果');
            }
          }

          // 前端兜底：如果用户选择了银行且模板的银行不匹配或置信度<5分，则隐藏此候选
          const normalized = (s) => {
            if (!s) return '';
            const raw = String(s).toLowerCase();
            const map = { '中国银行':'boc','中行':'boc','boc':'boc','交通银行':'bocm','交行':'bocm','bocm':'bocm' };
            for (const k in map) { if (raw.includes(k)) return map[k]; }
            return raw.replace(/中国|股份有限公司|股份|有限公司|银行|\s/g,'');
          };
          const targetCode = normalized(bankName);
          finalTemplates = finalTemplates.filter(t => {
            const bankInLabel = normalized(t.label);
            const bankInDesc  = normalized(t.description);
            const bankCode = bankInLabel || bankInDesc;
            const isUniversal = ['universal','通用'].some(x => t.label?.toLowerCase().includes(x) || t.description?.toLowerCase().includes(x));
            if (targetCode) {
              if (isUniversal) return false; // 已选银行时不显示通用
              if (bankCode && bankCode !== targetCode) return false; // 严格匹配
              if ((t.confidence ?? 0) < 5) return false; // 极低分直接隐藏
            }
            return true;
          });

          setAvailableTemplates(finalTemplates);
          setSmartAnalysisResult(`智能分析完成！找到${finalTemplates.length}个解析器，推荐使用"${finalTemplates[0]?.label || ''}"（置信度: ${finalTemplates[0]?.confidence || 0}%）`);
          if (finalTemplates[0]) setSelectedTemplate(finalTemplates[0].value);

        } else {
          setSmartAnalysisResult('智能分析未找到匹配的解析器，正在加载基础模板...');
          
          // 🔧 修复：智能分析无结果时，fallback到基础模板
          try {
            console.log('🔄 智能分析无结果，fallback到基础模板加载');
            const basicTemplates = await loadBasicTemplatesData();
            if (basicTemplates.length > 0) {
              setAvailableTemplates(basicTemplates);
              setSelectedTemplate(basicTemplates[0].value);
              setSmartAnalysisResult(`智能分析无结果，已加载${basicTemplates.length}个基础模板。`);
            } else {
              setSmartAnalysisResult('智能分析无结果，且未找到基础模板。');
            }
          } catch (fallbackError) {
            console.error('❌ 基础模板fallback失败:', fallbackError);
            setSmartAnalysisResult('智能分析无结果，基础模板加载也失败。');
          }
        }
      } else {
        throw new Error(`智能分析请求失败: ${response.statusText}`);
      }
    } catch (error) {
      console.error('智能分析失败:', error);
      setSmartAnalysisResult('智能分析失败，正在加载基础模板列表...');
      
      // 🔧 修复：智能分析失败时，fallback到基础模板
      try {
        console.log('🔄 智能分析失败，fallback到基础模板加载');
        const basicTemplates = await loadBasicTemplatesData();
        if (basicTemplates.length > 0) {
          setAvailableTemplates(basicTemplates);
          setSelectedTemplate(basicTemplates[0].value);
          setSmartAnalysisResult(`智能分析失败，已加载${basicTemplates.length}个基础模板。`);
        } else {
          setSmartAnalysisResult('智能分析失败，且未找到基础模板。');
        }
      } catch (fallbackError) {
        console.error('❌ 基础模板fallback也失败:', fallbackError);
        setSmartAnalysisResult('智能分析和基础模板加载均失败，请手动重试。');
      }
    } finally {
      setSmartAnalysisLoading(false);
    }
  };

  // 加载基础模板数据（不设置状态，仅返回数据）
  const loadBasicTemplatesData = async () => {
    try {
      console.log('📋 获取基础模板数据，银行:', selectedBank);
      const response = await fetch(buildApiUrl(API_ENDPOINTS.templates) + `?bank_name=${encodeURIComponent(selectedBank)}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '获取模板失败');
      }
      
      const result = await response.json();
      console.log('📋 获取基础模板数据成功:', result);
      
      // 🔧 修复：检查返回数据结构，安全访问templates
      const templates = (result.templates || result || []).map((template, index) => ({
        value: template.templateId,
        label: template.templateName,
        description: template.description,
        confidence: template.confidence || 75, // 给基础模板默认置信度
        isDefault: template.isRecommended || index === 0,
        isSmartAnalyzed: false
      }));
      
      console.log('🔧 转换后的模板数据:', templates);
      return templates;
    } catch (error) {
      console.error('❌ 获取基础模板数据失败:', error);
      return [];
    }
  };
  
  // 加载基础模板列表（设置状态）
  const loadBasicTemplates = async () => {
    try {
      console.log('📋 加载基础模板列表，银行:', selectedBank);
      const templates = await loadBasicTemplatesData();
      
      setAvailableTemplates(templates);
      
      // 自动选择默认模板
      const defaultTemplate = templates.find(t => t.isDefault) || templates[0];
      if (defaultTemplate) {
        setSelectedTemplate(defaultTemplate.value);
      }
    } catch (error) {
      console.error('❌ 加载基础模板失败:', error);
      setAvailableTemplates([]);
    }
  };

  // 文件类型验证
  const validateFileType = (file) => {
    const allowedTypes = {
      'excel': ['.xls', '.xlsx', '.csv'],
      'pdf': ['.pdf'],
      'image': ['.jpg', '.jpeg', '.png', '.bmp']
    };
    
    const fileName = file.name.toLowerCase();
    const currentAllowedTypes = allowedTypes[fileType];
    
    const isValidType = currentAllowedTypes.some(type => fileName.endsWith(type));
    
    if (!isValidType) {
      message.error(`请上传 ${currentAllowedTypes.join(', ')} 格式的文件`);
      return false;
    }
    
    const isValidSize = file.size / 1024 / 1024 < 50;
    if (!isValidSize) {
      message.error('文件大小不能超过50MB');
      return false;
    }
    
    return true;
  };
  
  // 处理文件上传
  const handleUpload = ({ fileList: newFileList }) => {
    const validFiles = newFileList.filter(file => {
      if (file.originFileObj) {
        return validateFileType(file.originFileObj);
      }
      return true;
    });
    
    setFileList(validFiles);
    
    // 如果已经选择了银行且有有效文件，自动重新分析模板
    if (validFiles.length > 0 && selectedBank) {
      console.log('📄 文件上传完成，开始文件预分析...');
      message.info('正在分析文件，推荐最佳解析器...');
      
      // 延迟一下让界面更新
      setTimeout(() => {
        loadAvailableTemplates();
      }, 500);
    }
  };
  
  // 处理下一步操作
  const handleNext = () => {
    if (currentStep === 0) {
      if (fileList.length === 0) {
        message.error('请先上传文件');
        return;
      }
      setCurrentStep(1);
    } else if (currentStep === 1) {
      if (!selectedBank) {
        message.error('请选择银行');
        return;
      }
      setCurrentStep(2);
    } else if (currentStep === 2) {
      if (!selectedTemplate) {
        message.error('请选择解析模板');
        return;
      }
      startParsing();
    }
  };
  
  // 处理上一步操作
  const handlePrev = () => {
    setCurrentStep(currentStep - 1);
  };

  // 重新开始
  const handleRestart = () => {
    setFileList([]);
    setSelectedBank(null);
    setSelectedTemplate(null);
    setFileType('excel');
    setParseStatus('idle');
    setParsingResult(null);
    setParseProgress(0);
    setUploadProgress(0);
    setCurrentStep(0);
  };
  
  // 开始解析文件
  const startParsing = async () => {
    setParseStatus('loading');
    setCurrentStep(3);
    setParseProgress(0);
    setUploadProgress(0);
    
    try {
      // 模拟上传进度
      const uploadInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 10;
          if (newProgress >= 100) {
            clearInterval(uploadInterval);
            return 100;
          }
          return newProgress;
        });
      }, 200);
      
      // 模拟解析进度
      const progressInterval = setInterval(() => {
        setParseProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + Math.random() * 20;
        });
      }, 500);
      
      // 构建表单数据
      const formData = new FormData();
      formData.append('bank_name', selectedBank);
      
      if (selectedTemplate) {
        formData.append('template_id', selectedTemplate);
      }
      
      // 添加上传的文件
      if (fileList.length > 0) {
        formData.append('file', fileList[0].originFileObj);
      }
      
      // 调用API（这里需要调用支持文件解析的API）
      const response = await fetch(buildApiUrl(API_ENDPOINTS.parser.analyze), {
        method: 'POST',
        body: formData
      });
      
      clearInterval(progressInterval);
      clearInterval(uploadInterval);
      setParseProgress(100);
      setUploadProgress(100);
      
      if (!response.ok) {
        throw new Error(`API调用失败: ${response.status} ${response.statusText}`);
      }
      
      const result = await response.json();
      
      // 处理解析结果
      if (result.success && result.parse_result) {
        // --- 数据增强逻辑 START ---
        const enhancedData = { ...result.parse_result };
        if (enhancedData.accounts && enhancedData.transactions) {
          console.log('🔧 前端数据增强：开始重新计算账户统计信息');
          enhancedData.accounts = enhancedData.accounts.map(account => {
            // 🔧 修复：以账号+卡号组合键匹配（卡号为空或“-”时不参与过滤）
            const accountTransactionsByBoth = enhancedData.transactions.filter(t => {
              const accountMatch = (t.account_number || t.账号) === account.account_number;
              const txCardRaw = (t.card_number || t.卡号 || '').trim();
              const accCardRaw = (account.card_number || '').trim();
              const txCard = (txCardRaw === '-' ? '' : txCardRaw);
              const accCard = (accCardRaw === '-' ? '' : accCardRaw);
              const cardMatch = accCard ? (txCard === accCard) : true;
              return accountMatch && cardMatch;
            });

            console.log(`📊 账户 ${account.account_number} | 卡号 ${account.card_number}: 匹配到 ${accountTransactionsByBoth.length} 条交易`);

            // 默认优先使用后端给出的统计值；若缺失则本地回退计算
            // 强制以“账号+卡号”本地重算，覆盖后端的错误统计
            const transactions_count = accountTransactionsByBoth.length;

            const total_income = accountTransactionsByBoth
              .filter(t => {
                const flag = (t.dr_cr_flag || t.direction || '').toString().trim().toUpperCase();
                if (['收', 'D', '借', 'IN'].some(k => flag.includes(k))) return true;
                if (['支', 'C', '贷', 'OUT'].some(k => flag.includes(k))) return false;
                return ((parseFloat(t.transaction_amount) || 0) > 0);
              })
              .reduce((sum, t) => sum + Math.abs(parseFloat(t.transaction_amount) || 0), 0);

            const total_expense = accountTransactionsByBoth
              .filter(t => {
                const flag = (t.dr_cr_flag || t.direction || '').toString().trim().toUpperCase();
                if (['收', 'D', '借', 'IN'].some(k => flag.includes(k))) return false;
                if (['支', 'C', '贷', 'OUT'].some(k => flag.includes(k))) return true;
                return ((parseFloat(t.transaction_amount) || 0) < 0);
              })
              .reduce((sum, t) => sum + Math.abs(parseFloat(t.transaction_amount) || 0), 0);

            return {
              ...account,
              transactions_count,
              total_income,
              total_expense,
            };
          });
          console.log('✅ 前端数据增强完成');
        }
        // --- 数据增强逻辑 END ---
        
        setParsingResult(enhancedData);
        setParseStatus('success');
        message.success('文件解析成功！');
      } else {
        throw new Error(result.message || '解析失败');
      }
      
    } catch (error) {
      console.error('解析文件失败:', error);
      setParseStatus('error');
      message.error(`解析失败: ${error.message}`);
    }
  };

  // 检查重复数据
  const checkDuplicateAccounts = async (accounts) => {
    try {
      console.log('🔍 开始检查重复账户数据...');
      console.log('📊 要检查的账户数据:', accounts);
      
      // 🔧 修复：确保正确映射账户数据，使用正确的字段名
      const accountsToCheck = accounts.map(acc => {
        const mappedAccount = {
          holder_name: acc.holder_name || acc.account_name || acc.name,
          account_number: acc.account_number || acc.card_number,
          card_number: acc.card_number || acc.account_number
        };
        console.log('📋 映射账户:', acc, '=> ', mappedAccount);
        return mappedAccount;
      });
      
      console.log('📤 发送到后端的账户数据:', accountsToCheck);
      
      const response = await fetch(buildApiUrl(API_ENDPOINTS.parser.checkDuplicates), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          project_id: projectId,  // 🔧 修复：添加缺失的project_id
          accounts: accountsToCheck
        })
      });
      
      if (!response.ok) {
        console.warn('⚠️ 重复检查API失败，继续保存流程');
        console.warn('❌ API错误:', response.status, response.statusText);
        try {
          const errorData = await response.json();
          console.warn('❌ 错误详情:', errorData);
        } catch (e) {
          console.warn('❌ 无法解析错误响应');
        }
        return { duplicates: [], unique: accounts };
      }
      
      const result = await response.json();
      console.log('📊 重复检查结果:', result);
      
      return result;
    } catch (error) {
      console.warn('⚠️ 重复检查失败，继续保存流程:', error);
      return { duplicates: [], unique: accounts };
    }
  };

  // 保存解析结果
  const saveParsingResult = async () => {
    console.log('💾 [重要] saveParsingResult函数被调用 - 开始保存到真实数据库！');
    console.log('🔄 当前状态检查:');
    console.log('📊 parsingResult:', parsingResult);
    console.log('🆔 projectId:', projectId);
    console.log('📦 Modal可用性:', typeof Modal);
    
    if (!projectId) {
      message.error('项目ID不能为空，请确保已正确选择项目');
      return;
    }
    
    if (!parsingResult) {
      message.error('没有可保存的解析结果');
      return;
    }
    
    // 先检查重复数据
    console.log('🔍 开始检查重复数据...');
    const duplicateCheck = await checkDuplicateAccounts(parsingResult.accounts || []);
    console.log('📋 重复检查结果:', duplicateCheck);
    console.log('🔢 重复账户数量:', duplicateCheck.duplicates?.length);
    console.log('🔢 唯一账户数量:', duplicateCheck.unique?.length);
    
    if (duplicateCheck.duplicates && duplicateCheck.duplicates.length > 0) {
      console.log('⚠️ 发现重复账户:', duplicateCheck.duplicates.length, '个');
      console.log('🪟 准备显示重复数据处理对话框');
      
      // 🔧 修复：使用标准Modal而不是Modal.confirm，确保按钮可以点击
      const modal = Modal.info({
        title: '发现重复账户数据',
        width: 900,
        className: 'duplicate-choice-modal',
        content: (
          <div>
            <Alert
              message="检测到重复数据"
              description={`在数据库中发现 ${duplicateCheck.duplicates.length} 个重复账户（相同持卡人+账户号），请选择处理方式：`}
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            <div style={{ backgroundColor: '#fff7e6', padding: '12px', borderRadius: '6px', border: '1px solid #ffd591', marginBottom: '16px' }}>
              <p><strong>📋 重复账户列表：</strong></p>
              <ul style={{ maxHeight: '200px', overflowY: 'auto' }}>
                {duplicateCheck.duplicates.map((dup, index) => (
                  <li key={index} style={{ marginBottom: '8px' }}>
                    <strong>{dup.holder_name}</strong> - {dup.account_number} ({dup.card_number})
                  </li>
                ))}
              </ul>
            </div>
            
            <div style={{ backgroundColor: '#f6ffed', padding: '12px', borderRadius: '6px', border: '1px solid #b7eb8f' }}>
              <p><strong>📊 数据统计：</strong></p>
              <ul style={{ marginBottom: 0 }}>
                <li>总账户数：{parsingResult.accounts.length}</li>
                <li>重复账户：{duplicateCheck.duplicates.length}</li>
                <li>可上传账户：{duplicateCheck.unique.length}</li>
                <li>总交易数：{parsingResult.transactions.length}</li>
              </ul>
            </div>
            
            <div style={{ marginTop: '16px', padding: '12px', backgroundColor: '#f0f9ff', border: '1px solid #91d5ff', borderRadius: '6px' }}>
              <p style={{ marginBottom: '16px', fontWeight: 'bold', fontSize: '16px' }}>💡 请选择以下处理方式之一：</p>
              
              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                {/* 选项1: 跳过重复数据 */}
                <Button 
                  type={duplicateCheck.unique.length > 0 ? "primary" : "default"}
                  disabled={duplicateCheck.unique.length === 0}
                  size="large"
                  style={{ 
                    height: '60px',
                    fontSize: '18px',
                    fontWeight: 'bold',
                    backgroundColor: duplicateCheck.unique.length > 0 ? '#52c41a' : '#f5f5f5', 
                    borderColor: duplicateCheck.unique.length > 0 ? '#52c41a' : '#d9d9d9',
                    color: duplicateCheck.unique.length > 0 ? 'white' : '#999'
                  }}
                  onClick={async () => {
                    console.log('🔘 [用户操作] 选项1按钮被点击 - 跳过重复数据');
                    console.log('📊 唯一账户数量:', duplicateCheck.unique.length);
                    
                    modal.destroy(); // 关闭当前模态框
          console.log('✅ 用户选择跳过重复数据，上传剩余账户');
          
          if (duplicateCheck.unique.length === 0) {
                      Modal.info({
                        title: '无新数据可上传',
                        content: (
                          <div>
                            <p>所有账户都是重复数据，没有新账户可以上传。</p>
                            <p><strong>建议操作：</strong></p>
                            <ul>
                              <li>如需更新现有数据，请选择"覆盖原账户"</li>
                              <li>如需处理其他文件，请返回重新上传</li>
                            </ul>
                          </div>
                        ),
                        onOk: () => {
                          console.log('用户确认无新数据可上传');
                        }
                      });
            return;
          }
          
          // 过滤出唯一账户的交易记录
          const uniqueAccountNumbers = duplicateCheck.unique.map(acc => acc.account_number);
          const uniqueTransactions = parsingResult.transactions.filter(trans => 
            uniqueAccountNumbers.includes(trans.account_number)
          );
                    
                    console.log(`📊 准备保存：${duplicateCheck.unique.length}个账户，${uniqueTransactions.length}条交易`);
          
          // 使用过滤后的数据继续保存流程
          await performSave({
            accounts: duplicateCheck.unique,
            transactions: uniqueTransactions
                    }, { skip_duplicates: true });
                  }}
                >
                  选项1: 跳过重复数据 - 只上传{duplicateCheck.unique.length}个新账户
                  {duplicateCheck.unique.length === 0 && ' (无可上传账户)'}
                </Button>
                
                {/* 选项2: 覆盖原账户 */}
                <Button 
                  type="primary" 
                  danger
                  size="large"
                  style={{ 
                    height: '60px',
                    fontSize: '18px',
                    fontWeight: 'bold'
                  }}
                  onClick={() => {
                    console.log('🔘 [用户操作] 选项2按钮被点击 - 覆盖原账户');
                    console.log('📊 重复账户数量:', duplicateCheck.duplicates.length);
                    
                    modal.destroy(); // 关闭当前模态框
                    
                    // 显示覆盖确认对话框
                    Modal.confirm({
                      title: '确认覆盖原账户数据？',
                      width: 600,
                      content: (
                        <div>
                          <Alert
                            message="危险操作警告"
                            description="此操作将永久删除数据库中现有的重复账户数据，并用新上传的数据完全替换。此操作不可撤销！"
                            type="error"
                            showIcon
                            style={{ marginBottom: 16 }}
                          />
                          <div style={{ backgroundColor: '#fff2f0', padding: '12px', borderRadius: '6px', border: '1px solid #ffccc7' }}>
                            <p><strong>⚠️ 将被覆盖的数据：</strong></p>
                            <ul>
                              <li>重复账户：{duplicateCheck.duplicates.length} 个</li>
                              <li>相关交易记录：{parsingResult.transactions.length} 条</li>
                              <li>数据将被永久删除，无法恢复</li>
                            </ul>
                          </div>
                        </div>
                      ),
                      okText: '确认覆盖（不可撤销）',
                      okType: 'danger',
                      cancelText: '取消',
                      onOk: async () => {
                        console.log('🔄 [用户确认] 选择覆盖原账户');
                        console.log('📊 准备覆盖：', parsingResult.accounts.length, '个账户，', parsingResult.transactions.length, '条交易');
                        
                        // 使用强制覆盖模式保存全部数据
                        await performSave({
                          accounts: parsingResult.accounts || [],
                          transactions: parsingResult.transactions || []
                        }, { force_overwrite: true });
                      }
                    });
                  }}
                >
                  选项2: 覆盖原账户 - 替换{duplicateCheck.duplicates.length}个重复账户
                </Button>
                
                {/* 选项3: 暂不上传 */}
                <Button 
                  size="large"
                  style={{ 
                    height: '60px',
                    fontSize: '18px',
                    fontWeight: 'bold'
                  }}
                  onClick={() => {
                    console.log('🔘 [用户操作] 选项3按钮被点击 - 暂不上传');
                    
                    modal.destroy(); // 关闭当前模态框
                    console.log('🛑 用户选择暂不上传');
                    message.info('已取消上传，请处理重复数据后重试');
                  }}
                >
                  选项3: 暂不上传 - 取消本次导入
                </Button>
              </div>
              
              <div style={{ marginTop: '16px', fontSize: '14px', color: '#666', textAlign: 'center' }}>
                💡 建议：如果是第一次导入，选择"跳过重复数据"；如果需要更新数据，选择"覆盖原账户"
              </div>
            </div>
          </div>
        ),
        okText: '关闭',
        onOk: () => {
          console.log('🛑 用户关闭了重复数据处理对话框');
        }
      });
      
      console.log('🪟 重复数据处理对话框已显示，等待用户选择');
      return; // 等待用户选择
    }
    
    console.log('✅ 无重复数据，直接保存');
    // 没有重复数据，直接保存
    await performSave({
      accounts: parsingResult.accounts || [],
      transactions: parsingResult.transactions || []
    });
  };
  
  // 下载通用模板
  const downloadUniversalTemplate = async () => {
    try {
      const response = await fetch(buildApiUrl('/api/templates/universal/download'), {
        method: 'GET'
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = '通用解析器模板.xlsx';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        message.success('通用模板下载成功，请按照模板格式整理您的数据');
      } else {
        throw new Error('下载失败');
      }
    } catch (error) {
      console.error('下载通用模板失败:', error);
      message.error('下载通用模板失败，请稍后重试');
    }
  };
  
  // 显示通用模板使用说明
  const showUniversalTemplateInfo = async () => {
    try {
      const response = await fetch(buildApiUrl('/api/templates/universal/info'), {
        method: 'GET'
      });
      
      if (response.ok) {
        const data = await response.json();
        const { templateInfo, instructions } = data;
        
        Modal.info({
          title: '通用解析器使用说明',
          width: 800,
          content: (
            <div style={{ fontSize: '14px' }}>
              <Alert
                message={instructions.description}
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
              
              <h4>使用步骤：</h4>
              <ol>
                {instructions.steps.map((step, index) => (
                  <li key={index} style={{ marginBottom: 8 }}>{step}</li>
                ))}
              </ol>
              
              <h4>注意事项：</h4>
              <ul>
                {instructions.notes.map((note, index) => (
                  <li key={index} style={{ marginBottom: 8 }}>{note}</li>
                ))}
              </ul>
              
              <h4>标准字段说明（A-R列）：</h4>
              <Row gutter={16}>
                {templateInfo.standardFields.map((field, index) => (
                  <Col span={8} key={index} style={{ marginBottom: 8 }}>
                    <Text strong>{field.column}列: </Text>
                    <Text>{field.name}</Text>
                    {field.required && <Tag color="red" size="small" style={{ marginLeft: 4 }}>必填</Tag>}
                  </Col>
                ))}
              </Row>
            </div>
          ),
          onOk() {
            // 用户可以选择下载模板
            Modal.confirm({
              title: '下载模板',
              content: '是否立即下载通用解析器模板？',
              onOk() {
                downloadUniversalTemplate();
              }
            });
          }
        });
      } else {
        throw new Error('获取说明失败');
      }
    } catch (error) {
      console.error('获取通用模板信息失败:', error);
      message.error('获取使用说明失败，请稍后重试');
    }
  };

  // 执行实际保存操作
  const performSave = async (dataToSave, options = {}) => {
      console.log('📡 [关键] 准备发送保存请求到真实数据库...');
      
      // 开始保存进度
      setIsSaving(true);
      setSaveProgress(0);
      
    try {
      // 模拟保存进度
      const progressInterval = setInterval(() => {
        setSaveProgress(prev => {
          if (prev >= 90) {
            return prev;
          }
          return prev + Math.random() * 15;
        });
      }, 300);
      
      // 🔧 修复：数据格式转换 - 将解析API格式转换为保存API格式
      console.log('🔄 [数据转换] 开始转换数据格式...');
      console.log('📊 [原始数据] 账户数量:', dataToSave.accounts?.length || 0);
      console.log('📊 [原始数据] 交易数量:', dataToSave.transactions?.length || 0);
      
      // 转换账户数据格式
      const convertedAccounts = (dataToSave.accounts || []).map(account => {
        const convertedAccount = {
          // 保存API期望的字段名
          holder_name: account.cardholder_name || account.cardholder || account.holder_name || '',
          account_number: account.account_number || '',
          bank_name: account.bank_name || selectedBank || '中国工商银行',
          card_number: account.card_number || '',
          account_type: account.account_type || '个人账户',
          currency: account.currency || '人民币'
        };
        
        console.log('🔄 [账户转换]', {
          原始: { cardholder: account.cardholder, cardholder_name: account.cardholder_name },
          转换后: { holder_name: convertedAccount.holder_name }
        });
        
        return convertedAccount;
      });
      
      // 转换交易数据格式
      const convertedTransactions = (dataToSave.transactions || []).map(transaction => {
        // 合并日期和时间
        const transaction_datetime = transaction.transaction_date && transaction.transaction_time 
          ? `${transaction.transaction_date} ${transaction.transaction_time}`
          : transaction.transaction_date || transaction.transaction_datetime || '';
        
        const convertedTransaction = {
          // 保存API期望的字段名
          holder_name: transaction.cardholder_name || transaction.cardholder || transaction.holder_name || '',
          account_number: transaction.account_number || '',
          // 关键：用于与账户建立映射的三元组之一
          card_number: transaction.card_number || transaction.cardNo || '',
          transaction_datetime: transaction_datetime,
          transaction_amount: transaction.amount || transaction.transaction_amount || 0,
          balance: transaction.balance || transaction.balance_amount || 0,
          dr_cr_flag: transaction.dr_cr_flag || '',
          transaction_method: transaction.transaction_method || '',
          counterparty_account_number: transaction.counterparty_account || transaction.counterparty_account_number || '',
          counterparty_account_name: transaction.counterparty || transaction.counterparty_name || transaction.counterparty_account_name || '',
          counterparty_bank_name: transaction.counterparty_bank || transaction.counterparty_bank_name || '',
          remarks: transaction.description || transaction.remarks || transaction.remark1 || ''
        };
        
        return convertedTransaction;
      });
      
      console.log('✅ [数据转换完成]');
      console.log('📊 [转换后] 账户数量:', convertedAccounts.length);
      console.log('📊 [转换后] 交易数量:', convertedTransactions.length);
      console.log('🔍 [样本检查] 第一个账户:', convertedAccounts[0]);
      console.log('🔍 [样本检查] 第一条交易:', convertedTransactions[0]);
      
      const saveData = {
        project_id: projectId,
        accounts: convertedAccounts,
        transactions: convertedTransactions,
        metadata: {
          bank_name: selectedBank,
          template_id: selectedTemplate,
          file_name: fileList[0]?.name || 'unknown',
          total_accounts: convertedAccounts.length,
          total_transactions: convertedTransactions.length
        },
        // 添加保存选项
        force_overwrite: options.force_overwrite || false,
        skip_duplicates: !options.force_overwrite
      };
      
      console.log('📤 [调试] 发送的保存数据:', {
        project_id: saveData.project_id,
        accounts_count: saveData.accounts.length,
        transactions_count: saveData.transactions.length,
        bank_name: saveData.metadata.bank_name,
        template_id: saveData.metadata.template_id,
        file_name: saveData.metadata.file_name,
        force_overwrite: saveData.force_overwrite,
        skip_duplicates: saveData.skip_duplicates
      });
      
      setSaveProgress(50); // 请求发送阶段
      
      const response = await fetch(buildApiUrl(API_ENDPOINTS.parser.save), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(saveData)
      });
      
      setSaveProgress(80); // 响应接收阶段
      
      console.log('📨 [状态] 保存API响应状态:', response.status, response.statusText);
      
      if (!response.ok) {
        clearInterval(progressInterval);
        const errorData = await response.json();
        console.error('❌ [错误] 保存API错误响应:', errorData);
        throw new Error(errorData.detail || `保存失败: ${response.status}`);
      }
      
      const result = await response.json();
      clearInterval(progressInterval);
      setSaveProgress(100); // 完成
      
      console.log('✅ [成功] 数据库保存成功响应:', result);
      
      // 检查是否有重复数据信息
      const hasSkipped = result.skipped_accounts > 0 || result.skipped_transactions > 0;
      const hasOverwritten = result.overwritten_accounts > 0 || result.overwritten_transactions > 0;
      
      // 显示详细的保存结果
      let successTitle = '🎉 数据保存成功！';
      let successContent = '';
      
      if (options.force_overwrite && hasOverwritten) {
        successTitle = '🔄 数据覆盖更新成功！';
        successContent = (
          <div>
            <p style={{ fontSize: '16px', marginBottom: '16px' }}>
              已覆盖更新现有数据：
            </p>
            <div style={{ backgroundColor: '#f6ffed', padding: '12px', borderRadius: '6px', border: '1px solid #b7eb8f' }}>
              <p><strong>📊 覆盖统计：</strong></p>
              <ul style={{ marginBottom: 0 }}>
                <li>覆盖账户：<strong style={{ color: '#52c41a' }}>{result.overwritten_accounts || 0}</strong></li>
                <li>新增账户：<strong style={{ color: '#52c41a' }}>{result.saved_accounts || 0}</strong></li>
                <li>交易记录：<strong style={{ color: '#52c41a' }}>{result.saved_transactions || 0}</strong></li>
                <li>总账户数：<strong style={{ color: '#1890ff' }}>{(result.overwritten_accounts || 0) + (result.saved_accounts || 0)}</strong></li>
              </ul>
            </div>
          </div>
        );
      } else if (hasSkipped) {
        successTitle = '⚠️ 部分数据已存在！';
        successContent = (
          <div>
            <p style={{ fontSize: '16px', marginBottom: '16px' }}>
              检测到重复数据，系统已自动跳过：
            </p>
            <div style={{ backgroundColor: '#fff7e6', padding: '12px', borderRadius: '6px', border: '1px solid #ffd591', marginBottom: '16px' }}>
              <p><strong>📊 保存统计：</strong></p>
              <ul style={{ marginBottom: 8 }}>
                <li>新保存账户：<strong style={{ color: '#52c41a' }}>{result.saved_accounts || 0}</strong></li>
                <li>新保存交易：<strong style={{ color: '#52c41a' }}>{result.saved_transactions || 0}</strong></li>
                <li>跳过重复账户：<strong style={{ color: '#fa8c16' }}>{result.skipped_accounts || 0}</strong></li>
                <li>跳过重复交易：<strong style={{ color: '#fa8c16' }}>{result.skipped_transactions || 0}</strong></li>
              </ul>
            </div>
            <p style={{ color: '#666', fontSize: '14px' }}>
              💡 如需覆盖现有数据，请选择"覆盖原账户"选项。
            </p>
          </div>
        );
      } else {
        successContent = (
          <div>
            <p style={{ fontSize: '16px', marginBottom: '16px' }}>
              银行流水数据已成功保存到数据库！
            </p>
            <div style={{ backgroundColor: '#f6ffed', padding: '12px', borderRadius: '6px', border: '1px solid #b7eb8f' }}>
              <p><strong>📊 保存统计：</strong></p>
              <ul style={{ marginBottom: 0 }}>
                <li>新增账户：<strong style={{ color: '#52c41a' }}>{result.saved_accounts || 0}</strong></li>
                <li>交易记录：<strong style={{ color: '#52c41a' }}>{result.saved_transactions || 0}</strong></li>
                <li>项目ID：<code>{projectId}</code></li>
                <li>银行：{selectedBank}</li>
                <li>解析模板：{availableTemplates.find(t => t.value === selectedTemplate)?.label || '未知'}</li>
              </ul>
            </div>
          </div>
        );
      }
      
      // 立即显示成功提示
      message.success({
        content: options.force_overwrite ? 
          `🔄 数据覆盖成功！覆盖 ${result.overwritten_accounts || 0} 个账户，新增 ${result.saved_accounts || 0} 个账户，总计 ${(result.overwritten_accounts || 0) + (result.saved_accounts || 0)} 个账户` :
          hasSkipped ? 
          `⚠️ 跳过${result.skipped_accounts}个重复账户，保存${result.saved_accounts}个新账户` :
          `🎉 数据保存成功！已保存 ${result.saved_accounts || 0} 个账户和 ${result.saved_transactions || 0} 条交易记录`,
        duration: 6,
        style: {
          marginTop: '20vh',
          fontSize: '16px'
        }
      });
      
      // 延迟显示详细确认对话框
      setTimeout(() => {
        Modal.success({
          title: successTitle,
          content: successContent,
          width: 600,
        okText: '继续导入',
        cancelText: '查看数据',
          onOk: () => {
            console.log('🔄 用户选择继续导入');
            handleRestart();
          },
        onCancel: () => {
            console.log('👀 用户选择查看数据');
            navigate(`/projects/${projectId}`);
        }
      });
      }, 1500);
      
    } catch (error) {
      console.error('❌ [严重错误] 保存失败:', error);
      message.error({
        content: `保存失败: ${error.message}`,
        duration: 10,
        style: {
          marginTop: '20vh',
          fontSize: '16px'
        }
      });
    } finally {
      // 重置保存状态
      setTimeout(() => {
        setIsSaving(false);
        setSaveProgress(0);
      }, 2000);
    }
  };
  
  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return renderFileUploadStep();
      case 1:
        return renderBankSelectionStep();
      case 2:
        return renderTemplateSelectionStep();
      case 3:
        return renderParsingStep();
      default:
        return null;
    }
  };
  
  // 渲染文件上传步骤
  const renderFileUploadStep = () => {
    return (
      <div>
        <Alert
          message="文件上传指南"
          description="请选择文件类型并上传银行流水文件。支持Excel、PDF和图片格式。建议使用Excel格式以获得最佳解析效果。"
          type="info"
          showIcon
          style={{ marginBottom: 16, fontSize: '18px' }}
        />
        
        <Card title="选择文件类型" style={{ marginBottom: 16, fontSize: '18px' }}>
          <Radio.Group value={fileType} onChange={e => setFileType(e.target.value)} size="large">
            <Radio.Button 
              value="excel" 
              style={{ 
                fontSize: '18px', 
                padding: '12px 20px',
                height: '50px',
                display: 'inline-flex',
                alignItems: 'center',
                marginRight: '12px'
              }}
            >
              <FileExcelOutlined style={{ marginRight: 8, fontSize: '20px' }} />
              Excel文件
              <Tag 
                color="green" 
                style={{ 
                  marginLeft: 8, 
                  fontSize: '14px',
                  lineHeight: '20px',
                  height: '22px',
                  display: 'inline-flex',
                  alignItems: 'center'
                }}
              >
                推荐
              </Tag>
            </Radio.Button>
            <Radio.Button 
              value="pdf" 
              style={{ 
                fontSize: '18px', 
                padding: '12px 20px',
                height: '50px',
                display: 'inline-flex',
                alignItems: 'center',
                marginRight: '12px'
              }}
            >
              <FilePdfOutlined style={{ marginRight: 8, fontSize: '20px' }} />
              PDF文件
              <Tag 
                color="orange" 
                style={{ 
                  marginLeft: 8, 
                  fontSize: '14px',
                  lineHeight: '20px',
                  height: '22px',
                  display: 'inline-flex',
                  alignItems: 'center'
                }}
              >
                OCR
              </Tag>
            </Radio.Button>
            <Radio.Button 
              value="image" 
              style={{ 
                fontSize: '18px', 
                padding: '12px 20px',
                height: '50px',
                display: 'inline-flex',
                alignItems: 'center'
              }}
            >
              <FileImageOutlined style={{ marginRight: 8, fontSize: '20px' }} />
              图片文件
              <Tag 
                color="orange" 
                style={{ 
                  marginLeft: 8, 
                  fontSize: '14px',
                  lineHeight: '20px',
                  height: '22px',
                  display: 'inline-flex',
                  alignItems: 'center'
                }}
              >
                OCR
              </Tag>
            </Radio.Button>
          </Radio.Group>
        </Card>
        
        <Card 
          title={
            <Space style={{ fontSize: '18px', fontWeight: '500' }}>
              <CloudUploadOutlined style={{ fontSize: '18px' }} />
              上传文件
              {fileList.length > 0 && <Badge count={fileList.length} />}
            </Space>
          }
        >
          <Dragger
            fileList={fileList}
            onChange={handleUpload}
            beforeUpload={() => false}
            multiple={false}
            accept={fileType === 'excel' ? '.xls,.xlsx,.csv' : fileType === 'pdf' ? '.pdf' : '.jpg,.jpeg,.png,.bmp'}
            style={{ padding: '20px 0' }}
            progress={{
              strokeColor: {
                '0%': '#108ee9',
                '100%': '#87d068',
              },
              strokeWidth: 3,
              format: (percent) => `${formatUploadProgress(percent)}%`,
            }}
            itemRender={(originNode, file, fileList) => {
              // 自定义文件显示，增大字体并添加删除按钮
              return (
                <div style={{ 
                  padding: '12px 16px', 
                  border: '1px solid #d9d9d9', 
                  borderRadius: '8px', 
                  marginTop: '12px',
                  backgroundColor: '#fafafa'
                }}>
                  <div style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'space-between' 
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <FileExcelOutlined style={{ 
                        fontSize: '22px', 
                        color: '#52c41a', 
                        marginRight: '12px' 
                      }} />
                      <div>
                        <span style={{ 
                          fontSize: '18px', 
                          fontWeight: '500',
                          color: '#262626',
                          display: 'block'
                        }}>
                          {file.name}
                        </span>
                        <span style={{ 
                          fontSize: '16px', 
                          color: '#8c8c8c'
                        }}>
                          {(file.size / 1024 / 1024).toFixed(2)} MB
                        </span>
                      </div>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <Tag color="green" style={{ fontSize: '14px' }}>
                        已选择
                      </Tag>
                      <Button 
                        type="text" 
                        danger 
                        size="small"
                        onClick={() => {
                          // 从fileList中移除当前文件
                          const newFileList = fileList.filter(f => f.uid !== file.uid);
                          setFileList(newFileList);
                          message.success(`文件 "${file.name}" 已删除`);
                        }}
                        style={{ fontSize: '16px' }}
                      >
                        删除
                      </Button>
                    </div>
                  </div>
                </div>
              );
            }}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined style={{ fontSize: 36, color: '#1890ff' }} />
            </p>
            <p className="ant-upload-text" style={{ fontSize: 18, fontWeight: 500 }}>
              点击或拖拽文件到此区域上传
            </p>
            <p className="ant-upload-hint" style={{ color: '#666', fontSize: '16px' }}>
              {fileType === 'excel' ? '支持 .xls, .xlsx, .csv 格式文件，单个文件不超过50MB' : 
               fileType === 'pdf' ? '支持 .pdf 格式文件，单个文件不超过50MB' :
               '支持 .jpg, .jpeg, .png, .bmp 格式文件，单个文件不超过50MB'}
            </p>
          </Dragger>
          
          {/* 通用模板下载区域 */}
          <Divider style={{ margin: '16px 0' }}>
            <Text type="secondary" style={{ fontSize: '14px' }}>
              如果现有解析器无法处理您的文件格式，可使用通用模板
            </Text>
          </Divider>
          
          <Row gutter={16} style={{ marginTop: 16 }}>
            <Col span={12}>
              <Alert
                message="通用解析器模板"
                description="适用于所有银行的标准格式模板，当其他解析器无法识别您的文件时使用"
                type="info"
                showIcon
                style={{ fontSize: '14px' }}
              />
            </Col>
            <Col span={12}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button
                  type="primary"
                  icon={<FileExcelOutlined />}
                  onClick={downloadUniversalTemplate}
                  size="large"
                  style={{ width: '100%', fontSize: '16px', height: '45px' }}
                >
                  下载通用模板
                </Button>
                <Button
                  type="default"
                  icon={<FileTextOutlined />}
                  onClick={showUniversalTemplateInfo}
                  size="large"
                  style={{ width: '100%', fontSize: '16px', height: '45px' }}
                >
                  查看使用说明
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>
      </div>
    );
  };
  
  // 渲染银行选择步骤
  const renderBankSelectionStep = () => {
    return (
      <div>
        <Alert
          message="银行选择指南"
          description="请选择银行流水所属的银行。系统将根据银行类型推荐适合的解析模板，提高解析准确率。"
          type="info"
          showIcon
          style={{ marginBottom: 16, fontSize: '18px' }}
        />
        
        <Card title={<><BankOutlined /> 选择银行</>} style={{ fontSize: '18px' }}>
          <Row gutter={16}>
            <Col span={12}>
        <Select
          placeholder="请选择银行"
                style={{ 
            width: '100%',
            fontSize: '18px',
            height: '52px'
          }}
          value={selectedBank}
          onChange={value => {
            setSelectedBank(value);
            // 自动设置对应的解析器
            const defaultParser = getDefaultParserForBank(value);
            setSelectedTemplate(defaultParser);
            console.log(`🏦 选择银行: ${value}, 自动设置解析器: ${defaultParser}`);
          }}
          size="large"
          showSearch
          loading={bankLoading}
          dropdownStyle={{
            fontSize: '18px',
            maxHeight: '400px'
          }}
          listHeight={400}
          optionLabelProp="label"
          filterOption={(input, option) =>
                  option.label.toLowerCase().includes(input.toLowerCase())
          }
              >
                {bankOptions.map(bank => (
                  <Option 
            key={bank.key || bank.value} 
            value={bank.value}
            label={bank.label}
            style={{
              fontSize: '18px',
              lineHeight: '40px',
              padding: '8px 12px',
              height: '48px',
              display: 'flex',
              alignItems: 'center'
            }}
          >
              <Space style={{ 
              display: 'flex', 
              alignItems: 'center',
              fontSize: '18px',
              lineHeight: '40px',
              width: '100%'
            }}>
                      <span style={{ 
                fontSize: '20px',
                marginRight: '12px'
              }}>
                {bank.icon}
              </span>
                      <span style={{ 
                fontSize: '18px',
                fontWeight: '500',
                color: '#262626',
                lineHeight: '40px'
              }}>
                      {bank.label}
              </span>
              </Space>
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={12}>
              {selectedBank && (
                <Alert
                  message="银行已选择"
                  description={`已选择 ${bankOptions.find(b => b.value === selectedBank)?.label}`}
                  type="success"
                  showIcon
                  style={{ fontSize: '16px' }}
                />
              )}
            </Col>
          </Row>
      </Card>
      </div>
    );
  };
  
  // 渲染模板选择步骤
  const renderTemplateSelectionStep = () => {
    
    return (
      <div>
        <Alert
          message="解析器选择指南"
          description={
            fileList.length > 0 ? 
              `已上传文件"${fileList[0]?.name}"，系统已加载可用解析器并进行智能分析。置信度越高表示匹配度越好。` :
              "请选择适合的流水解析模板。系统已根据您选择的银行加载了相关解析器，置信度越高表示匹配度越好。"
          }
          type={
            availableTemplates.some(t => t.isSmartAnalyzed) ? "success" : 
            fileList.length > 0 ? "info" : "info"
          }
          showIcon
          style={{ marginBottom: 16, fontSize: '18px' }}
        />
        
        <Card 
          title={
            <Space style={{ fontSize: '20px' }}>
              <FileTextOutlined />
              选择解析模板
              {selectedBank && <Tag color="blue" style={{ fontSize: '16px' }}>{bankOptions.find(b => b.value === selectedBank)?.label}</Tag>}
              {availableTemplates.some(t => t.isSmartAnalyzed) && <Tag color="green" style={{ fontSize: '16px' }}>智能分析完成</Tag>}
              {fileList.length > 0 && <Tag color="orange" style={{ fontSize: '16px' }}>已上传文件</Tag>}
            </Space>
          }
          loading={templateLoading}
          style={{ fontSize: '18px' }}
        >
          {availableTemplates.length > 0 ? (
            <div>
              <Radio.Group 
          value={selectedTemplate}
                onChange={e => setSelectedTemplate(e.target.value)}
                style={{ width: '100%' }}
              >
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  {availableTemplates.map((template, index) => (
                    <div 
                      key={template.value}
                        style={{ 
                          border: selectedTemplate === template.value ? '2px solid #1890ff' : '1px solid #d9d9d9',
                        borderRadius: '8px',
                        padding: '16px',
                        backgroundColor: selectedTemplate === template.value ? '#f0f8ff' : '#ffffff',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease'
                        }}
                        onClick={() => setSelectedTemplate(template.value)}
                      >
                        <Radio value={template.value} style={{ marginBottom: 8, fontSize: '18px' }}>
                          <Space>
                            <Text strong style={{ fontSize: '18px' }}>{template.label}</Text>
                          {index === 0 && availableTemplates.length > 1 && (
                            <Tag color="gold" style={{ fontSize: '16px' }}>推荐</Tag>
                          )}
                            <Progress 
                              percent={template.confidence} 
                              size="small" 
                            status={template.confidence >= 95 ? 'success' : template.confidence >= 90 ? 'normal' : template.confidence >= 80 ? 'active' : 'exception'}
                              format={percent => `${percent}%`}
                            style={{ minWidth: 120 }}
                            />
                          </Space>
                        </Radio>
                      {/* 简化显示，移除冗余的解析器描述和匹配原因 */}
                      
                      {/* 🔧 4维度智能评分一行汇总显示 */}
                      {template.fourDMetrics && (
                        <div style={{ marginLeft: '24px', backgroundColor: '#f9f9f9', padding: '8px 12px', borderRadius: '4px', marginTop: '4px' }}>
                          <Text style={{ fontSize: '12px', color: '#666' }}>
                            <Text strong style={{ color: '#1890ff' }}>4维度智能评分：</Text>
                            姓名{template.fourDMetrics.cardholder_name_score?.percentage?.toFixed(0) || 0}% |
                            时间{template.fourDMetrics.time_format_score?.percentage?.toFixed(0) || 0}% |
                            账号{template.fourDMetrics.account_number_score?.percentage?.toFixed(0) || 0}% |
                            金额{template.fourDMetrics.amount_parsing_score?.percentage?.toFixed(0) || 0}% |
                            <Text strong style={{ color: '#1890ff' }}>总分{template.totalScore?.toFixed(1) || 0}/100</Text>
                            <span style={{ fontSize: '11px', color: '#999', marginLeft: '8px' }}>analysis_details</span>
                          </Text>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </Radio.Group>
        
              {selectedTemplate && (
                <div style={{ marginTop: 16 }}>
          <Alert
                    message="模板已选择"
                    description={
                      <Text strong>{availableTemplates.find(t => t.value === selectedTemplate)?.label}</Text>
                    }
                    type="success"
            showIcon
          />
                </div>
              )}
            </div>
          ) : (
            <Empty
              description="暂无可用模板"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            >
              <Button type="primary" onClick={async () => {
                console.log('🔄 用户点击重新加载模板');
                try {
                  await loadAvailableTemplates();
                  // 如果智能分析又失败了，直接调用基础模板
                  if (availableTemplates.length === 0) {
                    console.log('🔄 重新加载后仍无模板，尝试基础模板');
                    const basicTemplates = await loadBasicTemplatesData();
                    if (basicTemplates.length > 0) {
                      setAvailableTemplates(basicTemplates);
                      setSelectedTemplate(basicTemplates[0].value);
                    }
                  }
                } catch (error) {
                  console.error('❌ 重新加载失败:', error);
                }
              }}>
                重新加载
              </Button>
            </Empty>
          )}
      </Card>
      </div>
    );
  };
  
  // 渲染解析步骤
  const renderParsingStep = () => {
    if (parseStatus === 'loading') {
      return (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 20 }}>
            <Title level={4}>正在解析文件...</Title>
            
            <Alert
              message="进度说明"
              description="上传进度：文件传输到服务器的进度（仅临时存储）；解析进度：文件解析处理的进度。此阶段数据未保存到数据库，需要手动点击'保存'按钮才会写入数据库。"
              type="info"
              showIcon
              style={{ marginTop: 16, marginBottom: 20, textAlign: 'left' }}
            />
            
            <Space direction="vertical" style={{ width: '100%', marginTop: 20 }}>
              <div>
                <Text strong>文件上传进度：</Text>
                <Text type="secondary" style={{ marginLeft: 8 }}>(文件传输到服务器)</Text>
          <Progress 
                  percent={formatUploadProgress(uploadProgress)} 
                  strokeColor="#52c41a"
                  format={percent => `${percent}%`}
                  style={{ marginTop: 8 }}
                />
              </div>
              <div>
                <Text strong>文件解析进度：</Text>
                <Text type="secondary" style={{ marginLeft: 8 }}>(分析文件内容结构)</Text>
                <Progress 
                  percent={formatUploadProgress(parseProgress)} 
                  strokeColor="#1890ff"
                  format={percent => `${percent}%`}
                  style={{ marginTop: 8 }}
                />
              </div>
            </Space>
          </div>
        </div>
      );
    }
    
    if (parseStatus === 'error') {
      return (
        <Result
          status="error"
          title="解析失败"
          subTitle="文件解析过程中出现错误，请检查文件格式或重新选择模板。"
          extra={[
            <Button type="primary" key="retry" onClick={startParsing}>
              重新解析
            </Button>,
            <Button key="back-to-template" onClick={() => setCurrentStep(2)}>
              重选解析器
            </Button>,
            <Button key="restart" onClick={handleRestart}>
              重新开始
            </Button>
          ]}
        />
      );
    }
    
    if (parseStatus === 'success' && parsingResult) {
      // 账户信息表格列定义 - 修复为三列并排显示
      const accountColumns = [
        {
          title: '持卡人',
          dataIndex: 'holder_name',
          key: 'holder_name',
          width: 150,
          render: (text, record) => {
            // 🔧 修复：尝试多个可能的字段名，确保正确显示持卡人姓名
            const holderNameData = text || record.cardholder_name || record.account_name || record.name || record.accountHolderName || record.customer_name || record.account_holder;
            console.log('持卡人字段映射调试:', { text, record, holderNameData });
            return (
              <span style={{ fontWeight: 'bold', color: holderNameData ? '#1890ff' : '#999' }}>
                {holderNameData || '未知'}
              </span>
            );
          }
        },
        {
          title: '账号',
          dataIndex: 'account_number',
          key: 'account_number',
          width: 220,
          render: (text) => (
            <span style={{ whiteSpace: 'nowrap', fontFamily: 'monospace', fontSize: '16px', fontWeight: '500' }}>
              {text || '-'}
            </span>
          )
        },
        {
          title: '卡号',
          dataIndex: 'card_number',
          key: 'card_number',
          width: 220,
          render: (text) => (
            <span style={{ whiteSpace: 'nowrap', fontFamily: 'monospace', fontSize: '16px', fontWeight: '500' }}>
              {text || '-'}
            </span>
          )
        },
        {
          title: '交易笔数',
          dataIndex: 'transactions_count',
          key: 'transactions_count',
          width: 80,
          render: (text) => `${text || 0} 笔`
        },
        {
          title: '收入总额',
          dataIndex: 'total_inflow',
          key: 'total_inflow',
          width: 100,
          render: (amount) => {
            const numAmount = parseFloat(amount) || 0;
            return (
              <span style={{ color: '#3f8600' }}>
                ¥{numAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </span>
            );
          }
        },
        {
          title: '支出总额',
          dataIndex: 'total_outflow',
          key: 'total_outflow',
          width: 100,
          render: (amount) => {
            const numAmount = parseFloat(amount) || 0;
            return (
              <span style={{ color: '#cf1322' }}>
                ¥{numAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </span>
            );
          }
        },
        {
          title: '时间范围',
          dataIndex: 'date_range',
          key: 'date_range',
          width: 120,
          render: (text) => text || '未知'
        },
        {
          title: '操作',
          key: 'action',
          width: 150, // 增加宽度以容纳两个按钮
          render: (text, record) => (
            <Space>
            <Button 
              type="primary" 
              size="small"
              onClick={() => viewAccountTransactions(record)}
            >
              查看交易
            </Button>
              <Button 
                type="primary" 
                danger
                size="small"
                onClick={() => deleteAccount(record)}
              >
                删除
              </Button>
            </Space>
          )
        }
      ];

      // 交易记录表格列定义 - 符合PARSER_DEVELOPMENT_RULES.md的17个必需字段规范
      const transactionColumns = [
        {
          title: '序号',
          dataIndex: 'sequence_number',
          key: 'sequence_number',
          width: 80,
          render: (text, record, index) => {
            // 🔧 修复：每个账户的交易详情从1开始重新编号
            return index + 1;
          }
        },
        {
          title: '持卡人姓名',
          dataIndex: 'holder_name',
          key: 'holder_name',
          width: 160, // 增加宽度确保横向显示
          render: (text) => (
            <span style={{ whiteSpace: 'nowrap' }}>
              {text || '未知'}
            </span>
          )
        },
        {
          title: '银行名称',
          dataIndex: 'bank_name',
          key: 'bank_name',
          width: 120, // 增加宽度
          render: (text) => (
            <span style={{ whiteSpace: 'nowrap' }}>
              {text || '未知'}
            </span>
          )
        },
        {
          title: '账号',
          dataIndex: 'account_number',
          key: 'account_number',
          width: 180, // 增加宽度适应长账户号
          render: (text) => (
            <span style={{ whiteSpace: 'nowrap', fontFamily: 'monospace' }}>
              {text || '未知'}
            </span>
          )
        },
        {
          title: '卡号',
          dataIndex: 'card_number',
          key: 'card_number',
          width: 120,
          render: (text) => (
            <span style={{ whiteSpace: 'nowrap', fontFamily: 'monospace' }}>
              {text || '-'}
            </span>
          )
        },
        {
          title: '交易日期',
          dataIndex: 'transaction_date',
          key: 'transaction_date',
          width: 110,
          render: (text) => (
            <span style={{ whiteSpace: 'nowrap' }}>
              {text || '-'}
            </span>
          )
        },
        {
          title: '交易时间',
          dataIndex: 'transaction_time',
          key: 'transaction_time',
          width: 100,
          render: (time) => (
            <span style={{ whiteSpace: 'nowrap', fontFamily: 'monospace' }}>
              {formatTransactionTime(time)}
            </span>
          )
        },
        {
          title: '交易方式',
          dataIndex: 'transaction_method',
          key: 'transaction_method',
          width: 120, // 增加宽度
          render: (text) => (
            <span style={{ whiteSpace: 'nowrap' }}>
              {text || '-'}
            </span>
          )
        },
        {
          title: '交易金额',
          dataIndex: 'transaction_amount',
          key: 'transaction_amount',
          width: 130, // 增加宽度适应千位分隔符
          render: (amount, record) => {
            const numAmount = parseFloat(amount) || 0;
            // 🔧 修复：根据dr_cr_flag判断颜色，支出用红色，收入用绿色
            const isIncome = record.dr_cr_flag === '收';
            const isOutflow = record.dr_cr_flag === '支';
            
            let color = '#666666'; // 默认颜色
            if (isIncome) {
              color = '#3f8600'; // 绿色表示收入
            } else if (isOutflow) {
              color = '#cf1322'; // 红色表示支出
            } else {
              // 如果没有dr_cr_flag，根据金额正负判断
              color = numAmount >= 0 ? '#3f8600' : '#cf1322';
            }
            
            return (
              <span style={{ 
                color: color,
                whiteSpace: 'nowrap',
                fontFamily: 'monospace',
                fontWeight: 'bold'
              }}>
                ¥{Math.abs(numAmount).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </span>
            );
          }
        },
        {
          title: '交易余额',
          dataIndex: 'balance_amount',
          key: 'balance_amount',
          width: 130, // 增加宽度适应千位分隔符
          render: (amount) => {
            const numAmount = parseFloat(amount) || 0;
            return (
              <span style={{ whiteSpace: 'nowrap', fontFamily: 'monospace' }}>
                ¥{numAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </span>
            );
          }
        },
        {
          title: '收支符号',
          dataIndex: 'dr_cr_flag',
          key: 'dr_cr_flag',
          width: 80,
          render: (flag) => {
            const isIncome = flag === '收';
            return (
              <Tag color={isIncome ? 'green' : 'red'}>
                {flag || '未知'}
              </Tag>
            );
          }
        },
        {
          title: '对方户名',
          dataIndex: 'counterparty_name',
          key: 'counterparty_name',
          width: 160, // 增加宽度
          render: (text) => (
            <span style={{ whiteSpace: 'nowrap' }}>
              {text || '-'}
            </span>
          )
        },
        {
          title: '对方账号',
          dataIndex: 'counterparty_account',
          key: 'counterparty_account',
          width: 180, // 增加宽度适应长账户号
          render: (text) => (
            <span style={{ whiteSpace: 'nowrap', fontFamily: 'monospace' }}>
              {text || '-'}
            </span>
          )
        },
        {
          title: '对方银行',
          dataIndex: 'counterparty_bank',
          key: 'counterparty_bank',
          width: 140, // 增加宽度
          render: (text) => (
            <span style={{ whiteSpace: 'nowrap' }}>
              {text || '-'}
            </span>
          )
        },
        {
          title: '备注1',
          dataIndex: 'remark1',
          key: 'remark1',
          width: 120, // 增加宽度
          render: (text) => (
            <span style={{ whiteSpace: 'nowrap' }}>
              {text || '-'}
            </span>
          )
        },
        {
          title: '备注2',
          dataIndex: 'remark2',
          key: 'remark2',
          width: 120, // 增加宽度
          render: (text) => (
            <span style={{ whiteSpace: 'nowrap' }}>
              {text || '-'}
            </span>
          )
        },
        {
          title: '备注3',
          dataIndex: 'remark3',
          key: 'remark3',
          width: 120, // 增加宽度
          render: (text) => (
            <span style={{ whiteSpace: 'nowrap' }}>
              {text || '-'}
            </span>
          )
        }
      ];

      return (
        <div>
          <Result
            status="success"
            title="解析完成"
            subTitle={`成功解析银行流水文件，识别到 ${parsingResult.accounts?.length || 0} 个账户和 ${parsingResult.transactions?.length || 0} 条交易记录。`}
            extra={[
              <Button 
                type="primary" 
                key="save" 
                icon={<SaveOutlined />} 
                onClick={saveParsingResult}
                loading={isSaving}
                size="large"
                disabled={isSaving}
              >
                {isSaving ? '保存中...' : '保存到数据库'}
              </Button>,
              <Button key="restart" onClick={handleRestart} disabled={isSaving}>
                重新导入
              </Button>
            ]}
          />
          
          {/* 保存进度条 */}
          {isSaving && (
            <Card title="保存进度" style={{ marginTop: 16 }}>
              <Alert
                message="正在保存数据到数据库"
                description="请耐心等待，正在将解析结果保存到数据库中，请勿关闭页面。"
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
              <Progress 
                percent={Math.floor(saveProgress)} 
                status={saveProgress === 100 ? 'success' : 'active'}
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
                format={percent => `${percent}%`}
              />
              <div style={{ textAlign: 'center', marginTop: 8, color: '#666' }}>
                {saveProgress < 30 && '准备保存数据...'}
                {saveProgress >= 30 && saveProgress < 60 && '发送数据到服务器...'}
                {saveProgress >= 60 && saveProgress < 90 && '处理数据中...'}
                {saveProgress >= 90 && saveProgress < 100 && '即将完成...'}
                {saveProgress === 100 && '保存完成！'}
              </div>
            </Card>
          )}
          
          {/* 账户信息详情表格 */}
          <Card title="账户信息详情" style={{ marginTop: 16 }}>
            <Table
              columns={accountColumns}
              dataSource={parsingResult.accounts || []}
              rowKey="account_id"
              pagination={false}
              size="middle"
              scroll={{ x: 'max-content' }}
            />
          </Card>
          
          {/* 交易详情显示区域 - 移动到账户信息详情下方 */}
          {showTransactionDetails && selectedAccount && (
            <Card 
              id="transaction-details-section"
              title={
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span>交易详情 - {selectedAccount.holder_name || selectedAccount.cardholder_name || selectedAccount.account_name || selectedAccount.name || '未知'}</span>
                  <Button 
                    type="link" 
                    onClick={() => setShowTransactionDetails(false)}
                    style={{ color: '#ff4d4f' }}
                  >
                    关闭详情
                  </Button>
                </div>
              }
              style={{ marginTop: 16 }}
            >
              <Descriptions bordered size="small" style={{ marginBottom: 16 }}>
                <Descriptions.Item label="持卡人">{selectedAccount.holder_name || selectedAccount.cardholder_name || selectedAccount.account_name || selectedAccount.name || '未知'}</Descriptions.Item>
                <Descriptions.Item label="账号">{selectedAccount.account_number || '未知'}</Descriptions.Item>
                <Descriptions.Item label="卡号">{selectedAccount.card_number || '未知'}</Descriptions.Item>
                <Descriptions.Item label="交易笔数">{accountTransactions.length} 笔</Descriptions.Item>
                <Descriptions.Item label="账户余额">
                  ¥{(parseFloat(selectedAccount.account_balance) || 0).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </Descriptions.Item>
              </Descriptions>
              
              {/* 交易记录表格 - 符合PARSER_DEVELOPMENT_RULES.md的17个必需字段规范 */}
              <Table
                columns={transactionColumns}
                dataSource={accountTransactions}
                rowKey="transaction_id"
                pagination={{ 
                  pageSize: 100, // 符合规范：显示前100条记录
                  showSizeChanger: true, 
                  showQuickJumper: true,
                  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
                }}
                scroll={{ x: 2200 }} // 更新总宽度适应17个优化后的字段
                size="small"
              />
          </Card>
          )}
          
          {/* 解析结果统计 */}
          <Card title="解析结果统计" style={{ marginTop: 16 }}>
            <Row gutter={16}>
            <Col span={6}>
                <Statistic
                  title="账户数量" 
                  value={parsingResult.accounts?.length || 0} 
                  prefix={<BankOutlined />}
                />
            </Col>
            <Col span={6}>
                <Statistic
                  title="交易记录"
                  value={parsingResult.transactions?.length || 0} 
                  prefix={<FileTextOutlined />}
                />
            </Col>
            <Col span={6}>
                <Statistic
                  title="文件大小" 
                  value={fileList[0]?.size ? (fileList[0].size / 1024 / 1024).toFixed(2) : 0} 
                  suffix="MB"
                  prefix={<CloudUploadOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="解析模板"
                  value={availableTemplates.find(t => t.value === selectedTemplate)?.label || '未知'}
                  prefix={<FileTextOutlined />}
                />
              </Col>
            </Row>
          </Card>

          {/* 交易记录汇总 */}
          {parsingResult.transactions && parsingResult.transactions.length > 0 && (
            <Card title="交易记录汇总" style={{ marginTop: 16 }}>
              <Alert
                message="交易记录预览"
                description={`共识别到 ${parsingResult.transactions.length} 条交易记录，点击上方账户的"查看交易"按钮可查看详细交易明细。`}
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
              
              <Row gutter={16}>
                <Col span={8}>
                <Statistic
                  title="总收入"
                    value={parsingResult.transactions
                      .filter(t => (parseFloat(t.transaction_amount) || 0) > 0)
                      .reduce((sum, t) => sum + (parseFloat(t.transaction_amount) || 0), 0)
                    }
                  precision={2}
                    valueStyle={{ color: '#3f8600' }}
                  prefix="¥"
                    formatter={(value) => value.toLocaleString('zh-CN')}
                />
            </Col>
                <Col span={8}>
                <Statistic
                  title="总支出"
                    value={Math.abs(parsingResult.transactions
                      .filter(t => (parseFloat(t.transaction_amount) || 0) < 0)
                      .reduce((sum, t) => sum + (parseFloat(t.transaction_amount) || 0), 0)
                    )}
                  precision={2}
                    valueStyle={{ color: '#cf1322' }}
                  prefix="¥"
                    formatter={(value) => value.toLocaleString('zh-CN')}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="净流水"
                    value={parsingResult.transactions
                      .reduce((sum, t) => sum + (parseFloat(t.transaction_amount) || 0), 0)
                    }
                    precision={2}
                    valueStyle={{ color: parsingResult.transactions
                      .reduce((sum, t) => sum + (parseFloat(t.transaction_amount) || 0), 0) >= 0 ? '#3f8600' : '#cf1322' }}
                    prefix="¥"
                    formatter={(value) => value.toLocaleString('zh-CN')}
                  />
            </Col>
          </Row>
          </Card>
          )}
          

        </div>
      );
    }
    
    return null;
  };

  // 查看账户交易详情
  const viewAccountTransactions = (account) => {
    console.log('🖱️ 查看账户交易被点击，账户:', account);
    
    // 🔧 关键修复：确保只有在实际解析完成后才能查看交易详情
    if (!parsingResult || !parsingResult.transactions) {
      message.warning('请先完成文件解析后再查看交易详情');
      console.log('❌ 拒绝显示交易详情：解析结果为空或不完整');
      return;
    }
    
    // 🔧 验证当前解析结果的有效性
    if (parsingResult.transactions.length === 0) {
      message.warning('当前文件没有解析出交易数据，无法查看交易详情');
      console.log('❌ 拒绝显示交易详情：交易数据为空');
      return;
    }
    
    // 🔧 修复：获取该账户的交易记录，使用持卡人+账户号的组合进行精确匹配
    console.log('🔍 开始过滤账户交易:', {
      账户持卡人: account.holder_name || account.cardholder_name || account.person_name || account.account_name,
      账户账号: account.account_number,
      账户卡号: account.card_number,
      总交易数: parsingResult.transactions?.length
    });
    
    const accountTransactions = parsingResult.transactions?.filter(
      transaction => {
        // 使用持卡人+账号必需匹配，若账户行有卡号则再加卡号匹配，确保明细与总表一致
        const transactionHolder = transaction.holder_name || transaction.cardholder_name || '';
        const transactionAccount = transaction.account_number || '';
        const transactionCard = transaction.card_number || '';
        const accountHolder = account.holder_name || account.cardholder_name || account.person_name || account.account_name || '';
        const accountNumber = account.account_number || '';
        const accountCard = account.card_number || '';

        const holderMatch = transactionHolder === accountHolder;
        const accountMatch = transactionAccount === accountNumber;
        const cardMatch = !accountCard || transactionCard === accountCard;
        
        const isMatch = holderMatch && accountMatch && cardMatch;
        
        if (isMatch) {
          console.log('✅ 交易匹配成功:', {
            交易持卡人: transactionHolder,
            交易账号: transactionAccount,
            交易时间: transaction.transaction_date,
            交易金额: transaction.transaction_amount
          });
        }
        
        return isMatch;
      }
    ) || [];

    console.log('📝 账户交易记录数量:', accountTransactions.length);
    console.log('📊 来源解析结果总交易数:', parsingResult.transactions.length);

    // 🔧 验证账户交易数据的有效性
    if (accountTransactions.length === 0) {
      message.warning(`账户 "${account.holder_name || account.cardholder_name || account.person_name || account.account_name || '未知'}" 没有找到相关交易记录`);
      console.log('❌ 拒绝显示交易详情：该账户无交易数据');
      return;
    }

    // 🔧 修复：计算账户余额 = 最后一笔交易的余额
    let account_balance = 0;
    if (accountTransactions.length > 0) {
      // 按时间排序，获取最后一笔交易的余额
      const sortedTransactions = [...accountTransactions].sort((a, b) => {
        const timeA = new Date(a.transaction_datetime || `${a.transaction_date} ${a.transaction_time}` || a.transaction_date || 0);
        const timeB = new Date(b.transaction_datetime || `${b.transaction_date} ${b.transaction_time}` || b.transaction_date || 0);
        return timeB - timeA; // 降序排列，最新的在前
      });
      const latestTransaction = sortedTransactions[0];
      account_balance = parseFloat(latestTransaction.balance_amount || latestTransaction.balance) || 0;
      console.log('📊 账户余额计算:', { 
        最新交易时间: latestTransaction.transaction_datetime || `${latestTransaction.transaction_date} ${latestTransaction.transaction_time}`,
        余额: account_balance 
      });
    }

    // 更新账户信息，包含计算的余额
    const updatedAccount = {
      ...account,
      account_balance: account_balance
    };

    // 符合PARSER_DEVELOPMENT_RULES.md规范：原页面展示，不使用弹窗
    setSelectedAccount(updatedAccount);
    setAccountTransactions(accountTransactions);
    setShowTransactionDetails(true);
    
    console.log('✅ 成功显示交易详情，数据来源已验证');
    
    // 滚动到交易详情区域
    setTimeout(() => {
      const transactionDetailsElement = document.getElementById('transaction-details-section');
      if (transactionDetailsElement) {
        transactionDetailsElement.scrollIntoView({ behavior: 'smooth' });
      }
    }, 100);
  };
  
  // 删除账户功能
  const deleteAccount = (account) => {
    console.log('🗑️ 删除账户被点击，账户:', account);
    
    Modal.confirm({
      title: '确认删除账户',
      content: (
        <div>
          <p>您确定要删除以下账户吗？</p>
          <div style={{ backgroundColor: '#f5f5f5', padding: '12px', borderRadius: '6px', marginTop: '12px' }}>
            <p><strong>持卡人：</strong>{account.holder_name || '未知'}</p>
            <p><strong>账号：</strong>{account.account_number || '未知'}</p>
            <p><strong>银行：</strong>{account.bank_name || '未知'}</p>
            <p><strong>交易笔数：</strong>{account.transactions_count || 0} 笔</p>
          </div>
          <p style={{ color: '#ff4d4f', marginTop: '12px' }}>
            ⚠️ 删除后将无法恢复，相关的交易记录也会被移除！
          </p>
            </div>
      ),
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        // 🔧 修复：检查是否删除的是无数据账户
        const isEmptyAccount = !account.transactions_count || account.transactions_count === 0;

        console.log('🔧 开始删除账户:', {
          要删除的账户: account,
          是否无数据账户: isEmptyAccount,
          原始账户数: parsingResult.accounts?.length || 0,
          原始交易数: parsingResult.transactions?.length || 0
        });

        // 🔧 关键修复：按三元组(持卡人+账号+卡号)精确删除，不影响其他记录
        const updatedAccounts = parsingResult.accounts.filter(acc => {
          // 精确三元组匹配：持卡人、账号、卡号必须全部一致
          const isTargetAccount = (
            acc.account_number === account.account_number &&
            acc.card_number === account.card_number &&
            (acc.holder_name === account.holder_name || acc.cardholder_name === account.holder_name)
          );

          if (isTargetAccount) {
            console.log('🗑️ 将删除账户:', acc);
          }

          return !isTargetAccount; // 返回true表示保留，false表示删除
        });

        // 🔧 关键修复：按三元组精确删除交易记录
        const updatedTransactions = parsingResult.transactions.filter(transaction => {
          // 精确三元组匹配：持卡人、账号、卡号必须全部一致
          const isTargetTransaction = (
            transaction.account_number === account.account_number &&
            transaction.card_number === account.card_number &&
            (transaction.holder_name === account.holder_name || transaction.cardholder_name === account.holder_name)
          );

          return !isTargetTransaction; // 返回true表示保留，false表示删除
        });

        console.log('🔧 过滤结果:', {
          过滤后账户数: updatedAccounts.length,
          过滤后交易数: updatedTransactions.length,
          删除的账户数: (parsingResult.accounts?.length || 0) - updatedAccounts.length,
          删除的交易数: (parsingResult.transactions?.length || 0) - updatedTransactions.length
        });

        // 🔧 修复：确保删除后不会导致显示异常，保持完整的解析结果结构
        const newParsingResult = {
          ...parsingResult,
          accounts: updatedAccounts,
          transactions: updatedTransactions,
          // 确保保持原有的元数据和状态
          success: true,
          message: parsingResult.message || '解析成功',
          metadata: {
            ...parsingResult.metadata,
            // 更新统计信息
            total_accounts: updatedAccounts.length,
            total_transactions: updatedTransactions.length
          }
        };

        console.log('🔧 删除账户后的新解析结果:', {
          原账户数: parsingResult.accounts?.length || 0,
          新账户数: updatedAccounts.length,
          原交易数: parsingResult.transactions?.length || 0,
          新交易数: updatedTransactions.length,
          删除的账户: account.holder_name || '未知'
        });

        // 🔧 修复：如果删除后没有账户了，保持解析状态为成功，但显示合理的信息
        if (updatedAccounts.length === 0) {
          console.log('⚠️ 删除后没有账户了，但保持解析成功状态');
          // 保持解析成功状态，只是数据为空
          newParsingResult.message = '所有账户已被删除，解析结果为空';
        }

        setParsingResult(newParsingResult);

        // 如果当前显示的交易详情是被删除的账户，则关闭详情显示
        if (selectedAccount &&
           (selectedAccount.account_id === account.account_id ||
            selectedAccount.account_number === account.account_number)) {
          setShowTransactionDetails(false);
          setSelectedAccount(null);
          setAccountTransactions([]);
        }

        if (isEmptyAccount) {
          message.success(`无数据账户 "${account.holder_name || '未知'}" 已删除`);
        } else {
          message.success(`账户 "${account.holder_name || '未知'}" 已删除`);
        }
        console.log('✅ 账户删除成功，剩余账户数:', updatedAccounts.length);
      }
    });
  };
  
  // 获取步骤对应的状态
  const getStepStatus = (stepIndex) => {
    if (stepIndex < currentStep) return 'finish';
    if (stepIndex === currentStep) return 'process';
    return 'wait';
  };
  
  // 定义步骤配置 - 使用新的items格式
  const stepItems = [
    {
      title: '上传文件',
      description: fileList.length > 0 ? `已上传 ${fileList.length} 个文件` : '选择并上传银行流水文件',
      icon: React.createElement(CloudUploadOutlined),
      status: getStepStatus(0)
    },
    {
      title: '选择银行',
      description: selectedBank ? bankOptions.find(b => b.value === selectedBank)?.label : '选择银行类型',
      icon: React.createElement(BankOutlined),
      status: getStepStatus(1)
    },
    {
      title: '选择模板',
      description: selectedTemplate ? availableTemplates.find(t => t.value === selectedTemplate)?.label : '选择解析模板',
      icon: React.createElement(FileTextOutlined),
      status: getStepStatus(2)
    },
    {
      title: '解析文件',
      description: parseStatus === 'success' ? '解析完成' : parseStatus === 'loading' ? '解析中...' : '等待解析',
      icon: parseStatus === 'loading' ? React.createElement(LoadingOutlined) : React.createElement(CheckCircleOutlined),
      status: getStepStatus(3)
    }
  ];

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16, textAlign: 'center' }}>
          <Title level={2} className="page-main-title" style={{ color: '#1890ff', fontSize: '42px' }}>
            银行流水导入
          </Title>
          <Text type="secondary" style={{ fontSize: '18px' }}>上传并解析银行流水文件，自动识别账户和交易信息</Text>
        </div>
        
        <Steps 
          current={currentStep} 
          style={{ marginBottom: 24 }} 
          items={stepItems}
        />
        
        {renderStepContent()}
        
        <Divider />
        
        <div style={{ textAlign: 'center' }}>
          <Space size="large">
            {currentStep > 0 && currentStep < 3 && (
              <Button size="large" onClick={handlePrev}>
              上一步
            </Button>
          )}
            {currentStep < 2 && (
          <Button 
            type="primary" 
                size="large" 
            onClick={handleNext}
          >
                下一步
          </Button>
            )}
            {currentStep === 2 && selectedTemplate && (
              <Button 
                type="primary" 
                size="large" 
                icon={<FileTextOutlined />}
                onClick={handleNext}
                loading={parseStatus === 'loading'}
              >
                开始解析
          </Button>
            )}
            {currentStep === 3 && parseStatus === 'success' && (
              <Button 
                type="primary" 
                size="large" 
                icon={<SaveOutlined />}
                onClick={saveParsingResult}
              >
                保存数据
              </Button>
            )}
          </Space>
        </div>
        
        {/* 调试信息 */}
        <Card title="调试信息" style={{ marginTop: 16, backgroundColor: '#f6f6f6' }} size="small">
          <Row gutter={16}>
            <Col span={6}>
              <Text strong>当前步骤:</Text> <Text style={{ fontSize: '18px' }}>{currentStep}</Text>
            </Col>
            <Col span={6}>
              <Text strong>选择银行:</Text> <Text style={{ fontSize: '18px' }}>{selectedBank || '未选择'}</Text>
            </Col>
            <Col span={6}>
              <Text strong>选择模板:</Text> <Text style={{ fontSize: '18px' }}>{selectedTemplate || '未选择'}</Text>
            </Col>
            <Col span={6}>
              <Text strong>解析状态:</Text> <Text style={{ fontSize: '18px' }}>{parseStatus}</Text>
            </Col>
          </Row>
          <Row gutter={16} style={{ marginTop: 8 }}>
            <Col span={6}>
              <Text strong>文件数量:</Text> <Text style={{ fontSize: '18px' }}>{fileList.length}</Text>
            </Col>
            <Col span={6}>
              <Text strong>可用模板:</Text> <Text style={{ fontSize: '18px' }}>{availableTemplates.length}</Text>
            </Col>
            <Col span={6}>
              <Text strong>项目ID:</Text> <Text style={{ fontSize: '18px' }}>{projectId || '未设置'}</Text>
            </Col>
            <Col span={6}>
              <Text strong>模板加载:</Text> <Text style={{ fontSize: '18px' }}>{templateLoading ? '是' : '否'}</Text>
            </Col>
          </Row>
        </Card>
      </Card>
    </div>
  );
};

export default BankStatementsImport; 