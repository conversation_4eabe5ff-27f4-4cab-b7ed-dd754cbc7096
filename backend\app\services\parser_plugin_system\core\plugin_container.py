"""
插件容器 - 提供隔离执行环境

为每个解析器插件提供独立的执行环境，实现错误隔离和资源监控
"""

import threading
import time
import traceback
import importlib.util
import sys
import os
from typing import Dict, Any, Optional
from ..isolation.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CircuitBreaker


class PluginContainer:
    """插件容器 - 提供隔离执行环境
    
    每个解析器插件在独立的容器中运行，确保错误隔离和资源控制
    """
    
    def __init__(self, plugin_info: Dict[str, Any]):
        """初始化插件容器
        
        Args:
            plugin_info: 插件信息字典
        """
        self.plugin_info = plugin_info
        self.plugin_instance = None
        self.error_handler = ErrorHandler()
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=5,
            timeout=300  # 5分钟
        )
        self.status = "stopped"
        self.last_error = None
        self.start_time = None
        self.execution_count = 0
        self.success_count = 0
        self.error_count = 0
        self._lock = threading.Lock()
        
        # 从配置加载设置
        self.config = plugin_info.get('config', {})
        self.timeout = self.config.get('timeout', 60)
        self.memory_limit = self.config.get('memory_limit', '512MB')
        self.retry_count = self.config.get('retry_count', 3)
    
    def start(self) -> bool:
        """启动插件容器
        
        Returns:
            bool: 启动是否成功
        """
        try:
            with self._lock:
                if self.status == "running":
                    return True
                
                print(f"🚀 启动插件容器: {self.plugin_info['name']}")
                
                # 动态加载插件模块
                plugin_module = self._load_plugin_module()
                if not plugin_module:
                    return False
                
                # 实例化插件
                self.plugin_instance = plugin_module.Plugin()
                
                # 初始化插件
                if hasattr(self.plugin_instance, 'initialize'):
                    if not self.plugin_instance.initialize():
                        print(f"❌ 插件初始化失败: {self.plugin_info['name']}")
                        return False
                
                self.status = "running"
                self.start_time = time.time()
                self.last_error = None
                
                print(f"✅ 插件容器启动成功: {self.plugin_info['name']}")
                return True
                
        except Exception as e:
            self.status = "error"
            self.last_error = str(e)
            print(f"❌ 插件容器启动失败 {self.plugin_info['name']}: {e}")
            traceback.print_exc()
            return False
    
    def stop(self):
        """停止插件容器"""
        try:
            with self._lock:
                if self.status == "stopped":
                    return
                
                print(f"🔒 停止插件容器: {self.plugin_info['name']}")
                
                # 清理插件资源
                if self.plugin_instance and hasattr(self.plugin_instance, 'cleanup'):
                    self.plugin_instance.cleanup()
                
                self.plugin_instance = None
                self.status = "stopped"
                self.start_time = None
                
                # 关闭错误处理器
                self.error_handler.shutdown()
                
                print(f"✅ 插件容器已停止: {self.plugin_info['name']}")
                
        except Exception as e:
            print(f"❌ 停止插件容器失败 {self.plugin_info['name']}: {e}")
    
    def restart(self) -> bool:
        """重启插件容器
        
        Returns:
            bool: 重启是否成功
        """
        print(f"🔄 重启插件容器: {self.plugin_info['name']}")
        self.stop()
        time.sleep(1)  # 短暂等待
        return self.start()
    
    def execute_parse(self, file_path: str) -> Dict[str, Any]:
        """在隔离环境中执行解析
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict[str, Any]: 解析结果
        """
        if self.status != "running":
            return {
                "success": False,
                "message": f"插件容器未运行 (状态: {self.status})",
                "transactions": [],
                "accounts": []
            }
        
        self.execution_count += 1
        
        try:
            # 通过熔断器执行
            result = self.circuit_breaker.call(
                self._execute_with_retry,
                file_path
            )
            
            # 统计成功次数
            if result.get('success', False):
                self.success_count += 1
            else:
                self.error_count += 1
            
            return result
            
        except Exception as e:
            self.error_count += 1
            self.last_error = str(e)
            
            return {
                "success": False,
                "message": f"插件执行被熔断: {str(e)}",
                "transactions": [],
                "accounts": [],
                "error_type": "circuit_breaker"
            }
    
    def _execute_with_retry(self, file_path: str) -> Dict[str, Any]:
        """带重试机制的执行
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict[str, Any]: 解析结果
        """
        last_error = None
        
        for attempt in range(self.retry_count):
            try:
                print(f"🔄 执行解析 (尝试 {attempt + 1}/{self.retry_count}): {os.path.basename(file_path)}")
                
                # 使用错误隔离机制执行
                result = self.error_handler.execute_with_isolation(
                    self._safe_parse,
                    file_path,
                    timeout=self.timeout
                )
                
                # 如果成功，直接返回
                if result.get('success', False):
                    if attempt > 0:
                        print(f"✅ 重试成功 (第 {attempt + 1} 次尝试)")
                    return result
                
                # 记录失败原因
                last_error = result.get('message', '未知错误')
                
                # 如果不是可重试的错误，直接返回
                if result.get('error_type') in ['file_not_found', 'permission_error']:
                    return result
                
                # 等待后重试
                if attempt < self.retry_count - 1:
                    wait_time = (attempt + 1) * 2  # 递增等待时间
                    print(f"⏳ 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                
            except Exception as e:
                last_error = str(e)
                print(f"❌ 执行失败 (尝试 {attempt + 1}/{self.retry_count}): {e}")
        
        # 所有重试都失败
        return {
            "success": False,
            "message": f"重试 {self.retry_count} 次后仍然失败: {last_error}",
            "transactions": [],
            "accounts": [],
            "error_type": "retry_exhausted"
        }
    
    def _safe_parse(self, file_path: str) -> Dict[str, Any]:
        """安全解析方法
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict[str, Any]: 解析结果
        """
        try:
            if not self.plugin_instance:
                raise Exception("插件实例未初始化")
            
            # 执行解析
            result = self.plugin_instance.parse(file_path)
            
            # 验证返回格式
            result = self._validate_and_normalize_result(result)
            
            return result
            
        except Exception as e:
            self.last_error = str(e)
            return {
                "success": False,
                "message": f"解析执行失败: {str(e)}",
                "transactions": [],
                "accounts": [],
                "error_type": "parse_error",
                "error_trace": traceback.format_exc()
            }
    
    def _validate_and_normalize_result(self, result: Any) -> Dict[str, Any]:
        """验证并标准化解析结果
        
        Args:
            result: 原始解析结果
            
        Returns:
            Dict[str, Any]: 标准化后的结果
        """
        if not isinstance(result, dict):
            raise ValueError(f"插件返回格式错误: 期望字典，得到 {type(result)}")
        
        # 确保必要字段存在
        if "success" not in result:
            result["success"] = True
        
        if "message" not in result:
            result["message"] = "解析成功" if result["success"] else "解析失败"
        
        if "transactions" not in result:
            result["transactions"] = []
        
        if "accounts" not in result:
            result["accounts"] = []
        
        # 添加容器信息
        result["plugin_name"] = self.plugin_info['name']
        result["plugin_version"] = self.plugin_info.get('version', '1.0.0')
        result["execution_time"] = time.time()
        
        return result
    
    def is_healthy(self) -> bool:
        """检查插件健康状态 - 优化版本

        Returns:
            bool: 是否健康
        """
        if self.status != "running":
            return False

        try:
            # 🔧 优化：新启动的插件给予宽容期
            uptime = time.time() - self.start_time if hasattr(self, 'start_time') else 0

            # 如果插件运行时间少于2分钟，认为是健康的（初始化期）
            if uptime < 120:
                return True

            # 检查插件自身健康状态
            plugin_healthy = True  # 默认健康
            if self.plugin_instance:
                try:
                    health = self.plugin_instance.get_health_status()
                    plugin_healthy = health.get("healthy", True)  # 默认健康
                except Exception:
                    # 如果健康检查本身失败，不认为插件不健康
                    plugin_healthy = True

            # 🔧 优化：放宽容器健康标准
            container_healthy = (
                self.error_count < 20 and  # 错误次数阈值从10提高到20
                self.circuit_breaker.state != 'open'  # 熔断器未开启
            )

            # 🔧 优化：只要容器健康就认为整体健康，不强制要求插件健康检查通过
            return container_healthy

        except Exception:
            # 健康检查异常不认为插件不健康
            return True
    
    def get_status(self) -> Dict[str, Any]:
        """获取容器状态
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        with self._lock:
            uptime = time.time() - self.start_time if self.start_time else 0
            
            return {
                "plugin_name": self.plugin_info['name'],
                "status": self.status,
                "uptime": uptime,
                "execution_count": self.execution_count,
                "success_count": self.success_count,
                "error_count": self.error_count,
                "success_rate": self.success_count / max(self.execution_count, 1),
                "last_error": self.last_error,
                "circuit_breaker": self.circuit_breaker.get_state(),
                "config": self.config,
                "healthy": self.is_healthy()
            }
    
    def _load_plugin_module(self):
        """动态加载插件模块
        
        Returns:
            模块对象或None
        """
        try:
            plugin_path = self.plugin_info["path"]
            plugin_name = self.plugin_info["name"]
            
            if not os.path.exists(plugin_path):
                print(f"❌ 插件文件不存在: {plugin_path}")
                return None
            
            # 使用importlib动态加载
            spec = importlib.util.spec_from_file_location(plugin_name, plugin_path)
            if not spec or not spec.loader:
                print(f"❌ 无法创建插件规格: {plugin_path}")
                return None
            
            module = importlib.util.module_from_spec(spec)
            
            # 将模块添加到sys.modules，避免重复加载
            module_key = f"plugin_{plugin_name}_{id(self)}"
            sys.modules[module_key] = module
            
            # 执行模块
            spec.loader.exec_module(module)
            
            # 验证插件类存在
            if not hasattr(module, 'Plugin'):
                print(f"❌ 插件模块缺少Plugin类: {plugin_path}")
                return None
            
            print(f"✅ 插件模块加载成功: {plugin_name}")
            return module
            
        except Exception as e:
            print(f"❌ 加载插件模块失败 {self.plugin_info['name']}: {e}")
            traceback.print_exc()
            return None
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标
        
        Returns:
            Dict[str, Any]: 性能指标
        """
        return {
            "execution_stats": self.error_handler.get_execution_stats(),
            "circuit_breaker_state": self.circuit_breaker.get_state(),
            "container_metrics": {
                "total_executions": self.execution_count,
                "successful_executions": self.success_count,
                "failed_executions": self.error_count,
                "success_rate": self.success_count / max(self.execution_count, 1),
                "average_execution_time": "需要实现时间统计",
                "memory_usage": "需要实现内存监控"
            }
        }
    
    def cleanup_resources(self):
        """清理容器资源"""
        try:
            # 清理执行记录
            self.error_handler.cleanup_old_executions()
            
            # 终止挂起的执行
            self.error_handler.kill_hanging_executions()
            
            # 重置熔断器（如果需要）
            if self.circuit_breaker.state == 'open':
                print(f"🔄 重置熔断器: {self.plugin_info['name']}")
                self.circuit_breaker.reset()
            
        except Exception as e:
            print(f"❌ 清理容器资源失败: {e}")
    
    def force_stop(self):
        """强制停止容器"""
        try:
            print(f"🚨 强制停止插件容器: {self.plugin_info['name']}")
            
            with self._lock:
                self.status = "force_stopped"
                self.plugin_instance = None
                
            # 强制关闭错误处理器
            self.error_handler.shutdown()
            
        except Exception as e:
            print(f"❌ 强制停止失败: {e}") 