"""
后端服务启动脚本
"""
import uvicorn
import logging
import os
import sys
import psutil
import time

# 添加当前目录到模块搜索路径，使app包可导入
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def kill_process_on_port(port):
    """查找并杀死占用指定端口的进程"""
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            connections = proc.connections()
            for conn in connections:
                if hasattr(conn, 'laddr') and conn.laddr.port == port:
                    logger.warning(f"发现进程 {proc.info['name']} (PID: {proc.info['pid']}) 正在占用端口 {port}，将强制终止...")
                    p = psutil.Process(proc.info['pid'])
                    p.kill()
                    logger.info(f"进程 {proc.info['pid']} 已被终止。")
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass

if __name__ == "__main__":
    # 导入统一配置
    from app.core.settings import settings
    
    # 在启动前清理端口
    server_port = settings.get_uvicorn_settings().get('port', 8000)
    logger.info(f"检查端口 {server_port} 是否被占用...")
    kill_process_on_port(server_port)
    
    # 等待一小段时间确保端口已释放
    time.sleep(1)

    # 启动FastAPI应用，使用统一配置
    uvicorn_settings = settings.get_uvicorn_settings()
    logger.info(f"🚀 启动服务器: {uvicorn_settings}")
    uvicorn.run("app.main:app", **uvicorn_settings) 