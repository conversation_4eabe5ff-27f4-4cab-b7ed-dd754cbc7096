"""
相关单位情况管理API
"""
import logging
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from fastapi import APIRouter, Depends, HTTPException, status, Query

from ..middleware.auth_middleware import get_current_user, get_user_db
from ..models.duckdb_models import DuckDBRelatedUnit as RelatedUnit

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/")
async def get_related_units(
    project_id: str = Query(..., description="项目ID"),
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> List[Dict[str, Any]]:
    """获取项目的相关单位列表"""
    try:
        logger.info(f"用户 '{current_user}' 获取项目 {project_id} 的相关单位列表")
        
        units = db.query(RelatedUnit).filter(
            RelatedUnit.project_id == project_id
        ).order_by(RelatedUnit.created_at.desc()).all()
        
        result = []
        for unit in units:
            result.append({
                "unit_id": unit.unit_id,
                "project_id": unit.project_id,
                "name": unit.name,
                "legal_representative": unit.legal_representative,
                "registered_capital": unit.registered_capital,
                "establishment_date": unit.establishment_date,
                "business_scope": unit.business_scope,
                "registered_address": unit.registered_address,
                "contact_info": unit.contact_info,
                "relationship_description": unit.relationship_description,
                "notes": unit.notes,
                "created_at": unit.created_at.isoformat() if unit.created_at else None,
                "updated_at": unit.updated_at.isoformat() if unit.updated_at else None
            })
        
        logger.info(f"用户 '{current_user}' 获取到 {len(result)} 条相关单位")
        return result
        
    except Exception as e:
        logger.error(f"用户 '{current_user}' 获取相关单位列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取相关单位列表失败: {str(e)}"
        )

@router.post("/")
async def create_related_unit(
    unit_data: Dict[str, Any],
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> Dict[str, Any]:
    """创建相关单位"""
    try:
        logger.info(f"用户 '{current_user}' 创建相关单位: {unit_data.get('name', '未知')}")
        
        new_unit = RelatedUnit(
            project_id=unit_data["project_id"],
            name=unit_data.get("name", ""),
            legal_representative=unit_data.get("legal_representative", ""),
            registered_capital=unit_data.get("registered_capital"),
            establishment_date=unit_data.get("establishment_date", ""),
            business_scope=unit_data.get("business_scope", ""),
            registered_address=unit_data.get("registered_address", ""),
            contact_info=unit_data.get("contact_info", ""),
            relationship_description=unit_data.get("relationship_description", ""),
            notes=unit_data.get("notes", ""),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        db.add(new_unit)
        db.commit()
        db.refresh(new_unit)
        
        logger.info(f"用户 '{current_user}' 成功创建相关单位: {new_unit.unit_id}")
        
        return {
            "unit_id": new_unit.unit_id,
            "project_id": new_unit.project_id,
            "name": new_unit.name,
            "legal_representative": new_unit.legal_representative,
            "registered_capital": new_unit.registered_capital,
            "establishment_date": new_unit.establishment_date,
            "business_scope": new_unit.business_scope,
            "registered_address": new_unit.registered_address,
            "contact_info": new_unit.contact_info,
            "relationship_description": new_unit.relationship_description,
            "notes": new_unit.notes,
            "created_at": new_unit.created_at.isoformat() if new_unit.created_at else None,
            "updated_at": new_unit.updated_at.isoformat() if new_unit.updated_at else None
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"用户 '{current_user}' 创建相关单位失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建相关单位失败: {str(e)}"
        )

@router.put("/{unit_id}")
async def update_related_unit(
    unit_id: str,
    unit_data: Dict[str, Any],
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> Dict[str, Any]:
    """更新相关单位信息"""
    try:
        logger.info(f"用户 '{current_user}' 更新相关单位，ID: {unit_id}")
        
        unit = db.query(RelatedUnit).filter(RelatedUnit.unit_id == unit_id).first()
        if not unit:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"相关单位 {unit_id} 不存在"
            )
        
        # 更新字段
        for field, value in unit_data.items():
            if field != "unit_id" and hasattr(unit, field):
                setattr(unit, field, value)
        
        unit.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(unit)
        
        logger.info(f"用户 '{current_user}' 成功更新相关单位: {unit.unit_id}")
        
        return {
            "unit_id": unit.unit_id,
            "project_id": unit.project_id,
            "name": unit.name,
            "legal_representative": unit.legal_representative,
            "registered_capital": unit.registered_capital,
            "establishment_date": unit.establishment_date,
            "business_scope": unit.business_scope,
            "registered_address": unit.registered_address,
            "contact_info": unit.contact_info,
            "relationship_description": unit.relationship_description,
            "notes": unit.notes,
            "created_at": unit.created_at.isoformat() if unit.created_at else None,
            "updated_at": unit.updated_at.isoformat() if unit.updated_at else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"用户 '{current_user}' 更新相关单位失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新相关单位失败: {str(e)}"
        )

@router.delete("/{unit_id}")
async def delete_related_unit(
    unit_id: str,
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_user_db)
) -> Dict[str, str]:
    """删除相关单位"""
    try:
        logger.info(f"用户 '{current_user}' 删除相关单位，ID: {unit_id}")
        
        unit = db.query(RelatedUnit).filter(RelatedUnit.unit_id == unit_id).first()
        if not unit:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"相关单位 {unit_id} 不存在"
            )
        
        db.delete(unit)
        db.commit()
        
        logger.info(f"用户 '{current_user}' 成功删除相关单位: {unit.unit_id}")
        
        return {"message": "相关单位删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"用户 '{current_user}' 删除相关单位失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除相关单位失败: {str(e)}"
        ) 