# 四维预解析与模板接口规范

> 目的：沉淀本次“只有工行候选”的问题排查与修复经验，形成统一的四维预解析与模板接口约定，避免后续前后端不一致导致的候选解析器缺失或展示异常。

## 1. 概述
- 适用范围：后端插件化解析系统（FastAPI）与前端导入流程（React）
- 核心对象：
  - 四维评分（4D）：cardholder、time format、account number、amount parsing
  - 预解析接口：POST /api/parser/pre-analyze
  - 模板接口：GET /api/parser/v2/templates、GET /api/parser/templates（兼容）

## 2. 统一的“银行名称归一化”规则
- 归一化函数必须在以下所有触点使用同一版本：
  - 后端：预解析（analyze_file_structure）、模板列表（get_templates_list）
  - 插件元数据匹配（metadata.bank_name、description、name）
- 处理逻辑（关键要点）：
  - 小写、去空格、去冗余（“中国”“股份有限公司”“银行”等）
  - 同义词映射到标准银行代码：
    - 邮政储蓄/邮储/psbc → psbc
    - 工商/工行/icbc → icbc
    - 建设/建行/ccb → ccb
    - 中国银行/中行/boc → boc
    - 农业/农行/abc → abc
    - …（按需扩展）
- 验证要点：
  - “中国邮政储蓄银行”“邮政储蓄银行”“邮储”查询应返回同一 psbc_* 模板集合

## 3. 预解析接口（POST /api/parser/pre-analyze）
- 请求（multipart/form-data）
  - file: 待分析文件（xls/xlsx 等）
  - bank_name: 用户选择银行（任意别名均可，后端统一归一化）
- 响应（关键字段）
  - success: bool
  - candidates: Candidate[]（按置信度降序）
    - parser_id: string（插件名，如 psbc_format1_plugin）
    - parser_info: {templateId, templateName, description}
    - confidence_evaluation:
      - confidence: number（0~100，总分）
      - match_reason: string
      - details: 统一同时返回“新旧两套键名别名”以兼容前端：
        - 新：cardholder_recognition、time_format_accuracy、account_recognition、amount_parsing（均含 score 与 percentage）
        - 旧：cardholder_name_score、time_format_score、account_number_score、amount_parsing_score（score/percentage）
      - evaluation_breakdown、sample_analysis：可选
- 约束：
  - 评分>0 的插件加入候选；银行匹配但评分=0 时提供保底候选（minimal_conf≥1），便于人工选择

## 4. 模板接口（GET /api/parser/v2/templates 与 /api/parser/templates）
- v2 返回值：直接返回模板数组 Template[]
- v1 返回值：{ templates: Template[], count, bank_name }
- Template 字段：{ templateId, templateName, bankName, description, version, source }
- 前端消费规范：
  - 统一写法：
    - templateArray = Array.isArray(result) ? result : (result?.templates || [])
  - 无文件时使用模板列表；有文件时优先使用预解析 candidates
- 银行过滤：必须与预解析完全一致的归一化规则（见第2节）

## 5. 前端渲染与数据转换（规范性要求）
- 智能分析（有文件）：
  - 取 result.candidates → map 到 UI Template 列表
  - 4D 映射：若 confidence_evaluation.details 缺失，再回退 evaluation_breakdown 或调用备用 4D 接口补全
  - UI 标签使用模板名短名（逗号/顿号/空格之前）
- 基础模板（无文件）：
  - 调用模板接口并用“模板数组兼容逻辑”解析
  - 自动选择默认模板（第一个或 isRecommended=true）
- 容错：
  - 智能分析失败或空候选 → fallback 到模板列表
  - 显示“已选择银行”“已上传文件/智能分析完成”等状态标签，加强用户心智

## 6. 测试与验收清单（必须通过）
- 后端单测/集成：
  - 归一化函数对“中国邮政储蓄银行/邮政储蓄银行/邮储/PSBC”一致
  - /api/parser/v2/templates?bank_name=中国邮政储蓄银行 返回 psbc_format1/2/3
  - 预解析对邮政样例返回≥1 个候选，且 best 为 psbc_*，confidence>0
- 前端 E2E（建议用 Playwright，登录：樊迪/123456）：
  - 进入导入页，选择“中国邮政储蓄银行”，不上传文件 → 页面出现 psbc_format1/2/3
  - 上传邮政样例 → 智能分析返回多个候选，4D 评分显示正确，推荐项与后端第一名一致
  - 模板 API 返回对象或数组两种结构皆能渲染
- 端口与启动：
  - 后端固定 8000、前端固定 3000，如遇占用先清占用，不得改端口

## 7. 常见坑与规避建议
- v1/v2 返回结构不一致 → 前端统一用“模板数组兼容逻辑”
- 只修预解析不修模板过滤 → 不一致导致“智能分析能找模板，模板列表找不到”
- React 严格模式导致副作用双触发 → 已在 index.js 移除 StrictMode
- 日志：
  - 重要路径加 INFO 日志（请求银行、目标 code、插件银行、最终是否匹配），便于快速定位

## 8. 代码约束（与插件规范）
- 禁止在 API 层直接导入解析器类；所有解析调用必须通过插件管理器（见 .augment/rules/代码审查要点）
- 新解析器必须作为插件开发，提供 metadata.json 的 bank_name、supported_formats 等

## 9. 变更记录
- 2025-08-20：补齐银行归一化在模板接口的使用；前端兼容 v1/v2 返回结构；沉淀本文档

