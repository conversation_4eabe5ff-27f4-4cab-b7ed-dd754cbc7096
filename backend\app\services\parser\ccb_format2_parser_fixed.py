#!/usr/bin/env python3
"""
建设银行Format2解析器 - 修复版本
专门处理"个人活期明细信息-新一代"格式的Excel文件，如"2号测试2次.xlsx"

修复要点：
1. 确保所有字段按照标准化输出结构进行映射
2. 修复交易记录中客户信息丢失的问题
3. 正确处理日期时间字段
4. 确保空值处理符合规范
"""

import pandas as pd
import numpy as np
import openpyxl
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import logging
import re
import uuid
from pathlib import Path

logger = logging.getLogger(__name__)

class CCBFormat2ParserFixed:
    """建设银行Format2解析器 - 修复版本"""
    
    def __init__(self, file_path: str):
        """
        初始化建设银行Format2解析器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.bank_name = "中国建设银行"
        self.parser_name = "CCBFormat2ParserFixed"
        self.format_name = "格式2多客户活期明细"
        
        # 目标工作表名称
        self.target_sheet_names = [
            "个人活期明细信息-新一代",
            "个人活期明细信息",
            "活期明细信息-新一代",
            "活期明细信息"
        ]
        
        # 数据表头关键字段
        self.header_keywords = ["交易日期", "借方发生额", "贷方发生额", "账户余额", "摘要"]
        
        # 客户信息行关键字段
        self.customer_info_keywords = ["客户名称:", "客户编号:", "客户账号:", "卡号:"]
        
        # 解析结果
        self.accounts = []
        self.transactions = []
        self.transactions_by_account = {}
    
    def parse(self) -> Dict[str, Any]:
        """
        解析建设银行Format2格式文件
        标准接口方法
        
        Returns:
            Dict: 解析结果
        """
        try:
            logger.info(f"开始解析建设银行Format2文件: {self.file_path}")
            
            # 1. 读取Excel文件，获取所有工作表
            excel_file = pd.ExcelFile(self.file_path)
            sheet_names = excel_file.sheet_names
            logger.info(f"发现工作表: {sheet_names}")
            
            # 2. 找到目标工作表
            target_sheet = self._find_target_sheet(sheet_names)
            if not target_sheet:
                return self._create_failed_result("未找到包含交易明细的工作表")
            
            logger.info(f"使用目标工作表: {target_sheet}")
            
            # 3. 读取目标工作表数据
            df = pd.read_excel(self.file_path, sheet_name=target_sheet, header=None)
            logger.info(f"工作表数据形状: {df.shape}")
            
            # 4. 解析数据表头位置
            header_row = self._find_header_row(df)
            if header_row is None:
                return self._create_failed_result("未找到数据表头行")
            
            logger.info(f"数据表头位置: 第{header_row + 1}行")
            
            # 5. 解析客户信息和交易数据
            customers_data = self._parse_customers_data(df, header_row)
            if not customers_data:
                return self._create_failed_result("未找到有效的客户交易数据")
            
            logger.info(f"成功解析 {len(customers_data)} 个客户的数据")
            
            # 6. 构建最终结果
            self._build_final_result(customers_data)
            
            # 7. 计算置信度
            confidence = self._calculate_confidence_score(self.accounts, self.transactions)
            
            # 8. 返回结果
            return self._build_result(confidence)
            
        except Exception as e:
            error_msg = f"解析失败: {str(e)}"
            logger.error(error_msg)
            return self._create_failed_result(error_msg)
    
    def _find_target_sheet(self, sheet_names: List[str]) -> Optional[str]:
        """寻找包含交易明细的目标工作表"""
        for sheet_name in sheet_names:
            if sheet_name in self.target_sheet_names:
                return sheet_name
        return None
    
    def _find_header_row(self, df: pd.DataFrame) -> Optional[int]:
        """寻找数据表头行"""
        for idx, row in df.iterrows():
            row_str = ' '.join([str(x) for x in row.values if pd.notna(x)])
            if all(keyword in row_str for keyword in self.header_keywords):
                return idx
        return None
    
    def _parse_customers_data(self, df: pd.DataFrame, header_row: int) -> List[Dict[str, Any]]:
        """解析客户信息和交易数据"""
        customers_data = []
        current_customer = None
        header_columns = df.iloc[header_row].values
        
        # 建立列映射
        column_mapping = self._build_column_mapping(header_columns)
        
        # 从表头的下一行开始解析
        for idx in range(header_row + 1, len(df)):
            row = df.iloc[idx]
            row_str = ' '.join([str(x) for x in row.values if pd.notna(x)])
            
            # 检查是否是客户信息行
            if any(keyword in row_str for keyword in self.customer_info_keywords):
                # 如果之前有客户数据，先保存
                if current_customer and current_customer.get('transactions'):
                    customers_data.append(current_customer)
                
                # 解析新客户信息
                current_customer = self._parse_customer_info(row_str)
                if current_customer:
                    current_customer['transactions'] = []
                    logger.info(f"发现客户: {current_customer['name']}")
            
            # 检查是否是交易数据行
            elif current_customer and self._is_transaction_row(row, column_mapping):
                transaction = self._parse_transaction_row(row, column_mapping, current_customer)
                if transaction:
                    current_customer['transactions'].append(transaction)
        
        # 保存最后一个客户的数据
        if current_customer and current_customer.get('transactions'):
            customers_data.append(current_customer)
        
        return customers_data
    
    def _build_column_mapping(self, header_columns) -> Dict[str, int]:
        """建立列名到列索引的映射"""
        mapping = {}
        
        for idx, col_name in enumerate(header_columns):
            if pd.isna(col_name):
                continue
                
            col_str = str(col_name).strip()
            
            # 日期相关
            if any(keyword in col_str for keyword in ["交易日期", "日期"]):
                mapping["date"] = idx
            # 时间相关
            elif any(keyword in col_str for keyword in ["交易时间", "时间"]):
                mapping["time"] = idx
            # 摘要相关
            elif any(keyword in col_str for keyword in ["摘要", "备注", "说明"]):
                mapping["description"] = idx
            # 借方金额
            elif "借方发生额" in col_str:
                mapping["debit_amount"] = idx
            # 贷方金额
            elif "贷方发生额" in col_str:
                mapping["credit_amount"] = idx
            # 账户余额
            elif "账户余额" in col_str:
                mapping["balance"] = idx
            # 交易卡号
            elif any(keyword in col_str for keyword in ["交易卡号", "卡号"]):
                mapping["card_number"] = idx
            # 明细号
            elif any(keyword in col_str for keyword in ["明细号", "序号"]):
                mapping["serial_number"] = idx
            # 交易机构
            elif any(keyword in col_str for keyword in ["交易机构", "机构"]):
                mapping["institution"] = idx
            # 对方账号
            elif any(keyword in col_str for keyword in ["对方账号"]):
                mapping["counterparty_account"] = idx
            # 对方户名
            elif any(keyword in col_str for keyword in ["对方户名"]):
                mapping["counterparty_name"] = idx
            # 交易备注
            elif any(keyword in col_str for keyword in ["交易备注", "备注"]):
                mapping["remark"] = idx
        
        logger.info(f"列映射: {mapping}")
        return mapping
    
    def _parse_customer_info(self, customer_str: str) -> Optional[Dict[str, Any]]:
        """解析客户信息行"""
        try:
            customer_info = {}
            
            # 解析客户名称
            name_match = re.search(r'客户名称:([^，,]+)', customer_str)
            if name_match:
                customer_info['name'] = name_match.group(1).strip()
            
            # 解析客户编号
            customer_no_match = re.search(r'客户编号:([^，,]+)', customer_str)
            if customer_no_match:
                customer_info['customer_number'] = customer_no_match.group(1).strip()
            
            # 解析客户账号
            account_match = re.search(r'客户账号:([^，,]+)', customer_str)
            if account_match:
                customer_info['account_number'] = account_match.group(1).strip()
            
            # 解析卡号
            card_match = re.search(r'卡号:([^，,]+)', customer_str)
            if card_match:
                card_value = card_match.group(1).strip()
                if card_value and card_value != '':
                    customer_info['card_number'] = card_value
                else:
                    customer_info['card_number'] = ""  # 规范：空值为空字符串
            
            # 解析币别
            currency_match = re.search(r'币别:([^，,]+)', customer_str)
            if currency_match:
                customer_info['currency'] = currency_match.group(1).strip()
            
            if customer_info.get('name'):
                return customer_info
            else:
                return None
                
        except Exception as e:
            logger.error(f"解析客户信息失败: {str(e)}")
            return None
    
    def _is_transaction_row(self, row: pd.Series, column_mapping: Dict[str, int]) -> bool:
        """判断是否是有效的交易数据行"""
        # 检查是否有日期字段
        if "date" in column_mapping:
            date_value = row.iloc[column_mapping["date"]]
            if pd.notna(date_value) and str(date_value).strip():
                # 简单的日期格式检查
                date_str = str(date_value).strip()
                if re.match(r'\d{4}-\d{1,2}-\d{1,2}', date_str):
                    return True
        
        return False
    
    def _parse_transaction_row(self, row: pd.Series, column_mapping: Dict[str, int], customer_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """解析交易数据行"""
        try:
            transaction = {}
            
            # 基本客户信息
            transaction["cardholder_name"] = customer_info.get('name', '')
            transaction["account_number"] = customer_info.get('account_number', '')
            transaction["card_number"] = customer_info.get('card_number', '')
            transaction["bank_name"] = self.bank_name
            
            # 交易日期
            if "date" in column_mapping:
                date_value = row.iloc[column_mapping["date"]]
                if pd.notna(date_value):
                    transaction["transaction_date"] = self._format_date(str(date_value))
            
            # 交易时间
            if "time" in column_mapping:
                time_value = row.iloc[column_mapping["time"]]
                if pd.notna(time_value) and str(time_value).strip():
                    transaction["transaction_time"] = str(time_value).strip()
                else:
                    transaction["transaction_time"] = ""
            
            # 摘要/备注
            if "description" in column_mapping:
                desc_value = row.iloc[column_mapping["description"]]
                if pd.notna(desc_value) and str(desc_value).strip():
                    transaction["summary"] = str(desc_value).strip()
                else:
                    transaction["summary"] = ""
            
            # 借方发生额（支出）
            if "debit_amount" in column_mapping:
                debit_value = row.iloc[column_mapping["debit_amount"]]
                transaction["debit_amount"] = self._convert_to_number(debit_value) or 0.0
            
            # 贷方发生额（收入）
            if "credit_amount" in column_mapping:
                credit_value = row.iloc[column_mapping["credit_amount"]]
                transaction["credit_amount"] = self._convert_to_number(credit_value) or 0.0
            
            # 账户余额
            if "balance" in column_mapping:
                balance_value = row.iloc[column_mapping["balance"]]
                transaction["balance"] = self._convert_to_number(balance_value) or 0.0
            
            # 对方账号
            if "counterparty_account" in column_mapping:
                counterparty_account_value = row.iloc[column_mapping["counterparty_account"]]
                if pd.notna(counterparty_account_value) and str(counterparty_account_value).strip():
                    transaction["counterparty_account"] = str(counterparty_account_value).strip()
                else:
                    transaction["counterparty_account"] = ""
            
            # 对方户名
            if "counterparty_name" in column_mapping:
                counterparty_name_value = row.iloc[column_mapping["counterparty_name"]]
                if pd.notna(counterparty_name_value) and str(counterparty_name_value).strip():
                    transaction["counterparty_name"] = str(counterparty_name_value).strip()
                else:
                    transaction["counterparty_name"] = ""
            
            # 交易备注
            if "remark" in column_mapping:
                remark_value = row.iloc[column_mapping["remark"]]
                if pd.notna(remark_value) and str(remark_value).strip():
                    transaction["remark"] = str(remark_value).strip()
                else:
                    transaction["remark"] = ""
            
            # 计算交易金额和方向
            debit = transaction.get("debit_amount") or 0.0
            credit = transaction.get("credit_amount") or 0.0
            
            if debit > 0:
                transaction["amount"] = -debit  # 支出为负数
                transaction["direction"] = "支出"
            elif credit > 0:
                transaction["amount"] = credit  # 收入为正数
                transaction["direction"] = "收入"
            else:
                transaction["amount"] = 0.0
                transaction["direction"] = "未知"
            
            # 合并日期时间
            date_str = transaction.get("transaction_date", "")
            time_str = transaction.get("transaction_time", "")
            if date_str and time_str:
                transaction["transaction_datetime"] = f"{date_str}T{time_str}"
            elif date_str:
                transaction["transaction_datetime"] = f"{date_str}T00:00:00"
            else:
                transaction["transaction_datetime"] = ""
            
            # 必须有日期和非零金额才算有效交易
            if transaction.get("transaction_date") and transaction.get("amount", 0) != 0:
                return transaction
            else:
                return None
                
        except Exception as e:
            logger.error(f"解析交易行失败: {str(e)}")
            return None
    
    def _convert_to_number(self, value) -> Optional[float]:
        """将文本格式数字转换为数字格式"""
        if pd.isna(value) or value == '':
            return None
        
        try:
            # 处理各种数字格式
            if isinstance(value, (int, float)):
                return float(value)
            
            # 字符串处理
            str_value = str(value).strip()
            if not str_value:
                return None
            
            # 移除货币符号和千位分隔符
            clean_value = str_value.replace('¥', '').replace(',', '').replace('，', '').replace(' ', '')
            
            # 尝试转换为浮点数
            return float(clean_value)
            
        except (ValueError, TypeError):
            logger.warning(f"无法转换为数字: {value}")
            return None
    
    def _format_date(self, date_str: str) -> str:
        """格式化日期字符串"""
        try:
            # 如果已经是标准格式，直接返回
            if re.match(r'\d{4}-\d{2}-\d{2}', date_str):
                return date_str
            
            # 尝试解析其他格式
            date_obj = pd.to_datetime(date_str)
            return date_obj.strftime('%Y-%m-%d')
            
        except Exception:
            return date_str
    
    def _build_final_result(self, customers_data: List[Dict[str, Any]]):
        """构建最终解析结果 - 按照标准化输出结构"""
        self.accounts = []
        self.transactions = []
        self.transactions_by_account = {}
        
        for customer in customers_data:
            customer_name = customer['name']
            account_number = customer.get('account_number', '')
            card_number = customer.get('card_number', '')
            
            # 创建账户信息
            account_info = {
                "person_name": customer_name,
                "bank_name": self.bank_name,
                "account_name": customer_name,
                "account_number": account_number,
                "card_number": card_number,
                "currency": customer.get('currency', 'CNY'),
                "total_inflow": 0.0,
                "total_outflow": 0.0,
                "transactions_count": 0
            }
            
            # 计算汇总数据
            for transaction in customer['transactions']:
                amount = transaction.get('amount', 0.0)
                if amount > 0:
                    account_info['total_inflow'] += amount
                else:
                    account_info['total_outflow'] += abs(amount)
                account_info['transactions_count'] += 1
            
            self.accounts.append(account_info)
            
            # 处理交易记录
            account_transactions = []
            for transaction in customer['transactions']:
                # 按照标准化输出结构格式化交易记录
                formatted_transaction = {
                    "transaction_id": str(uuid.uuid4()),
                    "transaction_date": transaction.get('transaction_date', ''),
                    "transaction_time": transaction.get('transaction_time', ''),
                    "transaction_datetime": transaction.get('transaction_datetime', ''),
                    "amount": transaction.get('amount', 0.0),
                    "balance": transaction.get('balance', 0.0),
                    "direction": transaction.get('direction', ''),
                    "transaction_type": transaction.get('summary', ''),
                    "counterparty_name": transaction.get('counterparty_name', ''),
                    "counterparty_account": transaction.get('counterparty_account', ''),
                    "summary": transaction.get('summary', ''),
                    "remark": transaction.get('remark', ''),
                    "account_name": customer_name,
                    "account_number": account_number,
                    "card_number": card_number,
                    "bank_name": self.bank_name
                }
                
                self.transactions.append(formatted_transaction)
                account_transactions.append(formatted_transaction)
            
            self.transactions_by_account[account_number] = account_transactions
    
    def _calculate_confidence_score(self, accounts: List[Dict[str, Any]], transactions: List[Dict[str, Any]]) -> float:
        """计算置信度分数"""
        if not accounts or not transactions:
            return 0.0
        
        score = 0.0
        
        # 基本数据完整性 (40%)
        if accounts:
            score += 0.2
        if transactions:
            score += 0.2
        
        # 字段完整性 (35%)
        if transactions:
            first_tx = transactions[0]
            if first_tx.get('transaction_date'):
                score += 0.1
            if first_tx.get('amount'):
                score += 0.1
            if first_tx.get('balance'):
                score += 0.1
            if first_tx.get('account_number'):
                score += 0.05
        
        # 数据质量 (25%)
        valid_dates = sum(1 for tx in transactions if tx.get('transaction_date'))
        if valid_dates > 0:
            score += 0.15 * (valid_dates / len(transactions))
        
        valid_amounts = sum(1 for tx in transactions if tx.get('amount', 0) != 0)
        if valid_amounts > 0:
            score += 0.1 * (valid_amounts / len(transactions))
        
        return min(score, 1.0)
    
    def _build_result(self, confidence: float) -> Dict[str, Any]:
        """构建最终结果"""
        # 计算日期范围
        date_range = ""
        if self.transactions:
            dates = [tx.get('transaction_date') for tx in self.transactions if tx.get('transaction_date')]
            if dates:
                min_date = min(dates)
                max_date = max(dates)
                date_range = f"{min_date} - {max_date}"
        
        # 计算汇总信息
        total_inflow = sum(account.get('total_inflow', 0) for account in self.accounts)
        total_outflow = sum(account.get('total_outflow', 0) for account in self.accounts)
        
        return {
            "success": True,
            "message": "解析成功",
            "summary": {
                "account_name": self.accounts[0].get('account_name', '') if self.accounts else '',
                "account_number": self.accounts[0].get('account_number', '') if self.accounts else '',
                "card_number": self.accounts[0].get('card_number', '') if self.accounts else '',
                "bank_name": self.bank_name,
                "total_inflow": total_inflow,
                "total_outflow": total_outflow,
                "date_range": date_range,
                "total_transactions": len(self.transactions),
                "currency": "CNY"
            },
            "transactions": self.transactions,
            "accounts": self.accounts,
            "transactions_by_account": self.transactions_by_account,
            "confidence_score": confidence,
            "parser_type": self.parser_name
        }
    
    def _create_failed_result(self, error_msg: str) -> Dict[str, Any]:
        """创建失败结果"""
        return {
            "success": False,
            "message": error_msg,
            "summary": {},
            "transactions": [],
            "accounts": [],
            "confidence_score": 0.0,
            "parser_type": self.parser_name
        } 