"""
用户数据库初始化脚本
确保每个用户的数据库都有完整的表结构
"""
import logging
from sqlalchemy import text
from sqlalchemy.orm import Session
from .duckdb_config import get_user_engine, get_user_session

logger = logging.getLogger(__name__)

def init_user_database_tables(username: str) -> bool:
    """
    初始化用户数据库的所有表结构
    
    Args:
        username: 用户名
        
    Returns:
        bool: 初始化是否成功
    """
    try:
        engine = get_user_engine(username)
        
        # 创建所有必要的表
        with engine.connect() as conn:
            # 1. 创建projects表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS projects (
                    project_id VARCHAR PRIMARY KEY,
                    project_name VARCHAR NOT NULL,
                    person_name VARCHAR,
                    db_path VARCHAR,
                    is_active BOOLEAN DEFAULT true,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    description TEXT
                )
            """))
            
            # 2. 创建accounts表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS accounts (
                    account_id VARCHAR PRIMARY KEY,
                    project_id VARCHAR NOT NULL,
                    account_number VARCHAR NOT NULL,
                    account_name VARCHAR,
                    bank_name VARCHAR,
                    account_type VARCHAR,
                    balance DECIMAL(15,2),
                    currency VARCHAR DEFAULT 'CNY',
                    status VARCHAR DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    notes TEXT
                )
            """))
            
            # 3. 创建case_clues表（问题线索）
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS case_clues (
                    clue_id VARCHAR PRIMARY KEY,
                    project_id VARCHAR NOT NULL,
                    clue_type VARCHAR NOT NULL,
                    title VARCHAR NOT NULL,
                    content TEXT,
                    source VARCHAR,
                    priority VARCHAR DEFAULT 'medium',
                    status VARCHAR DEFAULT 'pending',
                    assigned_to VARCHAR,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    due_date TIMESTAMP,
                    tags VARCHAR,
                    attachments TEXT
                )
            """))
            
            # 4. 创建case_subjects表（被反映人资料）
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS case_subjects (
                    subject_id VARCHAR PRIMARY KEY,
                    project_id VARCHAR NOT NULL,
                    name VARCHAR NOT NULL,
                    id_number VARCHAR,
                    position VARCHAR,
                    department VARCHAR,
                    level VARCHAR,
                    phone VARCHAR,
                    address VARCHAR,
                    email VARCHAR,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            # 5. 创建case_related_persons表（相关人员）
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS case_related_persons (
                    person_id VARCHAR PRIMARY KEY,
                    project_id VARCHAR NOT NULL,
                    name VARCHAR NOT NULL,
                    id_number VARCHAR,
                    role VARCHAR NOT NULL,
                    gender VARCHAR,
                    birthday VARCHAR,
                    phone VARCHAR,
                    address VARCHAR,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            # 6. 创建case_assets表（财产信息）
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS case_assets (
                    asset_id VARCHAR PRIMARY KEY,
                    project_id VARCHAR NOT NULL,
                    name VARCHAR NOT NULL,
                    type VARCHAR NOT NULL,
                    owner VARCHAR NOT NULL,
                    value DECIMAL(15,2),
                    status VARCHAR,
                    location VARCHAR,
                    identification_number VARCHAR,
                    acquisition_date VARCHAR,
                    acquisition_method VARCHAR,
                    custom_field1 VARCHAR,
                    custom_field2 VARCHAR,
                    custom_field3 VARCHAR,
                    detail TEXT,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            # 7. 创建case_related_units表（相关单位）
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS case_related_units (
                    unit_id VARCHAR PRIMARY KEY,
                    project_id VARCHAR NOT NULL,
                    name VARCHAR NOT NULL,
                    type VARCHAR NOT NULL,
                    legal_representative VARCHAR,
                    registration_number VARCHAR,
                    address VARCHAR,
                    phone VARCHAR,
                    email VARCHAR,
                    business_scope TEXT,
                    registration_capital DECIMAL(15,2),
                    establishment_date VARCHAR,
                    status VARCHAR DEFAULT 'active',
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            # 8. 创建case_relationships表（关系映射）
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS case_relationships (
                    relationship_id VARCHAR PRIMARY KEY,
                    project_id VARCHAR NOT NULL,
                    from_entity_type VARCHAR NOT NULL,
                    from_entity_id VARCHAR NOT NULL,
                    to_entity_type VARCHAR NOT NULL,
                    to_entity_id VARCHAR NOT NULL,
                    relationship_type VARCHAR NOT NULL,
                    description TEXT,
                    strength DECIMAL(3,2) DEFAULT 1.0,
                    start_date VARCHAR,
                    end_date VARCHAR,
                    status VARCHAR DEFAULT 'active',
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            # 9. 创建transactions表（交易记录）
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS transactions (
                    transaction_id VARCHAR PRIMARY KEY,
                    project_id VARCHAR NOT NULL,
                    account_id VARCHAR,
                    transaction_date DATE,
                    transaction_time TIME,
                    amount DECIMAL(15,2),
                    balance DECIMAL(15,2),
                    transaction_type VARCHAR,
                    counterparty VARCHAR,
                    counterparty_account VARCHAR,
                    description TEXT,
                    reference_number VARCHAR,
                    channel VARCHAR,
                    status VARCHAR DEFAULT 'normal',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            conn.commit()
            
        logger.info(f"✅ 用户 '{username}' 的数据库表结构初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 用户 '{username}' 数据库初始化失败: {e}")
        return False

def ensure_user_database_ready(username: str) -> bool:
    """
    确保用户数据库已准备就绪
    
    Args:
        username: 用户名
        
    Returns:
        bool: 数据库是否就绪
    """
    try:
        # 初始化表结构
        if not init_user_database_tables(username):
            return False
            
        # 验证表是否存在
        engine = get_user_engine(username)
        with engine.connect() as conn:
            # 检查关键表是否存在
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'main' 
                AND table_name IN ('projects', 'accounts', 'case_clues', 'case_subjects')
            """))
            
            existing_tables = [row[0] for row in result.fetchall()]
            required_tables = ['projects', 'accounts', 'case_clues', 'case_subjects']
            
            missing_tables = set(required_tables) - set(existing_tables)
            if missing_tables:
                logger.warning(f"用户 '{username}' 缺少表: {missing_tables}")
                return False
                
        logger.info(f"✅ 用户 '{username}' 数据库验证通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 用户 '{username}' 数据库验证失败: {e}")
        return False 