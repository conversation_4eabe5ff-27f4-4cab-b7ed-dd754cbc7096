"""
标准解析器插件接口定义

所有解析器插件必须实现此接口以确保兼容性和标准化
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
import time


class PluginInterface(ABC):
    """标准解析器插件接口
    
    所有解析器插件必须继承此接口并实现所有抽象方法
    """
    
    @abstractmethod
    def get_metadata(self) -> Dict[str, Any]:
        """获取插件元信息
        
        Returns:
            Dict[str, Any]: 包含插件基本信息的字典
            {
                "name": "插件名称",
                "version": "版本号", 
                "description": "插件描述",
                "supported_formats": ["支持的格式列表"],
                "confidence_threshold": 置信度阈值
            }
        """
        pass
    
    @abstractmethod
    def validate_file(self, file_path: str) -> bool:
        """验证文件是否适用于此解析器
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            bool: 文件是否适用于此解析器
        """
        pass
    
    @abstractmethod
    def calculate_confidence(self, file_path: str) -> float:
        """计算解析置信度
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            float: 置信度分数 (0.0 - 1.0)
        """
        pass
    
    @abstractmethod
    def parse(self, file_path: str) -> Dict[str, Any]:
        """执行文件解析
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            Dict[str, Any]: 解析结果，必须包含以下字段：
            {
                "success": bool,
                "message": str,
                "accounts": List[Dict],
                "transactions": List[Dict]
            }
        """
        pass
    
    @abstractmethod
    def get_health_status(self) -> Dict[str, Any]:
        """获取插件健康状态
        
        Returns:
            Dict[str, Any]: 健康状态信息
            {
                "healthy": bool,
                "last_check": float,
                "memory_usage": str,
                "error_count": int
            }
        """
        pass
    
    # 可选的默认实现方法
    
    def initialize(self) -> bool:
        """初始化插件
        
        Returns:
            bool: 初始化是否成功
        """
        return True
    
    def cleanup(self) -> bool:
        """清理插件资源
        
        Returns:
            bool: 清理是否成功
        """
        return True
    
    def get_supported_file_types(self) -> List[str]:
        """获取支持的文件类型
        
        Returns:
            List[str]: 支持的文件扩展名列表
        """
        return ['.xlsx', '.xls']
    
    def get_configuration_schema(self) -> Dict[str, Any]:
        """获取配置模式定义
        
        Returns:
            Dict[str, Any]: 配置模式
        """
        return {
            "timeout": {"type": "int", "default": 60, "description": "解析超时时间(秒)"},
            "memory_limit": {"type": "str", "default": "512MB", "description": "内存限制"},
            "retry_count": {"type": "int", "default": 3, "description": "重试次数"}
        }


class BasePlugin(PluginInterface):
    """基础插件类
    
    提供插件的基础实现，简化插件开发
    """
    
    def __init__(self):
        self.name = self.__class__.__name__
        self.version = "1.0.0"
        self.description = "基础解析器插件"
        self.initialized = False
        self.error_count = 0
        self.last_error = None
        self.creation_time = time.time()
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取插件元信息"""
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "supported_formats": self.get_supported_formats(),
            "confidence_threshold": 0.7,
            "created_at": self.creation_time
        }
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的格式列表 - 子类应重写此方法"""
        return ["通用格式"]
    
    def validate_file(self, file_path: str) -> bool:
        """基础文件验证"""
        try:
            import os
            if not os.path.exists(file_path):
                return False
            
            file_ext = os.path.splitext(file_path)[1].lower()
            return file_ext in self.get_supported_file_types()
        except Exception:
            return False
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        return {
            "healthy": self.error_count < 10,  # 错误次数少于10次认为健康
            "last_check": time.time(),
            "memory_usage": "normal",
            "error_count": self.error_count,
            "last_error": self.last_error,
            "uptime": time.time() - self.creation_time
        }
    
    def handle_error(self, error: Exception) -> None:
        """处理错误"""
        self.error_count += 1
        self.last_error = str(error)
    
    def reset_error_count(self) -> None:
        """重置错误计数"""
        self.error_count = 0
        self.last_error = None 