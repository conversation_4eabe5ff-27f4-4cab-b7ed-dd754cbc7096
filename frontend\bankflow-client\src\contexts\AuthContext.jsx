import React, { createContext, useContext, useState, useEffect } from 'react';

/**
 * 认证上下文
 */
const AuthContext = createContext();

/**
 * 认证提供者组件
 */
export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userInfo, setUserInfo] = useState(null);
  const [loading, setLoading] = useState(true);

  /**
   * 检查登录状态
   */
  const checkAuthStatus = () => {
    try {
      const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
      const savedUserInfo = localStorage.getItem('userInfo');
      
      if (isLoggedIn && savedUserInfo) {
        const parsedUserInfo = JSON.parse(savedUserInfo);
        setIsAuthenticated(true);
        setUserInfo(parsedUserInfo);
      } else {
        setIsAuthenticated(false);
        setUserInfo(null);
      }
    } catch (error) {
      console.error('检查认证状态失败:', error);
      setIsAuthenticated(false);
      setUserInfo(null);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 登录
   */
  const login = (userData) => {
    try {
      setIsAuthenticated(true);
      setUserInfo(userData);
      localStorage.setItem('isLoggedIn', 'true');
      localStorage.setItem('userInfo', JSON.stringify(userData));
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  };

  /**
   * 登出
   */
  const logout = () => {
    try {
      setIsAuthenticated(false);
      setUserInfo(null);
      localStorage.removeItem('isLoggedIn');
      localStorage.removeItem('userInfo');
      // 保留记住的用户名
      // localStorage.removeItem('rememberedUsername');
    } catch (error) {
      console.error('登出失败:', error);
    }
  };

  /**
   * 检查是否为管理员
   */
  const isAdmin = () => {
    return userInfo?.isAdmin === true;
  };

  /**
   * 获取用户名
   */
  const getUsername = () => {
    return userInfo?.username || '';
  };

  /**
   * 获取登录时间
   */
  const getLoginTime = () => {
    return userInfo?.loginTime || '';
  };

  // 组件挂载时检查认证状态
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const value = {
    isAuthenticated,
    userInfo,
    loading,
    login,
    logout,
    isAdmin,
    getUsername,
    getLoginTime,
    checkAuthStatus
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * 使用认证上下文的Hook
 */
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext; 