#!/usr/bin/env python3
"""
保存解析结果到项目的脚本
"""
import requests
import json
import time

def save_parsed_data_to_project():
    """保存解析结果到项目"""
    
    file_path = r"F:\流水清洗\银行流水数据参考\6邮政储蓄\3.xls"
    project_id = "c33ab419-b8b8-4b8b-8b8b-8b8b8b8b8b8b"
    
    print("🔄 正在解析文件...")
    
    # 1. 解析文件
    with open(file_path, 'rb') as f:
        files = {'file': f}
        data = {
            'bank_name': '邮政储蓄银行',
            'template_id': 'psbc_format3_plugin'
        }
        
        response = requests.post(
            'http://localhost:8000/api/parser/analyze',
            files=files,
            data=data
        )
    
    if response.status_code != 200:
        print(f"❌ 解析失败: {response.status_code}")
        print(response.text)
        return False
    
    result = response.json()
    if not result.get('success'):
        print(f"❌ 解析失败: {result.get('message', '未知错误')}")
        return False
    
    parse_result = result.get('parse_result', {})
    accounts = parse_result.get('accounts', [])
    transactions = parse_result.get('transactions', [])
    
    print(f"✅ 解析成功! 账户数: {len(accounts)}, 交易数: {len(transactions)}")
    
    # 2. 保存到项目
    print("🔄 正在保存到项目...")
    
    save_data = {
        'project_id': project_id,
        'accounts': accounts,
        'transactions': transactions,
        'metadata': {
            'bank_name': '邮政储蓄银行',
            'template_id': 'psbc_format3_plugin',
            'file_name': '3.xls',
            'upload_time': time.strftime('%Y-%m-%d %H:%M:%S')
        },
        'force_overwrite': True,
        'skip_duplicates': False
    }
    
    save_response = requests.post(
        'http://localhost:8000/api/parser/save-optimized',
        headers={'Content-Type': 'application/json'},
        data=json.dumps(save_data)
    )
    
    if save_response.status_code == 200:
        save_result = save_response.json()
        if save_result.get('success'):
            print(f"✅ 数据保存成功!")
            print(f"📊 保存统计: 账户{save_result.get('accounts_saved', 0)}个, 交易{save_result.get('transactions_saved', 0)}条")
            return True
        else:
            print(f"❌ 保存失败: {save_result.get('message', '未知错误')}")
            return False
    else:
        print(f"❌ 保存请求失败: {save_response.status_code}")
        print(save_response.text)
        return False

if __name__ == "__main__":
    success = save_parsed_data_to_project()
    if success:
        print("🎉 文件上传和保存完成! 现在可以在前端查看数据了。")
    else:
        print("❌ 操作失败!")
