@echo off
:: 设置控制台编码为UTF-8
chcp 65001 > nul 2>&1
setlocal enabledelayedexpansion

echo ========================================
echo Bank Flow Analysis System - Reliable Startup v2.1
echo 银行流水分析系统 - 可靠启动脚本 v2.1
echo ========================================

:: 1. 检查基础环境
echo [1/6] Checking Environment / 检查基础环境...
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not installed or not in PATH
    echo 错误：Python 未安装或不在PATH中
    pause
    exit /b 1
)

node --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not installed or not in PATH
    echo 错误：Node.js 未安装或不在PATH中
    pause
    exit /b 1
)

:: 2. 检查端口占用
echo [2/6] Checking Port Usage / 检查端口占用...
netstat -ano | findstr ":8000" | findstr "LISTENING" > nul 2>&1
if %errorlevel% equ 0 (
    echo WARNING: Port 8000 occupied, trying to close...
    echo 警告：端口8000已被占用，尝试关闭...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":8000" ^| findstr "LISTENING" 2^>nul') do (
        taskkill /F /PID %%a > nul 2>&1
    )
)

netstat -ano | findstr ":3000" | findstr "LISTENING" > nul 2>&1
if %errorlevel% equ 0 (
    echo WARNING: Port 3000 occupied, trying to close...
    echo 警告：端口3000已被占用，尝试关闭...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":3000" ^| findstr "LISTENING" 2^>nul') do (
        taskkill /F /PID %%a > nul 2>&1
    )
)

:: 3. 启动后端
echo [3/6] Starting Backend Service / 启动后端服务...
cd /d "%~dp0backend"
if not exist "app\main.py" (
    echo ERROR: Backend files not found, please check project structure
    echo 错误：后端文件不存在，请检查项目结构
    pause
    exit /b 1
)

start "Backend-8000" cmd /k "title Backend Service - Port 8000 && python -m uvicorn app.main:app --host 127.0.0.1 --port 8000 --reload"

:: 4. 等待后端启动
echo [4/6] Waiting for Backend / 等待后端启动...
set /a counter=0
:wait_backend
timeout /t 2 /nobreak > nul
netstat -ano | findstr ":8000" | findstr "LISTENING" > nul 2>&1
if %errorlevel% equ 0 (
    echo SUCCESS: Backend service started on port 8000
    echo 成功：后端服务启动成功 端口8000
    goto backend_ready
)
set /a counter+=1
if %counter% lss 15 goto wait_backend
echo ERROR: Backend service startup timeout
echo 错误：后端服务启动超时
pause
exit /b 1

:backend_ready
:: 5. 启动前端
echo [5/6] Starting Frontend Service / 启动前端服务...
cd /d "%~dp0frontend\bankflow-client"
if not exist "package.json" (
    echo ERROR: Frontend files not found, please check project structure
    echo 错误：前端文件不存在，请检查项目结构
    pause
    exit /b 1
)

set BROWSER=none
start "Frontend-3000" cmd /k "title Frontend Service - Port 3000 && set BROWSER=none && npm start"

:: 6. 等待前端启动
echo [6/6] Waiting for Frontend / 等待前端启动...
set /a counter=0
:wait_frontend
timeout /t 3 /nobreak > nul
netstat -ano | findstr ":3000" | findstr "LISTENING" > nul 2>&1
if %errorlevel% equ 0 (
    echo SUCCESS: Frontend service started on port 3000
    echo 成功：前端服务启动成功 端口3000
    goto all_ready
)
set /a counter+=1
if %counter% lss 20 goto wait_frontend
echo ERROR: Frontend service startup timeout
echo 错误：前端服务启动超时
pause
exit /b 1

:all_ready
echo ========================================
echo SUCCESS: System Startup Complete!
echo 成功：系统启动完成！
echo Backend API: http://127.0.0.1:8000/docs
echo Frontend UI: http://localhost:3000
echo ========================================
echo NOTE: Two service windows are open, keep them running
echo 注意：两个服务窗口已打开，请保持窗口开启
echo Press any key to open frontend page...
echo 按任意键打开前端页面...
pause > nul

:: 自动打开浏览器
start http://localhost:3000

echo Services are running... Please do not close this window
echo 服务运行中... 请不要关闭此窗口
pause > nul 