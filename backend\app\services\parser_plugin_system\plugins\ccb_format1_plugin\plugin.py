"""
建设银行Format1解析器插件
支持建设银行标准Excel格式的银行流水解析
基于Legacy CCBFormat1Parser的功能，适配插件系统接口
"""

import pandas as pd
import time
import os
import traceback
from datetime import datetime
from typing import Dict, Any, List, Optional
import logging
import json
from pathlib import Path
import re

# 导入基础插件接口
try:
    from ...core.plugin_interface import BasePlugin
except ImportError:
    # 如果在测试环境中，创建一个简单的基础类
    class BasePlugin:
        def __init__(self):
            self.name = "default_plugin"
            self.version = "1.0.0"
            self.description = "默认解析器插件"
            self.bank_name = "通用银行"
            self.format_type = "standard"

logger = logging.getLogger(__name__)

class Plugin(BasePlugin):
    """建设银行Format1解析器插件"""
    
    def __init__(self, file_path: str = None):
        super().__init__()
        self.name = "ccb_format1_plugin"
        self.version = "1.0.0"
        self.description = "建设银行Format1解析器插件"
        self.bank_name = "中国建设银行"
        self.format_type = "format1"
        self.start_time = time.time()
        self.error_count = 0
        self.file_path = file_path
        
        # 加载配置
        self.config = self._load_config()
        
        # 解析结果
        self.accounts = []
        self.transactions = []
        
        # 建设银行特定的字段映射
        self.field_mapping = {
            'date_field': '交易日期',
            'time_field': '交易时间', 
            'amount_field': '交易金额',
            'balance_field': '账户余额',
            'summary_field': '摘要',
            'remark_field': '扩充备注',
            'cardholder_field': '客户名称',
            'card_field': '交易卡号',
            'direction_field': '借贷方向',
            'account_field': '账号',
            'opposite_account_field': '对方账号',
            'opposite_name_field': '对方户名'
        }
        
        logger.info(f"✅ {self.name} v{self.version} 初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载插件配置"""
        try:
            config_path = Path(__file__).parent / "config.json"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"加载配置失败: {e}")
        
        # 默认配置
        return {
            "enabled": True,
            "confidence_threshold": 0.7,
            "plugin_settings": {
                "max_file_size": "100MB",
                "supported_extensions": [".xlsx", ".xls"]
            }
        }
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取插件元信息"""
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "supported_formats": ["中国建设银行Excel格式"],
            "confidence_threshold": self.config.get("confidence_threshold", 0.7),
            "bank_name": self.bank_name,
            "format_type": self.format_type
        }
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的格式列表"""
        return ["建设银行Excel格式", "CCB_Format1"]
    
    def validate_file(self, file_path: str) -> bool:
        """验证文件是否适用于此解析器"""
        try:
            if not os.path.exists(file_path):
                return False
            
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in ['.xlsx', '.xls']:
                return False
            
            # 尝试读取文件并检查是否包含建设银行特征
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            
            # 检查工作表内容是否包含建设银行特征
            for sheet_name in sheet_names:
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=20)
                    if df.empty:
                        continue
                    
                    # 转换为字符串进行特征检测
                    content = df.to_string()
                    
                    # 建设银行特征关键词
                    ccb_keywords = ["建设银行", "交易日期", "交易金额", "借贷方向", "客户名称"]
                    matches = sum(1 for keyword in ccb_keywords if keyword in content)
                    
                    if matches >= 3:
                        return True
                        
                except Exception:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"文件验证失败: {e}")
            return False
    
    def calculate_confidence(self, file_path: str) -> float:
        """计算解析置信度"""
        try:
            if not self.validate_file(file_path):
                return 0.0
            
            confidence = 0.0
            
            # 文件扩展名检查
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext in ['.xlsx', '.xls']:
                confidence += 20.0
            
            # 内容特征检查
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            
            max_sheet_confidence = 0.0
            
            for sheet_name in sheet_names:
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=50)
                    if df.empty:
                        continue
                    
                    sheet_confidence = 0.0
                    content = df.to_string()
                    
                    # 建设银行特征词汇
                    if "建设银行" in content or "CCB" in content:
                        sheet_confidence += 25.0
                    
                    # 必要字段检查
                    required_fields = ["交易日期", "交易金额", "借贷方向"]
                    field_matches = sum(1 for field in required_fields if field in content)
                    sheet_confidence += (field_matches / len(required_fields)) * 30.0
                    
                    # 可选字段检查
                    optional_fields = ["客户名称", "账号", "摘要", "账户余额"]
                    optional_matches = sum(1 for field in optional_fields if field in content)
                    sheet_confidence += (optional_matches / len(optional_fields)) * 15.0
                    
                    # 数据格式检查
                    date_columns = [col for col in df.columns if "日期" in str(col)]
                    amount_columns = [col for col in df.columns if "金额" in str(col)]
                    
                    if date_columns and amount_columns:
                        sheet_confidence += 10.0
                    
                    max_sheet_confidence = max(max_sheet_confidence, sheet_confidence)
                    
                except Exception:
                    continue
            
            confidence += max_sheet_confidence
            
            return min(confidence, 100.0)
            
        except Exception as e:
            logger.error(f"置信度计算失败: {e}")
            return 0.0
    
    def _clean_field(self, value) -> str:
        """清理字段值"""
        if value is None or pd.isna(value):
            return ""
        return str(value).strip().replace('\t', '').replace('\n', '').replace('\r', '')
    
    def _clean_amount_string(self, value) -> str:
        """清理金额字符串"""
        if value is None:
            return ""
        
        clean_value = str(value).replace(" ", "").replace(",", "").replace("，", "").replace("\t", "").replace("\n", "").replace("\r", "").strip()
        clean_value = clean_value.replace("¥", "").replace("$", "").replace("€", "").replace("￥", "")
        
        return clean_value
    
    def _parse_amount(self, amount_str: str, dr_cr_flag: str = "") -> float:
        """解析金额"""
        if not amount_str:
            return 0.0
        
        clean_amount = self._clean_amount_string(amount_str)
        
        try:
            amount = float(clean_amount)
            
            # 根据借贷标志调整正负
            if dr_cr_flag == "借":  # 借方=支出，转为负数
                return -abs(amount)
            elif dr_cr_flag == "贷":  # 贷方=收入，保持正数
                return abs(amount)
            else:
                return amount
        except (ValueError, TypeError):
            logger.warning(f"无法解析金额: {amount_str}")
            return 0.0
    
    def _standardize_time_format(self, date_str: str, time_str: str = "") -> str:
        """标准化时间格式"""
        if not date_str:
            return ""
        
        try:
            date_clean = self._clean_field(date_str)
            time_clean = self._clean_field(time_str) if time_str else ""
            
            if not time_clean and ' ' in date_clean:
                parts = date_clean.split(' ', 1)
                date_clean = parts[0]
                time_clean = parts[1] if len(parts) > 1 else ""
            
            if date_clean:
                date_obj = pd.to_datetime(date_clean).date()
                formatted_date = date_obj.strftime("%Y-%m-%d")
            else:
                formatted_date = ""
            
            if time_clean:
                time_clean = time_clean.replace('.', ':')
                if len(time_clean.split(':')) == 2:
                    time_clean += ":00"
                formatted_time = time_clean
            else:
                formatted_time = "00:00:00"
            
            return f"{formatted_date} {formatted_time}"
            
        except Exception as e:
            logger.warning(f"时间格式化失败: {date_str} {time_str}, 错误: {str(e)}")
            return date_str
    
    def _find_data_start_row(self, df: pd.DataFrame) -> int:
        """查找数据开始行"""
        for i, row in df.iterrows():
            row_str = ' '.join([str(cell) for cell in row if pd.notna(cell)])
            if '交易日期' in row_str and '交易金额' in row_str:
                logger.info(f"在第{i+1}行找到数据表头")
                return i
        
        logger.warning("未找到数据表头，使用默认第0行")
        return 0
    
    def _extract_account_info(self, df: pd.DataFrame) -> tuple:
        """从DataFrame中提取账户信息"""
        account_name = ""
        account_number = ""
        card_number = ""
        
        if not df.empty:
            if self.field_mapping['cardholder_field'] in df.columns:
                first_valid_name = df[self.field_mapping['cardholder_field']].dropna().iloc[0] if not df[self.field_mapping['cardholder_field']].dropna().empty else ""
                account_name = self._clean_field(first_valid_name)
            
            if self.field_mapping['account_field'] in df.columns:
                first_valid_account = df[self.field_mapping['account_field']].dropna().iloc[0] if not df[self.field_mapping['account_field']].dropna().empty else ""
                account_number = self._clean_field(first_valid_account)
            
            card_number = ""  # 建设银行原表无卡号字段
        
        return account_name, account_number, card_number
    
    def _process_transaction_data(self, df: pd.DataFrame, account_name: str, account_number: str, card_number: str) -> None:
        """处理交易数据"""
        transactions_added = 0
        
        for _, row in df.iterrows():
            # 提取交易数据
            trade_date = self._clean_field(row.get(self.field_mapping['date_field'], ''))
            trade_time = self._clean_field(row.get(self.field_mapping['time_field'], ''))
            amount_str = self._clean_field(row.get(self.field_mapping['amount_field'], ''))
            balance_str = self._clean_field(row.get(self.field_mapping['balance_field'], ''))
            summary = self._clean_field(row.get(self.field_mapping['summary_field'], ''))
            remark = self._clean_field(row.get(self.field_mapping['remark_field'], ''))
            direction = self._clean_field(row.get(self.field_mapping['direction_field'], ''))
            opposite_account = self._clean_field(row.get(self.field_mapping['opposite_account_field'], ''))
            opposite_name = self._clean_field(row.get(self.field_mapping['opposite_name_field'], ''))
            
            # 动态提取每条交易的持卡人和账户信息
            current_cardholder = self._clean_field(row.get(self.field_mapping['cardholder_field'], ''))
            current_account = self._clean_field(row.get(self.field_mapping['account_field'], ''))
            
            actual_cardholder = current_cardholder if current_cardholder else account_name
            actual_account = current_account if current_account else account_number
            
            # 跳过无效行
            if not trade_date or not amount_str:
                continue
            
            # 解析金额和余额
            amount = self._parse_amount(amount_str, direction)
            balance = self._parse_amount(balance_str) if balance_str else 0.0
            
            # 处理收支符号
            dr_cr_flag = "收" if direction in ["贷", "收入"] else "支"
            
            # 标准化时间格式
            standardized_datetime = self._standardize_time_format(trade_date, trade_time)
            formatted_date = trade_date
            formatted_time = trade_time
            
            # 构建交易记录
            transaction = {
                'sequence_number': len(self.transactions) + 1,
                'transaction_datetime': standardized_datetime,
                'transaction_date': formatted_date,
                'transaction_time': formatted_time,
                'transaction_amount': amount,
                'amount': amount,
                'balance_amount': balance,
                'balance': balance,
                'transaction_method': summary,
                'summary': summary,
                'remark1': remark,
                'remark': remark,
                'dr_cr_flag': dr_cr_flag,
                'direction': direction,
                'counterparty_account': opposite_account,
                'opposite_account': opposite_account,
                'counterparty_name': opposite_name,
                'opposite_name': opposite_name,
                'holder_name': actual_cardholder,
                'cardholder_name': actual_cardholder,
                'account_number': actual_account,
                'card_number': card_number,
                'bank_name': '中国建设银行',
                'currency': 'CNY'
            }
            
            self.transactions.append(transaction)
            transactions_added += 1
        
        logger.info(f"成功处理 {transactions_added} 条交易记录")
    
    def _process_sheet(self, sheet_name: str) -> None:
        """处理单个工作表"""
        try:
            logger.info(f"分析工作表: {sheet_name}")
            
            df = pd.read_excel(self.file_path, sheet_name=sheet_name)
            
            if df.empty:
                logger.warning(f"工作表 {sheet_name} 为空")
                return
            
            # 查找数据开始行
            data_start_row = self._find_data_start_row(df)
            
            # 重新读取，从数据开始行作为表头
            df = pd.read_excel(self.file_path, sheet_name=sheet_name, header=data_start_row)
            
            logger.info(f"从工作表'{sheet_name}'提取到{len(df)}行数据")
            
            # 验证必要字段
            required_fields = [self.field_mapping['date_field'], self.field_mapping['amount_field']]
            missing_fields = [field for field in required_fields if field not in df.columns]
            
            if missing_fields:
                logger.error(f"工作表 {sheet_name} 缺少必需字段: {missing_fields}")
                return
            
            # 提取账户信息
            account_name, account_number, card_number = self._extract_account_info(df)
            
            # 添加账户记录
            if account_name:
                account = {
                    'holder_name': account_name,
                    'cardholder_name': account_name,
                    'account_number': account_number,
                    'card_number': card_number,
                    'bank_name': '中国建设银行',
                    'account_type': '个人账户'
                }
                self.accounts.append(account)
                logger.info(f"找到账户: {account_name} - {account_number or card_number}")
            
            # 处理交易数据
            self._process_transaction_data(df, account_name, account_number, card_number)
            
        except Exception as e:
            logger.error(f"处理工作表 {sheet_name} 时出错: {str(e)}")
    
    def parse(self, file_path: str = None) -> Dict[str, Any]:
        """执行解析"""
        try:
            # 使用传入的文件路径或实例的文件路径
            if file_path:
                self.file_path = file_path
            elif not self.file_path:
                raise ValueError("未设置文件路径")
            
            logger.info(f"🔍 开始解析建设银行Format1文件: {os.path.basename(self.file_path)}")
            
            # 验证文件
            if not self.validate_file(self.file_path):
                raise ValueError("文件验证失败")
            
            # 重置状态
            self.accounts = []
            self.transactions = []
            
            if not os.path.exists(self.file_path):
                return {
                    'success': False,
                    'error': f'文件不存在: {self.file_path}',
                    'accounts': [],
                    'transactions_by_account': {}
                }
            
            # 获取所有工作表
            excel_file = pd.ExcelFile(self.file_path)
            sheet_names = excel_file.sheet_names
            logger.info(f"发现工作表: {sheet_names}")
            
            # 处理每个工作表
            for sheet_name in sheet_names:
                self._process_sheet(sheet_name)
            
            # 构建解析结果
            result = self._build_result()
            
            logger.info(f"✅ 解析完成: 共 {len(self.accounts)} 个账户, {len(self.transactions)} 条交易")
            return result
            
        except Exception as e:
            logger.error(f"文件解析失败: {str(e)}")
            self.handle_error(e)
            return {
                'success': False,
                'error': str(e),
                'accounts': [],
                'transactions_by_account': {},
                'summary': {'total_accounts': 0, 'total_transactions': 0},
                'metadata': {
                    'plugin_name': self.name,
                    'plugin_version': self.version,
                    'error_details': str(e)
                }
            }
    
    def _calculate_confidence_score(self, accounts: List[Dict], transactions: List[Dict]) -> float:
        """计算置信度分数"""
        score = 0.0
        
        if accounts:
            score += 20
        if transactions:
            score += 20
        
        if accounts:
            account = accounts[0]
            if account.get('holder_name'):
                score += 15
            if account.get('account_number') or account.get('card_number'):
                score += 10
        
        if transactions:
            valid_transactions = 0
            for trans in transactions[:10]:
                if trans.get('transaction_date') and trans.get('amount'):
                    valid_transactions += 1
            
            if valid_transactions >= 5:
                score += 15
            elif valid_transactions >= 2:
                score += 10
            else:
                score += 5
        
        return min(score, 100.0)
    
    def _build_result(self) -> Dict[str, Any]:
        """构建返回结果"""
        confidence_score = self._calculate_confidence_score(self.accounts, self.transactions)
        
        # 按账户分组交易记录
        transactions_by_account = {}
        corrected_accounts = []
        
        # 为每条交易分配序号并按账户分组
        for index, transaction in enumerate(self.transactions, 1):
            transaction['sequence_number'] = index
            
            cardholder_name = transaction['cardholder_name']
            account_number = transaction['account_number']
            group_key = f"{cardholder_name}_{account_number}" if account_number else cardholder_name
            
            if group_key not in transactions_by_account:
                transactions_by_account[group_key] = []
                
                account_data = {
                    'account_number': account_number,
                    'card_number': transaction['card_number'],
                    'holder_name': cardholder_name,
                    'cardholder_name': cardholder_name,
                    'bank_name': '中国建设银行',
                    'account_type': '个人账户',
                    'transactions_count': 0,
                    'total_inflow': 0.0,
                    'total_outflow': 0.0,
                    'date_range': '',
                    'account_id': account_number,
                    'account_name': cardholder_name,
                    'currency': 'CNY',
                    'is_primary': len(corrected_accounts) == 0
                }
                corrected_accounts.append(account_data)
                logger.info(f"创建账户记录: {cardholder_name} - {account_number}")
            
            transactions_by_account[group_key].append(transaction)
        
        # 计算每个账户的统计信息
        for account in corrected_accounts:
            cardholder_name = account['cardholder_name']
            account_number = account['account_number']
            group_key = f"{cardholder_name}_{account_number}" if account_number else cardholder_name
            account_transactions = transactions_by_account.get(group_key, [])
            
            account['transactions_count'] = len(account_transactions)
            
            total_inflow = sum(t['transaction_amount'] for t in account_transactions if t['dr_cr_flag'] == '收')
            total_outflow = sum(t['transaction_amount'] for t in account_transactions if t['dr_cr_flag'] == '支')
            
            account['total_inflow'] = total_inflow
            account['total_outflow'] = total_outflow
            
            if account_transactions:
                dates = [t['transaction_date'] for t in account_transactions if t['transaction_date']]
                if dates:
                    min_date = min(dates)
                    max_date = max(dates)
                    account['date_range'] = f"{min_date} 至 {max_date}"
            
            logger.info(f"账户 {cardholder_name} ({account_number}): {account['transactions_count']}笔交易")
        
        # 构建汇总信息
        summary = {
            'total_accounts': len(corrected_accounts),
            'total_transactions': len(self.transactions),
            'confidence_score': confidence_score
        }
        
        if corrected_accounts:
            first_account = corrected_accounts[0]
            summary.update({
                'account_name': first_account.get('cardholder_name', ''),
                'account_number': first_account.get('account_number', ''),
                'card_number': '',
                'total_inflow': first_account.get('total_inflow', 0.0),
                'total_outflow': first_account.get('total_outflow', 0.0),
                'date_range': first_account.get('date_range', '')
            })
        
        logger.info(f"解析完成: {len(corrected_accounts)}个账户，{len(self.transactions)}条交易记录")
        
        return {
            'success': True,
            'message': f'成功解析 {len(corrected_accounts)} 个账户，{len(self.transactions)} 条交易记录',
            'summary': summary,
            'accounts': corrected_accounts,
            'transactions': self.transactions,
            'transactions_by_account': transactions_by_account,
            'confidence': confidence_score,
            'metadata': {
                'plugin_name': self.name,
                'plugin_version': self.version,
                'parser_type': 'CCB_Format1_Plugin',
                'total_accounts': len(corrected_accounts),
                'total_transactions': len(self.transactions),
                'confidence_score': confidence_score,
                'file_path': self.file_path,
                'parse_time': datetime.now().isoformat()
            }
        }
    
    def handle_error(self, error: Exception, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理错误"""
        logger.error(f"CCBFormat1插件错误: {str(error)}")
        if context:
            logger.error(f"错误上下文: {context}")
        
        self.error_count += 1
        self.last_error = str(error)
        
        return {
            'success': False,
            'error': f"CCBFormat1插件处理失败: {str(error)}",
            'plugin_name': self.name,
            'context': context or {}
        } 