#!/usr/bin/env python3
"""
中国银行Format1解析器插件 - 根据用户要求修复版

支持解析中国银行流水文件，正确处理：
1. 持卡人姓名：只提取中文姓名，跳过身份证号行
2. 卡号格式：保持文本格式，避免科学计数法
3. 交易金额统计：正确统计收入和支出
4. 时间提取：正确提取交易时间
5. 字段映射：按照用户截图的字段结构映射

版本: 3.0.0
作者: 银行流水分析系统开发团队
"""

import os
import logging
import pandas as pd
import numpy as np
import re
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from collections import Counter
import traceback

# 修复导入路径（优先绝对导入，回退相对导入，最后兜底轻量基类）
try:
    from app.services.parser_plugin_system.core.plugin_interface import BasePlugin  # 绝对导入，容器环境更稳
except Exception:
    try:
        from ...core.plugin_interface import BasePlugin  # 相对导入回退
    except Exception:
        class BasePlugin:  # 兜底轻量基类（仅用于预解析/实例创建）
            def __init__(self):
                self.name = "boc_format1_plugin"
                self.version = "3.0.0"
                self.description = "中国银行Format1解析器插件"
                self.bank_name = "中国银行"

# 配置日志
logger = logging.getLogger(__name__)

class BOCFormat1Parser(BasePlugin):
    """中国银行Format1解析器 - 根据用户要求修复版"""

    def __init__(self, file_path: str = None):
        super().__init__()
        self.plugin_id = "boc_format1_plugin"
        self.plugin_name = "中国银行Format1解析器插件"
        self.supported_banks = ["中国银行"]
        self.version = "3.0.0"
        self.description = "解析中国银行Format1格式的流水文件，正确处理持卡人姓名、卡号格式、交易金额统计等"
        self.file_path = file_path

        logger.info(f"🚀 BOC_FORMAT1_PLUGIN 初始化 {self.plugin_name} v{self.version}")

    def _is_valid_chinese_name(self, name: str) -> bool:
        """检查是否为有效的中文姓名"""
        if not name or pd.isna(name):
            return False

        name_str = str(name).strip()

        # 跳过身份证号（18位数字字母组合）
        if re.match(r'^\d{17}[\dX]$', name_str):
            return False

        # 检查是否为2-4位中文字符
        if re.match(r'^[\u4e00-\u9fff]{2,4}$', name_str):
            return True

        return False

    def _format_card_number(self, card_number: Any) -> str:
        """格式化卡号，确保为文本格式"""
        if pd.isna(card_number) or card_number is None:
            return ""

        # 转换为字符串，避免科学计数法
        card_str = str(card_number).strip()

        # 如果是浮点数格式，去掉小数点
        if '.' in card_str and card_str.replace('.', '').isdigit():
            card_str = card_str.split('.')[0]

        # 检查是否为有效卡号（13-19位数字）
        if re.match(r'^\d{13,19}$', card_str):
            return card_str

        return ""

    def _find_all_valid_sheets(self, excel_file, engine: str = 'openpyxl') -> List[str]:
        """
        收集所有符合条件的工作表：
        - 必须包含“交易金额”“交易后余额”；优先包含“借贷标识”
        - 用有效行数排序，返回全量候选（用于多子表聚合）
        """
        try:
            logger.info("🔍 收集所有符合条件的工作表用于聚合...")
            candidates = []  # (sheet_name, valid_rows, has_drcr)
            for sheet_name in excel_file.sheet_names:
                try:
                    df_raw = pd.read_excel(excel_file, sheet_name=sheet_name, header=None, dtype=str, engine=engine)
                    head = df_raw.head(10).astype(str)
                    has_amount_kw = (head.apply(lambda s: s.str.contains('交易金额|金额', na=False))).any().any()
                    has_balance_kw = (head.apply(lambda s: s.str.contains('交易后余额|余额', na=False))).any().any()
                    has_drcr_kw = (head.apply(lambda s: s.str.contains('借贷标识|借贷标志|收支符号|收支', na=False))).any().any()
                    if not (has_amount_kw and has_balance_kw):
                        continue
                    header_row = self._detect_header_row(df_raw)
                    df = pd.read_excel(excel_file, sheet_name=sheet_name, header=header_row, dtype=str, engine=engine)
                    mapping = self._find_column_mapping(df)
                    # 新线硬条件：交易金额 + 交易后余额 + 主账户账号
                    if ('transaction_amount' not in mapping or 'account_balance' not in mapping or not mapping.get('_has_primary_account')):
                        continue
                    amt_col = mapping['transaction_amount']
                    bal_col = mapping['account_balance']
                    pair = df.iloc[:, [amt_col, bal_col]].astype(str)
                    valid_rows = 0
                    for i in range(len(pair)):
                        a = str(pair.iloc[i, 0]).strip()
                        b = str(pair.iloc[i, 1]).strip()
                        if a and a != 'nan' and b and b != 'nan':
                            valid_rows += 1
                    candidates.append((sheet_name, valid_rows, has_drcr_kw))
                    logger.info(f"📊 子表评分: {sheet_name} 有效行={valid_rows}, 含借贷标识={has_drcr_kw}")
                except Exception as e:
                    logger.warning(f"⚠️ 子表评分失败 {sheet_name}: {e}")
                    continue
            if not candidates:
                logger.error("❌ 未找到包含交易金额/交易后余额的工作表")
                return []
            # 优先含借贷标识，再按有效行数降序
            candidates.sort(key=lambda x: (not x[2], -x[1]))
            return [c[0] for c in candidates]
        except Exception as e:
            logger.error(f"❌ 收集有效工作表时出错: {e}")
            return []

    def _find_valid_sheet(self, excel_file, engine: str = 'openpyxl') -> Optional[str]:
        """
        兼容旧逻辑：选择一个最佳子表（保留给 1.xlsx 场景）
        """
        try:
            logger.info("🔍 开始评分选择最佳工作表...")
            candidates = []  # (sheet_name, valid_rows, has_drcr)
            for sheet_name in excel_file.sheet_names:
                try:
                    df_raw = pd.read_excel(excel_file, sheet_name=sheet_name, header=None, dtype=str, engine=engine)
                    head = df_raw.head(10).astype(str)
                    has_amount_kw = (head.apply(lambda s: s.str.contains('交易金额', na=False)).any()).any()
                    has_balance_kw = (head.apply(lambda s: s.str.contains('交易后余额', na=False)).any()).any()
                    has_drcr_kw = (head.apply(lambda s: s.str.contains('借贷标识', na=False)).any()).any()
                    if not (has_amount_kw and has_balance_kw):
                        continue

                    header_row = self._detect_header_row(df_raw)
                    # 🔧 轻量级预解析：只读取前20行进行快速评估
                    df = pd.read_excel(excel_file, sheet_name=sheet_name, header=header_row, dtype=str, nrows=20, engine=engine)
                    mapping = self._find_column_mapping(df)
                    # 新线硬条件：交易金额 + 交易后余额 + 主账户账号
                    if ('transaction_amount' not in mapping or 'account_balance' not in mapping or not mapping.get('_has_primary_account')):
                        continue

                    amt_col = mapping['transaction_amount']
                    bal_col = mapping['account_balance']
                    pair = df.iloc[:, [amt_col, bal_col]].astype(str)
                    valid_rows = 0
                    # 🔧 轻量级预解析：只检查前20行
                    for i in range(min(20, len(pair))):
                        a = str(pair.iloc[i, 0]).strip()
                        b = str(pair.iloc[i, 1]).strip()
                        if a and a != 'nan' and b and b != 'nan':
                            valid_rows += 1
                    candidates.append((sheet_name, valid_rows, has_drcr_kw))
                    logger.info(f"📊 子表评分: {sheet_name} 有效行={valid_rows}, 含借贷标识={has_drcr_kw}")
                except Exception as e:
                    logger.warning(f"⚠️ 子表评分失败 {sheet_name}: {e}")
                    continue

            if not candidates:
                logger.error("❌ 未找到包含交易金额/交易后余额(及借贷标识)的工作表")
                return None

            with_drcr = [c for c in candidates if c[2]]
            pool = with_drcr if with_drcr else candidates
            best = max(pool, key=lambda x: x[1])
            logger.info(f"✅ 选择最佳工作表: {best[0]} (有效行={best[1]}, 含借贷标识={best[2]})")
            return best[0]
        except Exception as e:
            logger.error(f"❌ 查找有效工作表时出错: {e}")
            return None
        """
        评分选择最佳工作表：
        - 优先必须包含“交易金额”“交易后余额”“借贷标识”
        - 在候选中选择“有效行数最多”的子表；若没有借贷标识，则在金额+余额命中的子表中择优
        有效行：金额和余额均非空（可解析为数字）
        """
        try:
            logger.info("🔍 开始评分选择最佳工作表...")
            candidates = []  # (sheet_name, valid_rows, has_drcr)
            for sheet_name in excel_file.sheet_names:
                try:
                    df_raw = pd.read_excel(excel_file, sheet_name=sheet_name, header=None, dtype=str, engine=engine)
                    head = df_raw.head(10).astype(str)
                    has_amount_kw = (head.apply(lambda s: s.str.contains('交易金额|金额', na=False))).any().any()
                    has_balance_kw = (head.apply(lambda s: s.str.contains('交易后余额|余额', na=False))).any().any()
                    has_drcr_kw = (head.apply(lambda s: s.str.contains('借贷标识|借贷标志|收支符号|收支', na=False))).any().any()
                    if not (has_amount_kw and has_balance_kw):
                        continue

                    header_row = self._detect_header_row(df_raw)
                    df = pd.read_excel(excel_file, sheet_name=sheet_name, header=header_row, dtype=str, engine=engine)
                    mapping = self._find_column_mapping(df)
                    # 新线硬条件：交易金额 + 交易后余额 + 主账户账号
                    if ('transaction_amount' not in mapping or 'account_balance' not in mapping or not mapping.get('_has_primary_account')):
                        continue

                    amt_col = mapping['transaction_amount']
                    bal_col = mapping['account_balance']
                    pair = df.iloc[:, [amt_col, bal_col]].astype(str)
                    valid_rows = 0
                    for i in range(len(pair)):
                        a = str(pair.iloc[i, 0]).strip()
                        b = str(pair.iloc[i, 1]).strip()
                        if a and a != 'nan' and b and b != 'nan':
                            valid_rows += 1
                    candidates.append((sheet_name, valid_rows, has_drcr_kw))
                    logger.info(f"📊 子表评分: {sheet_name} 有效行={valid_rows}, 含借贷标识={has_drcr_kw}")
                except Exception as e:
                    logger.warning(f"⚠️ 子表评分失败 {sheet_name}: {e}")
                    continue

            if not candidates:
                logger.error("❌ 未找到包含交易金额/交易后余额(及借贷标识)的工作表")
                return None

            with_drcr = [c for c in candidates if c[2]]
            pool = with_drcr if with_drcr else candidates
            best = max(pool, key=lambda x: x[1])
            logger.info(f"✅ 选择最佳工作表: {best[0]} (有效行={best[1]}, 含借贷标识={best[2]})")
            return best[0]
        except Exception as e:
            logger.error(f"❌ 查找有效工作表时出错: {e}")
            return None

    def _get_excel_engine(self, file_path: str) -> str:
        """根据文件扩展名选择合适的Excel引擎"""
        if file_path.lower().endswith('.xls'):
            return 'xlrd'  # 使用xlrd 1.2.0支持.xls
        else:
            return 'openpyxl'  # 使用openpyxl支持.xlsx

    def parse_file(self, file_path: str) -> Dict[str, Any]:
        """
        解析中国银行Format1格式文件

        Args:
            file_path: 文件路径

        Returns:
            Dict: 解析结果
        """
        try:
            logger.info(f"🚀 BOC_FORMAT1_PLUGIN 开始解析文件: {file_path}")

            # 根据文件扩展名选择合适的引擎
            engine = self._get_excel_engine(file_path)
            logger.info(f"📊 使用Excel引擎: {engine}")

            # 读取Excel文件，确保卡号等数字字段保持文本格式
            excel_file = pd.ExcelFile(file_path, engine=engine)
            logger.info(f"📊 BOC_FORMAT1_PLUGIN 发现 {len(excel_file.sheet_names)} 个工作表: {excel_file.sheet_names}")

            # 收集所有有效子表（用于 2.xlsx 场景：每子表=一个客户），否则回退到单一最佳子表
            valid_sheets = self._find_all_valid_sheets(excel_file, engine)
            if not valid_sheets:
                best_sheet = self._find_valid_sheet(excel_file, engine)
                if not best_sheet:
                    raise ValueError("未找到包含交易金额和交易后余额字段的工作表")
                valid_sheets = [best_sheet]

            merged_accounts = {}
            all_transactions = []

            for valid_sheet in valid_sheets:
                raw_df = pd.read_excel(file_path, sheet_name=valid_sheet, header=None, dtype=str, engine=engine)
                header_row = self._detect_header_row(raw_df)
                df = pd.read_excel(file_path, sheet_name=valid_sheet, header=header_row, dtype=str, engine=engine)
                logger.info(f"🔍 BOC_FORMAT1_PLUGIN 处理工作表: {valid_sheet}, 形状: {df.shape}, 表头行: {header_row}")

                if df.empty:
                    logger.warning(f"⚠️ 工作表 {valid_sheet} 为空，跳过")
                    continue

                accounts, transactions, diagnostics = self._parse_sheet_data(df, valid_sheet)
                logger.info(f"✅ 工作表 {valid_sheet} 解析完成: {len(accounts)}个账户, {len(transactions)}条交易")

                # 合并账户：以 持卡人+账号+数据源 作为合并键
                for a in accounts:
                    key = f"{a['holder_name']}_{a['account_number']}_{a['data_source']}"
                    if key not in merged_accounts:
                        merged_accounts[key] = a
                    else:
                        # 汇总收入、支出与交易数；期末余额取较新的 latest
                        merged_accounts[key]['total_inflow'] += a.get('total_inflow', 0.0)
                        merged_accounts[key]['total_outflow'] += a.get('total_outflow', 0.0)
                        merged_accounts[key]['transaction_count'] += a.get('transaction_count', 0)
                all_transactions.extend(transactions)

            result = {
                'success': True,
                'accounts': list(merged_accounts.values()),
                'transactions': all_transactions,
                'summary': {
                    'total_accounts': len(merged_accounts),
                    'total_transactions': len(all_transactions),
                    'file_path': file_path,
                    'parsed_at': datetime.now().isoformat(),
                    'sheets': valid_sheets,
                    'diagnostics': diagnostics
                }
            }

            logger.info(f"✅ 解析完成: {len(accounts)}个账户, {len(transactions)}条交易记录")
            return result

        except Exception as e:
            logger.error(f"❌ 解析文件失败: {str(e)}")
            logger.error(f"❌ 错误详情: {traceback.format_exc()}")
            return {
                'success': False,
                'error': str(e),
                'accounts': [],
                'transactions': [],
                'summary': {
                    'total_accounts': 0,
                    'total_transactions': 0,
                    'file_path': file_path,
                    'parsed_at': datetime.now().isoformat()
                }
            }

    def _parse_sheet_data(self, df: pd.DataFrame, sheet_name: str) -> tuple[List[Dict], List[Dict], Dict[str, Any]]:
        """根据用户要求解析工作表数据 - 只处理有交易金额和交易后余额的表"""
        try:
            logger.info(f"📋 解析工作表 {sheet_name}，列数: {len(df.columns)}")
            logger.info(f"📋 列名: {list(df.columns)}")

            # 诊断统计
            diag = {
                'sheet': sheet_name,
                'rows_total': int(df.shape[0]),
                'rows_parsed': 0,
                'skipped_no_cardholder': 0,
                'skipped_no_account': 0,
                'skipped_no_date': 0,
                'skipped_other_error': 0,
                'header_row_guess': None,
                'column_mapping': {},
            }

            # 查找关键列的索引
            column_mapping = self._find_column_mapping(df)
            logger.info(f"📋 字段映射: {column_mapping}")
            diag['column_mapping'] = column_mapping

            # 检查是否满足“新线”子表的三项硬条件：交易金额、交易后余额、主账户账号
            if ('transaction_amount' not in column_mapping or
                'account_balance' not in column_mapping or
                not column_mapping.get('_has_primary_account')):
                logger.warning(f"⚠️ 工作表 {sheet_name} 不满足新线抽取条件（需含'交易金额'、'交易后余额'、'主账户账号'），跳过")
                return [], [], diag

            accounts = {}
            transactions = []
            sequence_counter = 0
            # 前向填充所需的最近非空值（应对“新线”只在组头出现持卡人/账号的情况）
            last_cardholder_name = ""
            last_account_number = ""
            last_card_number = ""
            last_transaction_date: Optional[datetime] = None
            last_group_key: Optional[str] = None
            # 账号前向填充限定到同一持卡人范围，避免跨持卡人串号
            last_account_number_by_holder: Dict[str, str] = {}

            for idx, row in df.iterrows():
                try:
                    # 提取持卡人姓名 - 据实列出，包括中文姓名和身份证号
                    cardholder_name = self._extract_cardholder_name(row, column_mapping)
                    # 前向填充“申请查询信息”
                    if not cardholder_name and last_cardholder_name:
                        cardholder_name = last_cardholder_name
                    elif cardholder_name:
                        last_cardholder_name = cardholder_name
                    if not cardholder_name:
                        cardholder_name = sheet_name
                        last_cardholder_name = cardholder_name
                    logger.debug(f"🔍 第{idx}行持卡人姓名: '{cardholder_name}'")

                    # 提取账号
                    account_number = self._extract_account_number(row, column_mapping)
                    # 前向填充账号（限定在同一持卡人内）
                    holder_key = cardholder_name or "__UNKNOWN__"
                    if not account_number:
                        if holder_key in last_account_number_by_holder:
                            account_number = last_account_number_by_holder[holder_key]
                    else:
                        last_account_number_by_holder[holder_key] = account_number
                        last_account_number = account_number
                    if not account_number:
                        diag['skipped_no_account'] += 1
                        continue

                    # 提取卡号（保持文本格式）
                    card_number = self._extract_card_number(row, column_mapping)

                    # 提取交易日期
                    transaction_date = self._extract_transaction_date(row, column_mapping)
                    # 日期前向填充：大量明细行不重复“交易日期”，使用上一条的日期
                    if not transaction_date and last_transaction_date is not None:
                        transaction_date = last_transaction_date
                    if not transaction_date:
                        diag['skipped_no_date'] += 1
                        continue
                    else:
                        last_transaction_date = transaction_date

                    # 提取交易时间
                    transaction_time = self._extract_transaction_time(row, column_mapping)

                    # 提取交易金额
                    transaction_amount = self._extract_transaction_amount(row, column_mapping)

                    # 提取交易余额
                    account_balance = self._extract_account_balance(row, column_mapping)

                    # 提取交易方式
                    transaction_method = self._extract_transaction_method(row, column_mapping)

                    # 提取收支符号
                    dr_cr_flag = self._extract_dr_cr_flag(row, column_mapping, transaction_amount)

                    # 提取对方信息
                    counterpart_info = self._extract_counterpart_info(row, column_mapping, dr_cr_flag)

                    # 提取备注信息
                    remarks = self._extract_remarks(row, column_mapping)

                    # 累计账户信息
                    # 账户唯一键：持卡人+账号+子表（不纳入卡号，避免同一账号下多卡被拆成多个账户）
                    account_key = f"{cardholder_name}|{account_number}|{sheet_name}"

                    # 不对“流水卡号”做前向填充，避免跨明细串号；明细仅使用本行显式的“流水卡号”
                    # card_number 在本行缺失时保持为空，账户层面的展示卡号由明细中出现频率最高的非空卡号决定
                    if account_key not in accounts:
                        accounts[account_key] = {
                            'cardholder_name': cardholder_name,
                            'account_number': account_number,
                            # 账户层面的卡号稍后由明细的众数决定
                            'card_number': '',
                            'total_income': 0.0,
                            'total_expense': 0.0,
                            'transaction_count': 0,
                            'data_source': sheet_name,
                            'earliest_date': transaction_date,
                            'latest_date': transaction_date,
                            # 账户期末余额跟踪
                            'latest_datetime': None,
                            'latest_balance': None,
                            # 卡号频次
                            'card_numbers_freq': Counter()
                        }

                    # 更新账户统计
                    if dr_cr_flag == "收":
                        accounts[account_key]['total_income'] += abs(transaction_amount)
                    elif dr_cr_flag == "支":
                        accounts[account_key]['total_expense'] += abs(transaction_amount)

                    accounts[account_key]['transaction_count'] += 1
                    accounts[account_key]['transactions_count'] = accounts[account_key]['transaction_count']  # 🔧 前端兼容字段

                    # 更新时间范围
                    if transaction_date < accounts[account_key]['earliest_date']:
                        accounts[account_key]['earliest_date'] = transaction_date
                    if transaction_date > accounts[account_key]['latest_date']:
                        accounts[account_key]['latest_date'] = transaction_date

                    # 记录账户层面的“最新余额”和卡号频次
                    # 计算当前行的完整时间（若有交易时间则参与比较）
                    current_dt = None
                    try:
                        if transaction_time and isinstance(transaction_time, str) and len(transaction_time) >= 5:
                            current_dt = datetime.strptime(
                                f"{transaction_date.strftime('%Y-%m-%d')} {transaction_time}", '%Y-%m-%d %H:%M:%S'
                            )
                        else:
                            current_dt = datetime.strptime(
                                f"{transaction_date.strftime('%Y-%m-%d')} 00:00:00", '%Y-%m-%d %H:%M:%S'
                            )
                    except Exception:
                        current_dt = None

                    prev_dt = accounts[account_key].get('latest_datetime')
                    if prev_dt is None or (current_dt and current_dt > prev_dt):
                        accounts[account_key]['latest_datetime'] = current_dt
                        accounts[account_key]['latest_balance'] = account_balance
                        accounts[account_key]['latest_seq'] = sequence_counter
                    elif current_dt and prev_dt and current_dt == prev_dt:
                        # 同一时间点，取序号更大的作为期末余额
                        if sequence_counter > accounts[account_key].get('latest_seq', 0):
                            accounts[account_key]['latest_balance'] = account_balance
                            accounts[account_key]['latest_seq'] = sequence_counter

                    if card_number:
                        accounts[account_key]['card_numbers_freq'][card_number] += 1

                    # 构建交易记录
                    sequence_counter += 1
                    transaction = {
                        'sequence_number': sequence_counter,
                        'cardholder_name': cardholder_name,
                        'holder_name': cardholder_name,
                        'bank_name': '中国银行',
                        'account_number': account_number,
                        'card_number': card_number,
                        'transaction_date': transaction_date.strftime('%Y-%m-%d'),
                        'transaction_time': transaction_time,
                        'transaction_type': transaction_method,
                        'transaction_method': transaction_method,  # 前端兼容字段
                        'transaction_amount': abs(transaction_amount),  # 统一显示为正数
                        'amount': abs(transaction_amount),              # 兼容测试别名
                        'account_balance': account_balance,
                        'balance_after': account_balance,               # 兼容测试别名
                        'balance_amount': account_balance,              # 前端兼容字段
                        'debit_credit_indicator': dr_cr_flag,
                        'income_expense_flag': dr_cr_flag,              # 兼容测试别名
                        'dr_cr_flag': dr_cr_flag,                       # 前端兼容字段
                        'counterpart_name': counterpart_info['counterpart_name'],
                        'counterpart_account': counterpart_info['counterpart_account'],
                        'counterpart_bank': counterpart_info['counterpart_bank'],
                        # 前端兼容命名
                        'counterparty_name': counterpart_info['counterpart_name'],
                        'counterparty_account': counterpart_info['counterpart_account'],
                        'counterparty_bank': counterpart_info['counterpart_bank'],
                        'remark1': remarks['remark1'],
                        'remark2': remarks['remark2'],
                        'remark3': remarks['remark3'],
                        'sheet_source': sheet_name
                    }

                    transactions.append(transaction)
                    diag['rows_parsed'] += 1

                except Exception as e:
                    logger.warning(f"⚠️ 解析第{idx}行时出错: {str(e)}")
                    diag['skipped_other_error'] += 1
                    continue

            # 输出账户字段兼容（holder_name 别名）
            account_list = list(accounts.values())
            for a in account_list:
                # 🔧 兼容字段别名 - 保留cardholder_name，同时添加holder_name
                cardholder_name = a.get('cardholder_name', '')
                a['holder_name'] = cardholder_name  # 不使用pop，保留原字段
                a['cardholder_name'] = cardholder_name  # 确保字段存在
                a['bank_name'] = '中国银行'
                a['total_inflow'] = a.get('total_income', 0.0)
                a['total_outflow'] = a.get('total_expense', 0.0)
                # 🔧 确保transactions_count字段存在
                a['transactions_count'] = a.get('transaction_count', 0)
                # 账户期末余额（最新一笔后的余额）
                final_bal = a.get('latest_balance')
                if final_bal is None:
                    final_bal = 0.0
                a['account_balance'] = final_bal
                a['balance'] = final_bal
                # 账户展示卡号：取出现频率最高的非空卡号
                if a.get('card_numbers_freq'):
                    try:
                        a['card_number'] = a['card_numbers_freq'].most_common(1)[0][0]
                    except Exception:
                        pass
                # 组合时间范围显示
                earliest = a.get('earliest_date')
                latest = a.get('latest_date')
                try:
                    if earliest and latest:
                        a['date_range'] = f"{earliest.strftime('%Y-%m-%d')} 至 {latest.strftime('%Y-%m-%d')}"
                    else:
                        a['date_range'] = '未知'
                except Exception:
                    a['date_range'] = '未知'
                # 清理内部字段
                a.pop('latest_datetime', None)
                a.pop('latest_balance', None)
                a.pop('latest_seq', None)
                a.pop('card_numbers_freq', None)
            # 排序：按持卡人姓名，再按账号，再按卡号排序（稳定展示：同一持卡人的各账号依次排列）
            account_list.sort(key=lambda x: (str(x.get('holder_name', '')), str(x.get('account_number', '')), str(x.get('card_number', ''))))
            logger.info(f"✅ 工作表 {sheet_name} 解析完成: {len(account_list)}个账户, {len(transactions)}条记录")
            return account_list, transactions, diag

        except Exception as e:
            logger.error(f"❌ 工作表 {sheet_name} 解析失败: {str(e)}")
            return [], [], {
                'sheet': sheet_name,
                'error': str(e)
            }

    def _find_column_mapping(self, df: pd.DataFrame) -> Dict[str, int]:
        """查找关键列的索引位置 - 根据中国银行实际字段结构"""
        column_mapping = {}
        account_candidates: list[int] = []

        # 根据实际Excel文件结构查找列
        # 尝试在前10行中探测真实表头（单元格内容含关键字段）
        # 如果列是多级索引（合并单元格导致），将其展平
        if isinstance(df.columns, pd.MultiIndex):
            df = df.copy()
            df.columns = [''.join([str(x) for x in col if str(x) != 'nan']) for col in df.columns]

        head = df.head(10).astype(str)
        for i in range(head.shape[0]):
            for j in range(head.shape[1]):
                cell = head.iloc[i, j]
                if '申请查询信息' in cell or '申请查询名称' in cell or cell == '姓名':
                    column_mapping['cardholder_name'] = j
                if '主账户账号' in cell:
                    if 'account_number' not in column_mapping:
                        column_mapping['account_number'] = j
                    if j not in account_candidates:
                        account_candidates.append(j)
                    # 标记新线特征：有“主账户账号”字段
                    column_mapping['_has_primary_account'] = True
                if '交易账号' in cell:
                    if 'account_number' not in column_mapping:
                        column_mapping['account_number'] = j
                    if j not in account_candidates:
                        account_candidates.append(j)
                if '帐号' in cell:
                    if 'account_number' not in column_mapping:
                        column_mapping['account_number'] = j
                    if j not in account_candidates:
                        account_candidates.append(j)
                if '新线账号' in cell:
                    if 'account_number' not in column_mapping:
                        column_mapping['account_number'] = j
                    if j not in account_candidates:
                        account_candidates.append(j)
                if '旧线账号' in cell:
                    if 'account_number' not in column_mapping:
                        column_mapping['account_number'] = j
                    if j not in account_candidates:
                        account_candidates.append(j)
                if '流水卡号' in cell:
                    column_mapping['card_number'] = j
                # 注意：避免把“原交易日期”误识别为“交易日期”
                if ('交易日期' in cell) and ('原交易' not in cell):
                    column_mapping['transaction_date'] = j
                # 备用日期列
                if '系统日期' in cell:
                    column_mapping['system_date'] = j
                if '系统记账日' in cell or '系统记帐日' in cell:
                    column_mapping['system_posting_date'] = j
                if '交易发生日' in cell:
                    column_mapping['occur_date'] = j
                if '原交易日期' in cell and 'original_transaction_date' not in column_mapping:
                    column_mapping['original_transaction_date'] = j
                if '交易时间' in cell:
                    column_mapping['transaction_time'] = j
                if '交易金额' in cell:
                    column_mapping['transaction_amount'] = j
                if '交易后余额' in cell:
                    column_mapping['account_balance'] = j
                if '交易类型描述' in cell:
                    column_mapping['transaction_method'] = j
                if '交易码' in cell and 'transaction_method' not in column_mapping:
                    column_mapping['transaction_method'] = j
                if '交易类型' in cell and 'transaction_method' not in column_mapping:
                    column_mapping['transaction_method'] = j
                if '借贷标识' in cell:
                    column_mapping['dr_cr_flag'] = j
                if '附言' in cell:
                    column_mapping['remark1'] = j
                if '摘要' in cell:
                    column_mapping['remark2'] = j
                if '真实收款人账户名称' in cell:
                    column_mapping['payee_name'] = j
                if '真实收款人卡号' in cell:
                    column_mapping['payee_account'] = j
                if '真实收款人所属机构名称' in cell:
                    column_mapping['payee_bank'] = j
                if '真实付款人账户名称' in cell:
                    column_mapping['payer_name'] = j
                if '真实付款人卡号' in cell:
                    column_mapping['payer_account'] = j
                if '真实付款人所属机构名称' in cell:
                    column_mapping['payer_bank'] = j
                if '对方账号' in cell:
                    column_mapping['counterpart_account'] = j
                if '对方账户名' in cell:
                    column_mapping['counterpart_name'] = j
                if '对方户名' in cell and 'counterpart_name' not in column_mapping:
                    column_mapping['counterpart_name'] = j

        if column_mapping:
            # 将候选账号列存入专用键，供提取阶段回退
            if 'account_number' in column_mapping:
                # 如果前面收集了候选列
                try:
                    if 'account_candidates' in locals() and isinstance(account_candidates, list) and account_candidates:
                        column_mapping['_account_candidates'] = account_candidates
                except Exception:
                    pass
            return column_mapping

        # 回退：如果没能在单元格内探测到，就用列索引名
        for idx, col_name in enumerate(df.columns):
            col_str = str(col_name).strip()

            if '申请查询信息' in col_str or '申请查询名称' in col_str or col_str == '姓名':
                column_mapping['cardholder_name'] = idx

            # 账号列候选 - 记录所有可用列
            elif '主账户账号' in col_str:
                column_mapping.setdefault('account_number', idx)
                if idx not in account_candidates:
                    account_candidates.append(idx)
                column_mapping['_has_primary_account'] = True
            elif '交易账号' in col_str:
                column_mapping.setdefault('account_number', idx)
                if idx not in account_candidates:
                    account_candidates.append(idx)
            elif '帐号' in col_str:
                column_mapping.setdefault('account_number', idx)
                if idx not in account_candidates:
                    account_candidates.append(idx)
            elif '新线账号' in col_str:
                column_mapping.setdefault('account_number', idx)
                if idx not in account_candidates:
                    account_candidates.append(idx)
            elif '旧线账号' in col_str:
                column_mapping.setdefault('account_number', idx)
                if idx not in account_candidates:
                    account_candidates.append(idx)

            # 卡号列 - "流水卡号"列
            elif '流水卡号' in col_str:
                column_mapping['card_number'] = idx

            # 交易日期列
            elif ('交易日期' in col_str) and ('原交易' not in col_str):
                column_mapping['transaction_date'] = idx
            elif '系统日期' in col_str:
                column_mapping['system_date'] = idx
            elif '系统记账日' in col_str or '系统记帐日' in col_str:
                column_mapping['system_posting_date'] = idx
            elif '交易发生日' in col_str:
                column_mapping['occur_date'] = idx
            elif '原交易日期' in col_str and 'original_transaction_date' not in column_mapping:
                column_mapping['original_transaction_date'] = idx

            # 交易时间列
            elif '交易时间' in col_str:
                column_mapping['transaction_time'] = idx
            elif '系统时间' in col_str and 'transaction_time' not in column_mapping:
                column_mapping['transaction_time'] = idx

            # 交易金额列
            elif '交易金额' in col_str:
                column_mapping['transaction_amount'] = idx

            # 交易余额列 - "交易后余额"列
            elif '交易后余额' in col_str:
                column_mapping['account_balance'] = idx

            # 交易方式列 - "交易类型描述"列
            elif '交易类型描述' in col_str:
                column_mapping['transaction_method'] = idx
            elif '交易码' in col_str and 'transaction_method' not in column_mapping:
                column_mapping['transaction_method'] = idx
            elif '交易类型' in col_str and 'transaction_method' not in column_mapping:
                column_mapping['transaction_method'] = idx

            # 收支符号列 - "借贷标识"列
            elif '借贷标识' in col_str:
                column_mapping['dr_cr_flag'] = idx

            # 备注字段
            elif '附言' in col_str:
                column_mapping['remark1'] = idx
            elif '摘要' in col_str:
                column_mapping['remark2'] = idx

            # 对方信息字段 - 根据文件分析结果修正字段名
            elif '真实收款人账户名称' in col_str:
                column_mapping['payee_name'] = idx
            elif '真实收款人卡号' in col_str:
                column_mapping['payee_account'] = idx
            elif '真实收款人所属机构名称' in col_str:
                column_mapping['payee_bank'] = idx
            elif '真实付款人账户名称' in col_str:
                column_mapping['payer_name'] = idx
            elif '真实付款人卡号' in col_str:
                column_mapping['payer_account'] = idx
            elif '真实付款人所属机构名称' in col_str:
                column_mapping['payer_bank'] = idx
            elif '对方账号' in col_str:
                column_mapping['counterpart_account'] = idx
            elif '对方账户名' in col_str:
                column_mapping['counterpart_name'] = idx

        # 调试：输出列映射结果
        logger.debug(f"🔍 列映射结果: {column_mapping}")
        logger.debug(f"🔍 DataFrame列名: {list(df.columns)}")

        return column_mapping

    def _detect_header_row(self, df_no_header: pd.DataFrame) -> int:
        """自动检测表头行：返回最可能的列名所在行

        策略：在前20行内，统计每一行命中关键列名称的数量，选择命中最多的行；
        若存在并列，优先命中“交易金额”和“交易后余额”同时出现者；
        如果无命中，则回退到第0行。
        """
        try:
            max_hits = -1
            candidate_row = 0
            keywords = ['申请查询信息', '交易金额', '交易后余额', '交易类型描述', '借贷标识', '主账户账号', '流水卡号']
            limit = min(20, df_no_header.shape[0])
            for i in range(limit):
                row = df_no_header.iloc[i].astype(str)
                hits = sum(1 for v in row if any(k in v for k in keywords))
                has_amt = row.astype(str).str.contains('交易金额', na=False).any()
                has_bal = row.astype(str).str.contains('交易后余额', na=False).any()
                if hits > max_hits or (hits == max_hits and has_amt and has_bal):
                    max_hits = hits
                    candidate_row = i
            return candidate_row
        except Exception:
            return 0

    def _extract_cardholder_name(self, row: pd.Series, column_mapping: Dict[str, int]) -> str:
        """提取持卡人姓名 - 从"申请查询信息"列中提取，据实列出中文姓名和身份证号"""
        if 'cardholder_name' not in column_mapping:
            logger.warning("⚠️ 未找到申请查询信息列映射")
            return ""

        name_value = row.iloc[column_mapping['cardholder_name']]
        logger.debug(f"🔍 申请查询信息原始值: {name_value} (类型: {type(name_value)})")

        # 检查"申请查询信息"列的值 - 用户明确要求：据实列出，包括身份证号
        if not pd.isna(name_value):
            name_str = str(name_value).strip()
            logger.debug(f"🔍 处理后姓名: '{name_str}'")

            # 根据用户强制要求：据实列出，不管是中文姓名还是身份证号都要保留
            if name_str and name_str != 'nan':
                logger.info(f"✅ 提取到持卡人信息(申请查询信息): '{name_str}'")
                return name_str

        logger.warning("⚠️ 申请查询信息为空或无效")
        return ""

    def _extract_account_number(self, row: pd.Series, column_mapping: Dict[str, int]) -> str:
        """提取账号（按候选列优先级回退）"""
        # 优先使用 account_number 指定列
        candidates: list[int] = []
        if 'account_number' in column_mapping:
            candidates.append(column_mapping['account_number'])
        # 若在映射阶段记录了候选列，按记录顺序尝试
        extra = column_mapping.get('_account_candidates') if isinstance(column_mapping.get('_account_candidates'), list) else []
        candidates.extend([c for c in extra if isinstance(c, int)])
        # 去重且保证顺序
        seen = set()
        ordered = []
        for c in candidates:
            if c not in seen:
                ordered.append(c)
                seen.add(c)
        for idx in ordered:
            try:
                val = row.iloc[idx]
                if not pd.isna(val) and str(val).strip() and str(val).strip() != 'nan':
                    return str(val).strip()
            except Exception:
                continue
        return ""

    def _extract_card_number(self, row: pd.Series, column_mapping: Dict[str, int]) -> str:
        """提取卡号（保持文本格式）"""
        if 'card_number' not in column_mapping:
            return ""

        card = row.iloc[column_mapping['card_number']]
        return self._format_card_number(card)

    def _extract_transaction_date(self, row: pd.Series, column_mapping: Dict[str, int]) -> Optional[datetime]:
        """提取交易日期"""
        # 主列
        candidates: list[Optional[str]] = []
        if 'transaction_date' in column_mapping:
            candidates.append(str(row.iloc[column_mapping['transaction_date']]))
        # 备用列：系统日期、系统记账日/记帐日、交易发生日、原交易日期
        if 'system_date' in column_mapping:
            candidates.append(str(row.iloc[column_mapping['system_date']]))
        if 'system_posting_date' in column_mapping:
            candidates.append(str(row.iloc[column_mapping['system_posting_date']]))
        if 'occur_date' in column_mapping:
            candidates.append(str(row.iloc[column_mapping['occur_date']]))
        if 'original_transaction_date' in column_mapping:
            candidates.append(str(row.iloc[column_mapping['original_transaction_date']]))

        for candidate in candidates:
            dt = self._parse_date(candidate)
            if dt:
                return dt
        return None

    def _extract_transaction_time(self, row: pd.Series, column_mapping: Dict[str, int]) -> str:
        """提取交易时间"""
        if 'transaction_time' not in column_mapping:
            return "00:00:00"

        time_value = row.iloc[column_mapping['transaction_time']]
        return self._format_time(str(time_value))

    def _extract_transaction_amount(self, row: pd.Series, column_mapping: Dict[str, int]) -> float:
        """提取交易金额"""
        if 'transaction_amount' not in column_mapping:
            return 0.0

        amount_value = row.iloc[column_mapping['transaction_amount']]
        return self._parse_amount(str(amount_value))

    def _extract_account_balance(self, row: pd.Series, column_mapping: Dict[str, int]) -> float:
        """提取交易余额"""
        if 'account_balance' not in column_mapping:
            return 0.0

        balance_value = row.iloc[column_mapping['account_balance']]
        return self._parse_amount(str(balance_value))

    def _extract_transaction_method(self, row: pd.Series, column_mapping: Dict[str, int]) -> str:
        """提取交易方式"""
        if 'transaction_method' not in column_mapping:
            return ""

        method_value = row.iloc[column_mapping['transaction_method']]
        if pd.isna(method_value):
            return ""

        return str(method_value).strip()

    def _extract_dr_cr_flag(self, row: pd.Series, column_mapping: Dict[str, int], amount: float) -> str:
        """提取收支符号 - 根据用户要求：D表示收入，C表示支出"""
        if 'dr_cr_flag' in column_mapping:
            flag_value = row.iloc[column_mapping['dr_cr_flag']]
            if not pd.isna(flag_value):
                flag_str = str(flag_value).strip().upper()
                # 根据用户明确要求：D表示收入，C表示支出
                if flag_str == 'D':
                    return "收"
                elif flag_str == 'C':
                    return "支"

        # 如果没有收支符号列，根据金额正负判断
        if amount > 0:
            return "收"
        elif amount < 0:
            return "支"
        else:
            return "未知"

    def _extract_counterpart_info(self, row: pd.Series, column_mapping: Dict[str, int], dr_cr_flag: str) -> Dict[str, str]:
        """根据借贷标识提取对方信息"""
        counterpart_info = {
            'counterpart_name': "",
            'counterpart_account': "",
            'counterpart_bank': ""
        }

        if dr_cr_flag == "收":  # D标识，收入
            # 交易对象=真实收款人账户名称，对方账号=真实收款人卡号，对方银行=真实收款人所属机构名称
            if 'payee_name' in column_mapping:
                name_value = row.iloc[column_mapping['payee_name']]
                if not pd.isna(name_value):
                    counterpart_info['counterpart_name'] = str(name_value).strip()

            if 'payee_account' in column_mapping:
                account_value = row.iloc[column_mapping['payee_account']]
                if not pd.isna(account_value):
                    counterpart_info['counterpart_account'] = str(account_value).strip()

            if 'payee_bank' in column_mapping:
                bank_value = row.iloc[column_mapping['payee_bank']]
                if not pd.isna(bank_value):
                    counterpart_info['counterpart_bank'] = str(bank_value).strip()

        elif dr_cr_flag == "支":  # C标识，支出
            # 交易对象=真实付款人账户名称，对方账号=真实付款人卡号，对方银行=真实付款人所属机构名称
            if 'payer_name' in column_mapping:
                name_value = row.iloc[column_mapping['payer_name']]
                if not pd.isna(name_value):
                    counterpart_info['counterpart_name'] = str(name_value).strip()

            if 'payer_account' in column_mapping:
                account_value = row.iloc[column_mapping['payer_account']]
                if not pd.isna(account_value):
                    counterpart_info['counterpart_account'] = str(account_value).strip()

            if 'payer_bank' in column_mapping:
                bank_value = row.iloc[column_mapping['payer_bank']]
                if not pd.isna(bank_value):
                    counterpart_info['counterpart_bank'] = str(bank_value).strip()

        # 如果没有找到特定的收款人/付款人信息，尝试使用通用的对方信息
        if not counterpart_info['counterpart_name'] and 'counterpart_name' in column_mapping:
            name_value = row.iloc[column_mapping['counterpart_name']]
            if not pd.isna(name_value):
                counterpart_info['counterpart_name'] = str(name_value).strip()

        if not counterpart_info['counterpart_account'] and 'counterpart_account' in column_mapping:
            account_value = row.iloc[column_mapping['counterpart_account']]
            if not pd.isna(account_value):
                counterpart_info['counterpart_account'] = str(account_value).strip()

        return counterpart_info

    def _extract_remarks(self, row: pd.Series, column_mapping: Dict[str, int]) -> Dict[str, str]:
        """提取备注信息"""
        remarks = {
            'remark1': "",
            'remark2': "",
            'remark3': ""
        }

        # 附言 -> 备注1
        if 'remark1' in column_mapping:
            remark1_value = row.iloc[column_mapping['remark1']]
            if not pd.isna(remark1_value):
                remarks['remark1'] = str(remark1_value).strip()

        # 摘要 -> 备注2
        if 'remark2' in column_mapping:
            remark2_value = row.iloc[column_mapping['remark2']]
            if not pd.isna(remark2_value):
                remarks['remark2'] = str(remark2_value).strip()

        return remarks



    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """解析日期字符串"""
        try:
            if not date_str or date_str in ['nan', 'None', '']:
                return None

            # 如果包含时间部分，只取日期片段
            if ' ' in date_str:
                date_str = date_str.split(' ')[0]

            # 处理Excel序列号（可能以浮点字符串出现，如 '43333' 或 '43333.0'）
            serial_candidate = re.fullmatch(r"\d+(?:\.0+)?", str(date_str).strip())
            if serial_candidate:
                try:
                    from datetime import timedelta
                    days = int(float(date_str))
                    base = datetime(1899, 12, 30)  # Excel序列号基准（兼容1900误差）
                    return base + timedelta(days=days)
                except Exception:
                    pass

            # 尝试不同的日期格式
            date_formats = [
                '%Y-%m-%d',    # 2011-08-06
                '%Y/%m/%d',    # 2011/08/06
                '%Y%m%d',      # 20110806
                '%m/%d/%Y',    # 08/06/2011
                '%d/%m/%Y',    # 06/08/2011
                '%Y.%m.%d',    # 2018.02.10
                '%Y年%m月%d日',  # 2018年02月10日
            ]

            for fmt in date_formats:
                try:
                    return datetime.strptime(str(date_str).strip(), fmt)
                except ValueError:
                    continue

            logger.warning(f"⚠️ 无法解析日期: {date_str}")
            return None

        except Exception as e:
            logger.warning(f"⚠️ 日期解析错误: {date_str}, 错误: {e}")
            return None

    def _format_time(self, time_str: str) -> str:
        """格式化时间字符串"""
        try:
            if not time_str or time_str in ['nan', 'None', '']:
                return "00:00:00"

            time_str = str(time_str).strip()

            # 如果是数字格式 (如: 104719)
            if time_str.isdigit() and len(time_str) == 6:
                return f"{time_str[:2]}:{time_str[2:4]}:{time_str[4:6]}"

            # 如果已经是时间格式 (如: 00:00:00)
            if ':' in time_str:
                return time_str

            # 如果是其他格式，尝试解析
            if '.' in time_str:
                # 处理 HH.MM.SS 格式
                return time_str.replace('.', ':')

            return "00:00:00"

        except Exception as e:
            logger.warning(f"⚠️ 时间格式化错误: {time_str}, 错误: {e}")
            return "00:00:00"

    def _parse_amount(self, amount_str: str) -> float:
        """解析金额字符串"""
        try:
            if not amount_str or amount_str in ['nan', 'None', '']:
                return 0.0

            # 清理金额字符串
            amount_str = str(amount_str).strip()

            # 移除货币符号和千位分隔符
            amount_str = re.sub(r'[¥$,，\s]', '', amount_str)

            # 处理负号
            is_negative = amount_str.startswith('-')
            if is_negative:
                amount_str = amount_str[1:]

            # 尝试转换为浮点数
            amount = float(amount_str)

            return -amount if is_negative else amount

        except (ValueError, TypeError) as e:
            logger.warning(f"⚠️ 金额解析错误: {amount_str}, 错误: {e}")
            return 0.0

    def can_parse(self, file_path: str) -> float:
        """计算文件解析置信度（放宽为分项打分，适配中国银行1.xls/1.xlsx）
        评分项：
        - 基础特征：存在“交易金额”且“交易后余额”（0.60）
        - 收支标识：存在“借贷标识/借贷标志/收支符号/收支”（+0.25）
        - BOC特征：存在“申请查询信息/交易类型描述/流水卡号/主账户账号”（+0.15）
        - 文件名提示：文件名含“中国银行/BOC”（+0.15，上限0.98）
        仅当完全没有金额+余额特征时，返回0.0。
        """
        try:
            if not os.path.exists(file_path):
                return 0.0
            if not file_path.lower().endswith(('.xlsx', '.xls')):
                return 0.0

            engine = self._get_excel_engine(file_path)
            excel_data = pd.read_excel(file_path, sheet_name=None, header=None, nrows=20, dtype=str, engine=engine)

            # 文件名提示分
            fname = os.path.basename(file_path).lower()
            filename_bonus = 0.15 if ('中国银行' in fname or 'boc' in fname) else 0.0

            max_sheet_score = 0.0
            has_amount_balance_any = False

            for _, df in excel_data.items():
                if df is None or df.empty:
                    continue
                head = df.astype(str)
                hit_amount = (head.apply(lambda s: s.str.contains('交易金额|金额', na=False))).any().any()
                hit_balance = (head.apply(lambda s: s.str.contains('交易后余额|余额', na=False))).any().any()
                hit_drcr = (head.apply(lambda s: s.str.contains('借贷标识|借贷标志|收支符号|收支', na=False))).any().any()
                hit_boc_kw = (head.apply(lambda s: s.str.contains('申请查询信息|交易类型描述|流水卡号|主账户账号', na=False))).any().any()

                if hit_amount and hit_balance:
                    has_amount_balance_any = True
                    sheet_score = 0.60
                    if hit_drcr:
                        sheet_score += 0.25
                    if hit_boc_kw:
                        sheet_score += 0.15
                    if sheet_score > max_sheet_score:
                        max_sheet_score = sheet_score

            if not has_amount_balance_any:
                return 0.0

            total = min(0.98, max_sheet_score + filename_bonus)
            # 保底：若只有金额+余额但无额外特征，也给出可用分以便参与竞争
            if total < 0.60 and has_amount_balance_any:
                total = 0.60 + filename_bonus
            return total
        except Exception as e:
            logger.warning(f"⚠️ 文件检查失败: {e}")
            return 0.0

    def parse(self, file_path: str) -> Dict[str, Any]:
        """执行文件解析"""
        return self.parse_file(file_path)

    # 兼容接口：别名
    def validate_file(self, file_path: str) -> bool:
        return self.can_parse(file_path) > 0.0

    def calculate_confidence(self, file_path: str) -> float:
        return self.can_parse(file_path)

    def extract_sample(self, file_path: str = None, limit: int = 10) -> Dict[str, Any]:
        """
        提取样本数据用于快速置信度评估
        中国银行Format1专用版本 - 支持4维度评估

        Args:
            file_path: 文件路径
            limit: 样本数量限制

        Returns:
            Dict: 包含样本账户和交易的字典
        """
        try:
            target_file = file_path or self.file_path
            if not target_file:
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}

            logger.info(f"中国银行Format1解析器开始提取样本数据，限制条数: {limit}")

            # 快速读取Excel文件前几行
            try:
                excel_file = pd.ExcelFile(target_file)
                valid_sheet = self._find_valid_sheet(excel_file)
                if not valid_sheet:
                    return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}

                # 读取有限行数进行快速评估
                engine = self._get_excel_engine(target_file)
                raw_df = pd.read_excel(target_file, sheet_name=valid_sheet, header=None, dtype=str, nrows=limit + 10, engine=engine)
                header_row = self._detect_header_row(raw_df)
                df = pd.read_excel(target_file, sheet_name=valid_sheet, header=header_row, dtype=str, nrows=limit, engine=engine)

                if df.empty:
                    return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}

                # 查找列映射（仅当具备新线三项硬条件才继续）
                column_mapping = self._find_column_mapping(df)
                if ('transaction_amount' not in column_mapping or
                    'account_balance' not in column_mapping or
                    not column_mapping.get('_has_primary_account')):
                    return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}

                # 提取样本账户信息
                sample_accounts = []
                sample_transactions = []

                # 从第一行提取基本信息
                if len(df) > 0:
                    first_row = df.iloc[0]

                    # 🔧 修复：提取持卡人姓名，不使用虚假数据
                    holder_name = self._extract_cardholder_name(first_row, column_mapping)
                    if not holder_name:
                        # 如果无法提取真实姓名，返回空结果而不是虚假数据
                        logger.warning("无法提取持卡人姓名，返回空样本")
                        return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}

                    # 🔧 修复：提取账号，不使用虚假数据
                    account_number = self._extract_account_number(first_row, column_mapping)
                    if not account_number:
                        # 如果无法提取真实账号，返回空结果而不是虚假数据
                        logger.warning("无法提取账号，返回空样本")
                        return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}

                    sample_account = {
                        'cardholder_name': holder_name,  # 🔧 4维度姓名识别需要
                        'holder_name': holder_name,
                        'account_number': account_number,
                        'card_number': self._extract_card_number(first_row, column_mapping),
                        'bank_name': '中国银行',
                        'account_type': '个人账户' if len(holder_name) <= 4 else '企业账户'
                    }
                    sample_accounts.append(sample_account)

                # 提取样本交易数据
                transaction_count = 0
                for idx, row in df.iterrows():
                    if transaction_count >= limit:
                        break

                    try:
                        # 提取关键字段
                        transaction_date = self._extract_transaction_date(row, column_mapping)
                        if not transaction_date:
                            continue

                        amount = self._extract_transaction_amount(row, column_mapping)
                        balance = self._extract_account_balance(row, column_mapping)

                        # 基本验证
                        if amount == 0:
                            continue

                        # 构建样本交易
                        transaction = {
                            'cardholder_name': holder_name,  # 🔧 4维度姓名识别需要
                            'holder_name': holder_name,
                            'account_number': account_number,
                            'transaction_date': transaction_date.strftime('%Y-%m-%d'),
                            'transaction_amount': abs(amount),
                            'balance': balance,  # 🔧 4维度金额解析需要
                            'dr_cr_flag': self._extract_dr_cr_flag(row, column_mapping, amount),
                            'currency': 'CNY',
                            'transaction_method': self._extract_transaction_method(row, column_mapping),
                            'bank_name': '中国银行'
                        }

                        sample_transactions.append(transaction)
                        transaction_count += 1

                    except Exception as e:
                        logger.debug(f"跳过第{idx+1}行: {str(e)}")
                        continue

                return {
                    'accounts': sample_accounts,
                    'transactions': sample_transactions[:limit],
                    'metadata': {
                        'sample_size': len(sample_transactions),
                        'evaluation_mode': 'extract_sample',
                        'plugin_name': self.plugin_name,
                        'bank_name': '中国银行'
                    }
                }

            except Exception as e:
                logger.error(f"中国银行Format1解析器样本提取失败: {str(e)}")
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0, 'error': str(e)}}

        except Exception as e:
            logger.error(f"中国银行Format1解析器extract_sample方法失败: {str(e)}")
            return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0, 'error': str(e)}}

    def get_health_status(self) -> Dict[str, Any]:
        """获取插件健康状态"""
        return {
            "healthy": True,
            "last_check": time.time(),
            "memory_usage": "正常",
            "error_count": 0,
            "plugin_version": self.version
        }

# 为了兼容插件系统，创建Plugin类的别名
Plugin = BOCFormat1Parser


# 适配插件容器约定：导出统一的 Plugin 类
class Plugin(BOCFormat1Parser):
    """容器期望的入口类，继承实际实现 BOCFormat1Parser"""
    def __init__(self, file_path: str = None):
        super().__init__(file_path)
