"""
插件注册表 - 负责插件发现和管理

管理插件的注册、发现、配置加载等功能
"""

import os
import json
import glob
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path

logger = logging.getLogger(__name__)


class PluginRegistry:
    """插件注册表
    
    负责插件的发现、注册和配置管理
    """
    
    def __init__(self, plugins_dir: str):
        """初始化插件注册表
        
        Args:
            plugins_dir: 插件目录路径
        """
        self.plugins_dir = Path(plugins_dir)
        self.plugins: Dict[str, Dict[str, Any]] = {}
        
        # 确保插件目录存在
        self.plugins_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"📁 插件注册表初始化，目录: {self.plugins_dir}")
    
    def discover_plugins(self) -> int:
        """发现插件
        
        Returns:
            int: 发现的插件数量
        """
        print(f"🔍 在目录中发现插件: {self.plugins_dir}")
        
        discovered_count = 0
        
        # 扫描插件目录
        for plugin_path in self.plugins_dir.iterdir():
            if plugin_path.is_dir() and not plugin_path.name.startswith('.'):
                try:
                    plugin_info = self._load_plugin_info(plugin_path)
                    if plugin_info:
                        plugin_name = plugin_info['name']
                        self.plugins[plugin_name] = plugin_info
                        discovered_count += 1
                        print(f"✅ 发现插件: {plugin_name}")
                    
                except Exception as e:
                    print(f"❌ 加载插件失败 {plugin_path.name}: {e}")
        
        print(f"📊 共发现 {discovered_count} 个插件")
        return discovered_count
    
    def _load_plugin_info(self, plugin_path: Path) -> Optional[Dict[str, Any]]:
        """加载插件信息
        
        Args:
            plugin_path: 插件目录路径
            
        Returns:
            Optional[Dict[str, Any]]: 插件信息字典
        """
        # 检查必要文件
        plugin_py = plugin_path / "plugin.py"
        metadata_json = plugin_path / "metadata.json"
        config_json = plugin_path / "config.json"
        
        if not plugin_py.exists():
            logger.warning(f"⚠️ 插件缺少主文件: {plugin_path}/plugin.py")
            return None
        
        # 加载元信息
        metadata = {}
        if metadata_json.exists():
            try:
                with open(metadata_json, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
            except Exception as e:
                logger.error(f"❌ 加载元信息失败 {plugin_path.name}: {e}")
                return None
        
        # 加载配置信息
        config = {}
        if config_json.exists():
            try:
                with open(config_json, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            except Exception as e:
                logger.warning(f"❌ 加载配置失败 {plugin_path.name}: {e}")
                # 配置文件可以缺失，使用默认配置
                config = {"enabled": True}
        else:
            config = {"enabled": True}
        
        # 构建插件信息
        plugin_info = {
            "name": metadata.get("name", plugin_path.name),
            "version": metadata.get("version", "1.0.0"),
            "description": metadata.get("description", ""),
            "author": metadata.get("author", ""),
            "path": str(plugin_py),
            "directory": str(plugin_path),
            "metadata": metadata,
            "config": config,
            "supported_formats": metadata.get("supported_formats", []),
            "dependencies": metadata.get("dependencies", []),
            "entry_point": metadata.get("entry_point", "plugin.Plugin")
        }
        
        return plugin_info
    
    def get_plugin(self, plugin_name: str) -> Optional[Dict[str, Any]]:
        """获取插件信息
        
        Args:
            plugin_name: 插件名称
            
        Returns:
            Optional[Dict[str, Any]]: 插件信息
        """
        return self.plugins.get(plugin_name)
    
    def list_plugins(self) -> List[str]:
        """列出所有插件
        
        Returns:
            List[str]: 插件名称列表
        """
        return list(self.plugins.keys())
    
    def get_enabled_plugins(self) -> List[str]:
        """获取启用的插件列表
        
        Returns:
            List[str]: 启用的插件名称列表
        """
        enabled = []
        for plugin_name, plugin_info in self.plugins.items():
            if plugin_info.get('config', {}).get('enabled', True):
                enabled.append(plugin_name)
        
        return enabled
    
    def get_plugins_by_format(self, format_name: str) -> List[str]:
        """根据格式获取支持的插件
        
        Args:
            format_name: 格式名称
            
        Returns:
            List[str]: 支持该格式的插件列表
        """
        matching_plugins = []
        
        for plugin_name, plugin_info in self.plugins.items():
            supported_formats = plugin_info.get('supported_formats', [])
            if format_name in supported_formats:
                matching_plugins.append(plugin_name)
        
        return matching_plugins
    
    def register_plugin(self, plugin_info: Dict[str, Any]) -> bool:
        """注册插件
        
        Args:
            plugin_info: 插件信息
            
        Returns:
            bool: 注册是否成功
        """
        try:
            plugin_name = plugin_info['name']
            
            # 验证必要字段
            required_fields = ['name', 'path', 'version']
            for field in required_fields:
                if field not in plugin_info:
                    print(f"❌ 插件信息缺少必要字段: {field}")
                    return False
            
            self.plugins[plugin_name] = plugin_info
            print(f"✅ 插件注册成功: {plugin_name}")
            return True
            
        except Exception as e:
            print(f"❌ 插件注册失败: {e}")
            return False
    
    def unregister_plugin(self, plugin_name: str) -> bool:
        """注销插件
        
        Args:
            plugin_name: 插件名称
            
        Returns:
            bool: 注销是否成功
        """
        if plugin_name in self.plugins:
            del self.plugins[plugin_name]
            print(f"✅ 插件注销成功: {plugin_name}")
            return True
        else:
            print(f"⚠️ 插件不存在: {plugin_name}")
            return False
    
    def update_plugin_config(self, plugin_name: str, config: Dict[str, Any]) -> bool:
        """更新插件配置
        
        Args:
            plugin_name: 插件名称
            config: 新配置
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if plugin_name not in self.plugins:
                logger.error(f"❌ 插件不存在: {plugin_name}")
                return False
            
            # 更新内存中的配置
            self.plugins[plugin_name]['config'] = config
            
            # 写入配置文件
            plugin_info = self.plugins[plugin_name]
            config_path = Path(plugin_info['directory']) / "config.json"
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 插件配置更新成功: {plugin_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 更新插件配置失败 {plugin_name}: {e}")
            return False
    
    def get_plugin_dependencies(self, plugin_name: str) -> List[str]:
        """获取插件依赖
        
        Args:
            plugin_name: 插件名称
            
        Returns:
            List[str]: 依赖列表
        """
        plugin_info = self.get_plugin(plugin_name)
        if plugin_info:
            return plugin_info.get('dependencies', [])
        return []
    
    def validate_plugin_dependencies(self, plugin_name: str) -> bool:
        """验证插件依赖
        
        Args:
            plugin_name: 插件名称
            
        Returns:
            bool: 依赖是否满足
        """
        dependencies = self.get_plugin_dependencies(plugin_name)
        
        for dependency in dependencies:
            try:
                # 简单的导入测试
                __import__(dependency)
            except ImportError:
                print(f"❌ 插件 {plugin_name} 缺少依赖: {dependency}")
                return False
        
        return True
    
    def get_registry_stats(self) -> Dict[str, Any]:
        """获取注册表统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        total_plugins = len(self.plugins)
        enabled_plugins = len(self.get_enabled_plugins())
        disabled_plugins = total_plugins - enabled_plugins
        
        # 按格式统计
        format_stats = {}
        for plugin_info in self.plugins.values():
            for format_name in plugin_info.get('supported_formats', []):
                format_stats[format_name] = format_stats.get(format_name, 0) + 1
        
        return {
            "total_plugins": total_plugins,
            "enabled_plugins": enabled_plugins,
            "disabled_plugins": disabled_plugins,
            "supported_formats": len(format_stats),
            "format_stats": format_stats
        }
    
    def search_plugins(self, query: str) -> List[str]:
        """搜索插件
        
        Args:
            query: 搜索关键词
            
        Returns:
            List[str]: 匹配的插件名称列表
        """
        query_lower = query.lower()
        matching_plugins = []
        
        for plugin_name, plugin_info in self.plugins.items():
            # 搜索插件名称
            if query_lower in plugin_name.lower():
                matching_plugins.append(plugin_name)
                continue
            
            # 搜索描述
            description = plugin_info.get('description', '').lower()
            if query_lower in description:
                matching_plugins.append(plugin_name)
                continue
            
            # 搜索支持的格式
            for format_name in plugin_info.get('supported_formats', []):
                if query_lower in format_name.lower():
                    matching_plugins.append(plugin_name)
                    break
        
        return matching_plugins
    
    def export_registry(self) -> Dict[str, Any]:
        """导出注册表信息
        
        Returns:
            Dict[str, Any]: 注册表信息
        """
        return {
            "plugins_dir": str(self.plugins_dir),
            "plugins": self.plugins,
            "stats": self.get_registry_stats()
        }
    
    def create_plugin_template(self, plugin_name: str, description: str = "") -> bool:
        """创建插件模板
        
        Args:
            plugin_name: 插件名称
            description: 插件描述
            
        Returns:
            bool: 创建是否成功
        """
        try:
            plugin_dir = self.plugins_dir / plugin_name
            if plugin_dir.exists():
                print(f"❌ 插件目录已存在: {plugin_name}")
                return False
            
            plugin_dir.mkdir(parents=True)
            
            # 创建插件主文件
            plugin_py_content = f'''"""
{plugin_name} 解析器插件

{description}
"""

import time
from typing import Dict, Any
from app.services.parser_plugin_system.core.plugin_interface import BasePlugin


class Plugin(BasePlugin):
    """{plugin_name} 解析器插件"""
    
    def __init__(self):
        super().__init__()
        self.name = "{plugin_name}"
        self.version = "1.0.0"
        self.description = "{description}"
    
    def get_supported_formats(self) -> list:
        """获取支持的格式列表"""
        return ["{plugin_name}格式"]
    
    def calculate_confidence(self, file_path: str) -> float:
        """计算置信度"""
        try:
            # TODO: 实现置信度计算逻辑
            return 0.0
        except Exception:
            return 0.0
    
    def parse(self, file_path: str) -> Dict[str, Any]:
        """执行解析"""
        try:
            # TODO: 实现解析逻辑
            return {{
                "success": True,
                "message": "解析成功",
                "accounts": [],
                "transactions": []
            }}
        except Exception as e:
            return {{
                "success": False,
                "message": f"解析失败: {{str(e)}}",
                "accounts": [],
                "transactions": []
            }}
'''
            
            with open(plugin_dir / "plugin.py", 'w', encoding='utf-8') as f:
                f.write(plugin_py_content)
            
            # 创建元信息文件
            metadata = {
                "name": plugin_name,
                "version": "1.0.0",
                "description": description,
                "author": "系统生成",
                "license": "MIT",
                "supported_formats": [f"{plugin_name}格式"],
                "dependencies": ["pandas>=1.3.0", "openpyxl>=3.0.0"],
                "entry_point": "plugin.Plugin"
            }
            
            with open(plugin_dir / "metadata.json", 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            # 创建配置文件
            config = {
                "enabled": False,  # 新创建的插件默认禁用
                "priority": 10,
                "timeout": 60,
                "memory_limit": "512MB",
                "retry_count": 3
            }
            
            with open(plugin_dir / "config.json", 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            # 创建空的__init__.py
            (plugin_dir / "__init__.py").touch()
            
            print(f"✅ 插件模板创建成功: {plugin_name}")
            return True
            
        except Exception as e:
            print(f"❌ 创建插件模板失败: {e}")
            return False 