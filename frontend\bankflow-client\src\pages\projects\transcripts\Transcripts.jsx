import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, Modal, Form, Input, Select, Upload, Tag, message, Tabs, Empty } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, UploadOutlined, FilePdfOutlined, FileWordOutlined, AudioOutlined } from '@ant-design/icons';
import { useParams } from 'react-router-dom';

const { TextArea } = Input;
const { TabPane } = Tabs;
const { Option } = Select;

const Transcripts = () => {
  const [form] = Form.useForm();
  const [transcripts, setTranscripts] = useState([
    {
      id: '1',
      title: '张三初次谈话',
      interviewee: '张三',
      interviewer: '李警官',
      date: '2023-01-15',
      location: '市公安局询问室',
      type: 'suspect',
      status: 'completed',
      fileType: 'pdf',
    },
    {
      id: '2',
      title: '李四证人谈话',
      interviewee: '李四',
      interviewer: '王警官',
      date: '2023-01-18',
      location: '市公安局询问室',
      type: 'witness',
      status: 'completed',
      fileType: 'word',
    },
    {
      id: '3',
      title: '王五补充调查',
      interviewee: '王五',
      interviewer: '李警官',
      date: '2023-01-20',
      location: '派出所',
      type: 'related',
      status: 'draft',
      fileType: 'audio',
    },
  ]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState(null);
  const [editingId, setEditingId] = useState(null);
  const [activeTab, setActiveTab] = useState('list');

  // 表格列定义
  const columns = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '被询问人',
      dataIndex: 'interviewee',
      key: 'interviewee',
    },
    {
      title: '询问人',
      dataIndex: 'interviewer',
      key: 'interviewer',
    },
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      sorter: (a, b) => new Date(a.date) - new Date(b.date),
    },
    {
      title: '地点',
      dataIndex: 'location',
      key: 'location',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (text) => {
        let color;
        let label;
        switch (text) {
          case 'suspect':
            color = 'red';
            label = '嫌疑人';
            break;
          case 'witness':
            color = 'green';
            label = '证人';
            break;
          case 'victim':
            color = 'blue';
            label = '被害人';
            break;
          default:
            color = 'default';
            label = '其他相关人';
        }
        return <Tag color={color}>{label}</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => {
        let color;
        let label;
        switch (text) {
          case 'completed':
            color = 'success';
            label = '已完成';
            break;
          case 'draft':
            color = 'processing';
            label = '草稿';
            break;
          default:
            color = 'default';
            label = text;
        }
        return <Tag color={color}>{label}</Tag>;
      },
    },
    {
      title: '文件类型',
      dataIndex: 'fileType',
      key: 'fileType',
      render: (text) => {
        switch (text) {
          case 'pdf':
            return <FilePdfOutlined style={{ color: '#ff4d4f', fontSize: 20 }} />;
          case 'word':
            return <FileWordOutlined style={{ color: '#1890ff', fontSize: 20 }} />;
          case 'audio':
            return <AudioOutlined style={{ color: '#52c41a', fontSize: 20 }} />;
          default:
            return text;
        }
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button 
            type="text" 
            icon={<EyeOutlined />} 
            onClick={() => handleViewTranscript(record)}
          />
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => handleEditTranscript(record)}
          />
          <Button 
            type="text" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDeleteTranscript(record.id)}
          />
        </Space>
      ),
    },
  ];

  // 处理添加笔录
  const handleAddTranscript = () => {
    form.resetFields();
    setEditingId(null);
    setIsModalVisible(true);
  };

  // 处理编辑笔录
  const handleEditTranscript = (record) => {
    setEditingId(record.id);
    form.setFieldsValue({
      ...record,
      date: record.date // 在实际应用中可能需要转换为moment对象
    });
    setIsModalVisible(true);
  };

  // 处理查看笔录
  const handleViewTranscript = (record) => {
    setCurrentRecord(record);
    setViewModalVisible(true);
  };

  // 处理删除笔录
  const handleDeleteTranscript = (id) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条笔录记录吗？',
      onOk() {
        setTranscripts(transcripts.filter(transcript => transcript.id !== id));
        message.success('笔录已删除');
      },
    });
  };

  // 处理模态框确认
  const handleOk = () => {
    form.validateFields()
      .then(values => {
        if (editingId === null) {
          // 新增
          const newTranscript = {
            id: Date.now().toString(),
            ...values,
            fileType: 'pdf', // 默认文件类型
            status: 'draft', // 默认状态
          };
          setTranscripts([...transcripts, newTranscript]);
          message.success('笔录已添加');
        } else {
          // 编辑
          setTranscripts(transcripts.map(transcript => 
            transcript.id === editingId ? { ...transcript, ...values } : transcript
          ));
          message.success('笔录已更新');
        }
        setIsModalVisible(false);
      })
      .catch(info => {
        console.log('表单验证失败:', info);
      });
  };

  // 处理模态框取消
  const handleCancel = () => {
    setIsModalVisible(false);
  };

  // 处理查看模态框关闭
  const handleViewModalClose = () => {
    setViewModalVisible(false);
  };

  // 处理文件上传
  const handleFileUpload = (info) => {
    if (info.file.status === 'done') {
      message.success(`${info.file.name} 文件上传成功`);
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} 文件上传失败`);
    }
  };

  return (
    <div>
      <Card 
        title="谈话笔录管理"
        extra={
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={handleAddTranscript}
          >
            添加笔录
          </Button>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="笔录列表" key="list">
            <Table 
              columns={columns} 
              dataSource={transcripts} 
              rowKey="id" 
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
          <TabPane tab="笔录导入" key="import">
            <div style={{ padding: 24, textAlign: 'center' }}>
              <Upload.Dragger
                name="file"
                multiple
                action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                onChange={handleFileUpload}
              >
                <p className="ant-upload-drag-icon">
                  <UploadOutlined />
                </p>
                <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                <p className="ant-upload-hint">
                  支持单个或批量上传。支持 PDF、Word、音频文件等格式。
                </p>
              </Upload.Dragger>
            </div>
          </TabPane>
          <TabPane tab="笔录统计" key="stats">
            <Empty description="笔录统计功能开发中" />
          </TabPane>
        </Tabs>
      </Card>

      <Modal
        title={editingId === null ? "添加笔录" : "编辑笔录"}
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="title"
            label="标题"
            rules={[{ required: true, message: '请输入标题' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="interviewee"
            label="被询问人"
            rules={[{ required: true, message: '请输入被询问人' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="interviewer"
            label="询问人"
            rules={[{ required: true, message: '请输入询问人' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="date"
            label="日期"
            rules={[{ required: true, message: '请选择日期' }]}
          >
            <Space>
              <Form.Item name="year" style={{ marginBottom: 0 }}>
                <Select 
                  placeholder="选择年份" 
                  style={{ width: 120 }}
                >
                  {Array.from({ length: 20 }, (_, i) => {
                    const year = new Date().getFullYear() - i;
                    return (
                      <Option key={year} value={year}>
                        {year}年
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
              <Form.Item name="month" style={{ marginBottom: 0 }}>
                <Select 
                  placeholder="选择月份" 
                  style={{ width: 100 }}
                >
                  {Array.from({ length: 12 }, (_, i) => {
                    const month = i + 1;
                    return (
                      <Option key={month} value={month}>
                        {month.toString().padStart(2, '0')}月
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Space>
          </Form.Item>
          <Form.Item
            name="location"
            label="地点"
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="type"
            label="类型"
            rules={[{ required: true, message: '请选择类型' }]}
          >
            <Select>
              <Option value="suspect">嫌疑人</Option>
              <Option value="witness">证人</Option>
              <Option value="victim">被害人</Option>
              <Option value="related">其他相关人</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="content"
            label="内容摘要"
          >
            <TextArea rows={4} />
          </Form.Item>
          <Form.Item
            name="attachment"
            label="附件"
          >
            <Upload
              action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
              listType="text"
              maxCount={1}
            >
              <Button icon={<UploadOutlined />}>上传文件</Button>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="查看笔录详情"
        open={viewModalVisible}
        onCancel={handleViewModalClose}
        footer={[
          <Button key="close" onClick={handleViewModalClose}>
            关闭
          </Button>,
        ]}
        width={700}
      >
        {currentRecord && (
          <div>
            <p><strong>标题:</strong> {currentRecord.title}</p>
            <p><strong>被询问人:</strong> {currentRecord.interviewee}</p>
            <p><strong>询问人:</strong> {currentRecord.interviewer}</p>
            <p><strong>日期:</strong> {currentRecord.date}</p>
            <p><strong>地点:</strong> {currentRecord.location}</p>
            <p><strong>类型:</strong> {
              currentRecord.type === 'suspect' ? '嫌疑人' :
              currentRecord.type === 'witness' ? '证人' :
              currentRecord.type === 'victim' ? '被害人' : '其他相关人'
            }</p>
            <p><strong>状态:</strong> {
              currentRecord.status === 'completed' ? '已完成' :
              currentRecord.status === 'draft' ? '草稿' : currentRecord.status
            }</p>
            <p><strong>内容:</strong></p>
            <div style={{ background: '#f5f5f5', padding: 16, borderRadius: 4, minHeight: 200 }}>
              <Empty description="内容预览暂不可用" />
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Transcripts;