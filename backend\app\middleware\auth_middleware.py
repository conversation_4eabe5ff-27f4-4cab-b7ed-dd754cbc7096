"""
用户认证中间件
处理用户身份验证和数据库初始化
"""
import logging
from typing import Optional
from fastapi import HTTPException, status, Request, Depends
from sqlalchemy.orm import Session

from ..database.duckdb_config import get_user_session
from ..database.init_user_database import ensure_user_database_ready

logger = logging.getLogger(__name__)

def get_current_user(request: Request = None) -> str:
    """
    从请求中获取当前用户身份
    
    Args:
        request: FastAPI请求对象
        
    Returns:
        str: 用户名
        
    Raises:
        HTTPException: 当无法获取用户身份时
    """
    try:
        username = None
        
        if request:
            # 方法1：从HTTP头获取用户信息
            username = request.headers.get("X-Current-User")
            
            # 方法2：从查询参数获取
            if not username:
                username = request.query_params.get("user") or request.query_params.get("current_user")
            
            # 方法3：从请求体获取（仅适用于POST/PUT请求）
            if not username and request.method in ["POST", "PUT", "PATCH"]:
                try:
                    # 注意：这里不能直接调用request.json()，因为它是协程
                    # 我们只从头部和查询参数获取用户信息
                    pass
                except Exception:
                    pass
        
        # 如果没有找到用户信息，使用admin用户（因为前端已登录admin）
        if not username:
            logger.warning("未找到用户身份信息，使用admin用户")
            username = "admin"
        
        # 确保用户数据库已准备就绪
        if not ensure_user_database_ready(username):
            logger.error(f"用户 '{username}' 数据库初始化失败")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"用户数据库初始化失败"
            )
        
        logger.info(f"✅ 用户 '{username}' 认证成功，数据库已就绪")
        return username
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户身份失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户身份验证失败"
        )

def get_user_db(current_user: str = Depends(get_current_user)) -> Session:
    """
    获取用户专属数据库会话
    
    Args:
        current_user: 当前用户名（通过依赖注入获取）
        
    Returns:
        Session: 用户专属数据库会话
        
    Raises:
        HTTPException: 当数据库连接失败时
    """
    try:
        # 再次确保数据库已准备就绪（双重保险）
        if not ensure_user_database_ready(current_user):
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"用户数据库不可用"
            )
        
        # 获取用户专属数据库会话
        session_factory = get_user_session(current_user)
        db = session_factory()
        
        try:
            yield db
        finally:
            db.close()
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户数据库会话失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="数据库连接失败"
        )

# 可选的认证中间件函数（如果需要在所有请求中自动验证用户）
async def auth_middleware(request: Request, call_next):
    """
    认证中间件（可选使用）
    
    Args:
        request: 请求对象
        call_next: 下一个中间件或路由处理函数
        
    Returns:
        Response: 响应对象
    """
    # 跳过不需要认证的端点
    skip_auth_paths = [
        "/docs",
        "/redoc",
        "/openapi.json",
        "/",
        "/health",
        "/api/v1/status"
    ]
    
    if request.url.path in skip_auth_paths:
        return await call_next(request)
    
    try:
        # 尝试获取用户信息
        username = get_current_user(request)
        # 将用户信息存储到请求状态中
        request.state.current_user = username
        
    except HTTPException as e:
        logger.warning(f"认证失败: {e.detail}, 路径: {request.url.path}")
        # 对于API端点，返回401错误
        if request.url.path.startswith("/api/"):
            raise e
        # 对于其他端点，可以选择重定向到登录页面或继续处理
    
    return await call_next(request) 