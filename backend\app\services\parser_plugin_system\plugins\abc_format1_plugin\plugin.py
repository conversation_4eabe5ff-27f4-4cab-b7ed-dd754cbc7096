"""
农业银行Format1解析器插件

特性与要求：
- 支持多工作表，仅解析含有“交易金额”表头的工作表，跳过无关表
- 字段映射（原始列名 → 标准字段）：
  外部账号→账号(account_number)，交易日期→交易日期(transaction_date)，
  交易时间(如132724)→交易时间(transaction_time，转换为HH:MM:SS)，
  交易金额→交易金额(transaction_amount)，交易后余额→交易余额(balance_amount)，
  对手户名→对方户名(counterparty_name)，对手账号→对方账号(counterparty_account)，
  对手开户行→对方银行(counterparty_bank)，摘要→交易方式(transaction_method)
- 其余未提及字段留空；若某字段不存在则置空

实现说明：
- 仅通过插件管理器调用，符合插件化规范
- 返回结构包含 accounts、transactions、transactions_by_account
"""

import os
import json
import time
import logging
import re
from pathlib import Path
from typing import Dict, Any, List, Optional

import pandas as pd

# 基础插件接口
try:
    from ...core.plugin_interface import BasePlugin
except ImportError:
    class BasePlugin:  # 测试环境兜底
        def __init__(self):
            self.name = "abc_format1_plugin"
            self.version = "1.0.0"
            self.description = "农业银行Format1解析器插件"
            self.bank_name = "中国农业银行"


logger = logging.getLogger(__name__)


class Plugin(BasePlugin):
    """农业银行Format1解析器插件"""

    def __init__(self, file_path: str = None):
        super().__init__()
        self.name = "abc_format1_plugin"
        self.version = "1.0.0"
        self.description = "农业银行Format1解析器插件（多工作表，按含‘交易金额’筛选）"
        self.bank_name = "中国农业银行"
        self.format_type = "format1"
        self.file_path = file_path

        self.start_time = time.time()
        self.error_count = 0

        # 解析结果容器
        self.accounts: List[Dict[str, Any]] = []
        self.transactions: List[Dict[str, Any]] = []

        # 列映射（原始→目标语义）
        self.source_fields = {
            "account_number": ["外部账号", "账号", "账户", "账/卡号", "卡号"],
            "account_holder": ["客户名", "持卡人", "户名", "账户名", "持卡人姓名"],
            "transaction_date": ["交易日期", "日期"],
            "transaction_time": ["交易时间", "时间"],
            "transaction_amount": ["交易金额", "金额", "发生额", "借方发生额", "贷方发生额", "收入金额", "支出金额"],
            "debit_amount": ["借方发生额", "借方", "支出金额", "支出"],
            "credit_amount": ["贷方发生额", "贷方", "收入金额", "收入"],
            "balance_amount": ["交易后余额", "交易余额", "账户余额", "余额"],
            "counterparty_name": ["对手户名", "对方户名", "交易对方名称", "对方名称"],
            "counterparty_account": ["对手账号", "对方账号", "交易对方账卡号", "对方账户"],
            "counterparty_bank": ["对手开户行", "对方银行", "交易对方账户开户行", "对方开户行"],
            "transaction_method": ["摘要"]
        }

    # 元数据与能力
    def get_metadata(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "bank_name": self.bank_name,
            "format_type": self.format_type,
            "supported_formats": ["中国农业银行Excel格式", "ABC_Format1"],
            "confidence_threshold": 0.7
        }

    def get_supported_formats(self) -> List[str]:
        return ["农业银行Excel格式", "ABC_Format1"]

    # 文件验证与置信度
    def validate_file(self, file_path: str) -> bool:
        try:
            if not os.path.exists(file_path):
                return False
            ext = os.path.splitext(file_path)[1].lower()
            if ext not in [".xlsx", ".xls"]:
                return False

            # 快速扫描是否含有"交易金额/金额/发生额/借方发生额/贷方发生额"列的工作表（放宽匹配）
            ext = os.path.splitext(file_path)[1].lower()
            engine = 'openpyxl' if ext == '.xlsx' else None
            # 使用ExcelFile对象，确保编码处理一致
            xls = pd.ExcelFile(file_path, engine=engine)
            for sheet in xls.sheet_names:
                try:
                    df_head = pd.read_excel(file_path, sheet_name=sheet, nrows=30, engine=engine)
                    if df_head.empty:
                        continue
                    header_row = self._locate_header_row(df_head)
                    if header_row >= 0:
                        df_check = pd.read_excel(file_path, sheet_name=sheet, header=header_row, nrows=2, engine=engine)
                        cols_norm = [str(col).strip() for col in df_check.columns]
                        # 金额关键词
                        amount_keys = ['交易金额', '金额', '发生额', '借方发生额', '贷方发生额']
                        date_keys = ['交易日期', '日期', '记账日期', '业务日期']
                        has_amount = any(any(k in col for k in amount_keys) for col in cols_norm)
                        has_date = any(any(k == col or k in col for k in date_keys) for col in cols_norm)
                        if has_amount or (has_amount and has_date):
                            return True
                except Exception:
                    continue
            return False
        except Exception:
            return False

    def calculate_confidence(self, file_path: str) -> float:
        try:
            if not self.validate_file(file_path):
                return 0.0
            score = 40.0  # 基础分（扩展名正确+扫描成功）
            ext = os.path.splitext(file_path)[1].lower()
            engine = 'openpyxl' if ext == '.xlsx' else None
            xls = pd.ExcelFile(file_path, engine=engine)
            best = 0.0
            for sheet in xls.sheet_names:
                try:
                    df_head = pd.read_excel(file_path, sheet_name=sheet, nrows=40, engine=engine)
                    if df_head.empty:
                        continue
                    header_row = self._locate_header_row(df_head)
                    if header_row >= 0:
                        df_check = pd.read_excel(file_path, sheet_name=sheet, header=header_row, nrows=20, engine=engine)
                        cols = [str(c).strip() for c in df_check.columns]
                        # 放宽：日期或金额字段出现即计分
                        req_groups = [["交易日期", "日期", "记账日期", "业务日期"], ["交易金额", "金额", "发生额", "借方发生额", "贷方发生额"]]
                        matches = 0
                        for group in req_groups:
                            if any(any(r in c for r in group) for c in cols):
                                matches += 1
                        sheet_score = matches * 20.0
                        if any("摘要" in c for c in cols):
                            sheet_score += 10.0
                        best = max(best, sheet_score)
                except Exception:
                    continue
            return min(100.0, score + best)
        except Exception:
            return 0.0

    # 主解析
    def parse(self, file_path: str = None) -> Dict[str, Any]:
        try:
            if file_path:
                self.file_path = file_path
            if not self.file_path:
                raise ValueError("未指定文件路径")

            # 不强制校验失败终止，尝试解析（避免因列名轻微差异阻断解析）
            valid_hint = self.validate_file(self.file_path)

            self.accounts = []
            self.transactions = []

            ext = os.path.splitext(self.file_path)[1].lower()
            engine = 'openpyxl' if ext == '.xlsx' else None
            xls = pd.ExcelFile(self.file_path, engine=engine)
            logger.info(f"发现工作表: {xls.sheet_names}")

            diagnostics: Dict[str, Any] = {"sheets": []}

            for sheet in xls.sheet_names:
                try:
                    df_head = pd.read_excel(self.file_path, sheet_name=sheet, nrows=60, engine=engine)
                    if df_head.empty:
                        continue
                    header_row = self._locate_header_row(df_head)
                    if header_row < 0:
                        continue

                    df = pd.read_excel(self.file_path, sheet_name=sheet, header=header_row, engine=engine)
                    if df.empty:
                        continue

                    # 仅处理包含“交易金额/金额/发生额/借方发生额/贷方发生额”的表
                    headers_norm = [str(c).strip() for c in df.columns]
                    if not any(("交易金额" in c or "金额" in c or "发生额" in c or "借方发生额" in c or "贷方发生额" in c) for c in headers_norm):
                        continue

                    diagnostics["sheets"].append({
                        "sheet": sheet,
                        "header_row": int(header_row),
                        "columns": headers_norm[:50]
                    })

                    self._process_sheet_dataframe(df)
                except Exception as e:
                    logger.warning(f"跳过工作表 {sheet}，原因: {e}")

            # Fallback：若未产生交易，放宽筛选条件并重试
            if not self.transactions:
                for sheet in xls.sheet_names:
                    try:
                        df_head = pd.read_excel(self.file_path, sheet_name=sheet, nrows=80, engine=engine)
                        if df_head.empty:
                            continue
                        header_row = self._locate_header_row(df_head)
                        if header_row < 0:
                            header_row = 0
                        df = pd.read_excel(self.file_path, sheet_name=sheet, header=header_row, engine=engine)
                        if df.empty:
                            continue
                        headers_norm = [str(c).strip() for c in df.columns]
                        has_date = any(any(k in c for k in ["交易日期", "日期", "记账日期", "业务日期"]) for c in headers_norm)
                        has_debit_credit = any(any(k in c for k in ["借方发生额", "贷方发生额", "发生额", "金额"]) for c in headers_norm)
                        if not (has_date or has_debit_credit):
                            continue
                        diagnostics["sheets"].append({
                            "sheet": sheet,
                            "header_row": int(header_row),
                            "columns": headers_norm[:50],
                            "fallback": True
                        })
                        self._process_sheet_dataframe(df)
                    except Exception as e:
                        logger.warning(f"放宽筛选跳过工作表 {sheet}，原因: {e}")

            result = self._build_result()
            if isinstance(result.get("summary"), dict):
                result["summary"]["diagnostics"] = diagnostics
                result["summary"]["parsed_at"] = pd.Timestamp.now().isoformat()
            return result

        except Exception as e:
            self.error_count += 1
            logger.error(f"农业银行Format1解析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "accounts": [],
                "transactions": [],
                "summary": {"total_accounts": 0, "total_transactions": 0}
            }

    # DataFrame处理
    def _process_sheet_dataframe(self, df: pd.DataFrame) -> None:
        # 统一列名为字符串，去空白
        df.columns = [str(c).strip() for c in df.columns]

        # 抽取列名映射（支持包含关系）
        colmap: Dict[str, Optional[str]] = {}
        for target, candidates in self.source_fields.items():
            colmap[target] = self._find_first_column(list(df.columns), candidates)

        # 逐行构建交易
        for _, row in df.iterrows():
            # 跳过关键字段缺失与金额为空的行
            amount_raw = self._get_cell(row, colmap.get("transaction_amount"))
            date_raw = self._get_cell(row, colmap.get("transaction_date"))
            if amount_raw in (None, "") and date_raw in (None, ""):
                continue

            account_number = self._get_cell(row, colmap.get("account_number")) or ""
            account_holder = self._to_str(self._get_cell(row, colmap.get("account_holder")))
            transaction_date = self._parse_date(self._get_cell(row, colmap.get("transaction_date")))
            transaction_time = self._parse_time(self._get_cell(row, colmap.get("transaction_time")))
            # 金额优先级：交易金额字段；若为空则借方/贷方发生额合成带符号金额
            amount = self._parse_amount(self._get_cell(row, colmap.get("transaction_amount")))
            if amount is None:
                debit = self._parse_amount(self._get_cell(row, colmap.get("debit_amount")))
                credit = self._parse_amount(self._get_cell(row, colmap.get("credit_amount")))
                if debit is not None and (credit is None or debit != 0):
                    amount = -abs(debit)
                elif credit is not None and (debit is None or credit != 0):
                    amount = abs(credit)
            balance = self._parse_amount(self._get_cell(row, colmap.get("balance_amount")))
            counterparty_name = self._to_str(self._get_cell(row, colmap.get("counterparty_name")))
            counterparty_account = self._clean_account(self._to_str(self._get_cell(row, colmap.get("counterparty_account"))))
            counterparty_bank = self._to_str(self._get_cell(row, colmap.get("counterparty_bank")))
            transaction_method = self._to_str(self._get_cell(row, colmap.get("transaction_method")))

            # 收支符号（可为空；若未提供则按金额正负兜底）
            dr_cr_flag = None
            if amount is not None:
                if amount > 0:
                    dr_cr_flag = "收"
                elif amount < 0:
                    dr_cr_flag = "支"
                else:
                    dr_cr_flag = "未知"

            transaction: Dict[str, Any] = {
                "sequence_number": len(self.transactions) + 1,
                "holder_name": account_holder or "",  # 从客户名字段提取
                "cardholder_name": account_holder or "",
                "bank_name": self.bank_name,
                "account_number": account_number,
                "card_number": account_number,
                "transaction_date": transaction_date or "",
                "transaction_time": transaction_time or "",
                "transaction_amount": amount if amount is not None else 0.0,
                "amount": amount if amount is not None else 0.0,
                "balance_amount": balance if balance is not None else 0.0,
                "balance": balance if balance is not None else 0.0,
                "transaction_method": transaction_method or "",
                "summary": transaction_method or "",
                "dr_cr_flag": dr_cr_flag or "",
                "counterparty_name": counterparty_name or "",
                "counterparty_account": counterparty_account or "",
                "counterparty_bank": counterparty_bank or "",
                "currency": "CNY"
            }

            # 兼容后端校验：提供transaction_id
            try:
                import uuid
                transaction["transaction_id"] = str(uuid.uuid4())
            except Exception:
                transaction["transaction_id"] = f"abc_{transaction['sequence_number']}"

            self.transactions.append(transaction)

    # 结果构建
    def _build_result(self) -> Dict[str, Any]:
        transactions_by_account: Dict[str, List[Dict[str, Any]]] = {}
        accounts: List[Dict[str, Any]] = []

        # 分组
        for tx in self.transactions:
            key = f"{tx.get('holder_name','')}_{tx.get('account_number','')}"
            transactions_by_account.setdefault(key, []).append(tx)

        # 生成账户信息
        for key, txs in transactions_by_account.items():
            first = txs[0]
            account_number = first.get("account_number", "")
            # 账户余额取最后一条交易余额
            last_balance = 0.0
            if txs:
                try:
                    last_balance = float(txs[-1].get("balance_amount", 0.0))
                except Exception:
                    last_balance = 0.0

            # 统计
            total_inflow = sum(float(t.get("transaction_amount", 0) or 0) for t in txs if (t.get("dr_cr_flag") == "收" or float(t.get("transaction_amount", 0) or 0) >= 0))
            total_outflow = sum(abs(float(t.get("transaction_amount", 0) or 0)) for t in txs if (t.get("dr_cr_flag") == "支" or float(t.get("transaction_amount", 0) or 0) < 0))

            dates = [t.get("transaction_date") for t in txs if t.get("transaction_date")]
            date_range = f"{min(dates)} 至 {max(dates)}" if dates else ""

            account = {
                "account_id": account_number or key,
                "holder_name": first.get("holder_name", ""),
                "cardholder_name": first.get("holder_name", ""),
                "person_name": first.get("holder_name", ""),
                "bank_name": self.bank_name,
                "account_number": account_number,
                "card_number": account_number,
                "account_type": "个人账户",
                "account_balance": last_balance,
                "transactions_count": len(txs),
                "total_inflow": total_inflow,
                "total_outflow": total_outflow,
                "date_range": date_range,
                "currency": "CNY"
            }
            accounts.append(account)

        confidence_score = self._calculate_confidence(accounts, self.transactions)

        return {
            "success": True,
            "message": f"成功解析 {len(accounts)} 个账户，{len(self.transactions)} 条交易记录",
            "accounts": accounts,
            "transactions": self.transactions,
            "transactions_by_account": transactions_by_account,
            "summary": {
                "total_accounts": len(accounts),
                "total_transactions": len(self.transactions),
                "confidence_score": confidence_score
            },
            "metadata": {
                "plugin_name": self.name,
                "plugin_version": self.version,
                "bank_name": self.bank_name,
                "file_path": self.file_path
            }
        }

    # 工具函数
    def _locate_header_row(self, df_head: pd.DataFrame) -> int:
        """定位表头行：含有“交易金额/金额/发生额/借方发生额/贷方发生额”，或同时包含“交易日期/日期/记账日期/业务日期”和前述金额列"""
        try:
            for i, row in df_head.iterrows():
                values = [str(v).strip() for v in row.values if pd.notna(v)]
                if not values:
                    continue
                # 精确或包含匹配
                amount_keys = ['交易金额', '金额', '发生额', '借方发生额', '贷方发生额']
                date_keys = ['交易日期', '日期', '记账日期', '业务日期']
                if any((isinstance(v, str) and any((k == v or k in v) for k in amount_keys)) for v in values):
                    return int(i)
                # 容错匹配：日期与金额组合出现
                has_date = any((isinstance(v, str) and any((k == v or k in v) for k in date_keys)) for v in values)
                has_amount = any((isinstance(v, str) and any((k == v or k in v) for k in amount_keys)) for v in values)
                if has_date and has_amount:
                    return int(i)
            return -1
        except Exception:
            return -1

    def _find_first_column(self, columns: List[str], candidates: List[str]) -> Optional[str]:
        normalized = [str(col).strip() for col in columns]
        # 精确匹配优先
        for candidate in candidates:
            for idx, col in enumerate(normalized):
                if col == candidate:
                    return columns[idx]
        # 包含匹配兜底（如 交易金额(元)）
        for candidate in candidates:
            for idx, col in enumerate(normalized):
                if candidate in col:
                    return columns[idx]
        return None

    def _get_cell(self, row: pd.Series, col: Optional[str]):
        if not col:
            return None
        try:
            return row.get(col, None)
        except Exception:
            return None

    def _to_str(self, value) -> str:
        if value is None or (isinstance(value, float) and pd.isna(value)):
            return ""
        result = str(value).strip()
        # 尝试处理可能的编码问题
        try:
            # 如果包含乱码字符，尝试重新编码
            if any(ord(char) > 65535 for char in result if isinstance(char, str)):
                # 检测到可能的编码问题，尝试修复
                try:
                    # 尝试从latin-1解码再用utf-8编码
                    result = result.encode('latin-1').decode('gbk', errors='ignore')
                except:
                    pass
        except:
            pass
        return result

    def _clean_account(self, value: str) -> str:
        if not value:
            return ""
        try:
            # 去掉科学计数法、小数点尾随.0
            if value.endswith('.0'):
                value = value[:-2]
            if 'E+' in value.upper():
                num = float(value)
                return str(int(num))
            return value.replace(' ', '')
        except Exception:
            return value

    def _parse_amount(self, value) -> Optional[float]:
        if value is None or value == "":
            return None
        try:
            s = str(value).replace(',', '').replace('，', '').replace('¥', '').strip()
            return float(s)
        except Exception:
            return None

    def _parse_date(self, value) -> Optional[str]:
        if value in (None, ""):
            return None
        try:
            # NaN 直接为空
            if isinstance(value, float) and pd.isna(value):
                return None

            # Excel 序列日（从 1899-12-30 开始计数的天数）
            if isinstance(value, (int, float)) and not isinstance(value, bool):
                # 经验阈值：> 59 规避 Excel 的 1900 闰年 bug 前的日期
                if value > 59:
                    base = pd.Timestamp('1899-12-30')
                    try:
                        days = int(value)
                        dt = base + pd.to_timedelta(days, unit='D')
                        return dt.strftime('%Y-%m-%d')
                    except Exception:
                        pass
                # 小于等于 59 或无效则继续其他解析

            s = str(value).strip()
            # 纯数字 YYYYMMDD
            if len(s) == 8 and s.isdigit():
                return f"{s[0:4]}-{s[4:6]}-{s[6:8]}"

            # 常规日期解析
            dt = pd.to_datetime(s, errors='coerce')
            if pd.isna(dt):
                return s
            return dt.strftime('%Y-%m-%d')
        except Exception:
            return str(value)

    def _parse_time(self, value) -> Optional[str]:
        if value in (None, ""):
            return None
        try:
            # NaN 直接为空
            if isinstance(value, float) and pd.isna(value):
                return None
            # Excel时间序列（<1的浮点）
            if isinstance(value, (int, float)) and float(value) < 1:
                total_seconds = int(float(value) * 24 * 3600)
                h = total_seconds // 3600
                m = (total_seconds % 3600) // 60
                s = total_seconds % 60
                return f"{h:02d}:{m:02d}:{s:02d}"

            s = str(value).strip()
            if s.lower() == 'nan':
                return None
            # 形如 154529.0 → 154529
            if s.endswith('.0') and s.replace('.0', '').isdigit():
                s = s.replace('.0', '')
            # 仅数字，如 132724 -> 13:27:24
            if s.isdigit():
                if len(s) >= 6:
                    return f"{s[0:2]}:{s[2:4]}:{s[4:6]}"
                elif len(s) == 5:  # HMMSS → 0H:MM:SS
                    return f"0{s[0]}:{s[1:3]}:{s[3:5]}"
                elif len(s) == 4:
                    return f"{s[0:2]}:{s[2:4]}:00"
            # 带冒号，且可能多段(子秒) 12:45:23:572272 -> 12:45:23
            if ':' in s:
                parts = s.split(':')
                if len(parts) >= 3:
                    return f"{parts[0].zfill(2)}:{parts[1].zfill(2)}:{parts[2].zfill(2)}"
                if len(parts) == 2:
                    return f"{parts[0].zfill(2)}:{parts[1].zfill(2)}:00"
            return s
        except Exception:
            return None

    def _calculate_confidence(self, accounts: List[Dict[str, Any]], transactions: List[Dict[str, Any]]) -> float:
        score = 0.0
        if accounts:
            score += 30
        if transactions:
            score += 40
        # 字段覆盖度
        sample = transactions[:20]
        checks = 0
        total = 0
        for t in sample:
            for f in ["transaction_date", "transaction_time", "transaction_amount", "balance_amount", "counterparty_name", "counterparty_account", "transaction_method"]:
                total += 1
                if t.get(f) not in (None, ""):
                    checks += 1
        if total:
            score += (checks / total) * 30.0
        return min(100.0, score)

    def extract_sample(self, file_path: str = None, limit: int = 10) -> Dict[str, Any]:
        """
        提取样本数据用于快速置信度评估 - 农业银行精准识别版本
        参考工商银行成功模式：数据特征识别 + 固定列位置 + 严格验证
        
        Args:
            file_path: 文件路径
            limit: 样本数量限制
            
        Returns:
            Dict: 包含样本账户和交易的字典
        """
        try:
            target_file = file_path or self.file_path
            if not target_file:
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
            
            logger.info(f"🔍 农业银行解析器开始精准样本提取，限制条数: {limit}")
            
            # 🔧 第一步：精准数据特征识别 - 农业银行特征：第0列客户名(中文2-4字) + 第1列外部账号(16-20位数字)
            df = pd.read_excel(target_file, header=None, nrows=50)
            if df.empty:
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
            
            logger.info(f"精准识别模式：读取农业银行文件前50行")
            
            # 🔧 第二步：边界检测 - 寻找农业银行数据特征
            data_start_idx = None
            cardholder_name = "农业银行测试用户"
            account_number = "****************"
            
            for idx in range(min(30, len(df))):
                row = df.iloc[idx]
                if len(row) > 1:
                    name_cell = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else ""
                    account_cell = str(row.iloc[1]).strip() if pd.notna(row.iloc[1]) else ""
                    
                    # 农业银行数据特征：第0列客户名(中文2-4字) + 第1列外部账号(16-20位数字)
                    if (re.match(r'^[\u4e00-\u9fa5]{2,4}$', name_cell) and 
                        re.match(r'^\d{16,20}$', account_cell)):
                        data_start_idx = idx
                        cardholder_name = name_cell
                        account_number = account_cell
                        logger.info(f"精准检测到农业银行数据起始行 {idx+1}: 客户名={cardholder_name}")
                        break
            
            if data_start_idx is None:
                logger.warning("精准识别：未找到农业银行数据特征")
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
            
            # 🔧 第三步：基于识别结果的固定列位置提取
            sample_transactions = []
            
            for idx in range(data_start_idx, min(data_start_idx + limit * 2, len(df))):
                if len(sample_transactions) >= limit:
                    break
                
                row = df.iloc[idx]
                if row.isna().all():
                    continue
                
                try:
                    # 🔧 第四步：严格验证 - 农业银行固定列位置验证
                    # 农业银行格式：客户名(0) 外部账号(1) 内部账号(2) 交易日期(3) 交易金额(10) 交易后余额(11)
                    if len(row) < 12:
                        continue
                    
                    name_check = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else ""
                    account_check = str(row.iloc[1]).strip() if pd.notna(row.iloc[1]) else ""
                    transaction_date = str(row.iloc[3]).strip() if pd.notna(row.iloc[3]) else ""
                    amount_raw = str(row.iloc[10]).strip() if pd.notna(row.iloc[10]) else ""
                    balance_raw = str(row.iloc[11]).strip() if pd.notna(row.iloc[11]) else ""
                    
                    # 严格验证：必须符合农业银行数据特征
                    if not (re.match(r'^[\u4e00-\u9fa5]{2,4}$', name_check) and 
                            re.match(r'^\d{16,20}$', account_check)):
                        continue
                    
                    # 验证交易日期格式 (YYYYMMDD)
                    if not re.match(r'^\d{8}$', transaction_date):
                        continue
                    
                    # 解析交易金额和余额
                    try:
                        transaction_amount = float(amount_raw)
                        balance_value = float(balance_raw) if balance_raw else 0.0
                    except:
                        continue
                    
                    # 判断收支方向：负数=支出，正数=收入
                    dr_cr_flag = "支" if transaction_amount < 0 else "收"
                    
                    # 验证必要字段完整性
                    if name_check and account_check and transaction_date and transaction_amount != 0:
                        transaction = {
                            'cardholder_name': name_check,  # 🔧 4维度姓名识别需要
                            'account_number': account_check,
                            'transaction_date': f"{transaction_date[:4]}-{transaction_date[4:6]}-{transaction_date[6:8]}",  # 转换为标准日期格式
                            'transaction_amount': abs(transaction_amount),  # 使用绝对值
                            'balance': balance_value,  # 🔧 4维度金额解析需要
                            'dr_cr_flag': dr_cr_flag,
                            'card_number': "",
                            'currency': 'CNY',
                            'transaction_method': '农业银行交易',
                            'bank_name': '中国农业银行'
                        }
                        sample_transactions.append(transaction)
                        
                except Exception as e:
                    logger.debug(f"跳过第{idx+1}行: {str(e)}")
                    continue
            
            # 构建样本账户信息
            sample_account = {
                'cardholder_name': cardholder_name,  # 🔧 4维度姓名识别需要
                'account_number': account_number,
                'card_number': "",
                'bank_name': '中国农业银行',
                'account_type': '个人账户' if len(cardholder_name) <= 4 else '企业账户'
            }
            
            logger.info(f"精准识别完成：提取{len(sample_transactions)}条样本交易")
            
            return {
                'accounts': [sample_account] if sample_transactions else [],
                'transactions': sample_transactions[:limit],
                'metadata': {
                    'sample_size': len(sample_transactions),
                    'evaluation_mode': 'precise_recognition',
                    'plugin_name': self.name,
                    'bank_name': '中国农业银行',
                    'format_type': 'abc_format1'
                }
            }
                
        except Exception as e:
            logger.error(f"农业银行解析器extract_sample方法失败: {str(e)}")
            return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0, 'error': str(e)}}


