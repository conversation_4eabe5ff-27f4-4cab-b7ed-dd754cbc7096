#!/usr/bin/env python3
"""
API诊断脚本 - 检查插件加载状态
"""
import requests
import json

try:
    # 测试基本连接
    print("🔍 测试后端连接...")
    response = requests.get('http://localhost:8000/docs', timeout=5)
    print(f"后端响应状态: {response.status_code}")
    
    # 尝试获取插件状态（如果有这样的端点）
    try:
        print("\n🔍 测试一个简单的API调用...")
        test_data = {
            'bank_name': '中国银行'
        }
        
        # 使用现有的测试文件
        test_file = 'test_files/test_boc_complete.xlsx'
        print(f"📄 使用测试文件: {test_file}")
        
        with open(test_file, 'rb') as f:
            files = {'file': ('test_boc_complete.xlsx', f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            data = {'bank_name': '中国银行'}
            
            response = requests.post('http://localhost:8000/api/parser/pre-analyze', 
                                   files=files, data=data, timeout=30)
            
            print(f"预解析API状态码: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"预解析结果:")
                print(f"  成功: {result.get('success', False)}")
                print(f"  消息: {result.get('message', '')}")
                candidates = result.get('candidates', [])
                print(f"  候选解析器数量: {len(candidates)}")
                
                for i, candidate in enumerate(candidates):
                    parser_id = candidate.get('parser_id', 'Unknown')
                    confidence = candidate.get('confidence_evaluation', {}).get('confidence', 0)
                    print(f"    {i+1}. {parser_id}: {confidence}分")
                
                # 详细输出结果
                print(f"\n详细结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            else:
                print(f"预解析失败: {response.text}")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ API请求失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
except Exception as e:
    print(f"❌ 连接测试失败: {e}")
