"""
建设银行Format2解析器插件
支持建设银行多表头结构Excel格式的银行流水解析
基于Legacy CCBFormat2ParserFixed的功能，适配插件系统接口
"""

import pandas as pd
import numpy as np
import time
import os
import traceback
from datetime import datetime
from typing import Dict, Any, List, Optional
import logging
import json
from pathlib import Path
import re

# 导入基础插件接口
try:
    from ...core.plugin_interface import BasePlugin
except ImportError:
    # 如果在测试环境中，创建一个简单的基础类
    class BasePlugin:
        def __init__(self):
            self.name = "default_plugin"
            self.version = "1.0.0"
            self.description = "默认解析器插件"
            self.bank_name = "通用银行"
            self.format_type = "standard"

logger = logging.getLogger(__name__)

class Plugin(BasePlugin):
    """建设银行Format2解析器插件"""
    
    def __init__(self, file_path: str = None):
        super().__init__()
        self.name = "ccb_format2_plugin"
        self.version = "1.0.0"
        self.description = "建设银行Format2解析器插件"
        self.bank_name = "中国建设银行"
        self.format_type = "format2"
        self.start_time = time.time()
        self.error_count = 0
        self.file_path = file_path
        
        # 加载配置
        self.config = self._load_config()
        
        # 解析结果
        self.accounts = []
        self.transactions = []
        self.transactions_by_account = {}
        
        # 目标工作表名称
        self.target_sheet_names = [
            "个人活期明细信息-新一代",
            "个人活期明细信息",
            "活期明细信息-新一代",
            "活期明细信息"
        ]
        
        # 数据表头关键字段
        self.header_keywords = ["交易日期", "借方发生额", "贷方发生额", "账户余额", "摘要"]
        
        # 客户信息行关键字段
        self.customer_info_keywords = ["客户名称:", "客户编号:", "客户账号:", "卡号:"]
        
        logger.info(f"✅ {self.name} v{self.version} 初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载插件配置"""
        try:
            config_path = Path(__file__).parent / "config.json"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"加载配置失败: {e}")
        
        # 默认配置
        return {
            "enabled": True,
            "confidence_threshold": 0.7,
            "plugin_settings": {
                "max_file_size": "100MB",
                "supported_extensions": [".xlsx", ".xls"]
            }
        }
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取插件元信息"""
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "supported_formats": ["中国建设银行多表头Excel格式"],
            "confidence_threshold": self.config.get("confidence_threshold", 0.7),
            "bank_name": self.bank_name,
            "format_type": self.format_type
        }
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的格式列表"""
        return ["建设银行多表头Excel格式", "CCB_Format2"]
    
    def validate_file(self, file_path: str) -> bool:
        """验证文件是否适用于此解析器"""
        try:
            if not os.path.exists(file_path):
                return False
            
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in ['.xlsx', '.xls']:
                return False
            
            # 检查是否有目标工作表
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            
            target_sheet = self._find_target_sheet(sheet_names)
            if not target_sheet:
                return False
            
            # 检查工作表内容是否包含建设银行Format2特征
            try:
                df = pd.read_excel(file_path, sheet_name=target_sheet, nrows=50, header=None)
                if df.empty:
                    return False
                
                content = df.to_string()
                
                # 检查是否包含多表头结构特征
                multi_header_keywords = ["个人活期明细信息", "客户名称:", "借方发生额", "贷方发生额"]
                matches = sum(1 for keyword in multi_header_keywords if keyword in content)
                
                return matches >= 3
                
            except Exception:
                return False
            
        except Exception as e:
            logger.error(f"文件验证失败: {e}")
            return False
    
    def calculate_confidence(self, file_path: str) -> float:
        """计算解析置信度"""
        try:
            if not self.validate_file(file_path):
                return 0.0
            
            confidence = 0.0
            
            # 文件扩展名检查
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext in ['.xlsx', '.xls']:
                confidence += 15.0
            
            # 工作表名称检查
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            
            target_sheet = self._find_target_sheet(sheet_names)
            if target_sheet:
                confidence += 25.0
                
                # 内容特征检查
                try:
                    df = pd.read_excel(file_path, sheet_name=target_sheet, nrows=100, header=None)
                    content = df.to_string()
                    
                    # 建设银行Format2特征检查
                    if "个人活期明细信息" in content:
                        confidence += 20.0
                    
                    # 多客户块特征检查
                    customer_blocks = len(re.findall(r'客户名称:', content))
                    if customer_blocks >= 2:
                        confidence += 15.0
                    elif customer_blocks >= 1:
                        confidence += 10.0
                    
                    # 表头字段检查
                    header_matches = sum(1 for keyword in self.header_keywords if keyword in content)
                    confidence += (header_matches / len(self.header_keywords)) * 20.0
                    
                    # 数据格式检查
                    if "借方发生额" in content and "贷方发生额" in content:
                        confidence += 5.0
                    
                except Exception:
                    pass
            
            return min(confidence, 100.0)
            
        except Exception as e:
            logger.error(f"置信度计算失败: {e}")
            return 0.0
    
    def _find_target_sheet(self, sheet_names: List[str]) -> Optional[str]:
        """寻找包含交易明细的目标工作表"""
        for sheet_name in sheet_names:
            if sheet_name in self.target_sheet_names:
                return sheet_name
        return None
    
    def _find_header_row(self, df: pd.DataFrame) -> Optional[int]:
        """寻找数据表头行"""
        for idx, row in df.iterrows():
            row_str = ' '.join([str(x) for x in row.values if pd.notna(x)])
            if all(keyword in row_str for keyword in self.header_keywords):
                return idx
        return None
    
    def _build_column_mapping(self, header_columns) -> Dict[str, int]:
        """建立列名到列索引的映射"""
        mapping = {}
        
        for idx, col_name in enumerate(header_columns):
            if pd.isna(col_name):
                continue
                
            col_str = str(col_name).strip()
            
            # 日期相关
            if any(keyword in col_str for keyword in ["交易日期", "日期"]):
                mapping["date"] = idx
            # 时间相关
            elif any(keyword in col_str for keyword in ["交易时间", "时间"]):
                mapping["time"] = idx
            # 摘要相关
            elif any(keyword in col_str for keyword in ["摘要", "备注", "说明"]):
                mapping["description"] = idx
            # 借方金额
            elif "借方发生额" in col_str:
                mapping["debit_amount"] = idx
            # 贷方金额
            elif "贷方发生额" in col_str:
                mapping["credit_amount"] = idx
            # 账户余额
            elif "账户余额" in col_str:
                mapping["balance"] = idx
            # 交易卡号
            elif any(keyword in col_str for keyword in ["交易卡号", "卡号"]):
                mapping["card_number"] = idx
            # 对方账号
            elif any(keyword in col_str for keyword in ["对方账号"]):
                mapping["counterparty_account"] = idx
            # 对方户名
            elif any(keyword in col_str for keyword in ["对方户名"]):
                mapping["counterparty_name"] = idx
        
        logger.info(f"列映射: {mapping}")
        return mapping
    
    def _parse_customer_info(self, customer_str: str) -> Optional[Dict[str, Any]]:
        """解析客户信息行"""
        try:
            customer_info = {}
            
            # 解析客户名称
            name_match = re.search(r'客户名称:([^，,]+)', customer_str)
            if name_match:
                customer_info['name'] = name_match.group(1).strip()
            
            # 解析客户编号
            customer_no_match = re.search(r'客户编号:([^，,]+)', customer_str)
            if customer_no_match:
                customer_info['customer_number'] = customer_no_match.group(1).strip()
            
            # 解析客户账号
            account_match = re.search(r'客户账号:([^，,]+)', customer_str)
            if account_match:
                customer_info['account_number'] = account_match.group(1).strip()
            
            # 解析卡号
            card_match = re.search(r'卡号:([^，,]+)', customer_str)
            if card_match:
                card_value = card_match.group(1).strip()
                customer_info['card_number'] = card_value if card_value else ""
            
            if customer_info.get('name'):
                return customer_info
            else:
                return None
                
        except Exception as e:
            logger.error(f"解析客户信息失败: {str(e)}")
            return None
    
    def _convert_to_number(self, value) -> Optional[float]:
        """将文本格式数字转换为数字格式"""
        if pd.isna(value) or value == '':
            return None
        
        try:
            # 处理各种数字格式
            if isinstance(value, (int, float)):
                return float(value)
            
            # 字符串处理
            str_value = str(value).strip()
            if not str_value:
                return None
            
            # 移除货币符号和千位分隔符
            clean_value = str_value.replace('¥', '').replace(',', '').replace('，', '').replace(' ', '')
            
            # 尝试转换为浮点数
            return float(clean_value)
            
        except (ValueError, TypeError):
            return None
    
    def _format_date(self, date_str: str) -> str:
        """格式化日期字符串"""
        try:
            if not date_str or pd.isna(date_str):
                return ""
            
            # 尝试解析日期
            date_obj = pd.to_datetime(str(date_str).strip())
            return date_obj.strftime("%Y-%m-%d")
            
        except Exception:
            return str(date_str).strip()
    
    def parse(self, file_path: str = None) -> Dict[str, Any]:
        """执行解析"""
        try:
            # 使用传入的文件路径或实例的文件路径
            if file_path:
                self.file_path = file_path
            elif not self.file_path:
                raise ValueError("未设置文件路径")
            
            logger.info(f"🔍 开始解析建设银行Format2文件: {os.path.basename(self.file_path)}")
            
            # 验证文件
            if not self.validate_file(self.file_path):
                raise ValueError("文件验证失败")
            
            # 重置状态
            self.accounts = []
            self.transactions = []
            self.transactions_by_account = {}
            
            # 读取Excel文件
            excel_file = pd.ExcelFile(self.file_path)
            sheet_names = excel_file.sheet_names
            logger.info(f"发现工作表: {sheet_names}")
            
            # 找到目标工作表
            target_sheet = self._find_target_sheet(sheet_names)
            if not target_sheet:
                return self._create_failed_result("未找到包含交易明细的工作表")
            
            logger.info(f"使用目标工作表: {target_sheet}")
            
            # 读取目标工作表数据
            df = pd.read_excel(self.file_path, sheet_name=target_sheet, header=None)
            logger.info(f"工作表数据形状: {df.shape}")
            
            # 解析数据表头位置
            header_row = self._find_header_row(df)
            if header_row is None:
                return self._create_failed_result("未找到数据表头行")
            
            logger.info(f"数据表头位置: 第{header_row + 1}行")
            
            # 解析客户信息和交易数据
            customers_data = self._parse_customers_data(df, header_row)
            if not customers_data:
                return self._create_failed_result("未找到有效的客户交易数据")
            
            logger.info(f"成功解析 {len(customers_data)} 个客户的数据")
            
            # 构建最终结果
            self._build_final_result(customers_data)
            
            # 计算置信度
            confidence = self._calculate_confidence_score(self.accounts, self.transactions)
            
            # 返回结果
            result = self._build_result(confidence)
            
            logger.info(f"✅ 解析完成: 共 {len(self.accounts)} 个账户, {len(self.transactions)} 条交易")
            return result
            
        except Exception as e:
            logger.error(f"文件解析失败: {str(e)}")
            self.handle_error(e)
            return self._create_failed_result(f"解析失败: {str(e)}")
    
    def _parse_customers_data(self, df: pd.DataFrame, header_row: int) -> List[Dict[str, Any]]:
        """解析客户信息和交易数据（核心方法 - 待续）"""
        # 这个方法将在下一步中完成，因为代码较长
        return []
    
    def _create_failed_result(self, error_msg: str) -> Dict[str, Any]:
        """创建失败结果"""
        return {
            'success': False,
            'error': error_msg,
            'accounts': [],
            'transactions_by_account': {},
            'summary': {'total_accounts': 0, 'total_transactions': 0},
            'metadata': {
                'plugin_name': self.name,
                'plugin_version': self.version,
                'error_details': error_msg
            }
        }
    
    def handle_error(self, error: Exception, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理错误"""
        logger.error(f"CCBFormat2插件错误: {str(error)}")
        if context:
            logger.error(f"错误上下文: {context}")
        
        self.error_count += 1
        self.last_error = str(error)
        
        return {
            'success': False,
            'error': f"CCBFormat2插件处理失败: {str(error)}",
            'plugin_name': self.name,
            'context': context or {}
        } 