import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Select, 
  Table, 
  Space, 
  Tag, 
  Modal, 
  message,
  Descriptions,
  Alert,
  Popconfirm,
  Row,
  Col,
  Statistic
} from 'antd';
import { 
  SearchOutlined, 
  DownloadOutlined, 
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';
// import zhCN from 'antd/locale/zh_CN';

// 导入API配置
import { buildApiUrl, API_ENDPOINTS } from '../../../config/api';

const { Option } = Select;
// const { RangePicker } = DatePicker;
// const { Title, Text } = Typography;

/**
 * 银行流水账户查询整理模块
 * 用于查询和管理已上传的账户数据
 */
const BankStatementsQuery = () => {
  const { projectId } = useParams();
  const [form] = Form.useForm();
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [accountTransactions, setAccountTransactions] = useState([]);
  const [showTransactionModal, setShowTransactionModal] = useState(false);
  const [transactionLoading, setTransactionLoading] = useState(false);

  // 页面加载时获取账户数据
  useEffect(() => {
    if (projectId) {
      fetchAccounts();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectId]);

  // 获取项目的所有账户数据
  const fetchAccounts = async (searchParams = {}) => {
    setLoading(true);
    try {
      console.log('🔍 开始查询项目账户数据，项目ID:', projectId);
      
      const params = new URLSearchParams({
        project_id: projectId,
        ...searchParams
      });
      
      const response = await fetch(`${buildApiUrl(API_ENDPOINTS.accounts)}?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `查询失败: ${response.status}`);
      }
      
      const result = await response.json();
      console.log('📊 账户查询结果:', result);
      
      const baseAccounts = result.accounts || [];
      // 🔧 修复：以交易明细为准回填交易笔数，避免同账号不同卡号被合并导致的错误显示
      const withCounts = await Promise.all(
        baseAccounts.map(async (acc) => {
          try {
            const url = buildApiUrl(`${API_ENDPOINTS.accounts}/transactions?account_number=${encodeURIComponent(acc.account_number)}&project_id=${projectId}&card_number=${encodeURIComponent(acc.card_number || '')}`);
            const r = await fetch(url, { headers: { 'Content-Type': 'application/json' } });
            if (!r.ok) throw new Error(`count fetch ${r.status}`);
            const data = await r.json();
            return { ...acc, transactions_count: (data.total ?? (data.transactions || []).length) };
          } catch (e) {
            console.warn('⚠️ 加载交易笔数失败, 使用原值', e);
            return acc;
          }
        })
      );

      setAccounts(withCounts);
      
      if (result.accounts && result.accounts.length > 0) {
        message.success(`查询到 ${result.accounts.length} 个账户`);
      } else {
        message.info('未查询到符合条件的账户数据');
      }
      
    } catch (error) {
      console.error('❌ 查询账户数据失败:', error);
      message.error(`查询失败: ${error.message}`);
      setAccounts([]);
    } finally {
      setLoading(false);
    }
  };

  // 获取账户的交易明细
  const fetchAccountTransactions = async (account) => {
    setTransactionLoading(true);
    try {
      console.log('🔍 查询账户交易明细:', account.account_number);
      
      const response = await fetch(buildApiUrl(`${API_ENDPOINTS.accounts}/transactions?account_number=${encodeURIComponent(account.account_number)}&project_id=${projectId}&card_number=${encodeURIComponent(account.card_number || '')}`), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `查询交易明细失败: ${response.status}`);
      }
      
      const result = await response.json();
      console.log('📋 交易明细查询结果:', result);
      
      setAccountTransactions(result.transactions || []);
      
    } catch (error) {
      console.error('❌ 查询交易明细失败:', error);
      message.error(`查询交易明细失败: ${error.message}`);
      setAccountTransactions([]);
    } finally {
      setTransactionLoading(false);
    }
  };

  // 删除账户
  const deleteAccount = async (account) => {
    try {
      console.log('🗑️ 删除账户:', account.account_number, account.card_number);
      
      // 精确删除：带上card_number，仅删除该卡号下的交易；若交易删尽会自动删除账户
      const params = new URLSearchParams();
      if (account.card_number) params.append('card_number', account.card_number);
      if (account.account_number) params.append('account_number', account.account_number);
      if (account.holder_name) params.append('holder_name', account.holder_name);

      const response = await fetch(buildApiUrl(`${API_ENDPOINTS.accounts}/${account.account_id}?${params.toString()}`), {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `删除失败: ${response.status}`);
      }
      
      const res = await response.json();
      message.success(res.message || `删除完成`);
      
      // 刷新账户列表
      fetchAccounts();
      
    } catch (error) {
      console.error('❌ 删除账户失败:', error);
      message.error(`删除失败: ${error.message}`);
    }
  };

  // 查看账户交易详情
  const viewAccountTransactions = async (account) => {
    // 再次以接口返回的total覆盖一次，确保弹窗顶部“交易笔数”也准确
    setSelectedAccount(account);
    setShowTransactionModal(true);
    await fetchAccountTransactions(account);
    try {
      const url = buildApiUrl(`${API_ENDPOINTS.accounts}/transactions?account_number=${encodeURIComponent(account.account_number)}&project_id=${projectId}&card_number=${encodeURIComponent(account.card_number || '')}`);
      const r = await fetch(url, { headers: { 'Content-Type': 'application/json' } });
      if (r.ok) {
        const data = await r.json();
        setSelectedAccount({ ...account, transactions_count: data.total ?? (data.transactions || []).length });
      }
    } catch (e) {
      // ignore
    }
  };

  // 账户信息表格列定义 - 修复为三列并排显示
  const accountColumns = [
    {
      title: '持卡人',
      dataIndex: 'holder_name',
      key: 'holder_name',
      width: 150,
      render: (text) => (
        <span style={{ fontWeight: 'bold', color: text ? '#1890ff' : '#999' }}>
          {text || '-'}
        </span>
      )
    },
    {
      title: '账号',
      dataIndex: 'account_number',
      key: 'account_number',
      width: 220,
      render: (text) => (
        <span style={{ 
          whiteSpace: 'nowrap', 
          fontFamily: 'monospace', 
          fontSize: '16px',
          fontWeight: '500'
        }}>
          {text || '-'}
        </span>
      )
    },
    {
      title: '卡号',
      dataIndex: 'card_number',
      key: 'card_number',
      width: 220,
      render: (text) => (
        <span style={{ 
          whiteSpace: 'nowrap', 
          fontFamily: 'monospace', 
          fontSize: '16px',
          fontWeight: '500'
        }}>
          {text || '-'}
        </span>
      )
    },
    {
      title: '交易笔数',
      dataIndex: 'transactions_count',
      key: 'transactions_count',
      width: 100,
      render: (text) => `${text || 0} 笔`
    },
    {
      title: '收入总额',
      dataIndex: 'total_inflow',
      key: 'total_inflow',
      width: 130,
      render: (amount) => {
        const numAmount = parseFloat(amount) || 0;
        return (
          <span style={{ color: '#3f8600', fontFamily: 'monospace' }}>
            ¥{numAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
          </span>
        );
      }
    },
    {
      title: '支出总额',
      dataIndex: 'total_outflow',
      key: 'total_outflow',
      width: 130,
      render: (amount) => {
        const numAmount = parseFloat(amount) || 0;
        return (
          <span style={{ color: '#cf1322', fontFamily: 'monospace' }}>
            ¥{numAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
          </span>
        );
      }
    },
    {
      title: '时间范围',
      dataIndex: 'date_range',
      key: 'date_range',
      width: 130,
      render: (text) => text || '未知'
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (text, record) => (
        <Space>
          <Button 
            type="primary" 
            size="small"
            icon={<EyeOutlined />}
            onClick={() => viewAccountTransactions(record)}
          >
            查看交易
          </Button>
          <Popconfirm
            title="确认删除"
            description={`确定要删除账户"${record.holder_name}"吗？此操作不可恢复！`}
            onConfirm={() => deleteAccount(record)}
            okText="确认删除"
            okType="danger"
            cancelText="取消"
          >
            <Button 
              type="primary" 
              danger
              size="small"
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  // 交易记录表格列定义
  const transactionColumns = [
    {
      title: '序号',
      dataIndex: 'sequence_number',
      key: 'sequence_number',
      width: 80,
      render: (text) => text || '-'
    },
    {
      title: '交易日期',
      dataIndex: 'transaction_date',
      key: 'transaction_date',
      width: 110,
      render: (text) => text || '-'
    },
    {
      title: '交易时间',
      dataIndex: 'transaction_time',
      key: 'transaction_time',
      width: 100,
      render: (text) => text || '-'
    },
    {
      title: '交易方式',
      dataIndex: 'transaction_method',
      key: 'transaction_method',
      width: 120,
      render: (text) => text || '-'
    },
    {
      title: '交易金额',
      dataIndex: 'transaction_amount',
      key: 'transaction_amount',
      width: 130,
      render: (amount, record) => {
        const numAmount = parseFloat(amount) || 0;
        const isIncome = record?.dr_cr_flag === '收';
        const isOutflow = record?.dr_cr_flag === '支';
        const color = isIncome ? '#3f8600' : (isOutflow ? '#cf1322' : (numAmount >= 0 ? '#3f8600' : '#cf1322'));
        return (
          <span style={{ 
            color,
            fontFamily: 'monospace'
          }}>
            ¥{Math.abs(numAmount).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
          </span>
        );
      }
    },
    {
      title: '交易余额',
      dataIndex: 'balance_amount',
      key: 'balance_amount',
      width: 130,
      render: (amount) => {
        const numAmount = parseFloat(amount) || 0;
        return (
          <span style={{ fontFamily: 'monospace' }}>
            ¥{numAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
          </span>
        );
      }
    },
    {
      title: '收支符号',
      dataIndex: 'dr_cr_flag',
      key: 'dr_cr_flag',
      width: 80,
      render: (flag) => {
        const isIncome = flag === '收';
        return (
          <Tag color={isIncome ? 'green' : 'red'}>
            {flag || '未知'}
          </Tag>
        );
      }
    },
    {
      title: '对方户名',
      dataIndex: 'counterparty_name',
      key: 'counterparty_name',
      width: 160,
      render: (text, record) => (text || record?.counterpart_name || '-')
    },
    {
      title: '对方账号',
      dataIndex: 'counterparty_account',
      key: 'counterparty_account',
      width: 180,
      render: (text, record) => (
        <span style={{ fontFamily: 'monospace' }}>
          {text || record?.counterpart_account || '-'}
        </span>
      )
    },
    {
      title: '备注',
      dataIndex: 'remark1',
      key: 'remark1',
      width: 120,
      render: (text) => text || '-'
    }
  ];

  // 处理查询表单提交
  const handleSearch = (values) => {
    console.log('🔍 执行账户查询，参数:', values);
    
    const searchParams = {};
    
    if (values.holder_name) {
      searchParams.holder_name = values.holder_name;
    }
    if (values.account_number) {
      searchParams.account_number = values.account_number;
    }
    if (values.card_number) {
      searchParams.card_number = values.card_number;
    }
    if (values.bank_name) {
      searchParams.bank_name = values.bank_name;
    }
    if (values.startYear || values.endYear) {
      if (values.startYear) {
        searchParams.start_date = `${values.startYear}-01-01`;
      }
      if (values.endYear) {
        searchParams.end_date = `${values.endYear}-12-31`;
      }
    }
    
    fetchAccounts(searchParams);
  };

  // 处理重置
  const handleReset = () => {
    form.resetFields();
    fetchAccounts(); // 重新加载所有账户
  };

  // 处理导出
  const handleExport = () => {
    if (accounts.length === 0) {
      message.warning('没有可导出的数据');
      return;
    }
    
    // 这里可以实现导出功能
    message.info('导出功能开发中...');
  };

  return (
    <div>
      <Card title="银行流水查询整理" extra={
        <Space>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={() => fetchAccounts()}
            loading={loading}
          >
            刷新
          </Button>
          <Button 
            icon={<DownloadOutlined />} 
            onClick={handleExport}
            disabled={accounts.length === 0}
          >
            导出Excel
          </Button>
        </Space>
      }>
        <Alert
          message="账户数据查询"
          description={`当前项目：${projectId}。此页面用于查询和管理已上传的银行账户数据，支持查看交易明细和删除账户。`}
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        {/* 查询表单 */}
        <Card title="查询条件" size="small" style={{ marginBottom: 16 }}>
          <Form
            form={form}
            layout="inline"
            onFinish={handleSearch}
            style={{ gap: '8px' }}
          >
            <Form.Item name="holder_name" label="持卡人">
              <Input 
                placeholder="请输入持卡人姓名" 
                style={{ 
                  width: 150, 
                  height: 36, 
                  fontSize: '18px'
                }} 
              />
            </Form.Item>
            
            <Form.Item name="account_number" label="账号">
              <Input 
                placeholder="请输入账号" 
                style={{ 
                  width: 180, 
                  height: 36,
                  fontSize: '18px'
                }} 
              />
            </Form.Item>
            
            <Form.Item name="card_number" label="卡号">
              <Input 
                placeholder="请输入卡号" 
                style={{ 
                  width: 160, 
                  height: 36,
                  fontSize: '18px'
                }} 
              />
            </Form.Item>
            
            <Form.Item name="bank_name" label="银行">
              <Select 
                placeholder="请选择银行" 
                style={{ 
                  width: 150, 
                  height: 36,
                  fontSize: '18px'
                }} 
                allowClear
                size="middle"
              >
                <Option value="中国工商银行">工商银行</Option>
                <Option value="中国农业银行">农业银行</Option>
                <Option value="中国银行">中国银行</Option>
                <Option value="中国建设银行">建设银行</Option>
                <Option value="交通银行">交通银行</Option>
                <Option value="平安银行">平安银行</Option>
                <Option value="招商银行">招商银行</Option>
                <Option value="浦发银行">浦发银行</Option>
                <Option value="中信银行">中信银行</Option>
                <Option value="北部湾银行">北部湾银行</Option>
                <Option value="农村信用社">农村信用社</Option>
              </Select>
            </Form.Item>
            
            <Form.Item label="时间范围">
              <Space>
                <Form.Item name="startYear" style={{ marginBottom: 0 }}>
                  <Select 
                    placeholder="选择年份" 
                    style={{ 
                      width: 120, 
                      height: 36,
                      fontSize: '18px'
                    }} 
                    allowClear
                  >
                    {Array.from({ length: 20 }, (_, i) => {
                      const year = new Date().getFullYear() - i;
                      return (
                        <Option key={year} value={year}>
                          {year}年
                        </Option>
                      );
                    })}
                  </Select>
                </Form.Item>
                <span style={{ fontSize: '18px', color: '#8c8c8c' }}>至</span>
                <Form.Item name="endYear" style={{ marginBottom: 0 }}>
                  <Select 
                    placeholder="选择年份" 
                    style={{ 
                      width: 120, 
                      height: 36,
                      fontSize: '18px'
                    }} 
                    allowClear
                  >
                    {Array.from({ length: 20 }, (_, i) => {
                      const year = new Date().getFullYear() - i;
                      return (
                        <Option key={year} value={year}>
                          {year}年
                        </Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Space>
            </Form.Item>
            
            <Form.Item>
              <Space>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  icon={<SearchOutlined />}
                  loading={loading}
                  style={{ 
                    height: 36,
                    fontSize: '18px'
                  }}
                >
                  查询
                </Button>
                <Button 
                  onClick={handleReset}
                  style={{ 
                    height: 36,
                    fontSize: '18px'
                  }}
                >
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Card>

        {/* 查询结果统计 */}
        {accounts.length > 0 && (
          <Card title="查询结果统计" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={6}>
                <Statistic 
                  title="账户总数" 
                  value={accounts.length} 
                  prefix="🏦"
                />
              </Col>
              <Col span={6}>
                <Statistic 
                  title="交易总笔数" 
                  value={accounts.reduce((sum, acc) => sum + (acc.transactions_count || 0), 0)} 
                  prefix="📊"
                />
              </Col>
              <Col span={6}>
                <Statistic 
                  title="总收入" 
                  value={accounts.reduce((sum, acc) => sum + (parseFloat(acc.total_inflow) || 0), 0)}
                  precision={2}
                  prefix="¥"
                  valueStyle={{ color: '#3f8600' }}
                  formatter={(value) => value.toLocaleString('zh-CN')}
                />
              </Col>
              <Col span={6}>
                <Statistic 
                  title="总支出" 
                  value={accounts.reduce((sum, acc) => sum + (parseFloat(acc.total_outflow) || 0), 0)}
                  precision={2}
                  prefix="¥"
                  valueStyle={{ color: '#cf1322' }}
                  formatter={(value) => value.toLocaleString('zh-CN')}
                />
              </Col>
            </Row>
          </Card>
        )}

        {/* 账户列表表格 */}
        <Table
          columns={accountColumns}
          dataSource={accounts}
          rowKey="account_id"
          loading={loading}
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
          }}
          scroll={{ x: 1350 }}
          locale={{
            emptyText: loading ? '查询中...' : '暂无账户数据，请先上传银行流水文件'
          }}
        />
      </Card>

      {/* 交易明细Modal */}
      <Modal
        title={`交易明细 - ${selectedAccount?.holder_name || '未知'}`}
        open={showTransactionModal}
        onCancel={() => {
          setShowTransactionModal(false);
          setSelectedAccount(null);
          setAccountTransactions([]);
        }}
        footer={null}
        width={1400}
        style={{ top: 20 }}
      >
        {selectedAccount && (
          <>
            <Descriptions bordered size="small" style={{ marginBottom: 16 }}>
              <Descriptions.Item label="持卡人">{selectedAccount.holder_name || '未知'}</Descriptions.Item>
              <Descriptions.Item label="账号">{selectedAccount.account_number || '未知'}</Descriptions.Item>
              <Descriptions.Item label="银行">{selectedAccount.bank_name || '未知'}</Descriptions.Item>
              <Descriptions.Item label="交易笔数">{selectedAccount.transactions_count || 0} 笔</Descriptions.Item>
              <Descriptions.Item label="收入总额">
                <span style={{ color: '#3f8600' }}>
                  ¥{(parseFloat(selectedAccount.total_inflow) || 0).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="支出总额">
                <span style={{ color: '#cf1322' }}>
                  ¥{(parseFloat(selectedAccount.total_outflow) || 0).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </span>
              </Descriptions.Item>
            </Descriptions>
            
            <Table 
              columns={transactionColumns}
              dataSource={accountTransactions}
              rowKey="transaction_id"
              loading={transactionLoading}
              pagination={{
                pageSize: 50,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条交易记录`
              }}
              scroll={{ x: 1200, y: 400 }}
              size="small"
            />
          </>
        )}
      </Modal>
    </div>
  );
};

export default BankStatementsQuery;