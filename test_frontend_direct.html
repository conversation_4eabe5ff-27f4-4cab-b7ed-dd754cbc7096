<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>平安银行模板测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>平安银行模板加载测试</h1>
    
    <div class="test-section">
        <h2>1. 测试所有模板API</h2>
        <button onclick="testAllTemplates()">测试所有模板</button>
        <div id="allTemplatesResult"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 测试平安银行模板API</h2>
        <button onclick="testPinganTemplates()">测试平安银行模板</button>
        <div id="pinganTemplatesResult"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 测试前端API配置</h2>
        <button onclick="testFrontendConfig()">测试前端配置</button>
        <div id="frontendConfigResult"></div>
    </div>

    <div class="test-section">
        <h2>4. 测试前端实际页面</h2>
        <button onclick="testActualFrontend()">打开前端页面</button>
        <button onclick="testFrontendAPI()">测试前端API调用</button>
        <div id="frontendPageResult"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://127.0.0.1:8000';
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const className = type === 'error' ? 'error' : (type === 'success' ? 'success' : 'info');
            element.innerHTML += `<div class="${className}">${message}</div>`;
        }
        
        function clearLog(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }
        
        async function testAllTemplates() {
            const resultId = 'allTemplatesResult';
            clearLog(resultId);
            log(resultId, '🔍 测试所有模板API...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/parser/v2/templates`);
                log(resultId, `状态码: ${response.status}`, 'info');
                
                if (response.ok) {
                    const result = await response.json();
                    const templates = Array.isArray(result) ? result : (result.templates || []);
                    log(resultId, `✅ 成功获取 ${templates.length} 个模板`, 'success');
                    
                    // 统计各银行模板数量
                    const bankStats = {};
                    templates.forEach(template => {
                        const bank = template.bankName || '未知银行';
                        bankStats[bank] = (bankStats[bank] || 0) + 1;
                    });
                    
                    log(resultId, '📊 银行模板统计:', 'info');
                    Object.entries(bankStats).forEach(([bank, count]) => {
                        log(resultId, `   ${bank}: ${count}个模板`, 'info');
                    });
                    
                    // 检查平安银行
                    const pinganTemplates = templates.filter(t => t.bankName === '平安银行');
                    if (pinganTemplates.length > 0) {
                        log(resultId, `✅ 发现平安银行模板: ${pinganTemplates.length}个`, 'success');
                        pinganTemplates.forEach(template => {
                            log(resultId, `   - ${template.templateName} (ID: ${template.templateId})`, 'success');
                        });
                    } else {
                        log(resultId, '❌ 未发现平安银行模板', 'error');
                    }
                    
                } else {
                    log(resultId, `❌ API调用失败: ${response.statusText}`, 'error');
                    const errorText = await response.text();
                    log(resultId, `错误详情: ${errorText}`, 'error');
                }
            } catch (error) {
                log(resultId, `❌ 请求异常: ${error.message}`, 'error');
            }
        }
        
        async function testPinganTemplates() {
            const resultId = 'pinganTemplatesResult';
            clearLog(resultId);
            log(resultId, '🔍 测试平安银行模板API...', 'info');
            
            try {
                const url = `${API_BASE_URL}/api/parser/v2/templates?bank_name=平安银行`;
                log(resultId, `请求URL: ${url}`, 'info');
                
                const response = await fetch(url);
                log(resultId, `状态码: ${response.status}`, 'info');
                
                if (response.ok) {
                    const result = await response.json();
                    const templates = Array.isArray(result) ? result : (result.templates || []);
                    log(resultId, `✅ 成功获取平安银行模板 ${templates.length} 个`, 'success');
                    
                    templates.forEach(template => {
                        log(resultId, `📋 模板详情:`, 'info');
                        log(resultId, `   ID: ${template.templateId}`, 'info');
                        log(resultId, `   名称: ${template.templateName}`, 'info');
                        log(resultId, `   银行: ${template.bankName}`, 'info');
                        log(resultId, `   描述: ${template.description}`, 'info');
                    });
                    
                    // 测试前端格式转换
                    const frontendTemplates = templates.map((template, index) => ({
                        value: template.templateId,
                        label: template.templateName,
                        description: template.description,
                        confidence: template.confidence || 0,
                        isDefault: template.isRecommended || index === 0,
                        isSmartAnalyzed: false
                    }));
                    
                    log(resultId, `🔧 前端格式转换结果:`, 'info');
                    log(resultId, `<pre>${JSON.stringify(frontendTemplates, null, 2)}</pre>`, 'info');
                    
                } else {
                    log(resultId, `❌ API调用失败: ${response.statusText}`, 'error');
                    const errorText = await response.text();
                    log(resultId, `错误详情: ${errorText}`, 'error');
                }
            } catch (error) {
                log(resultId, `❌ 请求异常: ${error.message}`, 'error');
            }
        }
        
        async function testFrontendConfig() {
            const resultId = 'frontendConfigResult';
            clearLog(resultId);
            log(resultId, '🔍 测试前端API配置...', 'info');
            
            // 测试不同的API端点
            const endpoints = [
                '/api/parser/templates',
                '/api/parser/v2/templates',
                '/api/parser/templates?bank_name=平安银行',
                '/api/parser/v2/templates?bank_name=平安银行'
            ];
            
            for (const endpoint of endpoints) {
                try {
                    log(resultId, `测试端点: ${endpoint}`, 'info');
                    const response = await fetch(`${API_BASE_URL}${endpoint}`);
                    log(resultId, `   状态码: ${response.status}`, response.ok ? 'success' : 'error');
                    
                    if (response.ok) {
                        const result = await response.json();
                        const templates = Array.isArray(result) ? result : (result.templates || []);
                        const pinganCount = templates.filter(t => t.bankName === '平安银行').length;
                        log(resultId, `   模板总数: ${templates.length}, 平安银行: ${pinganCount}`, 'success');
                    } else {
                        const errorText = await response.text();
                        log(resultId, `   错误: ${errorText}`, 'error');
                    }
                } catch (error) {
                    log(resultId, `   异常: ${error.message}`, 'error');
                }
            }
        }
        
        async function testActualFrontend() {
            const resultId = 'frontendPageResult';
            clearLog(resultId);
            log(resultId, '🔍 测试前端实际页面...', 'info');

            // 在新窗口中打开前端页面
            const frontendWindow = window.open('http://localhost:3000', '_blank');
            log(resultId, '✅ 已在新窗口中打开前端页面', 'success');
            log(resultId, '请在新窗口中手动测试平安银行模板加载功能', 'info');
        }

        async function testFrontendAPI() {
            const resultId = 'frontendPageResult';
            clearLog(resultId);
            log(resultId, '🔍 模拟前端API调用流程...', 'info');

            try {
                // 步骤1：模拟页面加载时获取所有模板
                log(resultId, '1. 模拟页面加载时获取所有模板...', 'info');
                const allTemplatesResponse = await fetch(`${API_BASE_URL}/api/parser/v2/templates`);
                if (allTemplatesResponse.ok) {
                    const allTemplates = await allTemplatesResponse.json();
                    const templates = Array.isArray(allTemplates) ? allTemplates : (allTemplates.templates || []);
                    log(resultId, `   ✅ 获取到 ${templates.length} 个模板`, 'success');

                    // 检查平安银行模板
                    const pinganTemplates = templates.filter(t => t.bankName === '平安银行');
                    if (pinganTemplates.length > 0) {
                        log(resultId, `   ✅ 发现平安银行模板: ${pinganTemplates.length}个`, 'success');
                    } else {
                        log(resultId, '   ❌ 未发现平安银行模板', 'error');
                    }
                } else {
                    log(resultId, '   ❌ 获取所有模板失败', 'error');
                }

                // 步骤2：模拟用户选择平安银行
                log(resultId, '2. 模拟用户选择平安银行...', 'info');
                const pinganTemplatesResponse = await fetch(`${API_BASE_URL}/api/parser/v2/templates?bank_name=平安银行`);
                if (pinganTemplatesResponse.ok) {
                    const pinganResult = await pinganTemplatesResponse.json();
                    const pinganTemplates = Array.isArray(pinganResult) ? pinganResult : (pinganResult.templates || []);
                    log(resultId, `   ✅ 平安银行专用模板: ${pinganTemplates.length}个`, 'success');

                    // 转换为前端格式
                    const frontendTemplates = pinganTemplates.map((template, index) => ({
                        value: template.templateId,
                        label: template.templateName,
                        description: template.description,
                        confidence: template.confidence || 0,
                        isDefault: template.isRecommended || index === 0,
                        isSmartAnalyzed: false
                    }));

                    log(resultId, '   🔧 前端模板选项:', 'info');
                    frontendTemplates.forEach(template => {
                        log(resultId, `     - ${template.label} (${template.value})`, 'success');
                    });

                } else {
                    log(resultId, '   ❌ 获取平安银行模板失败', 'error');
                }

                // 步骤3：模拟智能分析（如果有测试文件）
                log(resultId, '3. 模拟智能分析功能...', 'info');
                log(resultId, '   ℹ️ 需要上传文件才能测试智能分析', 'info');

                log(resultId, '✅ 前端API调用流程测试完成', 'success');

            } catch (error) {
                log(resultId, `❌ 前端API测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动测试
        window.onload = function() {
            console.log('页面加载完成，开始自动测试...');
            setTimeout(testAllTemplates, 1000);
        };
    </script>
</body>
</html>
