"""
通用解析器 - 解析用户按通用模板整理的银行流水文件
"""
import pandas as pd
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import re
import uuid

logger = logging.getLogger(__name__)

class UniversalParser:
    """
    通用解析器 - 用于解析用户手工调整为标准格式的银行流水文件
    支持18个标准字段（A-R列），满足所有银行解析需求
    """
    
    def __init__(self, file_path: str):
        """
        初始化通用解析器
        
        Args:
            file_path: 文件路径
        """
        self.file_path = file_path
        self.workbook = None
        self.accounts = []
        self.transactions = []
        self.transactions_by_account = {}
        
        # 标准字段映射（A-R列）
        self.standard_columns = {
            'A': '序号',
            'B': '持卡人', 
            'C': '银行名称',
            'D': '账号',
            'E': '卡号',
            'F': '交易日期',
            'G': '交易时间',
            'H': '交易方式',
            'I': '交易金额',
            'J': '账户余额',
            'K': '借贷标志',
            'L': '对方户名',
            'M': '对方账号',
            'N': '对方开户行',
            'O': '备注1',
            'P': '备注2',
            'Q': '备注3',
            'R': '币种'
        }
        
        # 必填字段
        self.required_fields = ['序号', '持卡人', '银行名称', '账号', '交易日期', '交易金额', '账户余额']    
    def parse(self) -> Dict[str, Any]:
        """
        主解析方法 - 解析通用格式的银行流水文件
        
        Returns:
            Dict: 解析结果
        """
        try:
            logger.info(f"开始使用通用解析器解析文件: {self.file_path}")
            
            # 1. 加载文件
            self._load_workbook()
            
            # 2. 检测和验证表头
            if not self._validate_headers():
                return {
                    'success': False,
                    'error': '文件不符合通用格式标准，请使用通用模板整理数据'
                }
            
            # 3. 解析交易数据
            self._parse_transactions()
            
            # 4. 提取账户信息
            self._extract_accounts()
            
            # 5. 按账户组织交易数据
            self._organize_transactions_by_account()
            
            # 6. 构建返回结果
            return self._build_result()
            
        except Exception as e:
            logger.error(f"通用解析器解析出错: {str(e)}")
            return {
                'success': False,
                'error': f'通用解析器解析失败: {str(e)}'
            }    
    def _load_workbook(self):
        """加载Excel文件"""
        try:
            # 支持多种Excel格式
            if self.file_path.endswith('.xlsx'):
                self.df = pd.read_excel(self.file_path, engine='openpyxl')
            else:
                self.df = pd.read_excel(self.file_path, engine='xlrd')
                
            logger.info(f"成功加载文件，共{len(self.df)}行数据")
            
        except Exception as e:
            raise Exception(f"无法加载文件: {str(e)}")
    
    def _validate_headers(self) -> bool:
        """
        验证文件是否符合通用格式标准
        
        Returns:
            bool: 是否符合标准
        """
        try:
            # 检查文件是否为空
            if self.df.empty:
                logger.error("文件为空")
                return False
            
            # 获取表头行（使用列名）
            headers = self.df.columns.tolist()
            
            # 验证必填字段是否存在
            missing_fields = []
            for field in self.required_fields:
                if field not in headers:
                    missing_fields.append(field)
            
            if missing_fields:
                logger.error(f"缺少必填字段: {missing_fields}")
                return False
            
            # 检查列数（A-R最多18列）
            if len(headers) > 18:
                logger.warning(f"列数超过18列，将只处理前18列")
            
            logger.info("通用格式验证通过")
            return True
            
        except Exception as e:
            logger.error(f"表头验证失败: {str(e)}")
            return False    
    def _parse_transactions(self):
        """解析交易数据"""
        try:
            # 获取表头
            headers = self.df.columns.tolist()
            
            # 获取数据行（从第0行开始，因为列名已经是表头）
            data_rows = self.df
            
            # 清空之前的数据
            self.transactions = []
            
            for idx, row in data_rows.iterrows():
                try:
                    # 跳过空行
                    if row.isna().all():
                        continue
                    
                    # 构建交易记录
                    transaction = self._build_transaction_record(headers, row, idx)
                    
                    if transaction:
                        self.transactions.append(transaction)
                        
                except Exception as e:
                    logger.warning(f"解析第{idx+2}行数据时出错: {str(e)}")
                    continue
            
            logger.info(f"成功解析{len(self.transactions)}条交易记录")
            
        except Exception as e:
            raise Exception(f"解析交易数据失败: {str(e)}")    
    def _build_transaction_record(self, headers: List[str], row: pd.Series, row_idx: int) -> Optional[Dict[str, Any]]:
        """
        构建单条交易记录
        
        Args:
            headers: 表头列表
            row: 数据行
            row_idx: 行索引
            
        Returns:
            Optional[Dict]: 交易记录
        """
        try:
            transaction = {}
            
            # 遍历所有列，按标准字段映射
            for col_idx, header in enumerate(headers):
                if col_idx >= len(row):
                    break
                    
                value = row.iloc[col_idx] if col_idx < len(row) else None
                
                # 清理数据
                if pd.isna(value) or value == '':
                    value = None
                else:
                    value = str(value).strip()
                
                # 根据字段名映射到标准字段
                if header == '序号':
                    transaction['sequence_number'] = self._safe_int(value)
                elif header == '持卡人':
                    transaction['holder_name'] = value
                elif header == '银行名称':
                    transaction['bank_name'] = value
                elif header == '账号':
                    transaction['account_number'] = value
                elif header == '卡号':
                    transaction['card_number'] = value
                elif header == '交易日期':
                    transaction['transaction_date'] = self._parse_date(value)
                elif header == '交易时间':
                    transaction['transaction_time'] = self._parse_time(value)
                elif header == '交易方式':
                    transaction['transaction_method'] = value
                elif header == '交易金额':
                    transaction['transaction_amount'] = self._safe_decimal(value)
                elif header == '账户余额':
                    transaction['balance_amount'] = self._safe_decimal(value)
                elif header == '借贷标志':
                    transaction['dr_cr_flag'] = value
                elif header == '对方户名':
                    transaction['counterparty_name'] = value
                elif header == '对方账号':
                    transaction['counterparty_account'] = value
                elif header == '对方开户行':
                    transaction['counterparty_bank'] = value
                elif header == '备注1':
                    transaction['remark1'] = value
                elif header == '备注2':
                    transaction['remark2'] = value
                elif header == '备注3':
                    transaction['remark3'] = value
                elif header == '币种':
                    transaction['currency'] = value or 'CNY'
            
            # 验证必填字段
            required_check = ['holder_name', 'account_number', 'transaction_date', 'transaction_amount', 'balance_amount']
            for field in required_check:
                if not transaction.get(field):
                    logger.warning(f"第{row_idx+2}行缺少必填字段: {field}")
                    return None            
            # 添加系统字段
            transaction['transaction_id'] = str(uuid.uuid4())
            transaction['raw_transaction_id'] = f"universal_{row_idx}"
            transaction['person_name'] = transaction['holder_name']
            transaction['bank_name'] = transaction.get('bank_name', '未知银行')
            transaction['account_name'] = transaction['holder_name']
            transaction['transaction_type'] = self._determine_transaction_type(transaction['transaction_amount'])
            transaction['counterparty_name'] = transaction.get('counterparty_name', '')
            transaction['counterparty_account'] = transaction.get('counterparty_account', '')
            transaction['purpose'] = transaction.get('remark1', '')
            transaction['channel'] = transaction.get('transaction_method', '通用格式')
            transaction['reference_number'] = transaction.get('remark2', '')
            transaction['currency'] = transaction.get('currency', 'CNY')
            transaction['created_at'] = datetime.now()
            transaction['updated_at'] = datetime.now()
            
            return transaction
            
        except Exception as e:
            logger.error(f"构建交易记录失败 (行{row_idx+2}): {str(e)}")
            return None    
    def _extract_accounts(self):
        """从交易数据中提取账户信息"""
        try:
            # 清空之前的账户数据
            self.accounts = []
            
            # 按账户号分组
            account_groups = {}
            for transaction in self.transactions:
                account_number = transaction.get('account_number')
                if account_number:
                    if account_number not in account_groups:
                        account_groups[account_number] = []
                    account_groups[account_number].append(transaction)
            
            # 为每个账户创建账户记录
            for account_number, transactions in account_groups.items():
                first_transaction = transactions[0]
                
                account = {
                    'account_id': str(uuid.uuid4()),
                    'person_name': first_transaction.get('holder_name', ''),
                    'bank_name': first_transaction.get('bank_name', '通用格式'),
                    'account_name': first_transaction.get('holder_name', ''),
                    'account_number': account_number,
                    'card_number': first_transaction.get('card_number', ''),
                    'import_file_source': self.file_path,
                    'creation_timestamp': datetime.now().strftime('%Y%m%d_%H%M%S'),
                    'updated_at': datetime.now()
                }
                
                # 🔧 添加账户统计信息
                # 计算交易笔数
                account['transactions_count'] = len(transactions)
                
                # 计算收支统计
                total_inflow = sum(t['transaction_amount'] for t in transactions if t.get('transaction_amount', 0) > 0)
                total_outflow = sum(abs(t['transaction_amount']) for t in transactions if t.get('transaction_amount', 0) < 0)
                
                account['total_inflow'] = total_inflow
                account['total_outflow'] = total_outflow
                
                # 计算时间范围
                dates = []
                for t in transactions:
                    if t.get('transaction_date'):
                        try:
                            # 支持多种日期格式
                            date_str = t['transaction_date']
                            if isinstance(date_str, str):
                                # 如果是标准格式，直接解析
                                if re.match(r'\d{4}-\d{2}-\d{2}', date_str):
                                    date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                                else:
                                    # 尝试pandas解析其他格式
                                    date_obj = pd.to_datetime(date_str)
                                dates.append(date_obj)
                        except (ValueError, TypeError):
                            continue
                
                if dates:
                    min_date = min(dates).strftime('%Y-%m-%d')
                    max_date = max(dates).strftime('%Y-%m-%d')
                    if min_date == max_date:
                        account['date_range'] = min_date
                    else:
                        account['date_range'] = f"{min_date} 至 {max_date}"
                else:
                    account['date_range'] = "未知"
                
                self.accounts.append(account)
            
            logger.info(f"提取到{len(self.accounts)}个账户，已计算统计信息")
            
        except Exception as e:
            raise Exception(f"提取账户信息失败: {str(e)}")    
    def _organize_transactions_by_account(self):
        """按账户组织交易数据"""
        try:
            self.transactions_by_account = {}
            
            for transaction in self.transactions:
                account_number = transaction.get('account_number')
                if account_number:
                    if account_number not in self.transactions_by_account:
                        self.transactions_by_account[account_number] = []
                    self.transactions_by_account[account_number].append(transaction)
            
            logger.info(f"按账户组织完成，共{len(self.transactions_by_account)}个账户")
            
        except Exception as e:
            raise Exception(f"组织交易数据失败: {str(e)}")
    
    def _build_result(self) -> Dict[str, Any]:
        """构建返回结果"""
        confidence_score = self._calculate_confidence_score()
        
        return {
            'success': True,
            'accounts': self.accounts,
            'transactions': self.transactions,
            'transactions_by_account': self.transactions_by_account,
            'confidence_score': confidence_score,
            'parser_type': 'UniversalParser',
            'bank_name': '通用解析器',
            'file_path': self.file_path,
            'summary': {
                'total_accounts': len(self.accounts),
                'total_transactions': len(self.transactions),
                'confidence': confidence_score
            }
        }    
    def _calculate_confidence_score(self) -> float:
        """
        计算置信度得分
        通用解析器对符合格式的文件给予100%置信度
        
        Returns:
            float: 置信度得分 (0-100)
        """
        try:
            # 基础得分
            score = 0.0
            
            # 1. 数据完整性检查 (40分)
            if self.accounts and self.transactions:
                score += 40.0
                
                # 账户数据完整性
                account_completeness = sum(1 for account in self.accounts if account.get('account_number') and account.get('person_name')) / len(self.accounts)
                score += account_completeness * 10.0
                
                # 交易数据完整性
                transaction_completeness = sum(1 for t in self.transactions if t.get('transaction_date') and t.get('transaction_amount')) / len(self.transactions)
                score += transaction_completeness * 10.0
            
            # 2. 格式标准化程度 (30分)
            # 通用格式本身就是标准化的，给满分
            score += 30.0
            
            # 3. 数据质量 (20分)
            if self.transactions:
                # 检查日期格式
                valid_dates = sum(1 for t in self.transactions if t.get('transaction_date'))
                date_ratio = valid_dates / len(self.transactions)
                score += date_ratio * 10.0
                
                # 检查金额格式
                valid_amounts = sum(1 for t in self.transactions if isinstance(t.get('transaction_amount'), (int, float)))
                amount_ratio = valid_amounts / len(self.transactions)
                score += amount_ratio * 10.0
            
            # 4. 特殊加分 (10分)
            # 通用格式识别准确，给满分
            score += 10.0
            
            # 确保分数在0-100之间
            confidence = max(0.0, min(100.0, score))
            
            logger.info(f"通用解析器置信度得分: {confidence:.1f}")
            return confidence
            
        except Exception as e:
            logger.error(f"计算置信度失败: {str(e)}")
            return 95.0  # 默认给高置信度    
    def _safe_int(self, value: Any) -> Optional[int]:
        """安全转换为整数"""
        if value is None or value == '':
            return None
        try:
            return int(float(str(value)))
        except:
            return None
    
    def _safe_decimal(self, value: Any) -> Optional[float]:
        """安全转换为小数"""
        if value is None or value == '':
            return None
        try:
            # 处理可能的货币符号
            clean_value = str(value).replace(',', '').replace('¥', '').replace('$', '').strip()
            return float(clean_value)
        except:
            return None
    
    def _parse_date(self, value: Any) -> Optional[str]:
        """解析日期"""
        if value is None or value == '':
            return None
        try:
            # 尝试多种日期格式
            date_str = str(value).strip()
            
            # 如果已经是标准格式，直接返回
            if re.match(r'\d{4}-\d{2}-\d{2}', date_str):
                return date_str
            
            # 尝试解析其他格式
            try:
                date_obj = pd.to_datetime(date_str)
                return date_obj.strftime('%Y-%m-%d')
            except:
                return date_str  # 返回原始值
                
        except Exception as e:
            logger.warning(f"日期解析失败: {value} -> {str(e)}")
            return None    
    def _parse_time(self, value: Any) -> Optional[str]:
        """解析时间"""
        if value is None or value == '':
            return None
        try:
            time_str = str(value).strip()
            
            # 如果已经是标准格式，直接返回
            if re.match(r'\d{2}:\d{2}:\d{2}', time_str):
                return time_str
            
            # 尝试解析其他格式
            try:
                time_obj = pd.to_datetime(time_str)
                return time_obj.strftime('%H:%M:%S')
            except:
                return time_str  # 返回原始值
                
        except Exception as e:
            logger.warning(f"时间解析失败: {value} -> {str(e)}")
            return None
    
    def _determine_transaction_type(self, amount: Any) -> str:
        """根据金额判断交易类型"""
        try:
            if isinstance(amount, (int, float)):
                return "收入" if amount > 0 else "支出"
            return "未知"
        except:
            return "未知"    @staticmethod
    def calculate_confidence(file_path: str) -> float:
        """
        静态方法：计算文件的置信度得分
        用于解析器选择器的评估
        
        Args:
            file_path: 文件路径
            
        Returns:
            float: 置信度得分 (0-100)
        """
        try:
            # 快速检查文件内容（这是主要评估标准）
            try:
                if file_path.endswith('.xlsx'):
                    df = pd.read_excel(file_path, engine='openpyxl', nrows=10)
                else:
                    df = pd.read_excel(file_path, engine='xlrd', nrows=10)
                
                if df.empty:
                    return 0.0
                
                # 获取表头
                headers = df.iloc[0].fillna('').astype(str).tolist()
                
                # 检查是否包含通用格式标准字段
                universal_headers = ['序号', '持卡人', '银行名称', '账号', '交易日期', '交易金额', '账户余额']
                
                # 计算字段匹配度
                header_matches = 0
                for expected_header in universal_headers:
                    if expected_header in headers:
                        header_matches += 1
                
                # 基础得分：字段匹配度
                header_score = (header_matches / len(universal_headers)) * 70  # 表头匹配最多70分
                
                # 奖励得分：如果关键必填字段都存在
                required_fields = ['持卡人', '账号', '交易日期', '交易金额', '账户余额']
                required_matches = sum(1 for field in required_fields if field in headers)
                if required_matches >= 4:  # 5个必填字段中至少有4个
                    header_score += 20  # 额外20分
                
                # 数据格式检查得分
                data_score = 0
                if len(df) > 1:  # 有数据行
                    try:
                        # 检查是否有有效的数据行
                        data_row = df.iloc[1]  # 第二行应该是数据
                        non_empty_cells = sum(1 for val in data_row if pd.notna(val) and str(val).strip() != '')
                        if non_empty_cells >= 3:  # 至少3个非空字段
                            data_score = 10
                    except:
                        pass
                
                # 文件名指示得分（次要）
                filename_score = 0
                import os
                filename = os.path.basename(file_path).lower()
                universal_indicators = ['通用', '标准', 'universal', 'generic', 'template']
                for indicator in universal_indicators:
                    if indicator in filename:
                        filename_score = 10  # 文件名匹配给10分
                        break
                
                total_score = header_score + data_score + filename_score
                
                # 设置最低和最高分
                if header_matches >= 6:  # 7个字段中至少有6个匹配
                    total_score = max(total_score, 85.0)  # 高匹配度给85分
                elif header_matches >= 4:  # 至少4个字段匹配
                    total_score = max(total_score, 60.0)  # 中等匹配度给60分
                elif header_matches >= 2:  # 至少2个字段匹配
                    total_score = max(total_score, 30.0)  # 低匹配度给30分
                else:
                    total_score = max(total_score, 5.0)   # 几乎不匹配给5分参与度
                
                logger.info(f"通用解析器置信度评估: 字段匹配{header_matches}/{len(universal_headers)}, 得分{total_score:.1f}")
                return min(100.0, total_score)
            
            except Exception as e:
                logger.warning(f"通用解析器置信度计算中的文件读取错误: {str(e)}")
                return 5.0  # 文件读取失败，给最低参与度
            
        except Exception as e:
            logger.error(f"通用解析器置信度计算失败: {str(e)}")
            return 5.0  # 计算失败，给最低参与度

    def extract_sample(self, limit: int = 10) -> Dict[str, Any]:
        """
        提取样本数据用于快速置信度评估
        
        Args:
            limit: 样本数量限制
            
        Returns:
            Dict: 包含样本账户和交易的字典
        """
        try:
            logger.info(f"通用解析器开始提取样本数据，限制条数: {limit}")
            
            # 执行完整解析（通用解析器比较轻量，可以承受）
            parse_result = self.parse()
            
            if not parse_result.get('success'):
                logger.warning(f"解析失败，无法提取样本: {parse_result.get('error')}")
                return {
                    'accounts': [],
                    'transactions': [],
                    'success': False,
                    'error': parse_result.get('error', '解析失败')
                }
            
            # 获取账户和交易数据
            accounts = parse_result.get('accounts', [])
            transactions = parse_result.get('transactions', [])
            
            # 限制交易数量
            sample_transactions = transactions[:limit] if transactions else []
            
            logger.info(f"通用解析器样本提取完成: {len(accounts)}个账户, {len(sample_transactions)}条交易")
            
            return {
                'accounts': accounts,
                'transactions': sample_transactions,
                'success': True,
                'total_transactions': len(transactions),
                'sample_size': len(sample_transactions)
            }
            
        except Exception as e:
            logger.error(f"通用解析器样本提取失败: {str(e)}")
            return {
                'accounts': [],
                'transactions': [],
                'success': False,
                'error': str(e)
            }