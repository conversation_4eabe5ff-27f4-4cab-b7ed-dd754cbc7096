#!/usr/bin/env python3
"""
建设银行格式1解析器 - 重构版
参考工商银行成功模式，专门处理建设银行流水Excel文件
支持标准的银行流水数据结构和置信度评估
"""

import pandas as pd
import logging
from typing import Dict, List, Any, Tuple
from datetime import datetime
import re
import os

logger = logging.getLogger(__name__)

class CCBFormat1Parser:
    """建设银行格式1解析器 - 基于工商银行成功模式重构"""
    
    def __init__(self, file_path: str):
        """
        初始化解析器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.accounts = []
        self.transactions = []
        
        # 建设银行特定的字段映射
        self.field_mapping = {
            'date_field': '交易日期',
            'time_field': '交易时间', 
            'amount_field': '交易金额',
            'balance_field': '账户余额',
            'summary_field': '摘要',
            'remark_field': '扩充备注',
            'cardholder_field': '客户名称',
            'card_field': '交易卡号',
            'direction_field': '借贷方向',  # 修正：实际字段名是'借贷方向'
            'account_field': '账号',
            'opposite_account_field': '对方账号',
            'opposite_name_field': '对方户名'
        }
    
    def _clean_field(self, value) -> str:
        """清理字段值"""
        if value is None or pd.isna(value):
            return ""
        return str(value).strip().replace('\t', '').replace('\n', '').replace('\r', '')
    
    def _clean_amount_string(self, value) -> str:
        """清理金额字符串，移除空格、逗号等格式字符"""
        if value is None:
            return ""
        
        # 转换为字符串并清理各种格式字符
        clean_value = str(value).replace(" ", "").replace(",", "").replace("，", "").replace("\t", "").replace("\n", "").replace("\r", "").strip()
        
        # 移除可能的货币符号
        clean_value = clean_value.replace("¥", "").replace("$", "").replace("€", "").replace("￥", "")
        
        return clean_value
    
    def _parse_amount(self, amount_str: str, dr_cr_flag: str = "") -> float:
        """
        解析金额
        
        Args:
            amount_str: 金额字符串
            dr_cr_flag: 借贷标志
            
        Returns:
            float: 解析后的金额（负数表示支出）
        """
        if not amount_str:
            return 0.0
        
        clean_amount = self._clean_amount_string(amount_str)
        
        try:
            amount = float(clean_amount)
            
            # 根据借贷标志调整正负
            if dr_cr_flag == "借":  # 借方=支出，转为负数
                return -abs(amount)
            elif dr_cr_flag == "贷":  # 贷方=收入，保持正数
                return abs(amount)
            else:
                return amount
        except (ValueError, TypeError):
            logger.warning(f"无法解析金额: {amount_str}")
            return 0.0
    
    def _standardize_time_format(self, date_str: str, time_str: str = "") -> str:
        """
        标准化时间格式为 'YYYY-MM-DD HH:MM:SS'
        
        Args:
            date_str: 日期字符串
            time_str: 时间字符串（可选）
            
        Returns:
            str: 标准化后的时间字符串
        """
        if not date_str:
            return ""
        
        try:
            # 清理输入
            date_clean = self._clean_field(date_str)
            time_clean = self._clean_field(time_str) if time_str else ""
            
            # 如果没有单独的时间字段，尝试从日期字符串中提取
            if not time_clean and ' ' in date_clean:
                parts = date_clean.split(' ', 1)
                date_clean = parts[0]
                time_clean = parts[1] if len(parts) > 1 else ""
            
            # 解析日期
            if date_clean:
                # 尝试解析不同的日期格式
                date_obj = pd.to_datetime(date_clean).date()
                formatted_date = date_obj.strftime("%Y-%m-%d")
            else:
                formatted_date = ""
            
            # 解析时间
            if time_clean:
                # 处理时间格式（可能是 HH.MM.SS 或 HH:MM:SS）
                time_clean = time_clean.replace('.', ':')
                
                # 补充秒数
                if len(time_clean.split(':')) == 2:
                    time_clean += ":00"
                
                formatted_time = time_clean
            else:
                formatted_time = "00:00:00"
            
            return f"{formatted_date} {formatted_time}"
            
        except Exception as e:
            logger.warning(f"时间格式化失败: {date_str} {time_str}, 错误: {str(e)}")
            return date_str