#!/usr/bin/env python3
"""
建设银行Format2解析器 - 多表头结构版本
专门处理"个人活期明细信息-新一代"格式的Excel文件，如"2号测试2次.xlsx"

关键特性：
- 正确处理多表头结构：每个账户有独立的表头+数据块
- 按账户分块解析：确保数据一致性和正确关联
- 完整字段映射：所有9个用户指出的问题都已修复
"""

import pandas as pd
import numpy as np
import openpyxl
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import logging
import re
import uuid
from pathlib import Path

logger = logging.getLogger(__name__)

class CCBFormat2Parser:
    """建设银行Format2解析器 - 多表头结构处理版本"""
    
    def __init__(self, file_path: str):
        """
        初始化建设银行Format2解析器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.accounts = []
        self.transactions = []
        self.raw_data = None
        self.account_blocks = []  # 存储按账户分块的数据
        
        # Excel列索引映射（基于原表结构）
        self.column_mapping = {
            'transaction_date': 0,      # A列：交易日期
            'transaction_time': 1,      # B列：交易时间  
            'detail_number': 2,         # C列：明细号
            'card_number': 3,           # D列：交易卡号
            'summary': 4,               # E列：摘要（交易方式）
            'debit_amount': 5,          # F列：借方发生额（支出）
            'credit_amount': 6,         # G列：贷方发生额（收入）
            'balance': 7,               # H列：账户余额
            'institution_code': 8,      # I列：交易机构号
            'institution_name': 9,      # J列：交易机构名称
            'counterparty_account': 10, # K列：对方账号
            'counterparty_name': 11,    # L列：对方户名
            'counterparty_bank_code': 12, # M列：对方行号
            'counterparty_bank_name': 13, # N列：对方行名
            'teller_code': 14,          # O列：柜员号
            'transaction_serial': 15,   # P列：交易流水号
            'transaction_channel': 16,  # Q列：交易渠道
            'transaction_remark': 17    # R列：交易备注
        }
        
        logger.info(f"初始化CCB Format2解析器，文件: {file_path}")
    
    def parse(self) -> Dict[str, Any]:
        """
        解析建设银行Format2文件的主入口方法
        
        Returns:
            包含解析结果的字典
        """
        try:
            logger.info("开始解析建设银行Format2文件")
            
            # Step 1: 读取Excel文件并识别账户块
            self._read_excel_file()
            
            # Step 2: 按账户分块处理数据
            self._process_account_blocks()
            
            # Step 3: 构建最终结果
            result = self._build_result()
            
            logger.info(f"解析完成: {len(self.accounts)}个账户, {len(self.transactions)}条交易")
            return result
            
        except Exception as e:
            logger.error(f"解析过程中发生错误: {str(e)}")
            raise
    
    def _read_excel_file(self) -> None:
        """读取Excel文件并识别账户数据块"""
        try:
            # 使用openpyxl读取原始数据以保持格式
            workbook = openpyxl.load_workbook(self.file_path, read_only=True)
            
            # 找到"个人活期明细信息-新一代"工作表
            target_sheet = None
            for sheet_name in workbook.sheetnames:
                if "个人活期明细信息" in sheet_name and "新一代" in sheet_name:
                    target_sheet = workbook[sheet_name]
                    break
            
            if not target_sheet:
                raise ValueError("未找到'个人活期明细信息-新一代'工作表")
            
            logger.info(f"找到目标工作表: {target_sheet.title}")
            
            # 读取所有行数据
            self.raw_data = []
            for row in target_sheet.iter_rows(values_only=True):
                self.raw_data.append(row)
            
            logger.info(f"读取了 {len(self.raw_data)} 行原始数据")
            
            # 识别账户数据块
            self._identify_account_blocks()
            
        except Exception as e:
            logger.error(f"读取Excel文件失败: {str(e)}")
            raise
    
    def _identify_account_blocks(self) -> None:
        """识别每个账户的数据块（表头+数据）"""
        current_block = None
        header_row = None
        
        for i, row in enumerate(self.raw_data):
            if not row or not row[0]:
                continue
                
            row_text = str(row[0]).strip()
            
            # 检查是否是客户信息行（表头）
            if "客户名称:" in row_text and "客户账号:" in row_text:
                # 保存上一个块
                if current_block and header_row is not None:
                    current_block['data_end'] = i - 1
                    self.account_blocks.append(current_block)
                
                # 开始新的账户块
                current_block = {
                    'customer_info_row': i,
                    'customer_info': row_text,
                    'header_row': None,
                    'data_start': None,
                    'data_end': None,
                    'account_info': self._parse_customer_info(row_text)
                }
                header_row = None
                
            # 检查是否是数据表头行
            elif row and len(row) >= 6 and str(row[0]).strip() == "交易日期":
                if current_block:
                    current_block['header_row'] = i
                    current_block['data_start'] = i + 1
                    header_row = i
                    
            # 检查是否是有效的交易数据行
            elif (current_block and header_row is not None and 
                  self._is_valid_transaction_row(row)):
                # 更新数据块结束位置
                current_block['data_end'] = i
        
        # 保存最后一个块
        if current_block and header_row is not None:
            current_block['data_end'] = len(self.raw_data) - 1
            self.account_blocks.append(current_block)
        
        logger.info(f"识别到 {len(self.account_blocks)} 个账户数据块")
        
        # 打印每个块的信息用于调试
        for i, block in enumerate(self.account_blocks):
            account_info = block['account_info']
            logger.info(f"账户块 {i+1}: {account_info['customer_name']} - {account_info['account_number']}")
            logger.info(f"  数据范围: 第{block['data_start']+1}行 到 第{block['data_end']+1}行")
    
    def _parse_customer_info(self, info_text: str) -> Dict[str, str]:
        """解析客户信息行"""
        result = {
            'customer_name': '',
            'customer_id': '',
            'account_number': '',
            'card_number': '',
            'currency': '人民币元',
            'cash_flag': ''
        }
        
        try:
            # 使用正则表达式提取信息
            name_match = re.search(r'客户名称:([^，,]+)', info_text)
            if name_match:
                result['customer_name'] = name_match.group(1).strip()
            
            id_match = re.search(r'客户编号:([^，,]+)', info_text)
            if id_match:
                result['customer_id'] = id_match.group(1).strip()
            
            account_match = re.search(r'客户账号:([^，,]+)', info_text)
            if account_match:
                result['account_number'] = account_match.group(1).strip()
            
            card_match = re.search(r'卡号:([^，,]+)', info_text)
            if card_match:
                card_value = card_match.group(1).strip()
                result['card_number'] = card_value if card_value and card_value != '' else ''
            
            currency_match = re.search(r'币别:([^，,]+)', info_text)
            if currency_match:
                result['currency'] = currency_match.group(1).strip()
            
            cash_match = re.search(r'钞汇标志:([^，,]+)', info_text)
            if cash_match:
                result['cash_flag'] = cash_match.group(1).strip()
                
        except Exception as e:
            logger.error(f"解析客户信息失败: {str(e)}")
        
        return result
    
    def _is_valid_transaction_row(self, row: tuple) -> bool:
        """检查是否是有效的交易数据行"""
        if not row or len(row) < 8:
            return False
        
        # 检查交易日期格式
        date_cell = row[0]
        if not date_cell:
            return False
        
        date_str = str(date_cell).strip()
        if not date_str or date_str == '交易日期':
            return False
        
        # 检查是否有金额数据
        debit_amount = row[5] if len(row) > 5 else None
        credit_amount = row[6] if len(row) > 6 else None
        
        if not debit_amount and not credit_amount:
            return False
        
        # 简单的日期格式检查
        try:
            if len(date_str) >= 8 and ('-' in date_str or '/' in date_str):
                return True
        except:
            pass
        
        return False
    
    def _process_account_blocks(self) -> None:
        """按账户块处理所有数据"""
        for block_index, block in enumerate(self.account_blocks):
            try:
                logger.info(f"处理账户块 {block_index + 1}/{len(self.account_blocks)}")
                
                # 提取账户信息
                account_info = block['account_info']
                
                # 处理该账户的交易数据（返回属于该账户的交易列表）
                account_transactions = self._process_transactions_for_block(block, account_info)
                
                # 计算时间范围（基于该账户的实际交易）
                date_range = self._calculate_date_range_from_transactions(account_transactions)
                
                # 计算该账户的统计数据
                total_income = sum(t['transaction_amount'] for t in account_transactions if t['dr_cr_flag'] == '收')
                total_expense = sum(t['transaction_amount'] for t in account_transactions if t['dr_cr_flag'] == '支')
                
                # 创建账户记录（包含该账户专属的交易记录）
                account = {
                    'holder_name': account_info['customer_name'],
                    'cardholder_name': account_info['customer_name'], 
                    'account_number': account_info['account_number'],
                    'card_number': account_info['card_number'],
                    'bank_name': '中国建设银行',
                    'account_type': '个人活期账户',
                    'currency': account_info['currency'],
                    'date_range': date_range,
                    'transactions': account_transactions,  # 只包含属于该账户的交易
                    'transaction_count': len(account_transactions),
                    'total_income': total_income,
                    'total_expense': total_expense
                }
                
                self.accounts.append(account)
                
                # 将该账户的交易添加到全局交易列表（用于整体统计）
                self.transactions.extend(account_transactions)
                
                logger.info(f"账户 {account_info['customer_name']} - {account_info['account_number']}: {len(account_transactions)} 条交易")
                
            except Exception as e:
                logger.error(f"处理账户块 {block_index + 1} 时发生错误: {str(e)}")
                continue
    
    def _calculate_date_range_for_block(self, block: Dict) -> str:
        """计算单个账户块的时间范围"""
        if not block['data_start'] or not block['data_end']:
            return "未知"
        
        start_date = None
        end_date = None
        
        # 遍历该账户块的交易数据
        for i in range(block['data_start'], block['data_end'] + 1):
            if i >= len(self.raw_data):
                break
                
            row = self.raw_data[i]
            if not self._is_valid_transaction_row(row):
                continue
            
            try:
                date_str = str(row[0]).strip()
                parsed_date = self._parse_date(date_str)
                
                if parsed_date:
                    if not start_date or parsed_date < start_date:
                        start_date = parsed_date
                    if not end_date or parsed_date > end_date:
                        end_date = parsed_date
                        
            except Exception:
                continue
        
        if start_date and end_date:
            return f"{start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}"
        
        return "未知"
    
    def _process_transactions_for_block(self, block: Dict, account_info: Dict) -> List[Dict]:
        """处理单个账户块的交易数据，返回属于该账户的交易列表"""
        account_transactions = []
        
        if not block['data_start'] or not block['data_end']:
            return account_transactions
        
        # 遍历该账户块的交易数据行
        for i in range(block['data_start'], block['data_end'] + 1):
            if i >= len(self.raw_data):
                break
                
            row = self.raw_data[i]
            if not self._is_valid_transaction_row(row):
                continue
            
            try:
                # 解析交易记录（序号基于该账户内的顺序）
                transaction = self._parse_transaction_row(row, account_info, len(account_transactions) + 1)
                if transaction:
                    account_transactions.append(transaction)
                    
            except Exception as e:
                logger.error(f"解析交易行失败 (行{i+1}): {str(e)}")
                continue
        
        return account_transactions
    
    def _calculate_date_range_from_transactions(self, transactions: List[Dict]) -> str:
        """基于交易记录计算时间范围"""
        if not transactions:
            return "无交易记录"
        
        try:
            # 提取所有交易日期
            dates = []
            for transaction in transactions:
                date_str = transaction.get('transaction_date', '')
                if date_str and date_str != "未知":
                    try:
                        # 解析日期
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                        dates.append(date_obj)
                    except:
                        try:
                            # 尝试其他格式
                            date_obj = datetime.strptime(date_str.split()[0], '%Y-%m-%d')
                            dates.append(date_obj)
                        except:
                            continue
            
            if dates:
                min_date = min(dates)
                max_date = max(dates)
                return f"{min_date.strftime('%Y-%m-%d')} 至 {max_date.strftime('%Y-%m-%d')}"
            else:
                return "日期格式错误"
                
        except Exception as e:
            logger.error(f"计算时间范围失败: {e}")
            return "时间范围计算错误"
    
    def _parse_transaction_row(self, row: tuple, account_info: Dict, sequence_number: int) -> Optional[Dict]:
        """解析单行交易数据"""
        try:
            # 提取基础字段
            transaction_date = str(row[0]).strip() if row[0] else ""
            transaction_time = str(row[1]).strip() if len(row) > 1 and row[1] else ""
            detail_number = str(row[2]).strip() if len(row) > 2 and row[2] else ""
            card_number_from_row = str(row[3]).strip() if len(row) > 3 and row[3] else ""
            summary = str(row[4]).strip() if len(row) > 4 and row[4] else ""
            
            # 解析金额字段
            debit_amount = self._parse_amount_field(row[5]) if len(row) > 5 else 0.0
            credit_amount = self._parse_amount_field(row[6]) if len(row) > 6 else 0.0
            balance = self._parse_amount_field(row[7]) if len(row) > 7 else 0.0
            
            # 其他字段
            institution_code = str(row[8]).strip() if len(row) > 8 and row[8] else ""
            institution_name = str(row[9]).strip() if len(row) > 9 and row[9] else ""
            counterparty_account = str(row[10]).strip() if len(row) > 10 and row[10] else ""
            counterparty_name = str(row[11]).strip() if len(row) > 11 and row[11] else ""
            counterparty_bank_code = str(row[12]).strip() if len(row) > 12 and row[12] else ""
            counterparty_bank_name = str(row[13]).strip() if len(row) > 13 and row[13] else ""
            teller_code = str(row[14]).strip() if len(row) > 14 and row[14] else ""
            transaction_serial = str(row[15]).strip() if len(row) > 15 and row[15] else ""
            transaction_channel = str(row[16]).strip() if len(row) > 16 and row[16] else ""
            transaction_remark = str(row[17]).strip() if len(row) > 17 and row[17] else ""
            
            # 确定交易金额和收支符号
            if credit_amount > 0:
                transaction_amount = credit_amount
                dr_cr_flag = "收"
            elif debit_amount > 0:
                transaction_amount = debit_amount  
                dr_cr_flag = "支"
            else:
                transaction_amount = 0.0
                dr_cr_flag = "未知"
            
            # 构建完整的日期时间
            full_datetime = self._build_datetime(transaction_date, transaction_time)
            
            # 构建交易记录
            transaction = {
                'sequence_number': sequence_number,
                'holder_name': account_info['customer_name'],          # 硬编码与总表持卡人一致
                'cardholder_name': account_info['customer_name'],      # 硬编码与总表持卡人一致
                'bank_name': '中国建设银行',
                'account_number': account_info['account_number'],      # 硬编码与汇总表一致
                'card_number': account_info['card_number'],            # 硬编码与汇总表一致
                'transaction_date': transaction_date,
                'transaction_time': transaction_time,
                'transaction_datetime': full_datetime,
                'transaction_method': summary,                         # 取值原表的"摘要"
                'summary': summary,
                'transaction_amount': transaction_amount,              # 统一显示为正数
                'balance_amount': balance,                             # 对应原表的账户余额
                'balance': balance,
                'dr_cr_flag': dr_cr_flag,                             # 借方发生额=支出，贷方发生额=收入
                'counterparty_name': counterparty_name,
                'counterparty_account': counterparty_account,
                'counterparty_bank_name': counterparty_bank_name,
                'remark1': transaction_remark,                        # 取值原表的"交易备注"
                'remark2': "",
                'remark3': "",
                'currency': account_info['currency'],
                
                # 保持兼容性的字段
                'amount': transaction_amount,
                'opposite_name': counterparty_name,
                'opposite_account': counterparty_account,
                'remark': transaction_remark
            }
            
            return transaction
            
        except Exception as e:
            logger.error(f"解析交易行时发生错误: {str(e)}")
            return None
    
    def _parse_amount_field(self, value) -> float:
        """解析金额字段"""
        if not value:
            return 0.0
        
        try:
            if isinstance(value, (int, float)):
                return float(value)
            
            # 处理字符串格式的金额
            amount_str = str(value).strip()
            if not amount_str or amount_str.lower() in ['nan', 'none', '']:
                return 0.0
            
            # 移除可能的货币符号和空格
            amount_str = re.sub(r'[￥¥$,\s]', '', amount_str)
            
            return float(amount_str)
            
        except (ValueError, TypeError):
            return 0.0
    
    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """解析日期字符串"""
        if not date_str or date_str.strip() == '':
            return None
        
        try:
            # 尝试多种日期格式
            formats = [
                '%Y-%m-%d',
                '%Y/%m/%d', 
                '%Y.%m.%d',
                '%m/%d/%Y',
                '%d/%m/%Y'
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(date_str.strip(), fmt)
                except ValueError:
                    continue
                    
        except Exception:
            pass
        
        return None
    
    def _build_datetime(self, date_str: str, time_str: str) -> str:
        """构建完整的日期时间字符串"""
        try:
            # 清理日期和时间字符串
            date_clean = date_str.strip() if date_str else ""
            time_clean = time_str.strip() if time_str else "00:00:00"
            
            # 如果没有时间或时间为空，使用默认时间
            if not time_clean or time_clean in ['', 'nan', 'None']:
                time_clean = "00:00:00"
            
            # 标准化时间格式
            if ':' not in time_clean:
                time_clean = "00:00:00"
            elif len(time_clean.split(':')) == 2:
                time_clean += ":00"
            
            return f"{date_clean} {time_clean}"
            
        except Exception:
            return f"{date_str} 00:00:00"
    
    def _build_result(self) -> Dict[str, Any]:
        """构建最终解析结果 - 参考工商银行解析器的成功模式，按账户分组交易"""
        logger.info("开始构建解析结果...")
        
        # 🔥 关键修复：按账户分组交易记录
        transactions_by_account = {}
        corrected_accounts = []
        
        # 为每条交易分配序号并按账户分组
        for index, transaction in enumerate(self.transactions, 1):
            transaction['sequence_number'] = index
            
            # 使用account_number作为分组键
            account_number = transaction['account_number']
            
            if account_number not in transactions_by_account:
                transactions_by_account[account_number] = []
                
                # 🔧 为每个账户创建独立的账户记录
                # 从原accounts列表中找到对应的账户信息
                matching_account = None
                for acc in self.accounts:
                    if acc['account_number'] == account_number:
                        matching_account = acc
                        break
                
                if matching_account:
                    account_data = {
                        'account_number': account_number,
                        'card_number': matching_account['card_number'],
                        'holder_name': matching_account['holder_name'],
                        'cardholder_name': matching_account['cardholder_name'],
                        'bank_name': '中国建设银行',
                        'account_type': '个人活期账户',
                        
                        # 统计信息（将在后续计算）
                        'transactions_count': 0,
                        'total_inflow': 0.0,
                        'total_outflow': 0.0,
                        'date_range': matching_account.get('date_range', ''),
                        
                        # 兼容字段
                        'account_id': account_number,
                        'account_name': matching_account['holder_name'],
                        'currency': 'CNY',
                        'is_primary': len(corrected_accounts) == 0
                    }
                    corrected_accounts.append(account_data)
                    logger.info(f"创建账户记录: {matching_account['holder_name']} - {account_number}")
            
            # 🔥 关键：将交易记录分配给正确的账户
            transactions_by_account[account_number].append(transaction)
        
        # 🔧 计算每个账户的统计信息
        for account in corrected_accounts:
            account_number = account['account_number']
            account_transactions = transactions_by_account.get(account_number, [])
            
            # 计算交易笔数
            account['transactions_count'] = len(account_transactions)
            
            # 计算收支统计
            total_inflow = sum(t['transaction_amount'] for t in account_transactions if t['dr_cr_flag'] == '收')
            total_outflow = sum(t['transaction_amount'] for t in account_transactions if t['dr_cr_flag'] == '支')
            
            account['total_inflow'] = total_inflow
            account['total_outflow'] = total_outflow
            
            logger.info(f"账户 {account['holder_name']} ({account_number}): {account['transactions_count']}笔交易，收入¥{total_inflow:.2f}，支出¥{total_outflow:.2f}")
        
        # 计算全局统计信息
        total_income = sum(t['transaction_amount'] for t in self.transactions if t['dr_cr_flag'] == '收')
        total_expense = sum(t['transaction_amount'] for t in self.transactions if t['dr_cr_flag'] == '支')
        
        logger.info(f"解析完成: {len(corrected_accounts)}个账户，{len(self.transactions)}条交易记录")
        
        return {
            'success': True,
            'message': f'成功解析 {len(corrected_accounts)} 个账户，{len(self.transactions)} 条交易记录',
            'accounts': corrected_accounts,                    # 🔥 使用修正后的账户列表
            'transactions': self.transactions,                 # 保持所有交易记录用于兼容性
            'transactions_by_account': transactions_by_account, # 🔥 关键：按账户分组的交易记录
            'statistics': {
                'total_accounts': len(corrected_accounts),
                'total_transactions': len(self.transactions),
                'total_income': total_income,
                'total_expense': total_expense,
                'net_flow': total_income - total_expense
            },
            'confidence': 1.0,  # 由于是专门针对该格式设计的解析器，置信度为1.0
            'data': {
                'accounts': corrected_accounts,
                'transactions_by_account': transactions_by_account  # 🔥 前端使用这个字段来显示账户特定的交易
            }
        }

# 工厂函数，保持与现有系统的兼容性
def create_ccb_format2_parser(file_path: str) -> CCBFormat2Parser:
    """创建CCB Format2解析器实例"""
    return CCBFormat2Parser(file_path)

# 为了与现有API兼容，提供parse_ccb_format2函数
def parse_ccb_format2(file_path: str) -> Dict[str, Any]:
    """解析建设银行Format2文件"""
    parser = CCBFormat2Parser(file_path)
    return parser.parse() 