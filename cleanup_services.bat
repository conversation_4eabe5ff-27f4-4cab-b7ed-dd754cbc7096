@echo off
chcp 65001 > nul 2>&1
setlocal enabledelayedexpansion
echo ========================================
echo 银行流水分析系统 - 服务清理脚本 v2.1
echo ========================================

echo [1] 查找并清理端口占用...

:: 清理8000端口
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":8000" ^| findstr "LISTENING" 2^>nul') do (
    echo 正在关闭端口8000的进程 %%a...
    taskkill /F /PID %%a >nul 2>&1
)

:: 清理3000端口
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":3000" ^| findstr "LISTENING" 2^>nul') do (
    echo 正在关闭端口3000的进程 %%a...
    taskkill /F /PID %%a >nul 2>&1
)

echo [2] 清理相关进程...

:: 直接强制关闭可能的python和node进程
tasklist | findstr "python" >nul 2>&1
if %errorlevel% equ 0 (
    echo 正在清理Python进程...
    taskkill /F /IM python.exe >nul 2>&1
)

tasklist | findstr "node" >nul 2>&1
if %errorlevel% equ 0 (
    echo 正在清理Node进程...
    taskkill /F /IM node.exe >nul 2>&1
)

echo [3] 验证清理结果...
timeout /t 3 /nobreak >nul

netstat -ano | findstr ":8000" | findstr "LISTENING" >nul 2>&1
if %errorlevel% equ 0 (
    echo ERROR: Port 8000 still occupied / 端口8000仍被占用
) else (
    echo SUCCESS: Port 8000 released / 端口8000已释放
)

netstat -ano | findstr ":3000" | findstr "LISTENING" >nul 2>&1
if %errorlevel% equ 0 (
    echo ERROR: Port 3000 still occupied / 端口3000仍被占用
) else (
    echo SUCCESS: Port 3000 released / 端口3000已释放
)

echo ========================================
echo 清理完成！现在可以重新启动服务
echo ========================================
pause 