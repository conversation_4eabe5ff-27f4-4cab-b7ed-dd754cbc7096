# 银行流水解析器开发检查清单

## 📋 概述

本文档提供了银行流水解析器开发的完整检查清单，确保每个解析器都能正确解析数据并在前端正确显示。

## 🚨 关键原则

**永远不要只看汇总数据就认为解析成功！必须验证交易明细的每个字段！**

## 📝 开发阶段检查清单

### 1. 解析器代码开发

#### 1.1 基础结构
- [ ] 创建解析器插件目录结构
- [ ] 实现 `__init__.py` 和 `plugin.py`
- [ ] 定义正确的元数据（name, version, description等）
- [ ] 实现 `can_parse` 方法进行文件验证

#### 1.2 表头信息提取
- [ ] 正确提取持卡人姓名
- [ ] 正确提取账号信息（处理特殊分隔符如顿号）
- [ ] 正确提取银行名称
- [ ] 正确提取时间范围
- [ ] 正确提取币种信息
- [ ] 处理卡号（如果银行没有卡号，设置为空）

#### 1.3 交易数据解析
- [ ] 正确解析交易日期和时间
- [ ] 正确解析交易金额
- [ ] 正确解析交易类型/方式
- [ ] 正确解析收支方向（进=收入，出=支出）
- [ ] 正确解析对方信息（户名、账号、银行）
- [ ] 正确解析备注信息

#### 1.4 字段映射
- [ ] **关键**：确保后端字段名与前端期望的字段名一致
- [ ] 持卡人姓名：同时设置 `cardholder_name` 和 `holder_name`
- [ ] 银行名称：设置 `bank_name` 字段
- [ ] 账号：设置 `account_number` 字段
- [ ] 卡号：设置 `card_number` 字段（可为空）

### 2. 后端配置

#### 2.1 银行配置
- [ ] 在 `backend/app/config/banks.py` 中添加银行配置
- [ ] 设置正确的银行名称和代码
- [ ] 配置解析器映射关系

#### 2.2 解析器注册
- [ ] 确保解析器在插件管理器中正确注册
- [ ] 验证解析器可以被正确加载

### 3. 前端配置

#### 3.1 银行选择
- [ ] 在前端银行列表中添加新银行
- [ ] 实现银行到解析器的自动映射
- [ ] 确保银行选择后正确设置解析器

## 🧪 测试阶段检查清单

### 1. 强制测试步骤

#### 1.1 端到端测试
- [ ] 启动前后端服务
- [ ] 使用浏览器访问前端界面
- [ ] 上传测试文件
- [ ] 选择正确的银行
- [ ] 执行解析操作

#### 1.2 汇总数据验证
- [ ] 验证账户数量正确
- [ ] 验证交易记录总数正确
- [ ] 验证总收入、总支出、净流水正确
- [ ] 验证时间范围正确

#### 1.3 **关键**：交易明细验证
- [ ] **必须**：点击每个账户的"查看交易"按钮
- [ ] **必须**：验证持卡人姓名正确显示（不是"未知"）
- [ ] **必须**：验证银行名称正确显示（不是"未知"）
- [ ] **必须**：验证账号信息正确显示
- [ ] **必须**：验证卡号信息正确显示（可为空）
- [ ] **必须**：验证交易日期和时间格式正确
- [ ] **必须**：验证交易方式/类型正确
- [ ] **必须**：验证交易金额格式正确
- [ ] **必须**：验证备注信息正确映射
- [ ] **必须**：验证分页功能正常工作

### 2. 数据一致性验证

#### 2.1 字段完整性
- [ ] 所有必填字段都有值
- [ ] 没有字段显示为"未知"（除非确实应该为空）
- [ ] 数值字段格式正确（金额、日期等）

#### 2.2 数据准确性
- [ ] 交易明细与原始文件数据一致
- [ ] 账户信息与原始文件表头一致
- [ ] 统计数据与明细数据匹配

## ❌ 常见错误及解决方案

### 1. 字段映射错误

**问题**：前端显示"未知"
**原因**：后端字段名与前端期望不一致
**解决**：
- 检查前端代码中使用的字段名
- 确保后端解析器设置了正确的字段名
- 特别注意 `holder_name` vs `cardholder_name`

### 2. 银行名称缺失

**问题**：银行名称显示为"未知"
**原因**：解析器没有设置 `bank_name` 字段
**解决**：
- 在交易记录中添加 `bank_name` 字段
- 在账户信息中添加 `bank_name` 字段

### 3. 表头信息提取失败

**问题**：持卡人姓名等信息显示为"未知"
**原因**：表头信息提取逻辑有误
**解决**：
- 检查表头信息提取的正则表达式
- 验证表头信息是否正确关联到交易记录

## 📋 测试证据要求

每次声称修复完成时必须提供：

### 1. 具体测试场景
- [ ] 列出测试的具体文件
- [ ] 列出测试的具体功能点
- [ ] 列出验证的具体字段

### 2. 未测试场景
- [ ] 明确说明哪些场景没有测试
- [ ] 说明为什么没有测试这些场景

### 3. 用户验证步骤
- [ ] 提供具体的验证步骤
- [ ] 说明用户需要检查的具体内容

### 4. 可能存在的问题
- [ ] 诚实说明可能仍存在的问题
- [ ] 提供进一步测试的建议

## 🔄 持续改进

### 1. 文档更新
- [ ] 每次发现新问题时更新此检查清单
- [ ] 记录解决方案和最佳实践

### 2. 自动化测试
- [ ] 考虑添加自动化测试用例
- [ ] 建立回归测试机制

---

**记住：解析器开发不是一次性任务，需要持续验证和改进！**
