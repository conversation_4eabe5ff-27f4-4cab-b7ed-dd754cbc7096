"""
插件管理器 - 系统核心控制组件

负责插件的发现、加载、卸载、重载和生命周期管理
"""

import threading
import time
import os
import json
import glob
import logging
from typing import Dict, List, Optional, Any
from .plugin_container import PluginContainer
from ..registry.plugin_registry import PluginRegistry

logger = logging.getLogger(__name__)


class PluginManager:
    """插件管理器 - 系统核心
    
    负责管理所有解析器插件的生命周期和执行
    """
    
    def __init__(self, plugins_dir: str = "backend/app/services/parser_plugin_system/plugins"):
        """初始化插件管理器
        
        Args:
            plugins_dir: 插件目录路径
        """
        self.plugins_dir = plugins_dir
        self.registry = PluginRegistry(plugins_dir)
        self.containers: Dict[str, PluginContainer] = {}
        
        # 监控和管理
        self.health_monitor_thread = None
        self.cleanup_thread = None
        self._running = False
        self._lock = threading.Lock()
        
        # 统计信息
        self.total_executions = 0
        self.successful_executions = 0
        self.failed_executions = 0
        self.start_time = None
        
        logger.info(f"🏗️ 插件管理器初始化完成，插件目录: {plugins_dir}")
    
    def start(self):
        """启动插件管理器"""
        if self._running:
            logger.warning("⚠️ 插件管理器已在运行")
            return
        
        logger.info("🚀 启动插件管理器...")
        
        self._running = True
        self.start_time = time.time()
        
        # 发现和注册插件
        self.discover_plugins()
        
        # 自动加载启用的插件
        self.load_enabled_plugins()
        
        # 启动监控线程
        self.start_health_monitor()
        self.start_cleanup_monitor()
        
        logger.info("✅ 插件管理器启动完成")
    
    def stop(self):
        """停止插件管理器"""
        if not self._running:
            logger.warning("⚠️ 插件管理器未在运行")
            return
        
        logger.info("🔒 停止插件管理器...")
        
        self._running = False
        
        # 停止所有插件容器
        with self._lock:
            for plugin_name, container in list(self.containers.items()):
                try:
                    container.stop()
                    logger.info(f"✅ 停止插件: {plugin_name}")
                except Exception as e:
                    logger.error(f"❌ 停止插件失败 {plugin_name}: {e}")
            
            self.containers.clear()
        
        # 等待监控线程结束
        if self.health_monitor_thread and self.health_monitor_thread.is_alive():
            self.health_monitor_thread.join(timeout=5)
        
        if self.cleanup_thread and self.cleanup_thread.is_alive():
            self.cleanup_thread.join(timeout=5)
        
        logger.info("✅ 插件管理器已停止")
    
    def discover_plugins(self):
        """发现插件"""
        logger.info("🔍 开始发现插件...")
        discovered_count = self.registry.discover_plugins()
        logger.info(f"✅ 发现 {discovered_count} 个插件")
    
    def load_enabled_plugins(self):
        """加载所有启用的插件"""
        logger.info("📦 加载启用的插件...")
        
        enabled_plugins = self.registry.get_enabled_plugins()
        loaded_count = 0
        
        for plugin_name in enabled_plugins:
            if self.load_plugin(plugin_name):
                loaded_count += 1
        
        logger.info(f"✅ 成功加载 {loaded_count}/{len(enabled_plugins)} 个插件")
    
    def load_plugin(self, plugin_name: str) -> bool:
        """加载插件
        
        Args:
            plugin_name: 插件名称
            
        Returns:
            bool: 加载是否成功
        """
        try:
            with self._lock:
                # 检查是否已加载
                if plugin_name in self.containers:
                    logger.warning(f"⚠️ 插件已加载: {plugin_name}")
                    return True
                
                # 获取插件信息
                plugin_info = self.registry.get_plugin(plugin_name)
                if not plugin_info:
                    logger.error(f"❌ 插件信息不存在: {plugin_name}")
                    return False
                
                # 检查插件是否启用
                if not plugin_info.get('config', {}).get('enabled', True):
                    logger.warning(f"⚠️ 插件未启用: {plugin_name}")
                    return False
                
                logger.info(f"📦 正在加载插件: {plugin_name}")
                
                # 创建插件容器
                container = PluginContainer(plugin_info)
                
                # 启动容器
                if container.start():
                    self.containers[plugin_name] = container
                    logger.info(f"✅ 插件加载成功: {plugin_name}")
                    return True
                else:
                    logger.error(f"❌ 插件启动失败: {plugin_name}")
                    return False
                
        except Exception as e:
            logger.error(f"❌ 加载插件失败 {plugin_name}: {e}")
            return False
    
    def unload_plugin(self, plugin_name: str) -> bool:
        """卸载插件
        
        Args:
            plugin_name: 插件名称
            
        Returns:
            bool: 卸载是否成功
        """
        try:
            with self._lock:
                if plugin_name not in self.containers:
                    logger.warning(f"⚠️ 插件未加载: {plugin_name}")
                    return True
                
                logger.info(f"🗑️ 正在卸载插件: {plugin_name}")
                
                # 停止容器
                container = self.containers[plugin_name]
                container.stop()
                
                # 从容器列表中移除
                del self.containers[plugin_name]
                
                logger.info(f"✅ 插件卸载成功: {plugin_name}")
                return True
                
        except Exception as e:
            logger.error(f"❌ 卸载插件失败 {plugin_name}: {e}")
            return False
    
    def reload_plugin(self, plugin_name: str) -> bool:
        """重载插件
        
        Args:
            plugin_name: 插件名称
            
        Returns:
            bool: 重载是否成功
        """
        logger.info(f"🔄 重载插件: {plugin_name}")
        
        # 先卸载再加载
        if self.unload_plugin(plugin_name):
            # 短暂等待确保资源清理完成
            time.sleep(1)
            
            # 重新发现插件（防止配置变更）
            self.registry.discover_plugins()
            
            # 重新加载
            return self.load_plugin(plugin_name)
        
        return False
    
    def execute_plugin(self, plugin_name: str, file_path: str) -> Dict[str, Any]:
        """执行插件解析 - 增强错误处理版本
        
        Args:
            plugin_name: 插件名称
            file_path: 文件路径
            
        Returns:
            Dict[str, Any]: 解析结果
        """
        self.total_executions += 1
        
        try:
            # 检查插件是否已加载
            if plugin_name not in self.containers:
                # 尝试自动加载
                logger.info(f"🔄 插件未加载，尝试自动加载: {plugin_name}")
                if not self.load_plugin(plugin_name):
                    self.failed_executions += 1
                    
                    # 智能回退策略：尝试使用通用解析器
                    if plugin_name != "universal" and "universal" in self.containers:
                        logger.info(f"🔄 回退到通用解析器")
                        return self.execute_plugin("universal", file_path)
                    
                    return {
                        "success": False,
                        "message": f"插件 {plugin_name} 加载失败，且无可用的回退选项",
                        "transactions": [],
                        "accounts": [],
                        "error_type": "plugin_load_failed"
                    }
            
            # 获取容器
            container = self.containers[plugin_name]
            
            # 检查容器健康状态
            if not container.is_healthy():
                logger.warning(f"⚠️ 插件容器不健康，尝试重启: {plugin_name}")
                if self.reload_plugin(plugin_name):
                    container = self.containers[plugin_name]
                else:
                    # 重启失败，尝试回退
                    if plugin_name != "universal" and "universal" in self.containers:
                        logger.info(f"🔄 插件重启失败，回退到通用解析器")
                        return self.execute_plugin("universal", file_path)
            
            # 执行解析
            logger.info(f"🚀 执行插件解析: {plugin_name}")
            result = container.execute_parse(file_path)
            
            # 智能结果处理
            if result.get('success', False):
                self.successful_executions += 1
                logger.info(f"✅ 插件执行成功: {plugin_name}")
                
                # 验证结果完整性
                if not self._validate_parse_result(result):
                    logger.warning(f"⚠️ 解析结果不完整，但标记为成功")
                    result['validation_warnings'] = ["解析结果可能不完整"]
                
            else:
                self.failed_executions += 1
                error_type = result.get('error_type', 'unknown')
                
                logger.error(f"❌ 插件执行失败: {plugin_name}, 错误类型: {error_type}")
                
                # 根据错误类型决定是否重试或回退
                if self._should_fallback_to_universal(error_type, plugin_name):
                    logger.info(f"🔄 根据错误类型回退到通用解析器")
                    return self.execute_plugin("universal", file_path)
            
            return result
            
        except FileNotFoundError as e:
            self.failed_executions += 1
            error_msg = f"文件不存在: {str(e)}"
            logger.error(f"❌ {error_msg}")
            
            return {
                "success": False,
                "message": error_msg,
                "transactions": [],
                "accounts": [],
                "error_type": "file_not_found",
                "error_trace": str(e)
            }
            
        except PermissionError as e:
            self.failed_executions += 1
            error_msg = f"文件权限错误: {str(e)}"
            logger.error(f"❌ {error_msg}")
            
            return {
                "success": False,
                "message": error_msg,
                "transactions": [],
                "accounts": [],
                "error_type": "permission_error",
                "error_trace": str(e)
            }
            
        except MemoryError as e:
            self.failed_executions += 1
            error_msg = f"内存不足: {str(e)}"
            logger.error(f"❌ {error_msg}")
            
            # 内存错误时强制清理
            try:
                import gc
                gc.collect()
                logger.info("🧹 执行内存清理")
            except:
                pass
            
            return {
                "success": False,
                "message": error_msg,
                "transactions": [],
                "accounts": [],
                "error_type": "memory_error",
                "error_trace": str(e)
            }
            
        except Exception as e:
            self.failed_executions += 1
            import traceback
            error_trace = traceback.format_exc()
            error_msg = f"插件执行异常: {str(e)}"
            
            logger.error(f"❌ 插件管理器执行异常: {plugin_name}")
            logger.error(f"   错误信息: {error_msg}")
            logger.error(f"   完整堆栈:\n{error_trace}")
            
            # 记录到容器的错误处理器
            if plugin_name in self.containers:
                try:
                    self.containers[plugin_name].plugin_instance.handle_error(e)
                except:
                    pass
            
            # 对于未知异常，如果不是通用解析器，尝试回退
            if plugin_name != "universal" and "universal" in self.containers:
                logger.info(f"🔄 未知异常，尝试回退到通用解析器")
                return self.execute_plugin("universal", file_path)
            
            return {
                "success": False,
                "message": error_msg,
                "transactions": [],
                "accounts": [],
                "error_type": "unknown_error",
                "error_trace": error_trace
            }
    
    def _validate_parse_result(self, result: Dict[str, Any]) -> bool:
        """验证解析结果完整性"""
        try:
            required_fields = ['success', 'accounts', 'transactions']
            for field in required_fields:
                if field not in result:
                    return False
            
            # 检查账户和交易数据基本结构
            accounts = result.get('accounts', [])
            transactions = result.get('transactions', [])
            
            if not isinstance(accounts, list) or not isinstance(transactions, list):
                return False
            
            # 如果有数据，检查基本字段
            if accounts:
                account = accounts[0]
                required_account_fields = ['account_id', 'person_name', 'bank_name']
                if not all(field in account for field in required_account_fields):
                    return False
            
            if transactions:
                transaction = transactions[0]
                required_transaction_fields = ['transaction_id', 'transaction_date', 'transaction_amount']
                if not all(field in transaction for field in required_transaction_fields):
                    return False
            
            return True
            
        except Exception:
            return False
    
    def _should_fallback_to_universal(self, error_type: str, plugin_name: str) -> bool:
        """判断是否应该回退到通用解析器"""
        if plugin_name == "universal":
            return False  # 已经是通用解析器，不再回退
        
        if "universal" not in self.containers:
            return False  # 通用解析器不可用
        
        # 定义需要回退的错误类型
        fallback_error_types = [
            'parse_error',
            'format_not_supported',
            'confidence_too_low',
            'data_inconsistency',
            'unknown_error'
        ]
        
        return error_type in fallback_error_types
    
    def get_plugin_status(self, plugin_name: str) -> Dict[str, Any]:
        """获取插件状态
        
        Args:
            plugin_name: 插件名称
            
        Returns:
            Dict[str, Any]: 状态信息
        """
        if plugin_name not in self.containers:
            return {
                "status": "not_loaded",
                "message": "插件未加载"
            }
        
        container = self.containers[plugin_name]
        return container.get_status()
    
    def list_available_plugins(self) -> List[str]:
        """列出可用插件
        
        Returns:
            List[str]: 插件名称列表
        """
        return self.registry.list_plugins()
    
    def list_loaded_plugins(self) -> List[str]:
        """列出已加载插件
        
        Returns:
            List[str]: 已加载插件名称列表
        """
        with self._lock:
            return list(self.containers.keys())
    
    def get_best_plugin_for_file(self, file_path: str) -> Optional[str]:
        """为文件选择最佳插件
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[str]: 最佳插件名称
        """
        best_plugin = None
        best_confidence = 0.0
        
        with self._lock:
            for plugin_name, container in self.containers.items():
                try:
                    if container.status == "running" and container.plugin_instance:
                        confidence = container.plugin_instance.calculate_confidence(file_path)
                        
                        if confidence > best_confidence:
                            best_confidence = confidence
                            best_plugin = plugin_name
                            
                except Exception as e:
                    logger.error(f"❌ 计算插件置信度失败 {plugin_name}: {e}")
                    continue
        
        # 如果没有找到合适的插件，尝试通用解析器
        if best_plugin is None or best_confidence < 0.7:
            if "universal" in self.containers:
                return "universal"
        
        return best_plugin
    
    def start_health_monitor(self):
        """启动健康监控 - 优化版本"""
        def monitor():
            logger.info("💊 健康监控线程启动")
            # 🔧 优化：启动后等待2分钟再开始健康检查，给插件充分的初始化时间
            time.sleep(120)

            while self._running:
                try:
                    self.check_plugin_health()
                    # 🔧 优化：降低检查频率从30秒改为5分钟
                    time.sleep(300)  # 5分钟检查一次
                except Exception as e:
                    logger.error(f"❌ 健康检查失败: {e}")
                    time.sleep(60)  # 出错后1分钟重试

        self.health_monitor_thread = threading.Thread(target=monitor, name="PluginHealthMonitor")
        self.health_monitor_thread.daemon = True
        self.health_monitor_thread.start()
    
    def check_plugin_health(self):
        """检查插件健康状态 - 优化版本"""
        unhealthy_plugins = []

        with self._lock:
            for plugin_name, container in list(self.containers.items()):
                try:
                    # 🔧 优化：检查插件运行时间，新启动的插件给予宽容期
                    uptime = time.time() - container.start_time if hasattr(container, 'start_time') else 0

                    # 如果插件运行时间少于5分钟，跳过健康检查
                    if uptime < 300:  # 5分钟宽容期
                        logger.debug(f"🕐 插件 {plugin_name} 在宽容期内，跳过健康检查 (运行时间: {uptime:.1f}秒)")
                        continue

                    if not container.is_healthy():
                        # 🔧 优化：记录不健康次数，避免频繁重启
                        if not hasattr(container, 'unhealthy_count'):
                            container.unhealthy_count = 0
                        container.unhealthy_count += 1

                        logger.warning(f"🏥 发现不健康插件: {plugin_name} (连续不健康次数: {container.unhealthy_count})")

                        # 只有连续3次不健康才重启
                        if container.unhealthy_count >= 3:
                            unhealthy_plugins.append(plugin_name)
                    else:
                        # 插件恢复健康，重置计数器
                        if hasattr(container, 'unhealthy_count'):
                            container.unhealthy_count = 0

                except Exception as e:
                    logger.error(f"❌ 健康检查失败 {plugin_name}: {e}")
                    # 检查异常也计入不健康
                    if not hasattr(container, 'unhealthy_count'):
                        container.unhealthy_count = 0
                    container.unhealthy_count += 1

                    if container.unhealthy_count >= 3:
                        unhealthy_plugins.append(plugin_name)

        # 🔧 优化：智能重启策略
        for plugin_name in unhealthy_plugins:
            try:
                container = self.containers.get(plugin_name)
                if container:
                    logger.info(f"🔄 重启持续不健康插件: {plugin_name} (不健康次数: {container.unhealthy_count})")

                    if self.reload_plugin(plugin_name):
                        logger.info(f"✅ 插件重启成功: {plugin_name}")
                        # 重置不健康计数器
                        new_container = self.containers.get(plugin_name)
                        if new_container:
                            new_container.unhealthy_count = 0
                    else:
                        logger.error(f"❌ 插件重启失败: {plugin_name}")

            except Exception as e:
                logger.error(f"❌ 重启插件异常 {plugin_name}: {e}")
    
    def start_cleanup_monitor(self):
        """启动清理监控 - 优化版本"""
        def cleanup():
            logger.info("🧹 清理监控线程启动")
            # 🔧 优化：启动后等待10分钟再开始清理，减少初期资源消耗
            time.sleep(600)

            while self._running:
                try:
                    self.cleanup_resources()
                    # 🔧 优化：降低清理频率从5分钟改为15分钟
                    time.sleep(900)  # 15分钟清理一次
                except Exception as e:
                    logger.error(f"❌ 资源清理失败: {e}")
                    time.sleep(300)  # 出错后5分钟重试

        self.cleanup_thread = threading.Thread(target=cleanup, name="PluginCleanupMonitor")
        self.cleanup_thread.daemon = True
        self.cleanup_thread.start()
    
    def cleanup_resources(self):
        """清理资源 - 优化版本"""
        cleaned_count = 0

        with self._lock:
            for plugin_name, container in self.containers.items():
                try:
                    # 🔧 优化：只清理运行时间超过30分钟的插件
                    uptime = time.time() - container.start_time if hasattr(container, 'start_time') else 0

                    if uptime > 1800:  # 30分钟
                        container.cleanup_resources()
                        cleaned_count += 1
                        logger.debug(f"🧹 清理插件资源: {plugin_name} (运行时间: {uptime/60:.1f}分钟)")

                except Exception as e:
                    logger.error(f"❌ 清理插件资源失败 {plugin_name}: {e}")

        if cleaned_count > 0:
            logger.info(f"🧹 清理了 {cleaned_count} 个插件的资源")
        else:
            logger.debug(f"🧹 清理了 {cleaned_count} 条旧的执行记录")
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态
        
        Returns:
            Dict[str, Any]: 系统状态信息
        """
        uptime = time.time() - self.start_time if self.start_time else 0
        
        with self._lock:
            loaded_plugins = len(self.containers)
            running_plugins = len([c for c in self.containers.values() if c.status == "running"])
            error_plugins = len([c for c in self.containers.values() if c.status == "error"])
        
        success_rate = (self.successful_executions / max(self.total_executions, 1)) * 100
        
        return {
            "running": self._running,
            "uptime": uptime,
            "total_plugins": len(self.registry.list_plugins()),
            "loaded_plugins": loaded_plugins,
            "running_plugins": running_plugins,
            "error_plugins": error_plugins,
            "total_executions": self.total_executions,
            "successful_executions": self.successful_executions,
            "failed_executions": self.failed_executions,
            "success_rate": f"{success_rate:.2f}%"
        }
    
    def get_plugin_performance_report(self) -> Dict[str, Any]:
        """获取插件性能报告
        
        Returns:
            Dict[str, Any]: 性能报告
        """
        report = {
            "system_status": self.get_system_status(),
            "plugins": {}
        }
        
        with self._lock:
            for plugin_name, container in self.containers.items():
                try:
                    status = container.get_status()
                    metrics = container.get_performance_metrics()
                    
                    report["plugins"][plugin_name] = {
                        "status": status,
                        "metrics": metrics
                    }
                    
                except Exception as e:
                    report["plugins"][plugin_name] = {
                        "error": f"获取性能数据失败: {str(e)}"
                    }
        
        return report
    
    def force_reload_all_plugins(self):
        """强制重载所有插件"""
        logger.info("🔄 强制重载所有插件...")
        
        plugin_names = self.list_loaded_plugins()
        
        for plugin_name in plugin_names:
            try:
                self.reload_plugin(plugin_name)
            except Exception as e:
                logger.error(f"❌ 强制重载插件失败 {plugin_name}: {e}")
        
        logger.info("✅ 所有插件重载完成")
    
    def emergency_stop(self):
        """紧急停止"""
        logger.info("🚨 执行紧急停止...")
        
        self._running = False
        
        # 强制停止所有容器
        with self._lock:
            for plugin_name, container in list(self.containers.items()):
                try:
                    container.force_stop()
                except Exception as e:
                    logger.error(f"❌ 强制停止插件失败 {plugin_name}: {e}")
            
            self.containers.clear()
        
        logger.info("🚨 紧急停止完成")
    
    def get_plugin_logs(self, plugin_name: str, lines: int = 50) -> List[str]:
        """获取插件日志
        
        Args:
            plugin_name: 插件名称
            lines: 日志行数
            
        Returns:
            List[str]: 日志内容
        """
        # TODO: 实现日志收集功能
        return [f"日志功能待实现 - 插件: {plugin_name}"] 