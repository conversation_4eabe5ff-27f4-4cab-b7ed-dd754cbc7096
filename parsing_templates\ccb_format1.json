{"templateId": "ccb_format1", "bankName": "中国建设银行", "formatName": "格式1标准", "parserClass": "CCBFormat1Parser", "priority": 20, "description": "中国建设银行标准Excel格式流水解析器", "supportedFileTypes": [".xlsx", ".xls"], "identificationMarkers": {"bankKeywords": ["建设银行", "CCB", "中国建设银行"], "fileNamePatterns": ["建设", "ccb", "CCB"], "headerKeywords": ["交易日期", "摘要", "支出", "收入", "余额"]}, "fieldMapping": {"transaction_date": ["交易日期", "日期", "记账日期"], "transaction_amount": ["金额", "发生额", "支出", "收入"], "balance": ["余额", "账户余额", "当前余额"], "counterparty_name": ["对方户名", "对方姓名"], "purpose": ["用途", "备注", "摘要"], "account_number": ["账号", "卡号", "账户号"], "cardholder_name": ["户名", "持卡人", "账户名"]}, "validationRules": {"minTransactions": 1, "requiredFields": ["transaction_date", "transaction_amount"], "dateFormats": ["%Y-%m-%d", "%Y/%m/%d", "%Y.%m.%d"], "amountRange": [-1000000, 1000000]}, "confidenceWeights": {"cardholder_detection": 25, "date_accuracy": 25, "account_identification": 25, "amount_parsing": 25}}