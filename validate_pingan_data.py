#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
平安银行数据还原核验脚本（替代前端MCP自动化）

步骤：
1. 使用插件管理器解析文件，得到标准化transactions/accounts
2. 直接读取原始Excel（调用插件的表头行探测与列映射方法），抽取同字段的原值
3. 对比关键字段映射的等价性：
   - 总笔数、起止时间、持卡人/账号/卡号
   - 前50条交易的：日期、时间、金额(绝对值)、余额、摘要→交易方式、备注→备注1、D/C→收/支
输出：精简差异报告
"""

import os
import sys
import json
from typing import Dict, Any, List, Tuple

import pandas as pd

sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from app.services.parser_plugin_system.plugins.pingan_format1_plugin.plugin import Plugin as PinganPlugin


def main(file_path: str, sample: int = 50) -> int:
    plugin = PinganPlugin()

    # 1) 执行插件解析
    result = plugin.parse(file_path)
    if not result.get('success'):
        print('❌ 插件解析失败:', result.get('error'))
        return 2

    accounts = result.get('accounts', [])
    txs = result.get('transactions', [])

    # 2) 直接读取源表并构造“源字段视角”的记录
    xls = pd.ExcelFile(file_path)
    sheet, header_row = plugin._select_target_sheet_and_header(xls)
    df = pd.read_excel(file_path, sheet_name=sheet, header=header_row, dtype=str)
    cmap = plugin._build_column_mapping(df)
    h = plugin._extract_header_info(file_path, sheet, header_row)

    # 过滤出与插件相同的有效行（日期/金额/余额均有值）
    def has_value(row, key):
        if key not in cmap:
            return False
        v = row.iloc[cmap[key]]
        return pd.notna(v) and str(v).strip() not in ('', 'nan')

    valid_rows = []
    for _, row in df.iterrows():
        if has_value(row, 'date') and has_value(row, 'amount') and has_value(row, 'balance'):
            valid_rows.append(row)

    # 3) 账户层面对比
    print('=== 账户信息核对 ===')
    if accounts:
        a = accounts[0]
        ok_holder = a.get('holder_name') == h.get('holder_name')
        ok_account = a.get('account_number') == h.get('account_number')
        ok_card = a.get('card_number') == h.get('card_number')
        print('持卡人:', a.get('holder_name'), '| 源表:', h.get('holder_name'), '|', 'OK' if ok_holder else 'MISMATCH')
        print('账号  :', a.get('account_number'), '| 源表:', h.get('account_number'), '|', 'OK' if ok_account else 'MISMATCH')
        print('卡号  :', a.get('card_number'), '| 源表:', h.get('card_number'), '|', 'OK' if ok_card else 'MISMATCH')
    else:
        print('❌ 无账户数据')

    # 4) 总笔数与起止时间核对
    print('\n=== 汇总核对 ===')
    print('解析交易数:', len(txs))
    print('源表有效行数(等价规则):', len(valid_rows))
    # 起止日期
    dates = [t.get('transaction_date') for t in txs if t.get('transaction_date')]
    if dates:
        print('起止时间:', min(dates), '到', max(dates))

    # 5) 明细字段逐项对比（取前sample条）
    print('\n=== 明细核对(前{}条) ==='.format(min(sample, len(txs))))
    fields_ok = {
        'date': 0,
        'time': 0,
        'amount': 0,
        'balance': 0,
        'summary': 0,
        'remark': 0,
        'dc': 0,
    }
    total_compared = 0

    for i in range(min(sample, len(txs), len(valid_rows))):
        tx = txs[i]
        row = valid_rows[i]

        # 源字段
        src_date = plugin._format_date(row.iloc[cmap['date']]) if 'date' in cmap else ''
        src_time = plugin._format_time(row.iloc[cmap['time']]) if 'time' in cmap else ''
        src_amount = plugin._parse_amount(row.iloc[cmap['amount']]) if 'amount' in cmap else None
        src_balance = plugin._parse_amount(row.iloc[cmap['balance']]) if 'balance' in cmap else None
        src_summary = str(row.iloc[cmap['summary']]).strip() if 'summary' in cmap and pd.notna(row.iloc[cmap['summary']]) else ''
        src_remark = str(row.iloc[cmap['remark']]).strip() if 'remark' in cmap and pd.notna(row.iloc[cmap['remark']]) else ''
        src_dc_raw = str(row.iloc[cmap['dc']]).strip() if 'dc' in cmap and pd.notna(row.iloc[cmap['dc']]) else ''
        src_dc = '收' if src_dc_raw == '+' else ('支' if src_dc_raw == '-' else '')

        # 目标字段
        ok1 = (tx.get('transaction_date') == src_date)
        ok2 = (tx.get('transaction_time') == src_time or (tx.get('transaction_time') == '' and src_time in ('', '00:00:00')))
        ok3 = (abs(float(tx.get('transaction_amount', 0.0))) == abs(float(src_amount or 0.0)))
        ok4 = (float(tx.get('balance_amount', 0.0)) == float(src_balance or 0.0))
        ok5 = (tx.get('transaction_method', '') == src_summary)
        ok6 = (tx.get('remark1', '') == src_remark)
        ok7 = (tx.get('dr_cr_flag', '') == src_dc)

        fields_ok['date'] += 1 if ok1 else 0
        fields_ok['time'] += 1 if ok2 else 0
        fields_ok['amount'] += 1 if ok3 else 0
        fields_ok['balance'] += 1 if ok4 else 0
        fields_ok['summary'] += 1 if ok5 else 0
        fields_ok['remark'] += 1 if ok6 else 0
        fields_ok['dc'] += 1 if ok7 else 0
        total_compared += 1

        if not (ok1 and ok2 and ok3 and ok4 and ok5 and ok6 and ok7):
            print(f"第{i+1}条差异: date={ok1}, time={ok2}, amount={ok3}, balance={ok4}, summary={ok5}, remark={ok6}, dc={ok7}")

    if total_compared:
        print('\n字段一致率:')
        for k, v in fields_ok.items():
            print(f"  {k}: {v}/{total_compared} ({v*100.0/total_compared:.1f}%)")

    return 0


if __name__ == '__main__':
    fp = r"F:\\流水清洗\\银行流水数据参考\\8平安银行\\16000581774609-司法个人查询杨蕊瑜.xlsx"
    exit(main(fp))



