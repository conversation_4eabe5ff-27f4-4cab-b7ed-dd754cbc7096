# 数据库文件
*.duckdb
*.db
*.sqlite
*.sqlite3

# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 测试覆盖率报告
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 环境变量文件
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.eslintcache

# Electron
dist/
out/

# 临时文件
temp/
tmp/
*.tmp
*.temp
*.log

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 数据目录
backend/data/
data/

# 日志文件
*.log
logs/

# 备份文件
*.bak
*.backup

# 项目特定的测试和调试文件
debug_*.py
debug_*.json
test_*.py
check_*.py
analyze_*.py
final_test*.py
simple_test*.py
frontend_*test*.py
quick_test*.py
verify_*.py
*_test_*.py
*.test.py
*.debug.py

# 项目特定的文件
cursor密码.txt
icbc_test_file.xlsx
icbc_proper_test_file.xlsx
垃圾文件备份_*/
test_icbc_sample.xlsx