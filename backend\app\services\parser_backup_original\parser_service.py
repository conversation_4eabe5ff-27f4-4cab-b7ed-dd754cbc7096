"""
银行流水解析服务 - 使用新Enhanced解析器架构
"""
import os
import logging
import traceback
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session

from .data_storage_service import DataStorageService
from .parser.pre_parser_system import PreParserSystem
from .parser.icbc_format1_enhanced import ICBCFormat1EnhancedParser
from .parser.icbc_format3_standard import ICBCFormat3StandardParser
from .parser.icbc_format4_enhanced import ICBCFormat4EnhancedParser

logger = logging.getLogger(__name__)

class ParserService:
    """
    解析服务类 - 使用新Enhanced解析器架构
    """
    
    def __init__(self, template_dir: str = None):
        """
        初始化解析服务
        
        Args:
            template_dir: 模板目录路径
        """
        self.pre_parser = PreParserSystem()
        self.data_storage = DataStorageService()
        
        # 支持的Enhanced解析器映射
        self.enhanced_parsers = {
            'ICBCFormat1EnhancedParser': ICBCFormat1EnhancedParser,
            'ICBCFormat3StandardParser': ICBCFormat3StandardParser, 
            'ICBCFormat4EnhancedParser': ICBCFormat4EnhancedParser
        }
    
    def parse_file(self, file_path: str, db: Session = None) -> Optional[Dict[str, Any]]:
        """
        解析银行流水文件 - 使用新Enhanced解析器
        
        Args:
            file_path: 文件路径
            db: 数据库会话
            
        Returns:
            Optional[Dict[str, Any]]: 解析结果
        """
        try:
            logger.info(f"开始解析文件: {os.path.basename(file_path)}")
            
            # 使用预解析系统选择最佳Enhanced解析器
            pre_parse_result = self.pre_parser.select_best_parser(file_path)
            
            if not pre_parse_result or not pre_parse_result.get('best_parser'):
                logger.error("预解析失败，无法选择合适的解析器")
                return None
            
            best_parser_info = pre_parse_result['best_parser']
            parser_name = best_parser_info['name']
            
            logger.info(f"选择解析器: {parser_name} (置信度: {best_parser_info['confidence']:.2f})")
            
            # 使用选择的Enhanced解析器
            if parser_name in self.enhanced_parsers:
                parser_class = self.enhanced_parsers[parser_name]
                parser = parser_class(file_path)
                result = parser.parse()
                
                if result and result.get('success'):
                    logger.info(f"解析成功: {result.get('summary', {}).get('total_transactions', 0)}条交易")
                    return result
                else:
                    logger.warning(f"{parser_name}解析失败")
            else:
                logger.error(f"不支持的解析器: {parser_name}")
            
            return None
            
        except Exception as e:
            logger.error(f"解析文件时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return None
    
    def get_supported_banks(self) -> List[str]:
        """
        获取支持的银行列表
            
        Returns:
            List[str]: 支持的银行名称列表
        """
        return ["中国工商银行"]
    
    def validate_file_format(self, file_path: str) -> bool:
        """
        验证文件格式是否受支持
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 文件格式是否受支持
        """
        try:
            if not os.path.exists(file_path):
                return False
            
            # 检查文件扩展名
            _, ext = os.path.splitext(file_path.lower())
            return ext in ['.xlsx', '.xls']
            
        except Exception as e:
            logger.error(f"验证文件格式时出错: {str(e)}")
            return False