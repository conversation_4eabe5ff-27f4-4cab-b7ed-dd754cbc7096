#!/usr/bin/env python3
"""
建设银行格式1解析器 - 重构版
参考工商银行成功模式，专门处理建设银行流水Excel文件
支持标准的银行流水数据结构和置信度评估
"""

import pandas as pd
import logging
from typing import Dict, List, Any, Tuple
from datetime import datetime
import re
import os

logger = logging.getLogger(__name__)

class CCBFormat1Parser:
    """建设银行格式1解析器 - 基于工商银行成功模式重构"""
    
    def __init__(self, file_path: str):
        """
        初始化解析器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.accounts = []
        self.transactions = []
        
        # 建设银行特定的字段映射
        self.field_mapping = {
            'date_field': '交易日期',
            'time_field': '交易时间', 
            'amount_field': '交易金额',
            'balance_field': '账户余额',
            'summary_field': '摘要',
            'remark_field': '扩充备注',
            'cardholder_field': '客户名称',
            'card_field': '交易卡号',
            'direction_field': '借贷方向',
            'account_field': '账号',
            'opposite_account_field': '对方账号',
            'opposite_name_field': '对方户名'
        }
    
    def _clean_field(self, value) -> str:
        """清理字段值"""
        if value is None or pd.isna(value):
            return ""
        return str(value).strip().replace('\t', '').replace('\n', '').replace('\r', '')
    
    def _clean_amount_string(self, value) -> str:
        """清理金额字符串，移除空格、逗号等格式字符"""
        if value is None:
            return ""
        
        # 转换为字符串并清理各种格式字符
        clean_value = str(value).replace(" ", "").replace(",", "").replace("，", "").replace("\t", "").replace("\n", "").replace("\r", "").strip()
        
        # 移除可能的货币符号
        clean_value = clean_value.replace("¥", "").replace("$", "").replace("€", "").replace("￥", "")
        
        return clean_value
    
    def _parse_amount(self, amount_str: str, dr_cr_flag: str = "") -> float:
        """
        解析金额
        
        Args:
            amount_str: 金额字符串
            dr_cr_flag: 借贷标志
            
        Returns:
            float: 解析后的金额（负数表示支出）
        """
        if not amount_str:
            return 0.0
        
        clean_amount = self._clean_amount_string(amount_str)
        
        try:
            amount = float(clean_amount)
            
            # 根据借贷标志调整正负
            if dr_cr_flag == "借":  # 借方=支出，转为负数
                return -abs(amount)
            elif dr_cr_flag == "贷":  # 贷方=收入，保持正数
                return abs(amount)
            else:
                return amount
        except (ValueError, TypeError):
            logger.warning(f"无法解析金额: {amount_str}")
            return 0.0
    
    def _standardize_time_format(self, date_str: str, time_str: str = "") -> str:
        """
        标准化时间格式为 YYYY-MM-DD HH:MM:SS
        
        Args:
            date_str: 日期字符串
            time_str: 时间字符串
            
        Returns:
            str: 标准化后的时间字符串
        """
        if not date_str:
            return ""
        
        try:
            # 处理日期
            date_clean = self._clean_field(date_str)
            if not date_clean:
                return ""
            
            # 尝试解析日期
            date_obj = pd.to_datetime(date_clean).date()
            date_formatted = date_obj.strftime("%Y-%m-%d")
            
            # 处理时间
            if time_str:
                time_clean = self._clean_field(time_str)
                # 处理 HH.MM.SS 格式
                if '.' in time_clean:
                    time_clean = time_clean.replace('.', ':')
                
                # 确保时间格式正确
                time_parts = time_clean.split(':')
                if len(time_parts) == 3:
                    try:
                        hour = int(time_parts[0])
                        minute = int(time_parts[1])
                        second = int(time_parts[2])
                        time_formatted = f"{hour:02d}:{minute:02d}:{second:02d}"
                        return f"{date_formatted} {time_formatted}"
                    except ValueError:
                        pass
            
            # 如果没有时间或时间格式错误，返回只有日期的格式
            return f"{date_formatted} 00:00:00"
            
        except Exception as e:
            logger.warning(f"时间格式标准化失败: {date_str} {time_str}, 错误: {str(e)}")
            return date_str
    
    def _find_data_start_row(self, df: pd.DataFrame) -> int:
        """查找数据开始行"""
        for i, row in df.iterrows():
            # 寻找包含关键字段的行作为表头
            row_str = ' '.join([str(cell) for cell in row if pd.notna(cell)])
            if '交易日期' in row_str and '交易金额' in row_str:
                logger.info(f"在第{i+1}行找到数据表头")
                return i
        
        logger.warning("未找到数据表头，使用默认第0行")
        return 0
    
    def _extract_account_info(self, df: pd.DataFrame) -> Tuple[str, str, str]:
        """从DataFrame中提取账户信息"""
        account_name = ""
        account_number = ""
        card_number = ""
        
        # 从数据行中提取客户名称和账户信息
        if not df.empty:
            # 查找客户名称
            if self.field_mapping['cardholder_field'] in df.columns:
                first_valid_name = df[self.field_mapping['cardholder_field']].dropna().iloc[0] if not df[self.field_mapping['cardholder_field']].dropna().empty else ""
                account_name = self._clean_field(first_valid_name)
            
            # 查找账户号
            if self.field_mapping['account_field'] in df.columns:
                first_valid_account = df[self.field_mapping['account_field']].dropna().iloc[0] if not df[self.field_mapping['account_field']].dropna().empty else ""
                account_number = self._clean_field(first_valid_account)
            
            # 查找卡号
            if self.field_mapping['card_field'] in df.columns:
                first_valid_card = df[self.field_mapping['card_field']].dropna().iloc[0] if not df[self.field_mapping['card_field']].dropna().empty else ""
                card_number = self._clean_field(first_valid_card)
        
        return account_name, account_number, card_number
    
    def _process_transaction_data(self, df: pd.DataFrame, account_name: str, account_number: str, card_number: str) -> None:
        """处理交易数据"""
        transactions_added = 0
        
        for _, row in df.iterrows():
            # 提取交易数据
            trade_date = self._clean_field(row.get(self.field_mapping['date_field'], ''))
            trade_time = self._clean_field(row.get(self.field_mapping['time_field'], ''))
            amount_str = self._clean_field(row.get(self.field_mapping['amount_field'], ''))
            balance_str = self._clean_field(row.get(self.field_mapping['balance_field'], ''))
            summary = self._clean_field(row.get(self.field_mapping['summary_field'], ''))
            remark = self._clean_field(row.get(self.field_mapping['remark_field'], ''))
            direction = self._clean_field(row.get(self.field_mapping['direction_field'], ''))
            opposite_account = self._clean_field(row.get(self.field_mapping['opposite_account_field'], ''))
            opposite_name = self._clean_field(row.get(self.field_mapping['opposite_name_field'], ''))
            
            # 跳过无效行
            if not trade_date or not amount_str:
                continue
            
            # 解析金额和余额
            amount = self._parse_amount(amount_str, direction)
            balance = self._parse_amount(balance_str) if balance_str else 0.0
            
            # 🎯 处理收支符号 - 统一转换为"收/支"格式（与工商银行解析器一致）
            dr_cr_flag = "收" if direction in ["贷", "收入"] else "支"
            
            # 标准化时间  
            standardized_time = self._standardize_time_format(trade_date, trade_time)
            
            # 🎯 添加序号字段
            sequence_number = len(self.transactions) + 1
            
            # 构建交易记录 - 使用与保存API一致的字段名
            transaction = {
                'sequence_number': sequence_number,            # ✅ 新增：序号字段
                'transaction_datetime': standardized_time,     # ✅ 修复：transaction_datetime
                'transaction_date': standardized_time,         # 保持兼容性
                'transaction_time': standardized_time,         # 保持兼容性
                'transaction_amount': amount,                  # ✅ 修复：transaction_amount
                'amount': amount,                              # 保持兼容性
                'balance_amount': balance,                     # ✅ 修复：balance_amount
                'balance': balance,                            # 保持兼容性
                'transaction_method': summary,                 # ✅ 修复：transaction_method
                'summary': summary,                            # 保持兼容性
                'remark1': remark,                             # ✅ 修复：remark1
                'remark': remark,                              # 保持兼容性
                'dr_cr_flag': dr_cr_flag,                      # 🎯 修复：统一为"收/支"格式
                'direction': direction,                        # 保持原始值兼容性
                'counterparty_account': opposite_account,      # ✅ 修复：counterparty_account
                'opposite_account': opposite_account,          # 保持兼容性
                'counterparty_name': opposite_name,            # ✅ 修复：counterparty_name
                'opposite_name': opposite_name,                # 保持兼容性
                'holder_name': account_name,                   # ✅ 修复：holder_name
                'cardholder_name': account_name,               # 保持兼容性
                'account_number': account_number,
                'card_number': card_number,
                'bank_name': '中国建设银行',
                'currency': 'CNY'                              # ✅ 新增：币种字段
            }
            
            self.transactions.append(transaction)
            transactions_added += 1
        
        logger.info(f"成功处理 {transactions_added} 条交易记录")
    
    def _process_sheet(self, sheet_name: str) -> None:
        """处理单个工作表"""
        try:
            logger.info(f"分析工作表: {sheet_name}")
            
            # 读取工作表
            df = pd.read_excel(self.file_path, sheet_name=sheet_name)
            
            if df.empty:
                logger.warning(f"工作表 {sheet_name} 为空")
                return
            
            # 查找数据开始行
            data_start_row = self._find_data_start_row(df)
            
            # 重新读取，从数据开始行作为表头
            df = pd.read_excel(self.file_path, sheet_name=sheet_name, header=data_start_row)
            
            logger.info(f"从工作表'{sheet_name}'提取到{len(df)}行数据")
            logger.info(f"🔍 检查列名: {list(df.columns)}")
            
            # 验证必要字段是否存在
            required_fields = [self.field_mapping['date_field'], self.field_mapping['amount_field']]
            missing_fields = [field for field in required_fields if field not in df.columns]
            
            if missing_fields:
                logger.error(f"工作表 {sheet_name} 缺少必需字段: {missing_fields}")
                return
            
            # 提取账户信息
            account_name, account_number, card_number = self._extract_account_info(df)
            
            # 添加账户记录
            if account_name:
                account = {
                    'holder_name': account_name,
                    'cardholder_name': account_name,
                    'account_number': account_number,
                    'card_number': card_number,
                    'bank_name': '中国建设银行',
                    'account_type': '个人账户'
                }
                self.accounts.append(account)
                logger.info(f"✅ 找到账户: {account_name} - {account_number or card_number}")
            
            # 处理交易数据
            self._process_transaction_data(df, account_name, account_number, card_number)
            
        except Exception as e:
            logger.error(f"处理工作表 {sheet_name} 时出错: {str(e)}")
    
    def parse(self) -> Dict[str, Any]:
        """
        执行解析
        
        Returns:
            Dict[str, Any]: 解析结果
        """
        try:
            logger.info(f"开始解析建设银行Format1文件: {self.file_path}")
            
            if not os.path.exists(self.file_path):
                return {
                    'success': False,
                    'error': f'文件不存在: {self.file_path}'
                }
            
            # 获取所有工作表
            excel_file = pd.ExcelFile(self.file_path)
            sheet_names = excel_file.sheet_names
            logger.info(f"发现工作表: {sheet_names}")
            
            # 处理每个工作表
            for sheet_name in sheet_names:
                self._process_sheet(sheet_name)
            
            # 计算置信度
            confidence_score = self._calculate_confidence_score(self.accounts, self.transactions)
            
            logger.info(f"解析完成，置信度: {confidence_score}%，账户数: {len(self.accounts)}，交易数: {len(self.transactions)}")
            
            return self._build_result()
            
        except Exception as e:
            logger.error(f"解析文件失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': f'解析失败: {str(e)}'
            }
    
    def _calculate_confidence_score(self, accounts: List[Dict], transactions: List[Dict]) -> float:
        """计算置信度分数"""
        score = 0.0
        
        # 基础分数：找到数据
        if accounts:
            score += 20
        if transactions:
            score += 20
        
        # 字段完整性评分
        if accounts:
            account = accounts[0]
            if account.get('holder_name'):
                score += 15
            if account.get('account_number') or account.get('card_number'):
                score += 10
        
        # 交易数据质量评分
        if transactions:
            valid_transactions = 0
            for trans in transactions[:10]:  # 检查前10条
                if trans.get('transaction_date') and trans.get('amount'):
                    valid_transactions += 1
            
            if valid_transactions >= 5:
                score += 15
            elif valid_transactions >= 2:
                score += 10
            else:
                score += 5
        
        return min(score, 100.0)
    
    def _build_result(self) -> Dict[str, Any]:
        """构建返回结果"""
        confidence_score = self._calculate_confidence_score(self.accounts, self.transactions)
        
        return {
            'success': True,
            'accounts': self.accounts,
            'transactions': self.transactions,
            'metadata': {
                'total_accounts': len(self.accounts),
                'total_transactions': len(self.transactions),
                'confidence_score': confidence_score,
                'parser_name': '建设银行格式1解析器',
                'file_path': self.file_path,
                'parse_time': datetime.now().isoformat()
            },
            'data': {
                'accounts': self.accounts,
                'transactions': self.transactions
            }
        }
    
    def extract_sample(self, limit: int = 10) -> Dict[str, Any]:
        """
        提取样本数据进行快速评估
        
        Args:
            limit: 限制返回的交易数量
            
        Returns:
            Dict[str, Any]: 样本数据
        """
        try:
            logger.info(f"建设银行解析器开始提取样本数据，限制条数: {limit}")
            
            # 执行完整解析
            result = self.parse()
            
            if not result.get('success'):
                logger.warning(f"解析失败，无法提取样本: {result.get('error')}")
                return {}
            
            # 获取样本数据
            accounts = result.get('accounts', [])
            all_transactions = result.get('transactions', [])
            
            # 限制交易数量
            sample_transactions = all_transactions[:limit] if all_transactions else []
            
            logger.info(f"成功提取样本数据: {len(accounts)}个账户, {len(sample_transactions)}条交易")
            
            return {
                'accounts': accounts,
                'transactions': sample_transactions,
                'metadata': {
                    'total_accounts': len(accounts),
                    'total_transactions': len(all_transactions),
                    'sample_size': len(sample_transactions)
                }
            }
            
        except Exception as e:
            logger.error(f"提取样本数据失败: {str(e)}")
            return {} 