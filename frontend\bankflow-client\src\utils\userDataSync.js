/**
 * 用户数据同步工具
 * 解决不同浏览器和启动方式下的用户数据隔离问题
 */

/**
 * 默认用户配置
 */
export const DEFAULT_USERS = [
  {
    username: '樊迪',
    password: '123456',
    securityQuestion: '您的工号是？',
    securityAnswer: 'fandy',
    registerTime: new Date().toISOString(),
    isAdmin: false
  },
  {
    username: 'admin',
    password: 'lgmis2012%',
    securityQuestion: '管理员安全问题',
    securityAnswer: 'admin',
    registerTime: new Date().toISOString(),
    isAdmin: true
  },
  {
    username: '张三',
    password: '123456',
    securityQuestion: '您的姓名是？',
    securityAnswer: '张三',
    registerTime: new Date().toISOString(),
    isAdmin: false
  },
  {
    username: '李四',
    password: '123456',
    securityQuestion: '您的姓名是？',
    securityAnswer: '李四',
    registerTime: new Date().toISOString(),
    isAdmin: false
  }
];

/**
 * 初始化用户数据
 * 确保所有默认用户都存在于localStorage中
 */
export const initializeUserData = () => {
  console.log('🔧 开始初始化用户数据...');
  
  try {
    const existingUsers = JSON.parse(localStorage.getItem('registeredUsers') || '[]');
    let hasChanges = false;
    
    // 检查并添加缺失的默认用户
    DEFAULT_USERS.forEach(defaultUser => {
      const exists = existingUsers.find(u => u.username === defaultUser.username);
      if (!exists) {
        existingUsers.push(defaultUser);
        hasChanges = true;
        console.log(`✅ 添加默认用户: ${defaultUser.username}`);
      }
    });

    if (hasChanges) {
      localStorage.setItem('registeredUsers', JSON.stringify(existingUsers));
      console.log('💾 用户数据已更新到localStorage');
    }

    console.log('📊 当前用户列表:', existingUsers.map(u => ({
      用户名: u.username,
      密码: u.password,
      是否管理员: u.isAdmin
    })));

    return existingUsers;
  } catch (error) {
    console.error('❌ 初始化用户数据失败:', error);
    // 如果出错，直接设置默认用户
    localStorage.setItem('registeredUsers', JSON.stringify(DEFAULT_USERS));
    return DEFAULT_USERS;
  }
};

/**
 * 验证用户登录
 * @param {string} username - 用户名
 * @param {string} password - 密码
 * @returns {Object|null} 用户信息或null
 */
export const validateUser = (username, password) => {
  console.log(`🔍 验证用户登录: ${username}`);
  
  // 确保用户数据已初始化
  const users = initializeUserData();
  
  const user = users.find(u => u.username === username && u.password === password);
  
  if (user) {
    console.log(`✅ 用户验证成功: ${username}`);
    return {
      username: user.username,
      loginTime: new Date().toISOString(),
      isAdmin: user.isAdmin || false
    };
  } else {
    console.log(`❌ 用户验证失败: ${username}`);
    console.log('📋 可用用户列表:', users.map(u => `${u.username}:${u.password}`));
    return null;
  }
};

/**
 * 获取用户数据摘要
 * 用于调试和诊断
 */
export const getUserDataSummary = () => {
  const users = JSON.parse(localStorage.getItem('registeredUsers') || '[]');
  const currentUser = localStorage.getItem('currentUser');
  const isLoggedIn = localStorage.getItem('isLoggedIn');
  
  return {
    浏览器信息: {
      userAgent: navigator.userAgent,
      当前域名: window.location.origin,
      当前路径: window.location.pathname
    },
    用户数据: {
      注册用户数量: users.length,
      当前登录用户: currentUser,
      登录状态: isLoggedIn,
      用户列表: users.map(u => ({
        用户名: u.username,
        密码: u.password.substring(0, 3) + '***', // 隐藏部分密码
        是否管理员: u.isAdmin
      }))
    },
    localStorage键: Object.keys(localStorage).filter(key => 
      key.includes('user') || key.includes('login') || key.includes('registered')
    )
  };
};

/**
 * 清理并重置用户数据
 * 用于解决数据损坏问题
 */
export const resetUserData = () => {
  console.log('🔄 重置用户数据...');
  
  // 清除相关的localStorage数据
  const keysToRemove = [
    'registeredUsers',
    'currentUser', 
    'isLoggedIn',
    'userInfo',
    'rememberedUsername'
  ];
  
  keysToRemove.forEach(key => {
    localStorage.removeItem(key);
  });
  
  // 重新初始化
  const users = initializeUserData();
  
  console.log('✅ 用户数据重置完成');
  return users;
};

/**
 * 导出用户数据（用于跨浏览器同步）
 */
export const exportUserData = () => {
  const data = {
    registeredUsers: JSON.parse(localStorage.getItem('registeredUsers') || '[]'),
    exportTime: new Date().toISOString(),
    browser: navigator.userAgent,
    origin: window.location.origin
  };
  
  const dataStr = JSON.stringify(data, null, 2);
  console.log('📤 导出用户数据:', dataStr);
  
  // 创建下载链接
  const blob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `用户数据_${new Date().toISOString().split('T')[0]}.json`;
  a.click();
  URL.revokeObjectURL(url);
  
  return data;
};

/**
 * 导入用户数据（用于跨浏览器同步）
 */
export const importUserData = (jsonData) => {
  try {
    const data = typeof jsonData === 'string' ? JSON.parse(jsonData) : jsonData;
    
    if (data.registeredUsers && Array.isArray(data.registeredUsers)) {
      localStorage.setItem('registeredUsers', JSON.stringify(data.registeredUsers));
      console.log('✅ 用户数据导入成功');
      return true;
    } else {
      console.error('❌ 无效的用户数据格式');
      return false;
    }
  } catch (error) {
    console.error('❌ 导入用户数据失败:', error);
    return false;
  }
};

// 页面加载时自动初始化
if (typeof window !== 'undefined') {
  // 延迟初始化，避免影响页面加载
  setTimeout(() => {
    initializeUserData();
  }, 100);
} 