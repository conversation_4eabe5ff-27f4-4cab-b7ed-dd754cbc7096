"""
统一配置管理系统
整合所有分散的配置文件，提供统一的配置接口
"""
import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from .settings import settings

logger = logging.getLogger(__name__)

@dataclass
class ParserConfig:
    """解析器配置"""
    enabled: bool = True
    priority: int = 10
    timeout: int = 60
    memory_limit: str = "512MB"
    retry_count: int = 3
    confidence_threshold: float = 0.7
    max_file_size: str = "100MB"
    supported_extensions: List[str] = None
    
    def __post_init__(self):
        if self.supported_extensions is None:
            self.supported_extensions = [".xlsx", ".xls"]

@dataclass
class BankParserConfig:
    """银行解析器配置"""
    bank_name: str
    bank_code: str
    parser_configs: Dict[str, ParserConfig]
    field_mapping: Dict[str, str]
    
@dataclass
class SystemConfig:
    """系统配置"""
    # 服务器配置
    server_host: str = "127.0.0.1"
    server_port: int = 8000
    debug: bool = False
    
    # 数据库配置
    database_url: str = "duckdb://data/bankflow.duckdb"
    
    # 文件配置
    upload_dir: str = "./uploads"
    max_file_size: int = 100 * 1024 * 1024  # 100MB
    
    # 插件配置
    plugins_dir: str = "./backend/app/services/parser_plugin_system/plugins"
    plugin_timeout: int = 300
    health_check_interval: int = 30
    
    # 日志配置
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

class UnifiedConfigManager:
    """统一配置管理器"""
    
    def __init__(self):
        self.config_dir = Path(__file__).parent.parent.parent / "config"
        self.config_dir.mkdir(exist_ok=True)
        
        self.system_config = self._load_system_config()
        self.bank_configs = self._load_bank_configs()
        self.plugin_configs = self._load_plugin_configs()
        
        logger.info("✅ 统一配置管理器初始化完成")
    
    def _load_system_config(self) -> SystemConfig:
        """加载系统配置"""
        try:
            config_file = self.config_dir / "system.json"
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                return SystemConfig(**data)
            else:
                # 使用默认配置
                config = SystemConfig()
                # 从settings覆盖配置
                config.server_host = settings.server_host
                config.server_port = settings.server_port
                config.debug = settings.debug
                config.database_url = settings.database_url
                config.upload_dir = settings.upload_dir
                config.max_file_size = settings.max_file_size
                
                self._save_system_config(config)
                return config
        except Exception as e:
            logger.error(f"加载系统配置失败: {e}")
            return SystemConfig()
    
    def _save_system_config(self, config: SystemConfig):
        """保存系统配置"""
        try:
            config_file = self.config_dir / "system.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(config), f, indent=2, ensure_ascii=False)
            logger.info("✅ 系统配置保存成功")
        except Exception as e:
            logger.error(f"保存系统配置失败: {e}")
    
    def _load_bank_configs(self) -> Dict[str, BankParserConfig]:
        """加载银行配置"""
        bank_configs = {}
        
        # 预定义银行配置
        default_banks = {
            "ICBC": {
                "bank_name": "中国工商银行",
                "bank_code": "ICBC",
                "parser_configs": {
                    "icbc_format1_plugin": ParserConfig(
                        priority=10,
                        confidence_threshold=0.8,
                        supported_extensions=[".xlsx", ".xls"]
                    ),
                    "icbc_format3_plugin": ParserConfig(
                        priority=8,
                        confidence_threshold=0.8
                    ),
                    "icbc_format4_plugin": ParserConfig(
                        priority=9,
                        confidence_threshold=0.8,
                        supported_extensions=[".xlsx"]
                    )
                },
                "field_mapping": {
                    "date_field": "交易日期",
                    "time_field": "交易时间",
                    "amount_field": "交易金额",
                    "balance_field": "账户余额",
                    "summary_field": "摘要",
                    "cardholder_field": "持卡人姓名",
                    "account_field": "账号",
                    "card_field": "卡号"
                }
            },
            "CCB": {
                "bank_name": "中国建设银行",
                "bank_code": "CCB",
                "parser_configs": {
                    "ccb_format1_plugin": ParserConfig(
                        priority=20,
                        confidence_threshold=0.7
                    ),
                    "ccb_format2_plugin": ParserConfig(
                        priority=20,
                        confidence_threshold=0.7,
                        timeout=120
                    )
                },
                "field_mapping": {
                    "date_field": "交易日期",
                    "time_field": "交易时间", 
                    "amount_field": "交易金额",
                    "balance_field": "账户余额",
                    "summary_field": "摘要",
                    "cardholder_field": "客户名称",
                    "direction_field": "借贷方向",
                    "account_field": "账号"
                }
            },
            "BEIBUWAN": {
                "bank_name": "北部湾银行",
                "bank_code": "BEIBUWAN",
                "parser_configs": {
                    "beibuwan_format1_plugin": ParserConfig(
                        priority=10,
                        confidence_threshold=0.8,
                        max_file_size="50MB"
                    ),
                    "beibuwan_format2_plugin": ParserConfig(
                        priority=10,
                        confidence_threshold=0.8,
                        max_file_size="50MB"
                    )
                },
                "field_mapping": {
                    "date_field": "日期",
                    "amount_field": "发生额",
                    "balance_field": "余额",
                    "counterparty_field": "对方户名",
                    "summary_field": "摘要"
                }
            }
        }
        
        try:
            config_file = self.config_dir / "banks.json"
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for bank_code, bank_data in data.items():
                    # 转换ParserConfig对象
                    parser_configs = {}
                    for parser_name, parser_data in bank_data.get("parser_configs", {}).items():
                        parser_configs[parser_name] = ParserConfig(**parser_data)
                    
                    bank_configs[bank_code] = BankParserConfig(
                        bank_name=bank_data["bank_name"],
                        bank_code=bank_data["bank_code"],
                        parser_configs=parser_configs,
                        field_mapping=bank_data.get("field_mapping", {})
                    )
            else:
                # 使用默认配置并保存
                for bank_code, bank_data in default_banks.items():
                    parser_configs = {}
                    for parser_name, parser_config in bank_data["parser_configs"].items():
                        parser_configs[parser_name] = parser_config
                    
                    bank_configs[bank_code] = BankParserConfig(
                        bank_name=bank_data["bank_name"],
                        bank_code=bank_data["bank_code"],
                        parser_configs=parser_configs,
                        field_mapping=bank_data["field_mapping"]
                    )
                
                self._save_bank_configs(bank_configs)
                
        except Exception as e:
            logger.error(f"加载银行配置失败: {e}")
        
        return bank_configs
    
    def _save_bank_configs(self, bank_configs: Dict[str, BankParserConfig]):
        """保存银行配置"""
        try:
            config_file = self.config_dir / "banks.json"
            data = {}
            
            for bank_code, bank_config in bank_configs.items():
                parser_configs = {}
                for parser_name, parser_config in bank_config.parser_configs.items():
                    parser_configs[parser_name] = asdict(parser_config)
                
                data[bank_code] = {
                    "bank_name": bank_config.bank_name,
                    "bank_code": bank_config.bank_code,
                    "parser_configs": parser_configs,
                    "field_mapping": bank_config.field_mapping
                }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info("✅ 银行配置保存成功")
        except Exception as e:
            logger.error(f"保存银行配置失败: {e}")
    
    def _load_plugin_configs(self) -> Dict[str, Dict[str, Any]]:
        """加载插件配置（从现有的config.json文件）"""
        plugin_configs = {}
        
        try:
            plugins_dir = Path(settings.upload_dir).parent / "backend/app/services/parser_plugin_system/plugins"
            
            for plugin_dir in plugins_dir.iterdir():
                if plugin_dir.is_dir():
                    config_file = plugin_dir / "config.json"
                    if config_file.exists():
                        with open(config_file, 'r', encoding='utf-8') as f:
                            plugin_configs[plugin_dir.name] = json.load(f)
                            
        except Exception as e:
            logger.error(f"加载插件配置失败: {e}")
        
        return plugin_configs
    
    def get_parser_config(self, parser_name: str) -> Optional[ParserConfig]:
        """获取解析器配置"""
        for bank_config in self.bank_configs.values():
            if parser_name in bank_config.parser_configs:
                return bank_config.parser_configs[parser_name]
        return None
    
    def get_bank_config(self, bank_code: str) -> Optional[BankParserConfig]:
        """获取银行配置"""
        return self.bank_configs.get(bank_code)
    
    def get_field_mapping(self, bank_code: str) -> Dict[str, str]:
        """获取字段映射"""
        bank_config = self.get_bank_config(bank_code)
        return bank_config.field_mapping if bank_config else {}
    
    def get_all_parsers(self) -> List[Dict[str, Any]]:
        """获取所有解析器配置"""
        parsers = []
        
        for bank_config in self.bank_configs.values():
            for parser_name, parser_config in bank_config.parser_configs.items():
                parsers.append({
                    "parser_name": parser_name,
                    "bank_name": bank_config.bank_name,
                    "bank_code": bank_config.bank_code,
                    "config": asdict(parser_config)
                })
        
        return parsers
    
    def update_parser_config(self, parser_name: str, config_updates: Dict[str, Any]) -> bool:
        """更新解析器配置"""
        try:
            for bank_config in self.bank_configs.values():
                if parser_name in bank_config.parser_configs:
                    parser_config = bank_config.parser_configs[parser_name]
                    
                    # 更新配置
                    for key, value in config_updates.items():
                        if hasattr(parser_config, key):
                            setattr(parser_config, key, value)
                    
                    # 保存配置
                    self._save_bank_configs(self.bank_configs)
                    
                    logger.info(f"✅ 解析器 {parser_name} 配置更新成功")
                    return True
            
            logger.warning(f"⚠️ 解析器 {parser_name} 不存在")
            return False
            
        except Exception as e:
            logger.error(f"更新解析器配置失败: {e}")
            return False

# 创建全局配置管理器实例
config_manager = UnifiedConfigManager() 