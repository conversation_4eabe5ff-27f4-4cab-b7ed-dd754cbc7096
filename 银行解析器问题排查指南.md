# 银行解析器问题排查指南

## 🚨 问题分类与快速诊断

### 1. 字段映射问题

#### 症状识别
- 卡号字段显示账户账号
- 账号字段显示客户账号
- 字段内容颠倒或错误

#### 快速诊断
```bash
# 检查后端日志
grep "客户账号\|账户账号" backend/logs/parser.log

# 查看字段映射逻辑
grep -n "card_number\|account_number" backend/app/services/parser_plugin_system/plugins/[银行]_plugin/plugin.py
```

#### 解决步骤
1. **检查表头解析逻辑**
   ```python
   # 确认字段映射
   if "客户账号" in row_text:
       header_info["card_number"] = extract_value()  # 卡号
   if "账户账号" in row_text:
       header_info["account_number"] = extract_value()  # 账号
   ```

2. **验证字段赋值**
   ```python
   # 检查交易数据中的字段赋值
   transaction['card_number'] = header_info['card_number']
   transaction['account_number'] = header_info['account_number']
   ```

3. **重启服务并重新测试**

### 2. 统计数据问题

#### 症状识别
- 收入总额显示¥0.00
- 支出总额显示¥0.00
- 时间范围显示"未知"
- 净流水计算错误

#### 快速诊断
```bash
# 检查统计计算日志
grep "收入\|支出\|统计" backend/logs/parser.log

# 查看统计计算逻辑
grep -n "total_income\|total_expense" backend/app/services/parser_plugin_system/plugins/[银行]_plugin/plugin.py
```

#### 解决步骤
1. **检查金额解析**
   ```python
   # 确保金额正确解析为数值
   amount = float(amount_str.replace('¥', '').replace(',', ''))
   ```

2. **检查收支判断**
   ```python
   # 正确判断收支
   if transaction_type in ['存款', '转入'] or amount > 0:
       income_expense = '收入'
       total_income += abs(amount)
   else:
       income_expense = '支出'
       total_expense += abs(amount)
   ```

3. **检查时间范围计算**
   ```python
   # 正确计算时间范围
   dates = [t['transaction_date'] for t in transactions]
   start_date = min(dates)
   end_date = max(dates)
   date_range = f"{start_date} 至 {end_date}"
   ```

### 3. 工作表合并问题

#### 症状识别
- 账户数量少于工作表数量
- 相同卡号的不同工作表被合并
- 交易数据重复或丢失

#### 快速诊断
```bash
# 检查工作表处理日志
grep "工作表\|sheet" backend/logs/parser.log

# 查看账户ID生成逻辑
grep -n "account_id" backend/app/services/parser_plugin_system/plugins/[银行]_plugin/plugin.py
```

#### 解决步骤
1. **检查账户ID生成**
   ```python
   # 确保账户ID包含工作表标识
   account_id = f"{account_number}_{cardholder_name}_{sheet_name}"
   ```

2. **移除合并逻辑**
   ```python
   # 删除或注释掉合并逻辑
   # if account_id in existing_accounts:
   #     merge_accounts()  # 删除这部分
   ```

3. **确保独立处理**
   ```python
   # 每个工作表独立处理
   for sheet_name in workbook.sheet_names:
       account = parse_sheet(sheet_data, sheet_name)
       accounts.append(account)  # 直接添加，不合并
   ```

### 4. 交易明细问题

#### 症状识别
- 收支符号显示"未知"
- 某些字段显示为空或"-"
- 分页功能异常
- 数据显示不完整

#### 快速诊断
```bash
# 检查交易解析日志
grep "交易\|transaction" backend/logs/parser.log

# 查看字段解析逻辑
grep -n "income_expense\|对方" backend/app/services/parser_plugin_system/plugins/[银行]_plugin/plugin.py
```

#### 解决步骤
1. **检查收支符号逻辑**
   ```python
   # 确保收支符号正确设置
   if amount > 0 or transaction_type in ['存款', '转入']:
       transaction['income_expense'] = '收入'
   else:
       transaction['income_expense'] = '支出'
   ```

2. **检查字段完整性**
   ```python
   # 确保所有必要字段都有值
   transaction = {
       'cardholder_name': header_info.get('cardholder_name', ''),
       'bank_name': header_info.get('bank_name', ''),
       'account_number': header_info.get('account_number', ''),
       'card_number': header_info.get('card_number', ''),
       # ... 其他字段
   }
   ```

## 🔧 调试工具和技巧

### 1. 后端日志分析
```bash
# 实时查看日志
tail -f backend/logs/parser.log

# 搜索特定内容
grep -i "error\|exception" backend/logs/parser.log
grep "解析完成" backend/logs/parser.log

# 查看最近的日志
tail -100 backend/logs/parser.log
```

### 2. 前端调试
```javascript
// 浏览器控制台检查
console.log('解析结果:', parseResult);

// 检查网络请求
// 在Network标签中查看API请求和响应

// 检查元素状态
// 在Elements标签中查看DOM结构
```

### 3. 数据库检查
```sql
-- 检查账户数据
SELECT * FROM accounts WHERE project_id = 'xxx';

-- 检查交易数据
SELECT COUNT(*) FROM transactions WHERE account_id = 'xxx';

-- 检查统计数据
SELECT SUM(amount) FROM transactions WHERE income_expense = '收入';
```

## 📋 问题排查清单

### 步骤1：环境检查
- [ ] 后端服务正常运行（8000端口）
- [ ] 前端服务正常运行（3000端口）
- [ ] 数据库连接正常
- [ ] 解析器插件正确加载

### 步骤2：日志检查
- [ ] 查看后端启动日志
- [ ] 查看解析过程日志
- [ ] 查看错误和异常日志
- [ ] 查看数据库操作日志

### 步骤3：代码检查
- [ ] 检查字段映射逻辑
- [ ] 检查统计计算逻辑
- [ ] 检查工作表处理逻辑
- [ ] 检查数据解析逻辑

### 步骤4：数据检查
- [ ] 检查原始Excel文件
- [ ] 检查解析后的数据结构
- [ ] 检查数据库存储结果
- [ ] 检查前端显示结果

## 🚨 紧急问题处理

### 当解析完全失败时
1. **检查文件格式**：确认Excel文件格式正确
2. **检查解析器匹配**：确认选择了正确的银行和模板
3. **查看错误日志**：定位具体错误原因
4. **回滚到上一个工作版本**：如果是新修改导致的问题

### 当数据部分错误时
1. **定位错误范围**：确定是哪部分数据有问题
2. **对比原始数据**：与Excel文件进行对比
3. **检查相关代码**：重点检查出问题的部分
4. **逐步修复验证**：修复一个问题验证一次

### 当前端显示异常时
1. **检查API响应**：确认后端返回的数据正确
2. **检查前端代码**：确认前端处理逻辑正确
3. **清除缓存**：清除浏览器缓存重新测试
4. **重启前端服务**：如果必要的话

## 💡 预防措施

### 开发阶段
1. **严格按照指南开发**：遵循银行解析器开发通用指南
2. **及时测试验证**：每个功能完成后立即测试
3. **详细记录日志**：添加充分的调试日志
4. **代码审查**：重要逻辑进行代码审查

### 测试阶段
1. **使用标准测试文件**：准备标准的测试用例
2. **完整验证流程**：按照验证清单完整执行
3. **边界情况测试**：测试各种边界情况
4. **性能测试**：测试大文件的处理性能

### 部署阶段
1. **备份原有版本**：部署前备份工作版本
2. **灰度发布**：先在测试环境验证
3. **监控日志**：部署后密切监控日志
4. **快速回滚**：准备快速回滚方案

## 📞 求助指南

### 何时需要求助
- 问题超出个人能力范围
- 涉及系统架构层面的问题
- 需要修改核心框架代码
- 问题影响其他银行解析器

### 求助时需要提供的信息
1. **问题详细描述**：具体的问题现象
2. **复现步骤**：如何重现问题
3. **相关日志**：后端和前端的相关日志
4. **测试文件**：用于测试的Excel文件
5. **代码修改**：最近的代码修改记录

---

**记住：大多数问题都有标准的解决方案，按照这个排查指南逐步进行，通常能够快速定位和解决问题。如果遇到新的问题类型，及时更新这个指南。**
