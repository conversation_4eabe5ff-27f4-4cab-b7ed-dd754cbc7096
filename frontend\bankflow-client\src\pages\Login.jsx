import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Typography, message, Tabs, Modal } from 'antd';
import { UserOutlined, LockOutlined, QuestionCircleOutlined, SafetyOutlined, EyeInvisibleOutlined, EyeTwoTone, MailOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { initializeUserData, validateUser, getUserDataSummary } from '../utils/userDataSync';
import './Login.css';
// 导入本地图片
import logoImage from '../assets/images/new-logo.png';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

/**
 * 现代化登录注册页面组件
 * 支持用户注册、登录、忘记密码功能，银行流水分析系统主题设计
 */
const Login = () => {
  const [loginForm] = Form.useForm();
  const [registerForm] = Form.useForm();
  const [resetForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('login');
  const [isResetModalVisible, setIsResetModalVisible] = useState(false);
  const [resetStep, setResetStep] = useState(1); // 1: 验证问题, 2: 重置密码
  const [resetUsername, setResetUsername] = useState('');
  const navigate = useNavigate();
  const { login } = useAuth();

  useEffect(() => {
    // 页面加载时检查是否有保存的用户名
    const savedUsername = localStorage.getItem('rememberedUsername');
    if (savedUsername) {
      loginForm.setFieldsValue({ username: savedUsername });
    }

    // 初始化用户数据
    initializeUserData();
    
    // 在开发环境下显示用户数据摘要
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 用户数据摘要:', getUserDataSummary());
    }
  }, [loginForm]);

  /**
   * 处理用户注册
   */
  const handleRegister = async (values) => {
    setLoading(true);
    
    try {
      const { username, password, securityQuestion, securityAnswer } = values;
      
      // 检查用户名是否已存在
      const existingUsers = JSON.parse(localStorage.getItem('registeredUsers') || '[]');
      const userExists = existingUsers.find(user => user.username === username);
      
      if (userExists) {
        message.error('用户名已存在，请选择其他用户名');
        return;
      }

      // 保存用户信息
      const newUser = {
        username,
        password,
        securityQuestion,
        securityAnswer,
        registerTime: new Date().toISOString(),
        isAdmin: false
      };
      
      existingUsers.push(newUser);
      localStorage.setItem('registeredUsers', JSON.stringify(existingUsers));

      message.success('注册成功！请使用新账号登录');
      setActiveTab('login');
      loginForm.setFieldsValue({ username });
      registerForm.resetFields();
      
    } catch (error) {
      console.error('注册失败:', error);
      message.error('注册失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 处理用户登录
   */
  const handleLogin = async (values) => {
    setLoading(true);
    
    try {
      const { username, password, remember } = values;
      
      // 验证用户名和密码
      if (!username || !password) {
        message.error('请输入用户名和密码');
        return;
      }

      // 使用统一的用户验证函数
      const userInfo = validateUser(username, password);
      
      if (!userInfo) {
        message.error('用户名或密码错误，请重试');
        console.log('🔍 登录失败，可用用户信息:', getUserDataSummary());
        return;
      }

      // 处理记住用户名
      if (remember) {
        localStorage.setItem('rememberedUsername', username);
      } else {
        localStorage.removeItem('rememberedUsername');
      }

      // 保存登录状态 - 使用多个键确保兼容性
      localStorage.setItem('userInfo', JSON.stringify(userInfo));
      localStorage.setItem('isLoggedIn', 'true');
      localStorage.setItem('currentUser', username); // 添加这个键用于API调用

      // 调用认证上下文的登录方法
      login(userInfo);

      message.success(`登录成功！欢迎 ${username}`);
      
      console.log('✅ 登录成功:', {
        用户名: username,
        登录时间: userInfo.loginTime,
        是否管理员: userInfo.isAdmin,
        浏览器: navigator.userAgent.split(' ')[0],
        域名: window.location.origin
      });
      
      // 跳转到项目列表页面
      navigate('/projects');
      
    } catch (error) {
      console.error('登录失败:', error);
      message.error('登录失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 处理忘记密码
   */
  const handleForgotPassword = () => {
    setIsResetModalVisible(true);
    setResetStep(1);
    resetForm.resetFields();
  };

  /**
   * 验证安全问题
   */
  const handleVerifySecurityQuestion = async (values) => {
    const { username, securityAnswer } = values;
    
    const registeredUsers = JSON.parse(localStorage.getItem('registeredUsers') || '[]');
    const user = registeredUsers.find(u => u.username === username);
    
    if (!user) {
      message.error('用户名不存在');
      return;
    }
    
    if (user.securityAnswer !== securityAnswer) {
      message.error('安全问题答案错误');
      return;
    }
    
    setResetUsername(username);
    setResetStep(2);
    message.success('验证成功，请设置新密码');
  };

  /**
   * 重置密码
   */
  const handleResetPassword = async (values) => {
    const { newPassword } = values;
    
    const registeredUsers = JSON.parse(localStorage.getItem('registeredUsers') || '[]');
    const userIndex = registeredUsers.findIndex(u => u.username === resetUsername);
    
    if (userIndex === -1) {
      message.error('用户不存在');
      return;
    }
    
    registeredUsers[userIndex].password = newPassword;
    localStorage.setItem('registeredUsers', JSON.stringify(registeredUsers));
    
    message.success('密码重置成功！请使用新密码登录');
    setIsResetModalVisible(false);
    setResetStep(1);
    setActiveTab('login');
    loginForm.setFieldsValue({ username: resetUsername });
  };

  /**
   * 获取用户的安全问题
   */
  const getUserSecurityQuestion = (username) => {
    const registeredUsers = JSON.parse(localStorage.getItem('registeredUsers') || '[]');
    const user = registeredUsers.find(u => u.username === username);
    return user ? user.securityQuestion : '';
  };

  return (
    <div className="modern-login-container">
      {/* 动态背景 */}
      <div className="login-background-modern">
        <div className="background-layer layer-1"></div>
        <div className="background-layer layer-2"></div>
        <div className="background-layer layer-3"></div>
        <div className="floating-elements">
          <div className="floating-element element-1"></div>
          <div className="floating-element element-2"></div>
          <div className="floating-element element-3"></div>
          <div className="floating-element element-4"></div>
          <div className="floating-element element-5"></div>
        </div>
      </div>
      
      {/* 主要内容区域 */}
      <div className="login-content-wrapper">
        {/* 左侧品牌区域 */}
        <div className="brand-section">
          <div className="brand-content">
            <div className="brand-logo">
              <div className="logo-icon">
                <img 
                  src={logoImage} 
                  alt="柳州供电局LOGO" 
                  style={{
                    width: '160px',
                    height: '160px',
                    objectFit: 'contain',
                    borderRadius: '20px',
                    boxShadow: '0 16px 40px rgba(0, 0, 0, 0.4)',
                    background: 'rgba(255, 255, 255, 0.2)',
                    padding: '16px',
                    backdropFilter: 'blur(15px)',
                    border: '3px solid rgba(255, 255, 255, 0.3)'
                  }}
                />
              </div>
            </div>
            <div className="brand-text">
              <h1 className="brand-title">银行流水分析系统</h1>
              <div className="brand-description">
                <span className="company-name">中国南方电网有限责任公司·柳州供电局</span>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧登录区域 */}
        <div className="login-section">
          <Card className="modern-login-card" bordered={false}>
            <div className="login-header-modern">
              <Title level={2} className="login-title">欢迎回来</Title>
              <Text className="login-subtitle">请登录您的账户以继续使用系统</Text>
            </div>

            <Tabs 
              activeKey={activeTab} 
              onChange={setActiveTab} 
              centered
              className="modern-tabs"
              items={[
                {
                  key: 'login',
                  label: (
                    <span className="tab-label">
                      <UserOutlined />
                      登录
                    </span>
                  ),
                  children: (
                    <>
                      <Form
                        form={loginForm}
                        name="login"
                        onFinish={handleLogin}
                        autoComplete="off"
                        size="large"
                        className="modern-form"
                      >
                        <Form.Item
                          name="username"
                          rules={[
                            { required: true, message: '请输入用户名' },
                            { min: 2, message: '用户名至少2个字符' }
                          ]}
                        >
                          <Input
                            prefix={<UserOutlined className="input-icon" />}
                            placeholder="请输入用户名"
                            autoComplete="username"
                            className="modern-input"
                          />
                        </Form.Item>
    
                        <Form.Item
                          name="password"
                          rules={[
                            { required: true, message: '请输入密码' }
                          ]}
                        >
                          <Input.Password
                            prefix={<LockOutlined className="input-icon" />}
                            placeholder="请输入密码"
                            autoComplete="current-password"
                            className="modern-input"
                            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                          />
                        </Form.Item>
    
                        <Form.Item>
                          <div className="login-options">
                            <Form.Item name="remember" valuePropName="checked" noStyle>
                              <label className="remember-checkbox">
                                <input type="checkbox" />
                                <span className="checkmark"></span>
                                记住用户名
                              </label>
                            </Form.Item>
                            <Button type="link" onClick={handleForgotPassword} className="forgot-link">
                              忘记密码？
                            </Button>
                          </div>
                        </Form.Item>
    
                        <Form.Item>
                          <Button
                            type="primary"
                            htmlType="submit"
                            loading={loading}
                            block
                            className="modern-login-button"
                          >
                            立即登录
                          </Button>
                        </Form.Item>
                      </Form>
                      {activeTab === 'login' && (
                        <div className="login-footer-modern">
                          <Text type="secondary" className="footer-text">
                            © 2025 中国南方电网有限责任公司 · 柳州供电局
                          </Text>
                        </div>
                      )}
                    </>
                  )
                },
                {
                  key: 'register',
                  label: (
                    <span className="tab-label">
                      <MailOutlined />
                      注册
                    </span>
                  ),
                  children: (
                    <Form
                      form={registerForm}
                      name="register"
                      onFinish={handleRegister}
                      autoComplete="off"
                      size="large"
                      className="modern-form"
                      data-form-type="register"
                    >
                      <Form.Item
                        name="username"
                        rules={[
                          { required: true, message: '请输入用户名' },
                          { min: 2, message: '用户名至少2个字符' },
                          { max: 20, message: '用户名不能超过20个字符' }
                        ]}
                      >
                        <Input
                          prefix={<UserOutlined className="input-icon" />}
                          placeholder="用户名（支持中英文）"
                          className="modern-input"
                        />
                      </Form.Item>
    
                      <Form.Item
                        name="password"
                        rules={[
                          { required: true, message: '请输入密码' },
                          { min: 6, message: '密码至少6个字符' }
                        ]}
                      >
                        <Input.Password
                          prefix={<LockOutlined className="input-icon" />}
                          placeholder="密码（至少6位）"
                          className="modern-input"
                          iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                        />
                      </Form.Item>
    
                      <Form.Item
                        name="confirmPassword"
                        dependencies={['password']}
                        rules={[
                          { required: true, message: '请确认密码' },
                          ({ getFieldValue }) => ({
                            validator(_, value) {
                              if (!value || getFieldValue('password') === value) {
                                return Promise.resolve();
                              }
                              return Promise.reject(new Error('两次输入的密码不一致'));
                            },
                          }),
                        ]}
                      >
                        <Input.Password
                          prefix={<LockOutlined className="input-icon" />}
                          placeholder="确认密码"
                          className="modern-input"
                          iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                        />
                      </Form.Item>
    
                      <Form.Item
                        name="securityQuestion"
                        rules={[
                          { required: true, message: '请输入安全问题' }
                        ]}
                      >
                        <Input
                          prefix={<QuestionCircleOutlined className="input-icon" />}
                          placeholder="安全问题（用于找回密码）"
                          className="modern-input"
                        />
                      </Form.Item>
    
                      <Form.Item
                        name="securityAnswer"
                        rules={[
                          { required: true, message: '请输入安全问题答案' }
                        ]}
                      >
                        <Input
                          prefix={<SafetyOutlined className="input-icon" />}
                          placeholder="安全问题答案"
                          className="modern-input"
                        />
                      </Form.Item>
    
                      <Form.Item style={{ marginBottom: 0 }}>
                        <Button
                          type="primary"
                          htmlType="submit"
                          loading={loading}
                          block
                          className="modern-login-button"
                        >
                          立即注册
                        </Button>
                      </Form.Item>
                    </Form>
                  )
                }
              ]}
            />
          </Card>
        </div>
      </div>

      {/* 忘记密码模态框 */}
      <Modal
        title={
          <div className="modal-header">
            <LockOutlined style={{ marginRight: 8, color: '#0066cc' }} />
            找回密码
          </div>
        }
        open={isResetModalVisible}
        onCancel={() => {
          setIsResetModalVisible(false);
          setResetStep(1);
          resetForm.resetFields();
        }}
        footer={null}
        width={500}
        className="modern-modal"
      >
        {resetStep === 1 ? (
          <Form
            form={resetForm}
            onFinish={handleVerifySecurityQuestion}
            layout="vertical"
            className="modern-form"
          >
            <Form.Item
              name="username"
              label="用户名"
              rules={[{ required: true, message: '请输入用户名' }]}
            >
              <Input 
                placeholder="请输入要重置密码的用户名"
                className="modern-input"
                onChange={(e) => {
                  const question = getUserSecurityQuestion(e.target.value);
                  resetForm.setFieldsValue({ securityQuestion: question });
                }}
              />
            </Form.Item>

            <Form.Item
              name="securityQuestion"
              label="安全问题"
            >
              <Input disabled placeholder="将显示该用户的安全问题" className="modern-input" />
            </Form.Item>

            <Form.Item
              name="securityAnswer"
              label="安全问题答案"
              rules={[{ required: true, message: '请输入安全问题答案' }]}
            >
              <Input placeholder="请输入安全问题的答案" className="modern-input" />
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" block className="modern-login-button">
                验证身份
              </Button>
            </Form.Item>
          </Form>
        ) : (
          <Form
            form={resetForm}
            onFinish={handleResetPassword}
            layout="vertical"
            className="modern-form"
          >
            <Form.Item
              name="newPassword"
              label="新密码"
              rules={[
                { required: true, message: '请输入新密码' },
                { min: 6, message: '密码至少6个字符' }
              ]}
            >
              <Input.Password 
                placeholder="请输入新密码" 
                className="modern-input"
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>

            <Form.Item
              name="confirmNewPassword"
              label="确认新密码"
              dependencies={['newPassword']}
              rules={[
                { required: true, message: '请确认新密码' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('newPassword') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'));
                  },
                }),
              ]}
            >
              <Input.Password 
                placeholder="请再次输入新密码" 
                className="modern-input"
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" block className="modern-login-button">
                重置密码
              </Button>
            </Form.Item>
          </Form>
        )}
      </Modal>
    </div>
  );
};

export default Login; 