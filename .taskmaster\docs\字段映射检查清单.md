# 字段映射检查清单

## 📋 概述

本文档提供了前后端字段映射的标准检查清单，确保解析器开发过程中不遗漏关键字段，避免前端显示异常。

## ✅ 账户信息字段检查清单

### 🔴 必需字段（前端强依赖）

| 字段名 | 数据类型 | 前端用途 | 检查状态 | 备注 |
|--------|----------|----------|----------|------|
| `holder_name` | string | 持卡人姓名显示 | [ ] | ⭐ 核心字段 |
| `account_number` | string | 账号显示 | [ ] | ⭐ 核心字段 |
| `total_inflow` | float | 收入总额显示 | [ ] | ⭐ 核心字段 |
| `total_outflow` | float | 支出总额显示 | [ ] | ⭐ 核心字段 |
| `account_balance` | float | 账户余额显示 | [ ] | ⭐ 核心字段 |
| `transaction_count` | int | 交易笔数显示 | [ ] | ⭐ 核心字段 |

### 🟡 重要字段（影响用户体验）

| 字段名 | 数据类型 | 前端用途 | 检查状态 | 备注 |
|--------|----------|----------|----------|------|
| `bank_name` | string | 银行名称显示 | [ ] | 用户识别 |
| `card_number` | string | 卡号显示 | [ ] | 通常与账号相同 |
| `date_range` | string | 时间范围显示 | [ ] | 格式：YYYY-MM-DD 至 YYYY-MM-DD |
| `start_date` | string | 开始日期 | [ ] | 格式：YYYY-MM-DD |
| `end_date` | string | 结束日期 | [ ] | 格式：YYYY-MM-DD |
| `net_flow` | float | 净流水计算 | [ ] | total_inflow - total_outflow |

### 🟢 可选字段（增强功能）

| 字段名 | 数据类型 | 前端用途 | 检查状态 | 备注 |
|--------|----------|----------|----------|------|
| `account_id` | string | 唯一标识 | [ ] | 系统内部使用 |
| `account_type` | string | 账户类型 | [ ] | 如：个人账户、企业账户 |
| `bank_branch` | string | 开户行 | [ ] | 详细信息 |
| `currency` | string | 币种 | [ ] | 默认：人民币 |
| `sheet_name` | string | 工作表名称 | [ ] | 调试信息 |

### 🔄 兼容性字段（向后兼容）

| 新字段名 | 旧字段名 | 检查状态 | 备注 |
|----------|----------|----------|------|
| `holder_name` | `cardholder_name` | [ ] | 保持兼容性 |
| `total_inflow` | `total_income` | [ ] | 保持兼容性 |
| `total_outflow` | `total_expense` | [ ] | 保持兼容性 |
| `account_balance` | `balance` | [ ] | 保持兼容性 |

## ✅ 交易记录字段检查清单

### 🔴 必需字段（前端强依赖）

| 字段名 | 数据类型 | 前端用途 | 检查状态 | 备注 |
|--------|----------|----------|----------|------|
| `holder_name` | string | 持卡人姓名 | [ ] | ⭐ 核心字段 |
| `bank_name` | string | 银行名称 | [ ] | ⭐ 核心字段 |
| `account_number` | string | 账号 | [ ] | ⭐ 核心字段 |
| `transaction_date` | string | 交易日期 | [ ] | ⭐ 格式：YYYY-MM-DD |
| `transaction_type` | string | 交易方式 | [ ] | ⭐ 如：转账、取款 |
| `amount` | float | 交易金额 | [ ] | ⭐ 核心字段 |
| `balance` | float | 交易余额 | [ ] | ⭐ 核心字段 |
| `direction` | string | 收支符号 | [ ] | ⭐ 值：收/支 |

### 🟡 重要字段（影响用户体验）

| 字段名 | 数据类型 | 前端用途 | 检查状态 | 备注 |
|--------|----------|----------|----------|------|
| `card_number` | string | 卡号显示 | [ ] | 通常与账号相同 |
| `transaction_time` | string | 交易时间 | [ ] | 格式：HH:MM:SS |
| `counterpart_name` | string | 对方户名 | [ ] | 转账信息 |
| `counterpart_account` | string | 对方账号 | [ ] | 转账信息 |
| `counterpart_bank` | string | 对方银行 | [ ] | 转账信息 |

### 🟢 可选字段（增强功能）

| 字段名 | 数据类型 | 前端用途 | 检查状态 | 备注 |
|--------|----------|----------|----------|------|
| `transaction_id` | string | 唯一标识 | [ ] | 系统内部使用 |
| `remark1` | string | 备注1 | [ ] | 详细信息 |
| `remark2` | string | 备注2 | [ ] | 详细信息 |
| `remark3` | string | 备注3 | [ ] | 详细信息 |
| `summary` | string | 摘要 | [ ] | 交易描述 |

### 🔄 兼容性字段（向后兼容）

| 新字段名 | 旧字段名 | 检查状态 | 备注 |
|----------|----------|----------|------|
| `holder_name` | `cardholder_name` | [ ] | 保持兼容性 |
| `transaction_type` | `transaction_method` | [ ] | 保持兼容性 |
| `amount` | `transaction_amount` | [ ] | 保持兼容性 |
| `balance` | `balance_amount` | [ ] | 保持兼容性 |
| `direction` | `income_expense_flag` | [ ] | 保持兼容性 |

## 🔍 数据类型验证清单

### 数值类型验证

| 字段名 | 期望类型 | 验证规则 | 检查状态 |
|--------|----------|----------|----------|
| `total_inflow` | float | >= 0 | [ ] |
| `total_outflow` | float | >= 0 | [ ] |
| `account_balance` | float | 任意数值 | [ ] |
| `net_flow` | float | 任意数值 | [ ] |
| `transaction_count` | int | >= 0 | [ ] |
| `amount` | float | > 0 | [ ] |
| `balance` | float | 任意数值 | [ ] |

### 字符串类型验证

| 字段名 | 验证规则 | 检查状态 |
|--------|----------|----------|
| `holder_name` | 非空字符串 | [ ] |
| `account_number` | 非空字符串 | [ ] |
| `bank_name` | 非空字符串 | [ ] |
| `transaction_date` | 日期格式 YYYY-MM-DD | [ ] |
| `direction` | 值为 "收" 或 "支" | [ ] |

### 日期格式验证

| 字段名 | 格式要求 | 检查状态 |
|--------|----------|----------|
| `transaction_date` | YYYY-MM-DD | [ ] |
| `start_date` | YYYY-MM-DD | [ ] |
| `end_date` | YYYY-MM-DD | [ ] |
| `date_range` | YYYY-MM-DD 至 YYYY-MM-DD | [ ] |
| `transaction_time` | HH:MM:SS（可选） | [ ] |

## 🧪 测试验证清单

### 单元测试验证

- [ ] **字段存在性测试**
  - [ ] 所有必需字段都存在
  - [ ] 字段值不为 None
  - [ ] 字符串字段不为空

- [ ] **数据类型测试**
  - [ ] 数值字段为 float/int 类型
  - [ ] 字符串字段为 str 类型
  - [ ] 日期字段格式正确

- [ ] **数据逻辑测试**
  - [ ] 账户余额 = 最后交易余额
  - [ ] 净流水 = 总收入 - 总支出
  - [ ] 交易数量 = 交易记录条数

### API接口测试

- [ ] **返回结构验证**
  - [ ] parse_result 包含 accounts 和 transactions
  - [ ] accounts 数组不为空
  - [ ] transactions 数组不为空

- [ ] **字段映射验证**
  - [ ] 前端期望字段都存在
  - [ ] 字段值类型正确
  - [ ] 兼容性字段存在

### 前端集成测试

- [ ] **显示验证**
  - [ ] 持卡人姓名正确显示
  - [ ] 账号信息正确显示
  - [ ] 金额格式正确显示
  - [ ] 账户余额不为 ¥0.00

- [ ] **交互验证**
  - [ ] 交易明细正确显示
  - [ ] 分页功能正常
  - [ ] 数据保存功能正常

## 📝 代码实现模板

### 账户信息生成模板

```python
def _generate_account_info(self, header_info: dict, transactions: list, sheet_name: str) -> dict:
    \"\"\"生成标准账户信息\"\"\"
    
    # 计算统计数据
    total_income = sum(t['amount'] for t in transactions if t['direction'] == '收')
    total_expense = sum(t['amount'] for t in transactions if t['direction'] == '支')
    net_flow = total_income - total_expense
    
    # 计算账户余额（取最后一条交易的余额）
    account_balance = 0.0
    if transactions:
        sorted_transactions = sorted(transactions, key=lambda x: x.get('transaction_date', ''))
        last_transaction = sorted_transactions[-1]
        account_balance = last_transaction.get('balance', 0.0)
    
    # 时间范围
    dates = [t['transaction_date'] for t in transactions if t['transaction_date']]
    start_date = min(dates) if dates else "未知"
    end_date = max(dates) if dates else "未知"
    date_range = f"{start_date} 至 {end_date}" if dates else "未知"
    
    # ✅ 标准字段映射
    account = {
        # === 前端期望的字段名（必需） ===
        'holder_name': header_info.get('cardholder_name', ''),      # ⭐
        'account_number': header_info.get('account_number', ''),    # ⭐
        'total_inflow': float(total_income),                        # ⭐
        'total_outflow': float(total_expense),                      # ⭐
        'account_balance': float(account_balance),                  # ⭐
        'transaction_count': len(transactions),                     # ⭐
        
        # === 重要字段 ===
        'bank_name': header_info.get('bank_name', '银行名称'),
        'card_number': header_info.get('card_number', header_info.get('account_number', '')),
        'date_range': date_range,
        'start_date': start_date,
        'end_date': end_date,
        'net_flow': float(net_flow),
        
        # === 可选字段 ===
        'account_id': f"{header_info.get('account_number', '')}_{sheet_name}",
        'account_type': header_info.get('account_type', '个人账户'),
        'bank_branch': header_info.get('bank_branch', ''),
        'currency': '人民币',
        'sheet_name': sheet_name,
        
        # === 保持向后兼容性 ===
        'cardholder_name': header_info.get('cardholder_name', ''),
        'total_income': float(total_income),
        'total_expense': float(total_expense),
        'balance': float(account_balance),
    }
    
    return account
```

### 交易记录生成模板

```python
def _generate_transaction(self, row_data: dict, header_info: dict) -> dict:
    \"\"\"生成标准交易记录\"\"\"
    
    # ✅ 标准字段映射
    transaction = {
        # === 前端期望的字段名（必需） ===
        'holder_name': header_info.get('cardholder_name', ''),      # ⭐
        'bank_name': header_info.get('bank_name', '银行名称'),       # ⭐
        'account_number': header_info.get('account_number', ''),    # ⭐
        'transaction_date': self._safe_parse_date(row_data.get('date')),  # ⭐
        'transaction_type': row_data.get('type', ''),               # ⭐
        'amount': self._safe_parse_amount(row_data.get('amount')),  # ⭐
        'balance': self._safe_parse_amount(row_data.get('balance')), # ⭐
        'direction': row_data.get('direction', ''),                 # ⭐
        
        # === 重要字段 ===
        'card_number': header_info.get('card_number', header_info.get('account_number', '')),
        'transaction_time': row_data.get('time', ''),
        'counterpart_name': row_data.get('counterpart_name', ''),
        'counterpart_account': row_data.get('counterpart_account', ''),
        'counterpart_bank': row_data.get('counterpart_bank', ''),
        
        # === 可选字段 ===
        'transaction_id': f"{header_info.get('account_number', '')}_{row_data.get('date', '')}_{row_data.get('amount', '')}",
        'remark1': row_data.get('remark1', ''),
        'remark2': row_data.get('remark2', ''),
        'remark3': row_data.get('remark3', ''),
        'summary': row_data.get('summary', ''),
        
        # === 保持向后兼容性 ===
        'cardholder_name': header_info.get('cardholder_name', ''),
        'transaction_method': row_data.get('type', ''),
        'transaction_amount': self._safe_parse_amount(row_data.get('amount')),
        'balance_amount': self._safe_parse_amount(row_data.get('balance')),
        'income_expense_flag': row_data.get('direction', ''),
    }
    
    return transaction
```

## 📋 使用说明

### 开发阶段使用

1. **开发前检查**：根据清单确认需要实现的字段
2. **开发中验证**：逐项检查字段是否正确实现
3. **开发后测试**：使用测试清单验证功能

### 代码审查使用

1. **字段完整性**：检查是否遗漏必需字段
2. **数据类型**：验证字段类型是否正确
3. **兼容性**：确认向后兼容字段存在

### 测试验证使用

1. **单元测试**：验证字段存在性和类型
2. **集成测试**：验证前后端数据传递
3. **端到端测试**：验证用户界面显示

---

**更新时间**: 2025-01-04  
**版本**: v1.0  
**维护者**: 开发团队