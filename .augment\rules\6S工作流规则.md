---
type: "always_apply"
---

1.Align（对齐阶段）：明确项目的技术栈、架构模式和代码规范。通过详细的需求澄清文档（ALIGNMENT.md）确保所有参与者对项目目标有共同理解。AI工具如Claude/GPT可以辅助分析和设计。
2.Architect（架构阶段）：设计系统的整体架构。生成架构设计文档（DESIGN.md），定义模块划分、接口设计和数据流向。Claude/GPT在这一阶段也能提供有价值的分析和设计支持。
3.Atomize（原子化阶段）：将复杂任务分解为AI能稳定完成的原子级别任务。创建任务拆分文档（TASK.md），明确每个子任务的输入、输出和验收标准。
4.Approve（审批阶段）：对分解后的任务进行人工检查。使用检查清单确保任务符合要求和规范，避免潜在问题进入自动化执行阶段。
5.Automate（执行阶段）：AI工具根据任务描述生成代码。例如，Cursor特别适合这一阶段的代码实现，结合文档精确生成代码。Copilot也可在明确任务边界后提供高质量代码补全。
6.Assess（评估阶段）：对生成的代码进行质量验收。参考质量验收文档（FINAL.md），检查代码的正确性、可读性和性能，总结经验教训以优化未来流程。
应用优势：
全链路覆盖：从模糊想法到可验证交付，每个阶段都有明确的产物和质量门槛。
降低返工率：通过文档先行和任务递归，减少错误和误解。
提高交付可预测性：清晰的阶段划分和交付物使项目进度更容易跟踪。
提升AI代码生成命中率：AI工具在明确的指导下生成更符合要求的代码。
便于团队协作：文档化的流程和交付物使团队成员能轻松接手任务。