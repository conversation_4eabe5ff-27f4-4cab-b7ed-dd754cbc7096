"""
解析器选择器 - 基于新Enhanced解析器架构
"""
import os
import logging
from typing import Dict, List, Any, Optional, Tuple
from .feature_extractor import BankFileFeatureExtractor

logger = logging.getLogger(__name__)

class ParserSelector:
    """
    解析器选择器 - 负责根据文件特征选择最合适的Enhanced解析器
    """
    
    def __init__(self):
        """
        初始化解析器选择器
        """
        self.feature_extractor = BankFileFeatureExtractor()
        
        # 新Enhanced解析器配置
        self.enhanced_parser_configs = {
            "UniversalParser": {
                "name": "通用解析器",
                "description": "适用于用户手工调整的标准格式",
                "bank_name": "通用解析器",
                "supported_formats": [".xlsx", ".xls"],
                "confidence_threshold": 0.1,
                "priority": 100
            },
            "ICBCFormat1EnhancedParser": {
                "name": "工商银行格式1增强解析器",
                "description": "适用于多工作表个人账户格式",
                "bank_name": "中国工商银行",
                "supported_formats": [".xlsx", ".xls"],
                "confidence_threshold": 0.7,
                "priority": 20
            },
            "ICBCFormat3StandardParser": {
                "name": "工商银行格式3标准解析器", 
                "description": "适用于单工作表个人账户格式",
                "bank_name": "中国工商银行",
                "supported_formats": [".xlsx", ".xls"],
                "confidence_threshold": 0.7,
                "priority": 20
            },
            "ICBCFormat4EnhancedParser": {
                "name": "工商银行格式4增强解析器",
                "description": "适用于企业多账户格式",
                "bank_name": "中国工商银行", 
                "supported_formats": [".xlsx"],
                "confidence_threshold": 0.7,
                "priority": 20
            },
            "CCBFormat1Parser": {
                "name": "建设银行格式1解析器",
                "description": "适用于建设银行标准Excel格式",
                "bank_name": "中国建设银行",
                "supported_formats": [".xlsx", ".xls"],
                "confidence_threshold": 0.5,
                "priority": 20
            },
            "CCBFormat2Parser": {
                "name": "建设银行格式2解析器",
                "description": "适用于建设银行特殊Excel格式",
                "bank_name": "中国建设银行",
                "supported_formats": [".xlsx", ".xls"],
                "confidence_threshold": 0.5,
                "priority": 20
            }
        }
    
    def select_parser(self, file_path: str, bank_name: str = None) -> Dict[str, Any]:
        """
        选择最合适的Enhanced解析器
        
        Args:
            file_path: 文件路径
            bank_name: 银行名称（可选）
            
        Returns:
            Dict: 选择的解析器信息
        """
        try:
            # 提取文件特征
            features = self.feature_extractor.extract_features(file_path)
            
            # 如果未指定银行，使用检测到的银行
            if not bank_name:
                bank_name = features.get("detected_bank", "未知银行")
            
            # 获取文件扩展名
            _, ext = os.path.splitext(file_path.lower())
            
            # 🔧 新逻辑：让所有解析器都参与置信度评估
            return self._select_best_parser(file_path, features, ext, bank_name)
                
        except Exception as e:
            logger.error(f"选择解析器时出错: {str(e)}")
            return {
                "parser_class": None,
                "confidence": 0.0,
                "reason": f"选择器错误: {str(e)}"
            }
    
    def _select_icbc_parser(self, features: Dict[str, Any], file_ext: str) -> Dict[str, Any]:
        """
        为工商银行文件选择最合适的Enhanced解析器
        基于置信评估选择最佳解析器，而不是提前筛选
        
        Args:
            features: 文件特征
            file_ext: 文件扩展名
            
        Returns:
            Dict: 选择的解析器信息
        """
        # 🔧 新逻辑：让所有可能的解析器都参与置信评估
        parser_candidates = []
        
        # 1. 检查Format1解析器适用性
        if self._is_format1_pattern(features):
            parser_candidates.append({
                "parser_class": "ICBCFormat1EnhancedParser",
                "confidence": 0.9,
                "reason": "检测到多工作表个人账户格式特征"
            })
        
        # 3. 检查Format3解析器适用性（通用性强，始终参与评估）
        parser_candidates.append({
            "parser_class": "ICBCFormat3StandardParser",
            "confidence": 0.8,
            "reason": "标准格式3解析器（通用性强）"
        })
        
        # 4. 检查Format4解析器适用性
        if self._is_format4_pattern(features):
            parser_candidates.append({
                "parser_class": "ICBCFormat4EnhancedParser",
                "confidence": 0.9,
                "reason": "检测到企业多账户格式特征"
            })
        
        # 5. 如果没有找到任何候选解析器，使用Format3作为默认
        if not parser_candidates:
            return {
                "parser_class": "ICBCFormat3StandardParser",
                "confidence": 0.6,
                "reason": "使用默认标准格式3解析器（兜底方案）"
            }
        
        # 6. 选择置信度最高的解析器
        best_parser = max(parser_candidates, key=lambda x: x["confidence"])
        
        # 7. 返回选择结果，包含所有候选解析器信息（用于调试）
        result = best_parser.copy()
        result["all_candidates"] = parser_candidates
        result["candidate_count"] = len(parser_candidates)
        
        return result
    
    def _select_ccb_parser(self, features: Dict[str, Any], file_ext: str) -> Dict[str, Any]:
        """
        为建设银行文件选择最合适的解析器
        
        Args:
            features: 文件特征
            file_ext: 文件扩展名
            
        Returns:
            Dict: 选择的解析器信息
        """
        parser_candidates = []
        
        # 1. 检查Format1解析器适用性（标准格式）
        if self._is_ccb_format1_pattern(features):
            parser_candidates.append({
                "parser_class": "CCBFormat1Parser",
                "confidence": 0.9,
                "reason": "检测到建设银行标准Excel格式特征"
            })
        
        # 2. 检查Format2解析器适用性（特殊格式）
        if self._is_ccb_format2_pattern(features):
            parser_candidates.append({
                "parser_class": "CCBFormat2Parser",
                "confidence": 0.8,
                "reason": "检测到建设银行特殊Excel格式特征"
            })
        
        # 3. 如果没有特殊匹配，Format1作为默认（通用性强）
        if not parser_candidates:
            parser_candidates.append({
                "parser_class": "CCBFormat1Parser",
                "confidence": 0.6,
                "reason": "使用建设银行格式1解析器（兜底方案）"
            })
        
        # 4. 选择置信度最高的解析器
        best_parser = max(parser_candidates, key=lambda x: x["confidence"])
        
        # 5. 返回选择结果，包含所有候选解析器信息
        result = best_parser.copy()
        result["all_candidates"] = parser_candidates
        result["candidate_count"] = len(parser_candidates)
        
        return result
    
    def _select_best_parser(self, file_path: str, features: Dict[str, Any], file_ext: str, bank_name: str) -> Dict[str, Any]:
        """
        综合评估所有解析器，选择置信度最高的
        
        Args:
            file_path: 文件路径
            features: 文件特征
            file_ext: 文件扩展名
            bank_name: 银行名称
            
        Returns:
            Dict: 选择的解析器信息
        """
        parser_candidates = []
        
        # 1. 首先评估通用解析器（优先级最高）
        try:
            from .universal_parser import UniversalParser
            universal_confidence = UniversalParser.calculate_confidence(file_path)
            if universal_confidence > 10:  # 只要有基本可能性就参与
                parser_candidates.append({
                    "parser_class": "UniversalParser",
                    "confidence": universal_confidence,
                    "reason": f"通用解析器评估 - 置信度: {universal_confidence:.1f}%",
                    "priority": 100
                })
        except Exception as e:
            logger.warning(f"通用解析器评估失败: {str(e)}")
        
        # 2. 如果是工商银行，评估工商银行解析器
        if bank_name == "中国工商银行":
            icbc_result = self._select_icbc_parser(features, file_ext)
            if icbc_result.get("parser_class"):
                # 添加所有候选解析器
                for candidate in icbc_result.get("all_candidates", [icbc_result]):
                    candidate["priority"] = 20  # 工商银行解析器优先级为20
                    parser_candidates.append(candidate)
        
        # 3. 如果是建设银行，评估建设银行解析器
        elif bank_name == "中国建设银行":
            ccb_result = self._select_ccb_parser(features, file_ext)
            if ccb_result.get("parser_class"):
                # 添加所有候选解析器
                for candidate in ccb_result.get("all_candidates", [ccb_result]):
                    candidate["priority"] = 20  # 建设银行解析器优先级为20
                    parser_candidates.append(candidate)
        
        # 4. 如果没有找到任何候选解析器，使用通用解析器作为兜底
        if not parser_candidates:
            parser_candidates.append({
                "parser_class": "UniversalParser",
                "confidence": 10.0,
                "reason": "兜底方案 - 使用通用解析器",
                "priority": 100
            })
        
        # 5. 按优先级和置信度排序选择最佳解析器
        # 首先按优先级排序（数字越大优先级越高），然后按置信度排序
        parser_candidates.sort(key=lambda x: (x.get("priority", 0), x["confidence"]), reverse=True)
        
        best_parser = parser_candidates[0]
        
        # 6. 返回选择结果
        result = best_parser.copy()
        result["all_candidates"] = parser_candidates
        result["candidate_count"] = len(parser_candidates)
        result["selection_strategy"] = "priority_and_confidence"
        
        logger.info(f"选择解析器: {result['parser_class']} (置信度: {result['confidence']:.1f}%, 优先级: {result.get('priority', 0)})")
        
        return result
    
    def _is_format4_pattern(self, features: Dict[str, Any]) -> bool:
        """检测是否为Format4（企业多账户）格式"""
        # Format4特征：企业名称、多个账户、企业格式关键词
        keywords = features.get("keywords", [])
        entity_names = features.get("entity_names", [])
        sheet_count = features.get("sheet_count", 1)
        account_type = features.get("account_type", "")
        
        # 🔧 首先检查是否明确识别为个人账户
        if account_type == "个人账户":
            return False  # 个人账户不使用Format4解析器
        
        # 检查是否包含企业关键词
        enterprise_keywords = ["有限公司", "股份有限公司", "集团", "企业", "公司"]
        has_enterprise = any(keyword in " ".join(entity_names) for keyword in enterprise_keywords)
        
        # 检查是否有企业格式的特征
        has_enterprise_format = any(keyword in keywords for keyword in ["企业", "公司账户", "法人"])
        
        # 🔧 检查工作表分析，看是否真的包含多个企业账户
        sheet_analysis = features.get("sheet_analysis", [])
        enterprise_sheets = 0
        for sheet_info in sheet_analysis:
            sheet_keywords = sheet_info.get("account_type_keywords", [])
            if "对公账户" in sheet_keywords or "企业" in " ".join(sheet_info.get("keywords", [])):
                enterprise_sheets += 1
        
        # 🔧 增加更严格的条件：需要同时满足多个企业特征
        return (has_enterprise or has_enterprise_format) and enterprise_sheets >= 1
    
    def _is_format1_pattern(self, features: Dict[str, Any]) -> bool:
        """检测是否为Format1（多工作表个人）格式"""
        # Format1特征：多个工作表、个人姓名、特定的表头格式
        sheet_count = features.get("sheet_count", 1)
        entity_names = features.get("entity_names", [])
        
        # 检查是否为多工作表
        is_multi_sheet = sheet_count > 1
        
        # 检查是否包含个人姓名（中文姓名模式）
        has_personal_names = any(
            len(name) >= 2 and len(name) <= 4 and all('\u4e00' <= char <= '\u9fff' for char in name)
            for name in entity_names if name
        )
        
        return is_multi_sheet and has_personal_names
    
    def _is_ccb_format1_pattern(self, features: Dict[str, Any]) -> bool:
        """
        检查是否为建设银行格式1模式（标准Excel格式）
        
        Args:
            features: 文件特征
            
        Returns:
            bool: 是否匹配格式1模式
        """
        try:
            # 检查银行相关特征
            bank_keywords = features.get("bank_keywords", [])
            header_keywords = features.get("header_keywords", [])
            
            # 建设银行标识
            ccb_indicators = ["建设银行", "建行", "CCB"]
            has_ccb_indicator = any(indicator in ' '.join(bank_keywords) for indicator in ccb_indicators)
            
            # 标准Excel格式特征
            standard_headers = ["交易日期", "摘要", "支出", "收入", "余额"]
            header_matches = sum(1 for header in standard_headers if header in ' '.join(header_keywords))
            
            # 工作表数量（格式1通常是单工作表或少量工作表）
            sheet_count = features.get("sheet_count", 1)
            
            return has_ccb_indicator and header_matches >= 3 and sheet_count <= 3
            
        except Exception as e:
            logger.warning(f"检查建设银行格式1模式失败: {str(e)}")
            return False
    
    def _is_ccb_format2_pattern(self, features: Dict[str, Any]) -> bool:
        """
        检查是否为建设银行格式2模式（特殊Excel格式）
        
        Args:
            features: 文件特征
            
        Returns:
            bool: 是否匹配格式2模式
        """
        try:
            # 检查银行相关特征
            bank_keywords = features.get("bank_keywords", [])
            header_keywords = features.get("header_keywords", [])
            
            # 建设银行标识
            ccb_indicators = ["建设银行", "建行", "CCB"]
            has_ccb_indicator = any(indicator in ' '.join(bank_keywords) for indicator in ccb_indicators)
            
            # 特殊格式特征（可能有复杂表头或多个数据段）
            complex_headers = ["记账日期", "发生日期", "交易金额", "发生额", "对手方"]
            header_matches = sum(1 for header in complex_headers if header in ' '.join(header_keywords))
            
            # 数据行数量（格式2可能包含更多数据）
            data_rows = features.get("data_rows", 0)
            
            return has_ccb_indicator and (header_matches >= 2 or data_rows > 500)
            
        except Exception as e:
            logger.warning(f"检查建设银行格式2模式失败: {str(e)}")
            return False
    
    def get_validation_rules(self, bank_name: str, parser_class: str) -> Dict[str, Any]:
        """
        获取指定银行和解析器的验证规则
        
        Args:
            bank_name: 银行名称
            parser_class: 解析器类名
            
        Returns:
            Dict: 验证规则
        """
        if bank_name == "中国工商银行":
            return {
                "min_transactions": 1,
                "required_fields": ["account_number", "transaction_datetime", "amount"],
                "amount_precision": 2
            }
        else:
            return {}
    
    def get_supported_banks(self) -> List[str]:
        """
        获取支持的银行列表
        
        Returns:
            List[str]: 支持的银行名称列表
        """
        return ["中国工商银行", "中国建设银行"]
    
    def get_enhanced_parsers(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有Enhanced解析器配置
        
        Returns:
            Dict: Enhanced解析器配置
        """
        return self.enhanced_parser_configs 