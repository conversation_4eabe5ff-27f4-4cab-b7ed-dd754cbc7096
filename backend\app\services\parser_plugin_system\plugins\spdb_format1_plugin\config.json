{"plugin_id": "spdb_format1_plugin", "plugin_name": "上海浦东发展银行Format1解析器插件", "version": "1.0.0", "description": "解析上海浦东发展银行流水文件，支持XLS和XLSX格式，自动适应字段名差异", "bank_name": "上海浦东发展银行", "supported_formats": ["XLS", "XLSX"], "supported_file_extensions": [".xls", ".xlsx"], "confidence_threshold": 0.8, "priority": 10, "enabled": true, "config": {"timeout": 60, "memory_limit": "512MB", "retry_count": 3, "debug": false}, "field_mapping": {"cardholder_name": ["账户中文名", "户名"], "card_number": "", "account_number": "账号", "dr_cr_flag_mapping": {"0": "支出", "1": "收入"}, "transaction_method": ["摘要代码", "摘要"], "remark1": "附言", "counterpart_bank": "对方行名"}, "detection_keywords": ["浦发银行", "上海浦东发展银行", "SPDB", "账户中文名", "户名", "账号", "借贷标志", "摘要代码", "摘要", "附言", "对方行名"]}