{"name": "ccb_format2_plugin", "version": "1.0.0", "description": "建设银行Format2解析器插件，支持建设银行多表头结构Excel格式的银行流水解析", "author": "银行流水系统团队", "license": "MIT", "homepage": "https://github.com/your-org/bank-parser", "supported_formats": ["Excel (.xlsx)", "Excel (.xls)"], "supported_banks": ["中国建设银行"], "dependencies": ["pandas>=1.3.0", "openpyxl>=3.0.0", "xlrd>=2.0.0", "numpy>=1.21.0"], "entry_point": "plugin.Plugin", "confidence_threshold": 0.7, "keywords": ["建设银行", "银行流水", "解析器", "插件", "Format2", "多表头"], "format_features": {"multi_sheet": true, "multi_header": true, "time_format": "YYYY-MM-DD HH:MM:SS", "amount_format": "debit_credit_separate", "header_info": true, "customer_blocks": true, "transaction_sheet": "个人活期明细信息-新一代"}, "changelog": {"1.0.0": "初始版本，支持建设银行多表头结构Excel格式解析，包含多客户块处理、借贷方向分离、表头识别"}}