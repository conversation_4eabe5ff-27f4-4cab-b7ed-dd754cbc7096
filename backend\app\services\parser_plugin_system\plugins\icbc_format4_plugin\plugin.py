"""
工商银行Format4插件 - 基于完美解析器
专门处理4.xlsx文件的企业多账户结构
"""
import pandas as pd
import logging
from typing import Dict, List, Any, Tuple
from datetime import datetime
import re
import os
import sys
from pathlib import Path

# 修复导入 - 使用正确的相对路径
try:
    # 首先导入BasePlugin
    from ..core.plugin_interface import BasePlugin
    
    # 导入解析器 - 使用正确的相对于backend/app的路径
    from ...parser.icbc_format4_enhanced import ICBCFormat4EnhancedParser
except ImportError as e1:
    # 备用导入方案
    try:
        # 添加backend/app到sys.path
        current_dir = Path(__file__).resolve().parent
        backend_app_path = current_dir.parent.parent.parent.parent
        if str(backend_app_path) not in sys.path:
            sys.path.insert(0, str(backend_app_path))
        
        from services.parser_plugin_system.core.plugin_interface import BasePlugin
        from services.parser.icbc_format4_enhanced import ICBCFormat4EnhancedParser
    except ImportError as e2:
        print(f"❌ 插件导入失败: {e1}, {e2}")
        raise ImportError(f"无法导入必要模块: {e1}")

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Plugin(BasePlugin):
    """工商银行Format4插件"""
    
    def __init__(self, file_path: str = None):
        super().__init__()
        self.name = "icbc_format4_plugin"
        self.version = "1.0.0"
        self.description = "工商银行Format4增强解析器插件，专门用于解析4.xlsx等企业多账户流水文件"
        self.supported_formats = [".xlsx"]
        self.bank_name = "中国工商银行"
        self.file_path = file_path
        
    def validate_file(self, file_path: str) -> bool:
        """验证文件格式"""
        try:
            if not os.path.exists(file_path):
                return False
                
            # 检查文件扩展名
            if not file_path.lower().endswith('.xlsx'):
                return False
                
            # 尝试读取文件
            with pd.ExcelFile(file_path) as xls:
                sheet_names = xls.sheet_names
                
                # Format4特征：包含客户信息、开户信息等企业工作表
                has_customer_info = '客户信息' in sheet_names
                has_account_info = '开户信息' in sheet_names
                
                # 检查是否有企业交易工作表
                enterprise_sheets = [name for name in sheet_names 
                                   if name not in ['客户信息', '开户信息']]
                
                # Format4验证：至少有企业信息表和交易表
                if (has_customer_info or has_account_info) and enterprise_sheets:
                    # 进一步验证企业交易表格式
                    for sheet_name in enterprise_sheets[:1]:  # 只检查第一个
                        df = pd.read_excel(xls, sheet_name=sheet_name, header=None, nrows=5)
                        
                        # 检查是否包含企业格式特征
                        text_content = ""
                        for _, row in df.iterrows():
                            text_content += " ".join([str(cell) for cell in row if pd.notna(cell)])
                        
                        # 企业格式特征检查
                        if any(keyword in text_content for keyword in ["帐号", "工作日期", "借贷标志", "发生额", "余额"]):
                            return True
                            
            return False
            
        except Exception as e:
            logger.error(f"文件验证失败: {str(e)}")
            return False
    
    def calculate_confidence(self, file_path: str) -> float:
        """计算置信度 - 基于文件内容静态分析，避免解析结果污染"""
        try:
            if not self.validate_file(file_path):
                return 0.0
            
            # 🔧 关键修复：使用静态文件分析而不是实际解析结果
            parser = ICBCFormat4EnhancedParser(file_path)
            
            # 🔧 只进行样本检查，不进行完整解析
            sample_data = parser.extract_sample(limit=5)
            sample_accounts = sample_data.get('accounts', [])
            sample_transactions = sample_data.get('transactions', [])
            
            # 如果样本数据都没有，返回低置信度
            if not sample_accounts and not sample_transactions:
                logger.warning("Format4样本提取失败，置信度为5%")
                return 5.0
            
            # 🔧 基于样本数据计算置信度，不依赖实际解析结果
            confidence_score = 0.0
            
            # 1. 样本数据基础分 (30分)
            if sample_accounts:
                confidence_score += 15.0
            if sample_transactions:
                confidence_score += 15.0
            
            # 2. 账户信息质量检查 (25分)
            if sample_accounts:
                account_quality = 0
                for account in sample_accounts:
                    if account.get('account_number') and len(str(account['account_number'])) >= 10:
                        account_quality += 10
                    if account.get('holder_name') and len(account['holder_name']) >= 2:
                        account_quality += 15
                confidence_score += min(25.0, account_quality / len(sample_accounts))
            
            # 3. 交易数据质量检查 (25分)
            if sample_transactions:
                transaction_quality = 0
                for transaction in sample_transactions[:10]:
                    trans_score = 0
                    if transaction.get('transaction_date'):
                        trans_score += 8
                    if transaction.get('transaction_amount', 0) != 0:
                        trans_score += 12
                    if transaction.get('enterprise_name'):
                        trans_score += 5
                    transaction_quality += trans_score
                
                if len(sample_transactions) > 0:
                    avg_quality = transaction_quality / len(sample_transactions[:10])
                    confidence_score += min(25.0, avg_quality)
            
            # 4. Format4特定特征检查 (20分)
            format4_score = 0
            
            # 检查企业多账户格式特征
            if sample_accounts and len(sample_accounts) > 1:
                format4_score += 10  # 多账户特征
                
            # 检查企业名称字段
            has_enterprise_fields = any(
                transaction.get('enterprise_name') for transaction in sample_transactions[:5]
            )
            if has_enterprise_fields:
                format4_score += 10
            
            confidence_score += format4_score
            
            # 确保置信度在合理范围内
            final_confidence = max(0.0, min(100.0, confidence_score))
            
            logger.info(f"🎯 Format4置信度评估完成: {final_confidence:.1f}% (基于样本数据静态分析)")
            return final_confidence
            
        except Exception as e:
            logger.error(f"Format4置信度计算失败: {e}")
            return 5.0
    
    def extract_sample(self, file_path: str = None, limit: int = 10) -> Dict[str, Any]:
        """提取样本数据用于快速评估"""
        try:
            target_file = file_path or self.file_path
            if not target_file:
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
            
            # 使用原始解析器提取样本
            parser = ICBCFormat4EnhancedParser(target_file)
            sample_data = parser.extract_sample(limit=limit)
            
            return sample_data
            
        except Exception as e:
            logger.error(f"样本数据提取失败: {str(e)}")
            return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
    
    def parse(self, file_path: str) -> Dict[str, Any]:
        """解析文件"""
        try:
            logger.info(f"开始使用ICBCFormat4插件解析: {file_path}")
            
            # 使用完美的原始解析器
            parser = ICBCFormat4EnhancedParser(file_path)
            result = parser.parse()
            
            # 添加插件信息
            if result.get('success'):
                result['metadata'] = result.get('metadata', {})
                result['metadata'].update({
                    'plugin_name': self.name,
                    'plugin_version': self.version,
                    'parser_type': 'ICBC_Format4_Enhanced'
                })
            
            logger.info(f"ICBCFormat4插件解析完成: 成功={result.get('success')}, 账户={len(result.get('accounts', []))}, 交易={result.get('summary', {}).get('total_transactions', 0)}")
            return result
            
        except Exception as e:
            logger.error(f"ICBCFormat4插件解析失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'accounts': [],
                'transactions_by_account': {},
                'summary': {'total_accounts': 0, 'total_transactions': 0},
                'metadata': {
                    'plugin_name': self.name,
                    'plugin_version': self.version,
                    'error_details': str(e)
                }
            }
    
    def handle_error(self, error: Exception, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理错误"""
        logger.error(f"ICBCFormat4插件错误: {str(error)}")
        if context:
            logger.error(f"错误上下文: {context}")
        
        return {
            'success': False,
            'error': f"ICBCFormat4插件处理失败: {str(error)}",
            'plugin_name': self.name,
            'context': context or {}
        }
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取插件健康状态"""
        return {
            'plugin_name': self.name,
            'version': self.version,
            'status': 'healthy',
            'supported_formats': self.supported_formats,
            'bank_name': self.bank_name,
            'description': self.description
        } 