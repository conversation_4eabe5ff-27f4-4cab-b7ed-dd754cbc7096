"""
工商银行格式1增强解析器
专门处理1.xlsx文件的多工作表个人账户结构：
- 每个工作表代表一个持卡人
- 持卡人姓名+身份证号作为账户分隔符
- 支持同一持卡人多个账户的解析
"""
import pandas as pd
import logging
from typing import Dict, List, Any, Tuple
from datetime import datetime
import re
import os

logger = logging.getLogger(__name__)

class ICBCFormat1EnhancedParser:
    """工商银行格式1增强解析器"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.accounts = []
        self.transactions = []
    
    def _clean_field(self, value) -> str:
        """清理字段值"""
        if value is None or pd.isna(value):
            return ""
        return str(value).strip().replace('\t', '').replace('\n', '').replace('\r', '')
    
    def _clean_amount_string(self, value) -> str:
        """
        清理金额字符串，移除空格、逗号等格式字符
        
        Args:
            value: 原始金额值
            
        Returns:
            str: 清理后的金额字符串
        """
        if value is None:
            return ""
        
        # 转换为字符串并清理各种格式字符
        clean_value = str(value).replace(" ", "").replace(",", "").replace("，", "").replace("\t", "").replace("\n", "").replace("\r", "").strip()
        
        # 移除可能的货币符号
        clean_value = clean_value.replace("¥", "").replace("$", "").replace("€", "").replace("￥", "")
        
        return clean_value
    
    def _parse_amount(self, amount_str: str, dr_cr_flag: str = "") -> float:
        """
        解析金额
        
        Args:
            amount_str: 金额字符串
            dr_cr_flag: 借贷标志
            
        Returns:
            float: 解析后的金额（负数表示支出）
        """
        if not amount_str:
            return 0.0
        
        clean_amount = self._clean_amount_string(amount_str)
        
        try:
            amount = float(clean_amount)
            
            # 根据借贷标志调整正负
            if dr_cr_flag == "借":  # 借方=支出，转为负数
                return -abs(amount)
            elif dr_cr_flag == "贷":  # 贷方=收入，保持正数
                return abs(amount)
            else:
                return amount
        except (ValueError, TypeError):
            logger.warning(f"无法解析金额: {amount_str}")
            return 0.0
    
    def _parse_holder_name_and_id(self, name_id_str: str) -> Tuple[str, str]:
        """
        解析持卡人姓名和身份证号
        
        Args:
            name_id_str: 包含姓名和身份证号的字符串
            
        Returns:
            Tuple[str, str]: (姓名, 身份证号)
        """
        if not name_id_str:
            return "", ""
        
        # 使用正则表达式匹配姓名（2-4个中文字符）和身份证号（18位数字）
        pattern = r'^([\u4e00-\u9fa5]{2,4})(\d{18})$'
        match = re.match(pattern, name_id_str.strip())
        
        if match:
            return match.group(1), match.group(2)
        else:
            # 如果无法匹配，尝试其他方式
            cleaned = name_id_str.strip()
            if len(cleaned) >= 20:  # 假设至少有姓名+18位身份证
                # 从后面取18位作为身份证
                id_card = cleaned[-18:]
                if id_card.isdigit():
                    name = cleaned[:-18]
                    return name, id_card
            
            return cleaned, ""
    
    def _standardize_time_format(self, date_str: str, time_str: str = "") -> str:
        """
        标准化时间格式为 YYYY-MM-DD HH:MM:SS
        
        Args:
            date_str: 日期字符串
            time_str: 时间字符串
            
        Returns:
            str: 标准化后的时间字符串
        """
        if not date_str:
            return ""
        
        try:
            # 处理日期
            date_clean = self._clean_field(date_str)
            if not date_clean:
                return ""
            
            # 尝试解析日期
            date_obj = pd.to_datetime(date_clean).date()
            date_formatted = date_obj.strftime("%Y-%m-%d")
            
            # 处理时间
            if time_str:
                time_clean = self._clean_field(time_str)
                # 处理 HH.MM.SS 格式
                if '.' in time_clean:
                    time_clean = time_clean.replace('.', ':')
                
                # 确保时间格式正确
                time_parts = time_clean.split(':')
                if len(time_parts) == 3:
                    try:
                        hour = int(time_parts[0])
                        minute = int(time_parts[1])
                        second = int(time_parts[2])
                        time_formatted = f"{hour:02d}:{minute:02d}:{second:02d}"
                        return f"{date_formatted} {time_formatted}"
                    except ValueError:
                        pass
            
            # 如果没有时间或时间格式错误，返回只有日期的格式
            return f"{date_formatted} 00:00:00"
            
        except Exception as e:
            logger.warning(f"时间格式标准化失败: {date_str} {time_str}, 错误: {str(e)}")
            return date_str
    
    def _extract_account_info_from_cell(self, cell_value: str) -> Tuple[str, str]:
        """
        从单元格值中提取账户信息（持卡人姓名+身份证号）
        
        Args:
            cell_value: 单元格值
            
        Returns:
            Tuple[str, str]: (持卡人姓名, 身份证号)
        """
        if not cell_value:
            return "", ""
        
        cell_str = str(cell_value).strip()
        
        # 匹配格式：中文姓名+18位身份证号
        match = re.match(r'^([\u4e00-\u9fa5]{2,4})(\d{18})$', cell_str)
        if match:
            return match.group(1), match.group(2)
        
        # 尝试其他可能的格式
        if len(cell_str) >= 20:  # 至少2个字符姓名+18位身份证
            # 寻找连续的18位数字
            id_match = re.search(r'(\d{18})', cell_str)
            if id_match:
                id_number = id_match.group(1)
                # 身份证号前面的就是姓名
                name_part = cell_str[:id_match.start()].strip()
                if re.match(r'^[\u4e00-\u9fa5]{2,4}$', name_part):
                    return name_part, id_number
        
        return "", ""
    
    def _find_account_boundaries(self, df: pd.DataFrame) -> List[Tuple[int, int, str, str]]:
        """
        查找账户边界（持卡人姓名+身份证号所在行）
        
        Args:
            df: 数据框
            
        Returns:
            List[Tuple[int, int, str, str]]: [(起始行, 结束行, 持卡人姓名, 身份证号), ...]
        """
        boundaries = []
        
        for idx in range(len(df)):
            # 检查第一列是否包含持卡人信息
            cell_value = df.iloc[idx, 0] if not pd.isna(df.iloc[idx, 0]) else ""
            
            holder_name, holder_id = self._extract_account_info_from_cell(cell_value)
            
            if holder_name and holder_id:
                # 找到持卡人信息行，确定这个账户区段的范围
                start_idx = idx
                
                # 寻找下一个持卡人信息行作为结束边界
                end_idx = len(df)  # 默认到文件末尾
                
                for next_idx in range(idx + 1, len(df)):
                    next_cell = df.iloc[next_idx, 0] if not pd.isna(df.iloc[next_idx, 0]) else ""
                    next_name, next_id = self._extract_account_info_from_cell(next_cell)
                    
                    if next_name and next_id:
                        end_idx = next_idx
                        break
                
                boundaries.append((start_idx, end_idx, holder_name, holder_id))
                logger.info(f"找到账户边界: {holder_name}({holder_id}) 行{start_idx}-{end_idx}")
        
        return boundaries
    
    def _process_transaction_data(self, df: pd.DataFrame, start_row: int, 
                                account_name: str, account_number: str, card_number: str) -> None:
        """
        处理交易数据 - 重写以正确处理1.xlsx结构
        
        Args:
            df: 数据框
            start_row: 数据起始行
            account_name: 账户名
            account_number: 账号
            card_number: 卡号
        """
        i = start_row
        while i < len(df):
            row = df.iloc[i]
            
            # 跳过空行
            if row.isna().all():
                i += 1
                continue
            
            first_cell = self._clean_field(row.iloc[0])
            
            # 🔧 检查是否是新的持卡人信息行（姓名+身份证号）
            if re.match(r'^[\u4e00-\u9fa5]{2,4}\d{18}$', first_cell):
                # 提取新的持卡人姓名
                account_name = re.match(r'^([\u4e00-\u9fa5]{2,4})\d{18}$', first_cell).group(1)
                logger.info(f"发现新持卡人: {account_name}")
                i += 1
                continue
            
            # 🔧 检查是否是表头行
            if first_cell == "账号" or "交易日期" in first_cell:
                logger.info(f"跳过表头行: 第{i+1}行")
                i += 1
                continue
            
            # 🔧 检查是否是数据行（账号格式检查）
            if not re.match(r'^\d{16,20}$', first_cell.replace(' ', '').replace('\t', '')):
                i += 1
                continue
            
            try:
                # 🎯 **正确的字段映射**（基于调试结果）
                current_account = first_cell.replace(' ', '').replace('\t', '')  # A列：账号
                currency = self._clean_field(row.iloc[1])  # B列：币种
                current_card = self._clean_field(row.iloc[2])  # C列：卡号
                transaction_date_raw = self._clean_field(row.iloc[3])  # D列：交易日期
                transaction_time = self._clean_field(row.iloc[4])  # E列：记帐时间
                dr_cr_flag_raw = self._clean_field(row.iloc[5])  # F列：借贷标志
                amount_raw = self._clean_field(row.iloc[6])  # G列：发生额
                balance_raw = self._clean_field(row.iloc[7])  # H列：余额
                
                # 🔧 数据有效性检查
                if not transaction_date_raw or not amount_raw or not dr_cr_flag_raw:
                    i += 1
                    continue
                
                # 🔧 确保我们不会误解析时间格式为金额
                if re.match(r'^\d{2}\.\d{2}\.\d{2}$', amount_raw):
                    logger.error(f"❌ 第{i+1}行错误：发生额字段包含时间格式 {amount_raw}，数据行解析错误")
                    i += 1
                    continue
                
                if re.match(r'^\d{2}\.\d{2}\.\d{2}$', balance_raw):
                    logger.error(f"❌ 第{i+1}行错误：余额字段包含时间格式 {balance_raw}，数据行解析错误")
                    i += 1
                    continue
                
                # 提取日期（移除前导空格）
                transaction_date = str(transaction_date_raw).lstrip() if transaction_date_raw else ""
                
                # 使用当前交易行的卡号，不强制生成
                if not current_card or current_card in ['', 'nan', 'None']:
                    current_card = ""  # 保持空值，不强制生成
                
                # 处理借贷标志
                dr_cr_flag = "收" if dr_cr_flag_raw in ["贷", "收入"] else "支"
                
                # 🔧 安全的金额解析
                try:
                    amount = self._parse_amount(amount_raw, dr_cr_flag_raw)
                except Exception as e:
                    logger.warning(f"第{i+1}行金额解析失败: amount_raw='{amount_raw}', error={e}")
                    i += 1
                    continue
                
                try:
                    balance = self._parse_amount(balance_raw) if balance_raw and balance_raw not in ['', 'nan', 'None'] else 0.0
                except Exception as e:
                    logger.warning(f"第{i+1}行余额解析失败: balance_raw='{balance_raw}', error={e}")
                    balance = 0.0
                
                # 标准化时间
                datetime_str = self._standardize_time_format(transaction_date, transaction_time)
                date_part = datetime_str.split(' ')[0] if ' ' in datetime_str else datetime_str
                time_part = datetime_str.split(' ')[1] if ' ' in datetime_str else "00:00:00"
                
                # 获取其他字段
                remark1 = self._clean_field(row.iloc[8]) if len(row) > 8 else ""  # I列：注释
                counterparty_account = self._clean_field(row.iloc[9]) if len(row) > 9 else ""  # J列：对方帐户
                transaction_area = self._clean_field(row.iloc[10]) if len(row) > 10 else ""  # K列：交易地区号
                transaction_branch = self._clean_field(row.iloc[11]) if len(row) > 11 else ""  # L列：交易网点号
                cashier_no = self._clean_field(row.iloc[12]) if len(row) > 12 else ""  # M列：柜员号
                transaction_code = self._clean_field(row.iloc[13]) if len(row) > 13 else ""  # N列：交易代码
                service_interface = self._clean_field(row.iloc[14]) if len(row) > 14 else ""  # O列：服务界面
                
                # 构建交易方式描述
                transaction_method = remark1 if remark1 else "其他"  # 使用注释作为交易方式
                if service_interface:
                    transaction_method += f"({service_interface})"
                
                # 构建符合17个必需字段的交易记录
                transaction = {
                    'sequence_number': len(self.transactions) + 1,
                    'holder_name': account_name,  # 🔧 修复：使用前端期望的字段名
                    'cardholder_name': account_name,  # 🔧 保持兼容性
                    'bank_name': '中国工商银行',
                    'account_number': current_account,
                    'card_number': current_card,
                    'transaction_date': date_part,
                    'transaction_time': time_part,
                    'transaction_method': transaction_method,
                    'transaction_amount': amount,
                    'balance_amount': balance,
                    'dr_cr_flag': dr_cr_flag,
                    'counterparty_name': "",  # 1.xlsx格式中没有对方户名
                    'counterparty_account': counterparty_account,
                    'counterparty_bank': "",  # 1.xlsx格式中没有对方开户行
                    'remark1': remark1,  # 🔧 备注1对应表中的"注释"栏
                    'remark2': "",  # 🔧 按用户指导：直接取空值
                    'remark3': "",  # 🔧 按用户指导：直接取空值
                    'currency': currency or 'CNY'
                }
                
                self.transactions.append(transaction)
                logger.debug(f"✅ 成功解析第{i+1}行交易: {account_name} - {amount} - {transaction_method}")
                
            except Exception as e:
                logger.warning(f"处理第{i+1}行交易记录失败: {str(e)}")
            
            i += 1
    
    def _process_sheet(self, sheet_name: str) -> None:
        """
        处理单个工作表
        
        Args:
            sheet_name: 工作表名称
        """
        try:
            # 读取Excel文件的特定工作表
            df = pd.read_excel(self.file_path, sheet_name=sheet_name, header=None)
            
            # 检测账户边界
            boundaries = self._find_account_boundaries(df)
            
            if not boundaries:
                logger.warning(f"工作表 {sheet_name} 中未找到账户信息")
                return
            
            # 处理每个账户区段
            for i, (start_idx, end_idx, holder_name, holder_id) in enumerate(boundaries):
                logger.info(f"处理账户区段: {holder_name} ({start_idx}-{end_idx})")
                self._process_transaction_data(df, start_idx, holder_name, "", "")
                
        except Exception as e:
            logger.error(f"处理工作表 {sheet_name} 时发生错误: {str(e)}")
    
    def parse(self) -> Dict[str, Any]:
        """
        解析Excel文件
        
        Returns:
            Dict[str, Any]: 解析结果
        """
        try:
            # 获取所有工作表名称
            excel_file = pd.ExcelFile(self.file_path)
            sheet_names = excel_file.sheet_names
            
            logger.info(f"开始解析文件: {self.file_path}")
            logger.info(f"发现工作表: {sheet_names}")
            
            # 处理每个工作表
            for sheet_name in sheet_names:
                logger.info(f"处理工作表: {sheet_name}")
                self._process_sheet(sheet_name)
            
            # 构建解析结果
            result = self._build_result()
            
            logger.info(f"解析完成: 共 {len(self.accounts)} 个账户, {len(self.transactions)} 条交易")
            return result
            
        except Exception as e:
            logger.error(f"文件解析失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'accounts': [],
                'transactions_by_account': {},
                'summary': {'total_accounts': 0, 'total_transactions': 0}
            }
    
    def _calculate_confidence_score(self, accounts: List[Dict], transactions: List[Dict]) -> float:
        """
        计算解析置信度
        
        Returns:
            float: 置信度分数 (0-100)
        """
        if not accounts or not transactions:
            return 0.0
        
        total_score = 0
        max_score = 100
        
        # 1. 账户信息完整性评估 (25分)
        account_score = 0
        valid_accounts = 0
        for account in accounts:
            # 账号或卡号有其一即可得分 - 按照用户要求修正
            account_number = account.get('account_number', '')
            card_number = account.get('card_number', '')
            if (account_number and len(account_number) >= 10) or card_number:
                account_score += 15  # 账号或卡号有效得15分
            if account.get('cardholder_name') and len(account['cardholder_name']) >= 2:
                account_score += 10  # 持卡人姓名有效得10分
            valid_accounts += 1
        
        if valid_accounts > 0:
            account_score = min(25, account_score / valid_accounts)
        
        # 2. 交易数据完整性评估 (25分)  
        transaction_score = 0
        valid_transactions = 0
        for transaction in transactions:
            score = 0
            if transaction.get('transaction_date'):
                score += 5
            if transaction.get('transaction_amount') != 0:
                score += 10
            if transaction.get('dr_cr_flag'):
                score += 5
            if transaction.get('counterparty_name') or transaction.get('remark1'):
                score += 5
            
            transaction_score += score
            valid_transactions += 1
        
        if valid_transactions > 0:
            transaction_score = min(25, transaction_score / valid_transactions)
        
        # 3. 数据格式一致性评估 (25分)
        format_score = 25  # 基础分
        
        # 检查日期格式一致性
        date_formats = set()
        for transaction in transactions[:10]:  # 只检查前10条
            date = transaction.get('transaction_date', '')
            if date:
                if re.match(r'^\d{4}-\d{2}-\d{2}$', date):
                    date_formats.add('standard')
                else:
                    date_formats.add('non_standard')
        
        if len(date_formats) > 1:
            format_score -= 10  # 日期格式不一致扣分
        
        # 4. 数据量合理性评估 (25分)
        volume_score = 25
        
        # 检查账户数量是否合理（1-50个账户比较正常）
        account_count = len(accounts)
        if account_count == 0:
            volume_score = 0
        elif account_count > 100:
            volume_score -= 10  # 账户过多可能是解析错误
        
        # 检查交易数量是否合理
        transaction_count = len(transactions)
        if transaction_count == 0:
            volume_score = 0
        elif transaction_count < 10:
            volume_score -= 5  # 交易过少可能不完整
        
        total_score = account_score + transaction_score + format_score + volume_score
        return min(100.0, max(0.0, total_score))

    def _build_result(self) -> Dict[str, Any]:
        """构建解析结果 - 按照PARSER_DEVELOPMENT_RULES.md规范"""
        # 按账户分组交易
        transactions_by_account = {}
        accounts = []
        
        # 为每条交易分配序号
        for index, transaction in enumerate(self.transactions, 1):
            transaction['sequence_number'] = index
            
            account_number = transaction['account_number']
            
            if account_number not in transactions_by_account:
                transactions_by_account[account_number] = []
                
                # 🔧 修复：使用正确的字段名cardholder_name
                account_data = {
                    'account_number': account_number,
                    'card_number': transaction['card_number'],
                    'cardholder_name': transaction['cardholder_name'],  # 修复：从holder_name映射到cardholder_name
                    'bank_name': '中国工商银行',
                    'account_type': '个人账户',
                    
                    # 统计信息
                    'transactions_count': 0,  # 将在后续计算
                    'total_inflow': 0.0,
                    'total_outflow': 0.0,
                    'date_range': "",
                    
                    # 兼容字段
                    'account_id': account_number,
                    'account_name': transaction['cardholder_name'],
                    'currency': transaction['currency'],
                    'is_primary': len(accounts) == 0,
                    
                    # 🔧 添加transactions字段，用于前端显示
                    'transactions': []
                }
                accounts.append(account_data)
            
            transactions_by_account[account_number].append(transaction)
        
        # 计算统计信息并添加交易到账户
        for account in accounts:
            account_transactions = transactions_by_account[account['account_number']]
            account['transactions_count'] = len(account_transactions)
            
            # 🔧 添加交易列表到账户，用于前端显示
            account['transactions'] = account_transactions
            
            # 计算收支总额
            inflow = sum(t['transaction_amount'] for t in account_transactions if t['transaction_amount'] > 0)
            outflow = sum(abs(t['transaction_amount']) for t in account_transactions if t['transaction_amount'] < 0)
            
            account['total_inflow'] = inflow
            account['total_outflow'] = outflow
            
            # 计算时间范围
            dates = [t['transaction_date'] for t in account_transactions if t['transaction_date']]
            if dates:
                account['date_range'] = f"{min(dates)} 至 {max(dates)}"
        
        # 🔧 使用正确的置信度评估
        confidence_score = self._calculate_simple_confidence_score(accounts, self.transactions)
        
        # 构建最终结果
        return {
            'success': True,
            'file_info': {
                'name': os.path.basename(self.file_path),
                'format': 'xlsx'
            },
            'summary': {
                'total_accounts': len(accounts),
                'total_transactions': len(self.transactions),
                'date_range': self._calculate_overall_date_range()
            },
            'accounts': accounts,
            'transactions': self.transactions,  # 添加交易列表
            'transactions_by_account': transactions_by_account,
            'confidence_score': confidence_score,  # 添加置信度到顶层
            'metadata': {
                'parser_version': '1.0.0',
                'parsing_time': datetime.now().isoformat(),
                'confidence_score': confidence_score
            }
        }
    
    def _calculate_simple_confidence_score(self, accounts: List[Dict], transactions: List[Dict]) -> float:
        """
        简单置信度评估 - 按照用户要求的正确逻辑
        """
        if not accounts:
            return 0.0
        
        # 取前5个账户进行验证（预解析）
        sample_accounts = accounts[:5]
        
        # 1. 金额验证 (25分) - 检查是否有交易且金额格式正确
        amount_valid = False
        for account in sample_accounts:
            account_transactions = account.get('transactions', [])
            if account_transactions:
                for trans in account_transactions[:3]:  # 检查前3笔交易
                    amount = trans.get('transaction_amount')
                    if amount is not None:
                        try:
                            float(amount)
                            amount_valid = True
                            break
                        except:
                            pass
            if amount_valid:
                break
        
        amount_score = 25 if amount_valid else 0
        
        # 2. 持卡人姓名验证 (25分)
        name_valid = False
        for account in sample_accounts:
            name = account.get('cardholder_name', '')
            if name and re.match(r'^[\u4e00-\u9fff]{2,18}$', name):
                name_valid = True
                break
        
        name_score = 25 if name_valid else 0
        
        # 3. 账号验证 (25分)
        account_valid = False
        for account in sample_accounts:
            account_num = account.get('account_number', '')
            if account_num and re.match(r'^\d{10,25}$', str(account_num)):
                account_valid = True
                break
        
        account_score = 25 if account_valid else 0
        
        # 4. 卡号验证 (25分)
        card_valid = False
        for account in sample_accounts:
            card_num = account.get('card_number', '')
            if card_num and re.match(r'^\d{13,19}$', str(card_num)):
                card_valid = True
                break
        
        card_score = 25 if card_valid else 0
        
        total_confidence = amount_score + name_score + account_score + card_score
        return total_confidence
    
    def _calculate_overall_date_range(self) -> str:
        """计算整体时间范围"""
        if not self.transactions:
            return ""
        
        dates = [t['transaction_date'] for t in self.transactions if t['transaction_date']]
        if dates:
            return f"{min(dates)} 至 {max(dates)}"
        
        return ""
    
    def extract_sample(self, limit: int = 10) -> Dict[str, Any]:
        """
        提取样本数据用于快速置信度评估
        
        Args:
            limit: 样本数据限制数量
            
        Returns:
            Dict: 样本数据
        """
        try:
            # 🔧 快速读取：只读取第一个工作表的前50行，避免性能问题
            excel_file = pd.ExcelFile(self.file_path)
            first_sheet = excel_file.sheet_names[0]
            df = pd.read_excel(self.file_path, sheet_name=first_sheet, header=None, nrows=50)
            
            logger.info(f"快速评估模式：读取工作表 {first_sheet} 前50行")
            
            # 🔧 简化边界检测：只检查前30行
            boundaries = []
            for idx in range(min(30, len(df))):
                cell_value = df.iloc[idx, 0] if not pd.isna(df.iloc[idx, 0]) else ""
                holder_name, holder_id = self._extract_account_info_from_cell(cell_value)
                
                if holder_name and holder_id:
                    # 简单设置边界：从当前行到文件末尾
                    boundaries.append((idx, len(df), holder_name, holder_id))
                    logger.info(f"快速检测到账户: {holder_name}")
                    break  # 只找第一个账户即可
            
            if not boundaries:
                logger.warning("快速评估：未找到账户信息")
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
            
            # 使用第一个找到的账户
            start_idx, end_idx, holder_name, holder_id = boundaries[0]
            
            # 快速处理样本数据：只处理前limit条交易
            sample_transactions = []
            data_start = start_idx + 2  # 跳过姓名行和表头行
            
            for idx in range(data_start, min(data_start + limit * 2, len(df))):  # 多读一些行以确保有足够样本
                if len(sample_transactions) >= limit:
                    break
                    
                if idx >= len(df):
                    break
                
                row = df.iloc[idx]
                if row.isna().all():
                    continue
                
                # 快速验证：检查第一列是否像账号
                first_cell = self._clean_field(row.iloc[0])
                if not re.match(r'^\d{16,25}', first_cell.replace(' ', '').replace('\t', '')):
                    continue
                
                try:
                    # 基本字段提取（快速模式，不做复杂验证）
                    account_number = first_cell.replace(' ', '').replace('\t', '')
                    transaction_date = self._clean_field(row.iloc[3])  # D列：交易日期
                    amount_raw = self._clean_field(row.iloc[6])  # G列：发生额
                    dr_cr_flag = self._clean_field(row.iloc[5])  # F列：借贷标志
                    
                    if account_number and transaction_date and amount_raw:
                        transaction = {
                            'cardholder_name': holder_name,
                            'account_number': account_number,
                            'transaction_date': transaction_date,
                            'transaction_amount': self._parse_amount(amount_raw, dr_cr_flag),
                            'dr_cr_flag': dr_cr_flag,
                            'card_number': self._clean_field(row.iloc[2]) if len(row) > 2 else "",
                            'currency': self._clean_field(row.iloc[1]) if len(row) > 1 else "CNY"
                        }
                        sample_transactions.append(transaction)
                        
                except Exception as e:
                    logger.debug(f"跳过第{idx+1}行: {str(e)}")
                    continue
            
            # 构建样本账户信息
            sample_account = {
                'account_number': sample_transactions[0]['account_number'] if sample_transactions else "",
                'cardholder_name': holder_name,
                'card_number': sample_transactions[0]['card_number'] if sample_transactions else "",
                'bank_name': '中国工商银行',
                'account_type': '个人账户'
            }
            
            logger.info(f"快速评估完成：提取{len(sample_transactions)}条样本交易")
            
            return {
                'accounts': [sample_account] if sample_transactions else [],
                'transactions': sample_transactions[:limit],
                'metadata': {
                    'sample_size': len(sample_transactions),
                    'sheet_count': len(excel_file.sheet_names),
                    'evaluation_mode': 'quick_sample',
                    'holder_name': holder_name,
                    'holder_id': holder_id
                }
            }
            
        except Exception as e:
            logger.error(f"快速样本数据提取失败: {str(e)}")
            return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}} 