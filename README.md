# 银行流水分析系统

## 🚀 快速启动指南

### 方法一：一键启动（推荐）
```bash
# 直接双击运行
reliable_startup.bat
```

### 方法二：诊断后启动
```bash
# 1. 先诊断系统状态
system_diagnosis.bat

# 2. 如有问题，先清理
cleanup_services.bat

# 3. 再启动服务
reliable_startup.bat
```

### 启动故障排除
- **端口占用**：运行 `cleanup_services.bat`
- **依赖缺失**：运行 `system_diagnosis.bat` 检查
- **启动超时**：检查防火墙和杀毒软件设置

### 服务地址
- 🔗 后端API文档：http://127.0.0.1:8000/docs
- 🔗 前端界面：http://localhost:3000

---

## 🔧 重要系统更新记录

### 2025年1月 - 4维度置信度评估系统修复
**问题**: icbc_format3_plugin等解析器血条置信度低(15%)但4维度评分显示满分(100%)，逻辑矛盾
**根源**: API在检查空数据时不够严格，空字符串或无效数据被误判为有效
**修复**: 
- 增加严格的有效数据检查 (非空且有实际内容)
- 4维度评分必须基于实际解析出的有效数据
- 空数据或解析失败时强制返回0分
**影响**: 全局调整，影响所有解析器的4维度评分显示

### PowerShell启动命令修正
**问题**: `reliable_startup.bat` 执行失败
**解决**: 使用 `.\reliable_startup.bat` (Windows安全策略要求)

---

# 银行流水分析系统 - Web版

## 🚨 重要：标准化启动方式（2025年6月重构后）

**⚠️ 请只使用以下启动方式，避免端口冲突和配置混乱**

### 标准启动脚本
```bash
start_all.bat        # 完整应用（前端+后端）推荐使用
start_backend.bat    # 仅后端（API测试用）
start_frontend.bat   # 仅前端（需后端已启动）
```

### 端口配置（固定，不得修改）
- **后端端口**：8000 
- **前端端口**：3000
- **如遇端口占用**：解决占用问题，不要改端口

### 标准启动流程
1. 双击 `start_all.bat`
2. 等待后端启动（约5-10秒）
3. 等待前端启动（约10-15秒）
4. 自动打开浏览器：http://localhost:3000

## 🎯 项目概述

银行流水分析系统是一个专业的**Web应用程序**，专门用于解析和分析各种银行流水文件。采用现代化的技术栈，提供高性能的本地化数据处理能力。

### ✨ 核心特性

- 🌐 **Web应用架构**：基于React + FastAPI的现代Web应用
- 🚀 **高性能数据库**：DuckDB嵌入式数据库，分析查询性能卓越
- 📊 **智能解析引擎**：支持工商银行多种格式的自动识别和解析
- 🔒 **数据安全**：完全本地化存储，数据不出本地
- 💾 **一键启动**：使用DuckDB嵌入式数据库，无需复杂数据库配置
- 🛠️ **用户隔离**：每用户独立数据库文件，数据完全隔离

### 🏗️ 技术架构

```
Web应用架构:
├── 🎨 前端层：React 18 + Ant Design (端口3000)
├── ⚡ 后端层：FastAPI + Python (端口8000)
├── 💾 数据层：DuckDB (嵌入式分析数据库)
├── 🧪 测试层：pytest + Playwright (自动化测试)
└── 📦 未来计划：Electron桌面应用 (electron-app目录预留)
```

## 🆕 最新更新 (2025年1月)

### ✅ **4维度评分系统增强**
- **新功能**: 解析器选择界面现在显示详细的4维度评分
- **评分项目**: 持卡人姓名识别(25分) + 时间格式准确性(25分) + 账号识别(25分) + 金额解析(25分) = 总分100分
- **用户体验**: 帮助用户更好地了解解析器的具体能力和质量

### ✅ **系统稳定性提升** 
- **修复**: 银行API路径错误，消除控制台404错误
- **增强**: 插件文件格式检查逻辑优化
- **改进**: PowerShell环境兼容性增强

### ✅ **新增技术文档**
- 📚 [解析器开发经验教训与最佳实践](./银行流水解析器开发经验教训与最佳实践.md)
- 🔧 [解析器问题案例库](./解析器问题案例库.md)
- 📋 [银行流水解析验证清单](./银行流水解析验证清单.md)

### 已支持的银行解析器
- ✅ **工商银行**：
  - Format1 Enhanced（增强版格式1）
  - Format3 Standard（标准格式3）  
  - Format4 Enhanced（增强版格式4）
- ✅ **通用解析器**：18标准字段格式，支持所有银行（用户手工调整格式）
- 📋 **其他银行**：预留架构，逐个开发

## 🚀 快速开始

### 系统要求
- Windows 10/11 (64位)
- Python 3.9+
- Node.js 18.0+

### 一键启动（推荐）
```powershell
# 在项目根目录执行
.\start_all.bat
```

### 开发环境搭建

1. **激活Python虚拟环境**
```bash
# 如果虚拟环境不存在，先创建
python -m venv bankflow_env

# 激活虚拟环境
.\bankflow_env\Scripts\activate

# 安装后端依赖
cd backend
pip install -r requirements.txt
```

2. **安装前端依赖**
```bash
cd frontend/bankflow-client
npm install
```

3. **启动应用**
```bash
# 返回项目根目录
cd ../..

# 启动完整应用
.\start_all.bat
```

## 📁 项目结构

```
流水清洗/
├── ⚡ backend/               # FastAPI后端 (端口8000)
│   ├── app/
│   │   ├── api/              # API路由模块
│   │   ├── database/         # DuckDB数据库配置
│   │   ├── models/           # 数据模型和Schema
│   │   ├── services/         # 业务逻辑服务
│   │   │   ├── parser/       # 解析器服务
│   │   │   └── cleaner/      # 数据清洗服务
│   │   └── main.py           # FastAPI应用入口
│   └── requirements.txt      # Python依赖
├── 🎨 frontend/              # React前端 (端口3000)
│   └── bankflow-client/
│       ├── src/
│       │   ├── components/   # React组件
│       │   ├── pages/        # 页面组件
│       │   ├── services/     # API服务
│       │   └── contexts/     # React Context
│       └── package.json      # 前端依赖
├── 📊 parsing_templates/     # 解析模板配置
├── 🖥️ electron-app/          # 桌面应用预留（未来开发）
├── 📚 文档备份_旧版本/        # 项目文档备份
└── 🚀 启动脚本/               # 标准化启动脚本
```

## 🔧 核心功能

### 1. 智能文件解析
- **工商银行专用**：三种格式解析器，自动识别格式类型
- **通用解析器**：18标准字段格式，支持所有银行（用户手工调整格式）
- **智能推荐**：专用解析器优先，通用解析器兜底的智能选择机制
- **文件格式支持**：Excel (.xlsx, .xls)
- **批量处理**：支持多文件同时上传解析
- **解析验证**：内置解析结果验证和置信度评估（最高100%置信度）

### 2. 数据分析引擎
- **DuckDB驱动**：高性能分析查询，比传统数据库快数倍
- **用户隔离**：每用户独立数据库文件，数据完全隔离
- **数据清洗**：自动去重、格式标准化、数据验证
- **查询分析**：支持复杂的流水查询和统计分析

### 3. Web应用体验
- **响应式设计**：支持PC和移动端访问
- **现代UI**：基于Ant Design的专业界面
- **实时反馈**：上传进度、解析状态实时显示
- **用户认证**：安全的登录和会话管理

### 4. 数据管理
- **项目管理**：支持多项目数据组织
- **案件管理**：完整的案件相关人员、单位、关系管理
- **资产管理**：银行账户、资产记录管理
- **线索管理**：案件线索记录和跟踪

## 🛠️ 开发指南

### 添加新银行解析器
1. 在 `backend/app/services/parser/` 目录下创建新的解析器
2. 在 `parsing_templates/` 目录下添加解析模板配置
3. 更新解析器选择逻辑
4. 编写对应的测试用例

### 自定义前端功能
1. 在 `frontend/bankflow-client/src/pages/` 目录下创建页面
2. 在 `frontend/bankflow-client/src/components/` 目录下创建组件
3. 更新路由配置
4. 添加相应的API调用

### 扩展数据库模型
1. 修改 `backend/app/models/duckdb_models.py`
2. 更新相关的API端点
3. 修改前端数据模型

## 📊 性能特性

### DuckDB优势
- **启动速度**：嵌入式数据库，无需安装配置
- **查询性能**：分析查询速度比传统数据库快3-5倍
- **文件处理**：支持大文件高效解析和存储
- **并发支持**：多用户并发访问，数据隔离

### 系统要求
- **最小配置**：4GB RAM, 2GB 可用磁盘空间
- **推荐配置**：8GB RAM, 5GB 可用磁盘空间
- **大文件处理**：支持50MB+的银行流水文件高效解析

## 🔒 数据安全

- **本地存储**：所有数据完全存储在本地，不上传云端
- **用户隔离**：每用户独立数据库文件，数据完全隔离
- **会话管理**：安全的登录认证和会话控制
- **数据加密**：敏感数据加密存储和传输

## 🎯 未来规划

### 桌面应用计划
- **开发计划**：Web应用完善后开发桌面版
- **技术方案**：Electron + 当前Web应用
- **预留目录**：`electron-app/` 已预留
- **发布方式**：打包为独立exe可执行文件

### 功能扩展
- **更多银行**：逐步增加其他银行解析器支持
- **通用解析器**：最后开发，支持用户自定义格式
- **高级分析**：增加更多数据分析和可视化功能
- **导出功能**：支持多种格式的分析结果导出

## 📈 版本历史

### v2.1.0 (最新版本) - 2025年6月通用解析器完成
- ✅ **通用解析器正式发布**：18标准字段格式，支持所有银行
- ✅ **智能解析器选择机制完善**：专用解析器优先，通用解析器兜底
- ✅ **账户信息显示问题修复**：彻底解决收入支出总额显示为¥0.00的问题
- ✅ **端到端测试完成**：三轮银行验证测试（工商、浦发、建设银行）
- ✅ **前后端数据一致性验证**：确保账户信息表与交易记录汇总完全一致
- ✅ **生产就绪状态**：通用解析器功能完整、性能稳定，可立即投入使用
- ✅ **测试环境清理**：完整清理所有测试文件，系统恢复干净状态

### v2.0.0 - 2025年6月大清理
- ✅ 史上最全面的废案清理（38个文件/目录清理）
- ✅ 工商银行三种格式解析器优化
- ✅ 启动脚本标准化
- ✅ 文档体系完整更新
- ✅ 系统稳定性大幅提升

### v1.0.0 - 基础版本
- ✅ Web应用基础架构
- ✅ DuckDB嵌入式数据库集成
- ✅ 工商银行基础解析功能
- ✅ 用户认证和数据隔离

## 🚨 重要提醒

### 端口规范（强制）
- **后端8000**、**前端3000** - 禁止修改
- 如遇端口占用，解决占用问题，不要改端口

### 启动规范
- 只使用标准启动脚本：`start_all.bat`
- 废弃脚本已移至 `backup_startup_scripts/` 目录

## 📚 开发者文档

### 🔧 解析器开发
- [银行流水解析器开发统一规范](./银行流水解析器开发统一规范.md) - 开发标准和规范
- [银行流水解析器开发经验教训与最佳实践](./银行流水解析器开发经验教训与最佳实践.md) - 实战经验总结
- [解析器问题案例库](./解析器问题案例库.md) - 问题诊断和解决方案

### 🧪 测试和验证
- [银行流水解析验证清单](./银行流水解析验证清单.md) - 端到端测试规范
- [插件化架构设计方案](./插件化解析器架构设计方案.md) - 架构设计文档

### 📖 历史文档
- [银行流水分析系统_架构与开发规范](./银行流水分析系统_架构与开发规范.md) - 系统架构文档
- [银行流水分析系统_维护手册与快速入门](./银行流水分析系统_维护手册与快速入门.md) - 维护指南

### 开发规范
- 解析器修改只在 `services/parser/` 和 `parsing_templates/` 目录
- 禁止修改数据库配置、启动脚本、路由配置

## 📞 支持与反馈

- **系统维护**：参考项目文档（`银行流水分析系统_维护手册与快速入门.md`）
- **历史问题**：查看 `银行流水分析系统_历史问题与最佳实践.md`
- **架构规范**：参考 `银行流水分析系统_架构与开发规范.md`
- **清理记录**：查看 `银行流水分析系统_废案清理完成报告.md`

---

*最后更新: 2025年6月30日 - 通用解析器v2.1.0版本发布*

**🌐 银行流水分析系统 - 专业、安全、高效的Web应用！**  
**🎉 通用解析器正式发布，支持所有银行流水解析！** 