/* 全局基础样式 - 重新设计字体规范，参考腾讯元宝标准 */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px; /* 从16px增大到18px，参考腾讯元宝基础字体 */
  line-height: 1.7; /* 从1.6增大到1.7，增强可读性 */
  background-color: #f5f5f5;
  color: #1a1a1a; /* 从#262626改为更深的颜色，增强对比度 */
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

/* 全局字体大小统一规范 - 参考腾讯元宝和现代应用标准 */
* {
  box-sizing: border-box;
}

/* Ant Design 组件全局样式调整 - 大幅增大字体 */
.ant-typography {
  font-size: 18px !important; /* 从16px增大到18px */
  line-height: 1.7 !important;
  margin-bottom: 14px !important;
  color: #1a1a1a !important; /* 增强对比度 */
}

/* 标题层级 - 重新设计，参考腾讯元宝的大字体标准 */
h1, .ant-typography h1 {
  font-size: 36px !important; /* 从32px增大到36px，页面主标题更突出 */
  font-weight: 700 !important;
  margin: 18px 0 24px 0 !important;
  line-height: 1.3 !important;
  color: #1890ff !important;
}

h2, .ant-typography h2 {
  font-size: 30px !important; /* 从26px增大到30px，区块标题更清晰 */
  font-weight: 600 !important;
  margin: 16px 0 20px 0 !important;
  line-height: 1.3 !important;
  color: #1a1a1a !important;
}

h3, .ant-typography h3 {
  font-size: 26px !important; /* 从22px增大到26px，卡片标题更明显 */
  font-weight: 600 !important;
  margin: 14px 0 18px 0 !important;
  line-height: 1.3 !important;
  color: #1a1a1a !important;
}

h4, .ant-typography h4 {
  font-size: 22px !important; /* 从18px增大到22px，子标题更清晰 */
  font-weight: 500 !important;
  margin: 12px 0 16px 0 !important;
  line-height: 1.3 !important;
  color: #1a1a1a !important;
}

h5, .ant-typography h5 {
  font-size: 20px !important; /* 从16px增大到20px，小标题更易读 */
  font-weight: 500 !important;
  margin: 10px 0 14px 0 !important;
  line-height: 1.3 !important;
  color: #1a1a1a !important;
}

/* 系统标题特殊样式 - 优先级更高 */
.system-title {
  font-size: 32px !important;
  font-weight: 600 !important;
  color: #262626 !important;
  margin: 0 !important;
  line-height: 1.2 !important;
}

/* 页面主标题特殊样式 */
.page-main-title {
  font-size: 42px !important;
  font-weight: 700 !important;
  color: #1890ff !important;
  margin-bottom: 16px !important;
}

/* 表单组件统一样式 - 大幅增大尺寸，参考腾讯元宝标准 */
.ant-input, .ant-input-password {
  height: 50px !important; /* 从44px增大到50px */
  font-size: 18px !important; /* 从16px增大到18px */
  padding: 12px 16px !important;
  border-radius: 8px !important;
  color: #1a1a1a !important;
}

.ant-btn {
  height: 50px !important; /* 从44px增大到50px */
  font-size: 18px !important; /* 从16px增大到18px */
  font-weight: 500 !important;
  border-radius: 8px !important;
  padding: 0 20px !important;
  line-height: 1.4 !important;
}

.ant-btn-lg {
  height: 56px !important; /* 从50px增大到56px，大按钮更突出 */
  font-size: 20px !important;
  font-weight: 600 !important;
  padding: 0 28px !important;
}

.ant-btn-sm {
  height: 42px !important; /* 从36px增大到42px */
  font-size: 16px !important;
  padding: 0 16px !important;
}

/* 卡片组件样式 */
.ant-card {
  border-radius: 10px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  margin-bottom: 20px !important;
}

.ant-card-body {
  padding: 24px !important;
}

.ant-card-head {
  padding: 0 24px !important;
  min-height: 60px !important; /* 从52px增大到60px */
  border-bottom: 1px solid #f0f0f0 !important;
}

.ant-card-head-title {
  font-size: 22px !important; /* 从18px增大到22px */
  font-weight: 600 !important;
  color: #1a1a1a !important;
}

/* 表格组件样式 - 大幅增大字体 */
.ant-table {
  font-size: 17px !important; /* 从15px增大到17px */
}

.ant-table-thead > tr > th {
  padding: 16px 20px !important;
  font-size: 18px !important; /* 从15px增大到18px */
  font-weight: 600 !important;
  background-color: #fafafa !important;
  color: #1a1a1a !important;
}

.ant-table-tbody > tr > td {
  padding: 16px 20px !important;
  font-size: 17px !important; /* 从15px增大到17px */
  color: #1a1a1a !important;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5 !important;
}

/* 导航菜单样式 - 重新设计层次感，增大字体 */
.ant-menu {
  font-size: 18px !important; /* 从16px增大到18px */
  background: #001529 !important;
}

/* 一级菜单项 */
.ant-menu-item {
  height: 54px !important; /* 从48px增大到54px */
  line-height: 54px !important;
  padding: 0 24px !important;
  margin: 6px 10px !important;
  border-radius: 8px !important;
  font-size: 18px !important;
  font-weight: 500 !important;
  color: rgba(255, 255, 255, 0.85) !important;
}

.ant-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: #fff !important;
}

.ant-menu-item-selected {
  background-color: #1890ff !important;
  color: #fff !important;
}

/* 子菜单标题 */
.ant-menu-submenu-title {
  height: 54px !important;
  line-height: 54px !important;
  padding: 0 24px !important;
  margin: 6px 10px !important;
  border-radius: 8px !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  color: rgba(255, 255, 255, 0.85) !important;
}

.ant-menu-submenu-title:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: #fff !important;
}

/* 子菜单项 */
.ant-menu-submenu .ant-menu-item {
  height: 46px !important; /* 从40px增大到46px */
  line-height: 46px !important;
  padding-left: 52px !important;
  margin: 3px 10px !important;
  font-size: 17px !important; /* 从15px增大到17px */
  font-weight: 400 !important;
  color: rgba(255, 255, 255, 0.75) !important;
}

.ant-menu-submenu .ant-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.08) !important;
  color: rgba(255, 255, 255, 0.95) !important;
}

.ant-menu-submenu .ant-menu-item-selected {
  background-color: #1890ff !important;
  color: #fff !important;
}

/* 菜单图标样式 */
.ant-menu-item .anticon,
.ant-menu-submenu-title .anticon {
  font-size: 20px !important; /* 从18px增大到20px */
  margin-right: 14px !important;
}

.ant-menu-submenu .ant-menu-item .anticon {
  font-size: 18px !important; /* 从16px增大到18px */
  margin-right: 12px !important;
}

/* 标签页样式 */
.ant-tabs-tab {
  padding: 12px 20px !important;
  font-size: 18px !important; /* 从16px增大到18px */
  margin: 0 6px !important;
}

.ant-tabs-content-holder {
  padding: 20px 0 !important;
}

/* 表单项间距调整 */
.ant-form-item {
  margin-bottom: 20px !important;
}

.ant-form-item-label {
  padding-bottom: 6px !important;
}

.ant-form-item-label > label {
  font-size: 18px !important; /* 从16px增大到18px */
  font-weight: 500 !important;
  color: #1a1a1a !important;
}

/* 布局间距 */
.ant-layout-content {
  padding: 24px !important;
  background-color: #f5f5f5 !important;
}

.ant-layout-sider {
  min-height: 100vh;
  background-color: #001529 !important;
}

/* 统计数字显示 - 大幅增大 */
.ant-statistic-content {
  font-size: 34px !important; /* 从28px增大到34px */
  font-weight: 700 !important;
  color: #1890ff !important;
}

.ant-statistic-title {
  font-size: 18px !important; /* 从15px增大到18px */
  margin-bottom: 6px !important;
  color: #595959 !important;
  font-weight: 500 !important;
}

/* 描述列表样式 */
.ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: #fafafa !important;
  font-weight: 500 !important;
  font-size: 17px !important; /* 从15px增大到17px */
  padding: 16px 20px !important;
  color: #1a1a1a !important;
}

.ant-descriptions-bordered .ant-descriptions-item-content {
  padding: 16px 20px !important;
  font-size: 17px !important; /* 从15px增大到17px */
  color: #1a1a1a !important;
}

.ant-descriptions-item-label {
  font-weight: 500 !important;
  color: #1a1a1a !important;
}

/* 分割线样式 */
.ant-divider {
  margin: 24px 0 !important;
}

.ant-divider-horizontal.ant-divider-with-text {
  margin: 20px 0 !important;
}

.ant-divider-inner-text {
  font-size: 18px !important; /* 从16px增大到18px */
  font-weight: 600 !important;
  color: #1a1a1a !important;
}

/* 警告提示样式 */
.ant-alert {
  padding: 16px 20px !important;
  margin-bottom: 20px !important;
  border-radius: 8px !important;
}

.ant-alert-message {
  font-size: 18px !important; /* 从16px增大到18px */
  font-weight: 500 !important;
  color: #1a1a1a !important;
}

.ant-alert-description {
  font-size: 17px !important; /* 从15px增大到17px */
  margin-top: 8px !important;
  color: #595959 !important;
}

/* 模态框样式优化 */
.ant-modal-header {
  padding: 24px 28px !important;
  border-bottom: 1px solid #f0f0f0 !important;
}

.ant-modal-title {
  font-size: 24px !important; /* 从20px增大到24px */
  font-weight: 600 !important;
  color: #1a1a1a !important;
}

.ant-modal-body {
  padding: 24px 28px !important;
  font-size: 18px !important; /* 增大模态框内容字体 */
}

.ant-modal-footer {
  padding: 20px 28px !important;
  border-top: 1px solid #f0f0f0 !important;
}

/* 栅格系统间距优化 */
.ant-row {
  margin-left: -10px !important;
  margin-right: -10px !important;
}

.ant-col {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

/* 空间组件间距 */
.ant-space-item {
  margin-right: 10px !important;
}

.ant-space-compact .ant-space-item {
  margin-right: 8px !important;
}

/* 通用间距类 */
.compact-spacing > * {
  margin: 10px 0 !important;
}

.tight-spacing > * {
  margin: 6px 0 !important;
}

/* 重点突出样式 */
.highlight-text {
  color: #1890ff !important;
  font-weight: 600 !important;
}

.important-number {
  font-size: 28px !important; /* 从24px增大到28px */
  font-weight: 700 !important;
  color: #1890ff !important;
}

.section-title {
  font-size: 22px !important; /* 从18px增大到22px */
  font-weight: 600 !important;
  color: #1a1a1a !important;
  margin-bottom: 16px !important;
}

/* 状态标签样式 */
.ant-tag {
  margin: 0 8px 8px 0 !important;
  padding: 6px 12px !important;
  font-size: 16px !important; /* 从14px增大到16px */
  border-radius: 6px !important;
}

/* 面包屑导航 */
.ant-breadcrumb {
  margin-bottom: 20px !important;
  font-size: 17px !important; /* 从15px增大到17px */
}

/* 页面头部样式 */
.page-header {
  background: #fff;
  padding: 24px 28px !important;
  margin-bottom: 20px !important;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 32px !important; /* 从28px增大到32px */
  font-weight: 700 !important;
  color: #1890ff !important;
  margin: 0 !important;
}

/* 内容区域样式 */
.content-section {
  background: #fff;
  border-radius: 10px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  margin-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 选择器样式 */
.ant-select {
  font-size: 18px !important;
}

.ant-select-selector {
  height: 52px !important;
  padding: 12px 16px !important;
  font-size: 18px !important;
  display: flex !important;
  align-items: center !important;
}

.ant-select-selection-placeholder {
  line-height: 28px !important;
  font-size: 18px !important;
  color: #bfbfbf !important;
}

.ant-select-selection-item {
  line-height: 28px !important;
  font-size: 18px !important;
  color: #1a1a1a !important;
  display: flex !important;
  align-items: center !important;
}

.ant-select-arrow {
  font-size: 16px !important;
}

/* 下拉框样式优化 */
.ant-select-dropdown {
  border-radius: 8px !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12) !important;
}

.ant-select-item {
  padding: 8px 12px !important;
  font-size: 18px !important;
  line-height: 40px !important;
  min-height: 48px !important;
  display: flex !important;
  align-items: center !important;
}

.ant-select-item-option-selected {
  background-color: #e6f7ff !important;
  color: #1890ff !important;
  font-weight: 500 !important;
}

.ant-select-item-option-active {
  background-color: #f5f5f5 !important;
}

/* 日期选择器样式 */
.ant-picker {
  height: 50px !important;
  padding: 12px 16px !important;
  font-size: 18px !important;
}

/* 搜索框样式 */
.ant-input-search {
  font-size: 18px !important;
}

.ant-input-search .ant-input {
  height: 50px !important;
  font-size: 18px !important;
}

.ant-input-search .ant-input-search-button {
  height: 50px !important;
  font-size: 18px !important;
}

/* 文本域样式 */
.ant-input {
  font-size: 18px !important;
  color: #1a1a1a !important;
}

/* 复选框和单选框样式 */
.ant-checkbox-wrapper,
.ant-radio-wrapper {
  font-size: 18px !important;
  color: #1a1a1a !important;
}

/* 步骤条样式 */
.ant-steps-item-title {
  font-size: 18px !important;
  color: #1a1a1a !important;
}

.ant-steps-item-description {
  font-size: 17px !important;
  color: #595959 !important;
}

/* 空状态样式 */
.ant-empty-description {
  font-size: 18px !important;
  color: #595959 !important;
}

/* 加载状态样式 */
.ant-spin-text {
  font-size: 18px !important;
  color: #595959 !important;
}

/* 工具提示样式 */
.ant-tooltip-inner {
  font-size: 16px !important;
  color: #fff !important;
}

/* 下拉菜单样式 */
.ant-dropdown-menu-item {
  font-size: 18px !important;
  padding: 10px 20px !important;
  color: #1a1a1a !important;
}

/* 通知样式 */
.ant-notification-notice-message {
  font-size: 18px !important;
  color: #1a1a1a !important;
}

.ant-notification-notice-description {
  font-size: 17px !important;
  color: #595959 !important;
}

/* 消息样式 */
.ant-message-notice-content {
  font-size: 18px !important;
  line-height: 1.6 !important;
  color: #1a1a1a !important;
}

/* ================== 查询表单特定样式 ================== */
/* 查询条件表单控件统一样式 */
.ant-form-inline .ant-form-item {
  margin-bottom: 8px !important;
  margin-right: 16px !important;
}

.ant-form-inline .ant-form-item-label > label {
  font-size: 18px !important;
  font-weight: 500 !important;
  color: #262626 !important;
  line-height: 1.5 !important;
}

/* 输入框特定样式 - 修复placeholder显示问题 */
.ant-input {
  font-size: 18px !important;
  color: #262626 !important;
  line-height: 1.5 !important;
  padding: 8px 12px !important;
}

.ant-input::placeholder {
  font-size: 18px !important;
  color: #bfbfbf !important;
  font-weight: 400 !important;
  line-height: 1.5 !important;
  display: flex !important;
  align-items: center !important;
}

/* 时间选择器特定样式 - 增强中文显示 */
.ant-picker {
  height: 36px !important;
  font-size: 18px !important;
  border-radius: 6px !important;
  padding: 8px 12px !important;
}

.ant-picker-input > input {
  font-size: 18px !important;
  color: #262626 !important;
  line-height: 1.5 !important;
  height: 20px !important;
}

.ant-picker-input > input::placeholder {
  font-size: 18px !important;
  color: #bfbfbf !important;
  font-weight: 400 !important;
  line-height: 1.5 !important;
}

.ant-picker-range {
  height: 36px !important;
  padding: 8px 12px !important;
}

.ant-picker-range .ant-picker-input {
  font-size: 18px !important;
}

.ant-picker-range .ant-picker-range-separator {
  font-size: 18px !important;
  color: #8c8c8c !important;
  line-height: 1.5 !important;
}

/* 时间选择器下拉面板中文样式优化 */
.ant-picker-dropdown .ant-picker-panel {
  font-size: 18px !important;
}

.ant-picker-cell-inner {
  font-size: 18px !important;
  color: #262626 !important;
  line-height: 1.5 !important;
}

.ant-picker-time-panel-column > li {
  font-size: 18px !important;
  color: #262626 !important;
  line-height: 1.8 !important;
}

/* 年月选择器样式 */
.ant-picker-year-panel .ant-picker-cell-inner,
.ant-picker-month-panel .ant-picker-cell-inner {
  font-size: 18px !important;
  color: #262626 !important;
  line-height: 1.5 !important;
}

.ant-picker-header-view {
  font-size: 18px !important;
  color: #262626 !important;
}

.ant-picker-header-super-prev-btn,
.ant-picker-header-prev-btn,
.ant-picker-header-next-btn,
.ant-picker-header-super-next-btn {
  font-size: 18px !important;
}

/* 下拉选择器特定样式 */
.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  padding: 8px 12px !important;
}

.ant-select-selection-search-input {
  font-size: 18px !important;
  height: 20px !important;
}

.ant-select-selection-placeholder {
  font-size: 18px !important;
  color: #bfbfbf !important;
  line-height: 20px !important;
}

.ant-select-selection-item {
  font-size: 18px !important;
  line-height: 20px !important;
  color: #262626 !important;
}

/* 按钮特定样式 */
.ant-btn {
  font-size: 18px !important;
  font-weight: 500 !important;
  border-radius: 6px !important;
  line-height: 1.5 !important;
  padding: 8px 16px !important;
}

/* 确保查询表单中所有控件高度一致 */
.ant-form-inline .ant-input,
.ant-form-inline .ant-select-selector,
.ant-form-inline .ant-picker,
.ant-form-inline .ant-btn {
  height: 36px !important;
}

/* 优化间距 */
.ant-form-inline .ant-form-item {
  align-items: center !important;
}

.ant-form-inline .ant-form-item-control {
  line-height: 36px !important;
}

/* 下拉菜单样式 */
.ant-select-dropdown {
  font-size: 18px !important;
}

.ant-select-item-option-content {
  font-size: 18px !important;
  color: #262626 !important;
}

/* 时间选择器中文本地化样式 - 专门解决英文月份和星期显示问题 */

/* 强制时间选择器使用中文字体 */
.ant-picker-dropdown,
.ant-picker-dropdown * {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif !important;
}

/* 时间选择器面板整体优化 */
.ant-picker-dropdown .ant-picker-panel-container {
  font-size: 16px !important;
}

/* 强制覆盖英文月份显示 - 使用CSS content替换 */
.ant-picker-header-view button[title*="Jan"]:after { content: "1月"; }
.ant-picker-header-view button[title*="Feb"]:after { content: "2月"; }
.ant-picker-header-view button[title*="Mar"]:after { content: "3月"; }
.ant-picker-header-view button[title*="Apr"]:after { content: "4月"; }
.ant-picker-header-view button[title*="May"]:after { content: "5月"; }
.ant-picker-header-view button[title*="Jun"]:after { content: "6月"; }
.ant-picker-header-view button[title*="Jul"]:after { content: "7月"; }
.ant-picker-header-view button[title*="Aug"]:after { content: "8月"; }
.ant-picker-header-view button[title*="Sep"]:after { content: "9月"; }
.ant-picker-header-view button[title*="Oct"]:after { content: "10月"; }
.ant-picker-header-view button[title*="Nov"]:after { content: "11月"; }
.ant-picker-header-view button[title*="Dec"]:after { content: "12月"; }

/* 隐藏原始英文月份文字 */
.ant-picker-header-view button[title*="Jan"],
.ant-picker-header-view button[title*="Feb"],
.ant-picker-header-view button[title*="Mar"],
.ant-picker-header-view button[title*="Apr"],
.ant-picker-header-view button[title*="May"],
.ant-picker-header-view button[title*="Jun"],
.ant-picker-header-view button[title*="Jul"],
.ant-picker-header-view button[title*="Aug"],
.ant-picker-header-view button[title*="Sep"],
.ant-picker-header-view button[title*="Oct"],
.ant-picker-header-view button[title*="Nov"],
.ant-picker-header-view button[title*="Dec"] {
  font-size: 0 !important;
  position: relative !important;
}

.ant-picker-header-view button[title*="Jan"]:after,
.ant-picker-header-view button[title*="Feb"]:after,
.ant-picker-header-view button[title*="Mar"]:after,
.ant-picker-header-view button[title*="Apr"]:after,
.ant-picker-header-view button[title*="May"]:after,
.ant-picker-header-view button[title*="Jun"]:after,
.ant-picker-header-view button[title*="Jul"]:after,
.ant-picker-header-view button[title*="Aug"]:after,
.ant-picker-header-view button[title*="Sep"]:after,
.ant-picker-header-view button[title*="Oct"]:after,
.ant-picker-header-view button[title*="Nov"]:after,
.ant-picker-header-view button[title*="Dec"]:after {
  font-size: 16px !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  color: #262626 !important;
  font-weight: 500 !important;
}

/* 强制覆盖英文星期显示 */
.ant-picker-content thead th {
  font-size: 0 !important;
  position: relative !important;
  padding: 8px 0 !important;
}

.ant-picker-content thead th:nth-child(1):after { content: "日"; }
.ant-picker-content thead th:nth-child(2):after { content: "一"; }
.ant-picker-content thead th:nth-child(3):after { content: "二"; }
.ant-picker-content thead th:nth-child(4):after { content: "三"; }
.ant-picker-content thead th:nth-child(5):after { content: "四"; }
.ant-picker-content thead th:nth-child(6):after { content: "五"; }
.ant-picker-content thead th:nth-child(7):after { content: "六"; }

.ant-picker-content thead th:after {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #262626 !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

/* 月份选择器中文化 */
.ant-picker-month-panel .ant-picker-cell-inner {
  font-size: 16px !important;
  padding: 8px 12px !important;
  font-weight: 500 !important;
}

/* 年份选择器中文化 */
.ant-picker-year-panel .ant-picker-cell-inner {
  font-size: 16px !important;
  padding: 8px 12px !important;
  font-weight: 500 !important;
}

/* 日期单元格优化 */
.ant-picker-cell {
  padding: 4px !important;
}

.ant-picker-cell-inner {
  width: 28px !important;
  height: 28px !important;
  line-height: 26px !important;
  font-size: 16px !important;
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
}

/* 导航按钮优化 */
.ant-picker-header-super-prev-btn,
.ant-picker-header-prev-btn,
.ant-picker-header-next-btn,
.ant-picker-header-super-next-btn {
  width: 28px !important;
  height: 28px !important;
  line-height: 26px !important;
  border-radius: 4px !important;
  font-size: 16px !important;
}

/* 月份年份选择按钮 */
.ant-picker-header-view button {
  font-size: 16px !important;
  font-weight: 500 !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
  color: #262626 !important;
}

/* 时间范围选择器特殊样式 */
.ant-picker-range .ant-picker-active-bar {
  background: #1890ff !important;
}

.ant-picker-range .ant-picker-input {
  text-align: center !important;
}

/* 确保所有时间选择器的输入框样式统一 */
.ant-picker-input > input {
  font-size: 18px !important;
  text-align: center !important;
  color: #262626 !important;
  font-weight: 500 !important;
}

.ant-picker-input > input::placeholder {
  font-size: 18px !important;
  color: #bfbfbf !important;
  font-weight: 400 !important;
  text-align: center !important;
}

/* 范围选择器的分隔符 */
.ant-picker-range-separator {
  font-size: 16px !important;
  color: #bfbfbf !important;
  padding: 0 8px !important;
}

/* 输入框placeholder优化 - 解决覆盖问题 */
.ant-input::placeholder {
  color: #bfbfbf !important;
  font-weight: 400 !important;
  opacity: 1 !important;
  font-size: inherit !important;
}

/* 确保查询表单中的输入框placeholder正确显示 */
.ant-form-inline .ant-input::placeholder {
  color: #bfbfbf !important;
  font-weight: 400 !important;
  opacity: 0.85 !important;
  font-size: 18px !important;
}

/* 选择器placeholder优化 */
.ant-select-selection-placeholder {
  color: #bfbfbf !important;
  font-weight: 400 !important;
  opacity: 0.85 !important;
  font-size: 18px !important;
}

/* 针对查询整理模块的特殊优化 */
.ant-card .ant-form-inline .ant-input,
.ant-card .ant-form-inline .ant-select-selector,
.ant-card .ant-form-inline .ant-picker {
  height: 36px !important;
  font-size: 18px !important;
  line-height: 1.5 !important;
  padding: 6px 12px !important;
}

.ant-card .ant-form-inline .ant-picker-input > input {
  font-size: 18px !important;
  line-height: 1.5 !important;
  height: 24px !important;
}

.ant-card .ant-form-inline .ant-btn {
  height: 36px !important;
  font-size: 18px !important;
  line-height: 1.5 !important;
  padding: 0 16px !important;
}

/* 表单标签字体优化 */
.ant-card .ant-form-inline .ant-form-item-label > label {
  font-size: 18px !important;
  font-weight: 500 !important;
  color: #262626 !important;
}

/* 时间选择器下拉面板的中文化确保 */
.ant-picker-dropdown {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif !important;
}

/* 时间选择器面板内容中文化 */
.ant-picker-time-panel-column > li {
  font-size: 16px !important;
  padding: 4px 0 !important;
  text-align: center !important;
}

/* 修复输入框内容显示问题 */
.ant-input-affix-wrapper,
.ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {
  border-color: #d9d9d9 !important;
}

.ant-input-affix-wrapper-focused,
.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper:focus-within {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* 强制所有输入框样式完全一致 */
.ant-form-inline .ant-input,
.ant-form-inline .ant-input-affix-wrapper {
  height: 36px !important;
  font-size: 18px !important;
  line-height: 1.5 !important;
  padding: 6px 12px !important;
  border: 1px solid #d9d9d9 !important;
  border-radius: 6px !important;
  background-color: #fff !important;
  transition: all 0.2s ease !important;
}

.ant-form-inline .ant-input:focus,
.ant-form-inline .ant-input-affix-wrapper:focus,
.ant-form-inline .ant-input-affix-wrapper-focused {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
  outline: none !important;
}

/* 清除按钮样式统一 */
.ant-input-clear-icon {
  font-size: 14px !important;
  color: #bfbfbf !important;
  right: 8px !important;
}

.ant-input-clear-icon:hover {
  color: #8c8c8c !important;
}
