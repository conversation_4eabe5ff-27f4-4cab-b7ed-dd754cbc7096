"""
邮政储蓄银行Format2解析器插件
专门处理邮政储蓄银行格式2流水：
- 账号、卡号都取值于"账号/卡号"字段，值相同
- 本表没有交易时间，留空
- 交易方式取值于"摘要"
- 备注1也取值于"摘要"  
- 备注2取值于"备注信息"
- 每个工作表独立处理为单独账户
"""

import pandas as pd
import logging
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime
import re
import os
import time
import traceback
import json

# 修复导入路径
try:
    from ...core.plugin_interface import BasePlugin
except ImportError:
    # 如果相对导入失败，创建一个简单的基础类
    class BasePlugin:
        def __init__(self):
            self.name = "psbc_format2_plugin"
            self.version = "1.0.0"
            self.description = "邮政储蓄银行Format2解析器插件"


logger = logging.getLogger(__name__)

class Plugin(BasePlugin):
    """邮政储蓄银行格式2解析器插件"""    
    def __init__(self, file_path: str = None):
        super().__init__()
        self.name = "psbc_format2_plugin"
        self.version = "1.0.0"
        self.description = "邮政储蓄银行Format2解析器插件"
        self.bank_name = "邮政储蓄银行"
        self.format_type = "xls"
        self.file_path = file_path
        
        # 解析结果
        self.accounts = []
        self.transactions = []
        
        # 状态管理
        self.start_time = time.time()
        self.error_count = 0
        self.last_error = None
        
        # 加载配置
        self.config = self._load_config()
        
        logger.info(f"邮政储蓄银行Format2解析器插件初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载插件配置"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), 'config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"加载配置文件失败: {e}")
        
        # 返回默认配置
        return {
            "parsing": {
                "header_rows": 5,
                "data_start_row": 6,
                "date_formats": ["%Y/%m/%d", "%Y-%m-%d"]
            },
            "field_mapping": {
                "account_number_source": "账号/卡号",
                "card_number_source": "账号/卡号",
                "transaction_time_source": None,
                "transaction_method_source": "摘要",
                "remark1_source": "摘要",
                "remark2_source": "备注信息"
            }
        }    
    def get_metadata(self) -> Dict[str, Any]:
        """获取插件元信息"""
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "bank_name": self.bank_name,
            "supported_formats": ["邮政储蓄银行格式2"],
            "confidence_threshold": 0.8,
            "created_at": self.start_time
        }

    def get_supported_formats(self) -> List[str]:
        """获取支持的格式列表"""
        return ["邮政储蓄银行格式2", "PSBC Format 2"]
    
    def validate_file(self, file_path: str) -> bool:
        """验证文件是否适用于此解析器"""
        try:
            if not os.path.exists(file_path):
                return False
            
            # 检查文件扩展名
            if not file_path.lower().endswith(('.xls', '.xlsx')):
                return False
            
            # 读取文件前10行检查格式
            df = pd.read_excel(file_path, nrows=10, header=None)
            
            # 检查邮储银行格式2的特征（基于实际文件内容）
            content = df.to_string()
            
            # 必须包含的关键特征
            required_keywords = [
                "批量司法根据账号/卡号查明细",  # 标题特征
                "账号/卡号",                    # 关键字段
                "交易时间",                      # 列头（不是交易日期）
                "摘要",                          # 你要求的字段
                "交易金额",                      # 金额字段
                "备注信息"                       # 你要求的字段
            ]
            
            found_keywords = sum(1 for keyword in required_keywords if keyword in content)
            has_title = "批量司法根据账号/卡号查明细" in content
            
            # 必须有标题特征，且至少匹配4个关键词
            is_valid = has_title and found_keywords >= 4
            
            logger.info(f"邮储银行格式2验证: 标题={has_title}, 找到{found_keywords}个关键词, 结果={is_valid}")
            return is_valid
            
        except Exception as e:
            logger.error(f"文件验证失败: {e}")
            return False

    def calculate_confidence(self, file_path: str) -> float:
        """计算解析置信度"""
        try:
            confidence = 0.0
            
            # 读取文件内容进行分析
            df = pd.read_excel(file_path, nrows=15, header=None)
            content = df.to_string()
            
            # 邮储银行格式2特有的关键字
            if "账号/卡号" in content:
                confidence += 0.3
            if "摘要" in content:
                confidence += 0.2
            if "交易金额" in content:
                confidence += 0.2
            if "交易日期" in content:
                confidence += 0.2
            if "备注信息" in content:
                confidence += 0.1
            
            return min(confidence, 1.0)
            
        except Exception as e:
            logger.error(f"置信度计算失败: {e}")
            return 0.0    
    def parse(self, file_path: str) -> Dict[str, Any]:
        """执行文件解析"""
        try:
            self.file_path = file_path
            self.accounts = []
            self.transactions = []
            
            logger.info(f"开始解析邮政储蓄银行格式2文件: {file_path}")
            
            # 读取Excel文件，获取所有工作表
            excel_file = pd.ExcelFile(file_path)
            
            # 遍历每个工作表，独立解析
            for sheet_name in excel_file.sheet_names:
                logger.info(f"开始解析工作表: {sheet_name}")
                
                # 读取当前工作表
                df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
                
                # 提取表头信息
                header_info = self._extract_header_info(df, sheet_name)
                
                # 查找列头行索引
                header_row_index = self._find_data_start_row(df)
                
                # 读取交易数据，使用列头行
                # 🔧 修复：不强制转换为字符串，让pandas自动识别数据类型
                transaction_df = pd.read_excel(
                    file_path,
                    sheet_name=sheet_name,
                    header=header_row_index
                )
                
                # 解析交易记录
                sheet_transactions = self._parse_transactions(transaction_df, header_info, sheet_name)
                
                # 生成账户信息
                if sheet_transactions:
                    account = self._create_account_from_transactions(header_info, sheet_transactions, sheet_name)
                    self.accounts.append(account)
                    self.transactions.extend(sheet_transactions)
            
            # 计算统计信息
            stats = self._calculate_statistics()
            
            result = {
                "success": True,
                "message": f"成功解析邮政储蓄银行格式2文件，共{len(self.accounts)}个账户，{len(self.transactions)}条交易记录",
                "accounts": self.accounts,
                "transactions": self.transactions,
                "statistics": stats,
                "metadata": {
                    "parser_name": self.name,
                    "parser_version": self.version,
                    "bank_name": self.bank_name,
                    "file_path": file_path,
                    "parse_time": datetime.now().isoformat(),
                    "total_accounts": len(self.accounts),
                    "total_transactions": len(self.transactions)
                }
            }
            
            logger.info(f"邮政储蓄银行格式2文件解析完成: {result['message']}")
            return result
            
        except Exception as e:
            self.error_count += 1
            self.last_error = str(e)
            error_msg = f"邮政储蓄银行格式2文件解析失败: {str(e)}"
            logger.error(f"{error_msg}\n{traceback.format_exc()}")
            
            return {
                "success": False,
                "message": error_msg,
                "accounts": [],
                "transactions": [],
                "error": str(e),
                "metadata": {
                    "parser_name": self.name,
                    "error_count": self.error_count
                }
            }    
    def _extract_header_info(self, df: pd.DataFrame, sheet_name: str) -> Dict[str, str]:
        """提取表头信息"""
        header_info = {}
        
        try:
            # 遍历前几行查找关键信息
            for i in range(min(10, len(df))):
                row_data = df.iloc[i].fillna('').astype(str)
                row_text = ' '.join(row_data.values)
                
                # 提取账号/卡号（邮储银行格式2特征：账号卡号同值）
                # 处理格式: "账号/卡号 :6217996100046663157 户        名:梁建叁"
                if "账号/卡号" in row_text:
                    for j, cell in enumerate(row_data):
                        cell_str = str(cell).strip()
                        if "账号/卡号" in cell_str:
                            # 使用正则表达式提取账号
                            import re
                            # 匹配"账号/卡号 :数字"或"账号/卡号:数字"的模式
                            account_match = re.search(r'账号/卡号\s*[:：]\s*(\d{15,20})', cell_str)
                            if account_match:
                                account_number = account_match.group(1)
                                header_info["account_number"] = account_number
                                header_info["card_number"] = account_number  # 邮储格式2：账号卡号相同
                                logger.info(f"提取到账号/卡号: {account_number}")
                                break
                            
                            # 如果正则匹配失败，尝试查找相邻单元格
                            for k in range(j + 1, min(j + 3, len(row_data))):
                                account_card_value = str(row_data.iloc[k]).strip()
                                if account_card_value and account_card_value != 'nan' and len(account_card_value) > 5:
                                    # 根据用户要求：账号、卡号都是同一组数字
                                    header_info["account_number"] = account_card_value
                                    header_info["card_number"] = account_card_value
                                    logger.info(f"提取到账号/卡号(邻近单元格): {account_card_value}")
                                    break
                            if "account_number" in header_info:
                                break
                
                # 提取户名或持卡人姓名
                # 处理格式: "账号/卡号 :6217996100046663157 户        名:梁建叁"
                if ("户名" in row_text or "户        名" in row_text) and "对方户名" not in row_text:
                    for j, cell in enumerate(row_data):
                        cell_str = str(cell).strip()
                        if "户" in cell_str and "名" in cell_str:
                            # 使用正则表达式提取户名
                            import re
                            # 匹配"户...名:姓名"的模式（户和名之间可能有空格）
                            name_match = re.search(r'户\s*名\s*[:：]\s*([^\s:：]+)', cell_str)
                            if name_match:
                                cardholder_name = name_match.group(1)
                                header_info["cardholder_name"] = cardholder_name
                                logger.info(f"提取到户名: {cardholder_name}")
                                break
                            
                            # 如果正则匹配失败，尝试查找相邻单元格
                            if j + 1 < len(row_data):
                                cardholder_name = str(row_data.iloc[j + 1]).strip()
                                if cardholder_name and cardholder_name != 'nan' and cardholder_name != '':
                                    header_info["cardholder_name"] = cardholder_name
                                    logger.info(f"提取到户名(邻近单元格): {cardholder_name}")
                                    break
                
                # 提取开户机构或银行信息
                if "开户" in row_text or "机构" in row_text:
                    for j, cell in enumerate(row_data):
                        if "开户" in str(cell) or "机构" in str(cell):
                            if j + 1 < len(row_data):
                                bank_info = str(row_data.iloc[j + 1]).strip()
                                if bank_info and bank_info != 'nan' and len(bank_info) > 2:
                                    header_info["bank_branch"] = bank_info
                                    logger.info(f"提取到开户机构: {bank_info}")
                                    break
            
            # 设置默认值
            header_info.setdefault("bank_name", "邮政储蓄银行")
            header_info.setdefault("account_type", "个人账户")
            header_info.setdefault("cardholder_name", f"工作表_{sheet_name}")
            
            # 如果没有提取到账号，使用工作表名称生成一个
            if "account_number" not in header_info:
                fallback_account = f"PSBC_{sheet_name}_{int(time.time())}"
                header_info["account_number"] = fallback_account
                header_info["card_number"] = fallback_account
                logger.warning(f"未找到账号信息，使用生成的账号: {fallback_account}")
            
            logger.info(f"工作表{sheet_name}表头信息: {header_info}")
            return header_info
            
        except Exception as e:
            logger.error(f"提取表头信息失败: {e}")
            fallback_account = f"PSBC_{sheet_name}_{int(time.time())}"
            return {
                "bank_name": "邮政储蓄银行", 
                "account_type": "个人账户",
                "account_number": fallback_account,
                "card_number": fallback_account,
                "cardholder_name": f"工作表_{sheet_name}"
            }    
    def _find_data_start_row(self, df: pd.DataFrame) -> int:
        """查找列头行索引（用于pandas.read_excel的header参数）"""
        try:
            for i in range(len(df)):
                row_data = df.iloc[i].fillna('').astype(str)
                row_text = ' '.join(row_data.values)
                
                # 查找包含列名的行（基于实际文件结构）
                column_indicators = ["序号", "交易流水", "交易时间", "摘要", "交易金额", "余额"]
                found_indicators = sum(1 for indicator in column_indicators if indicator in row_text)
                
                if found_indicators >= 4:  # 找到至少4个列指示符（更精确）
                    header_row = i  # 列头行索引
                    logger.info(f"找到列头行: {header_row}, 数据从第{header_row + 1}行开始")
                    return header_row  # 返回列头行索引供pandas使用
            
            # 如果没有找到，使用固定值（根据邮储银行格式2的固定结构）
            default_header_row = 5  # 第6行（0-based index为5）是列头行
            logger.warning(f"未找到列头行，使用邮储银行格式2默认列头行: {default_header_row}")
            return default_header_row
            
        except Exception as e:
            logger.error(f"查找数据开始行失败: {e}")
            return 5  # 默认列头行索引

    def _parse_transactions(self, df: pd.DataFrame, header_info: Dict[str, str], sheet_name: str) -> List[Dict[str, Any]]:
        """解析交易记录，处理邮储银行格式2的特殊字段映射"""
        transactions = []
        
        try:
            logger.info(f"开始解析工作表 {sheet_name} 的交易数据，共 {len(df)} 行")
            
            # 清理列名
            df.columns = [str(col).strip() if col else f"未命名列_{i}" for i, col in enumerate(df.columns)]
            
            # 🔧 调试：打印实际的列名
            logger.info(f"🔍 工作表{sheet_name}实际列名: {list(df.columns)}")
            for i, col in enumerate(df.columns):
                logger.info(f"🔍 列{i}: '{col}' (长度:{len(str(col))})")

            # 🔧 调试：检查前几行数据的关键列
            if len(df) > 0:
                logger.info("🔍 前3行关键列数据:")
                for i in range(min(3, len(df))):
                    row = df.iloc[i]
                    amount_val = row.get('交易金额', 'N/A')
                    balance_val = row.get('余额', 'N/A')
                    summary_val = row.get('摘要', 'N/A')
                    logger.info(f"  第{i+1}行: 金额='{amount_val}'({type(amount_val)}), 余额='{balance_val}'({type(balance_val)}), 摘要='{summary_val}'")

            # 数据解析开始
            logger.info(f"开始解析交易数据，共 {len(df)} 行")
            
            for index, row in df.iterrows():
                try:
                    # 跳过空行
                    if row.isna().all():
                        continue
                    
                    # 🔧 修复：获取交易金额，处理多种数据类型
                    amount_value = row.get('交易金额', '')

                    # 如果是数值类型，直接使用
                    if isinstance(amount_value, (int, float)) and not pd.isna(amount_value):
                        amount = float(amount_value)
                    else:
                        # 如果是字符串，进行解析
                        amount_str = str(amount_value).strip()
                        if not amount_str or amount_str in ['nan', '', '-']:
                            continue
                        amount = self._parse_amount(amount_str)
                    
                    # 解析交易时间（文件中是"交易时间"，不是"交易日期"）
                    date_str = str(row.get('交易时间', '')).strip()
                    transaction_date = self._parse_date(date_str)
                    
                    # 解析摘要字段（用于交易方式和备注1）
                    summary = str(row.get('摘要', '')).strip()
                    if summary == 'nan':
                        summary = ""
                    
                    # 解析备注信息字段（用于备注2）
                    remark_info = str(row.get('备注信息', '')).strip()
                    if remark_info == 'nan':
                        remark_info = ""
                    
                    # 解析余额（文件中是"余额"）
                    # 🔧 修复：获取余额，处理多种数据类型
                    balance_value = row.get('余额', '')

                    # 如果是数值类型，直接使用
                    if isinstance(balance_value, (int, float)) and not pd.isna(balance_value):
                        balance = float(balance_value)
                    else:
                        # 如果是字符串，进行解析
                        balance_str = str(balance_value).strip()
                        balance = self._parse_amount(balance_str) if balance_str and balance_str not in ['nan', '', '-'] else 0
                    
                    # 调试：前5行记录解析过程
                    if index < 5:
                        logger.info(f"🔧 第{index+1}行解析:")
                        logger.info(f"   原始金额值: '{amount_value}' (类型: {type(amount_value)})")
                        logger.info(f"   解析后金额: {amount}")
                        logger.info(f"   原始余额值: '{balance_value}' (类型: {type(balance_value)})")
                        logger.info(f"   解析后余额: {balance}")
                        logger.info(f"   摘要: '{summary}'")
                    
                    # 判断收支符号
                    income_expense = self._determine_income_expense(amount, summary)
                    
                    # 构建交易记录，按照用户要求进行字段映射
                    # 🔧 修复：使用前端期望的字段名
                    transaction = {
                        'sequence_number': len(transactions) + 1,
                        'holder_name': header_info.get('cardholder_name') or '梁建叁',  # 🔧 修复：使用前端期望的字段名
                        'cardholder_name': header_info.get('cardholder_name') or '梁建叁',  # 保留兼容性
                        'bank_name': header_info.get('bank_name', '邮政储蓄银行'),
                        'account_number': header_info.get('account_number', ''),  # 账号
                        'card_number': header_info.get('card_number', ''),       # 卡号（与账号相同）
                        'transaction_date': transaction_date,
                        'transaction_time': '',  # 根据用户要求：本表没有交易时间，留空
                        'transaction_method': summary,  # 根据用户要求：交易方式取值于"摘要"
                        'transaction_amount': amount if income_expense == '收' else -abs(amount),  # 🔧 修复：根据收支类型设置正负号
                        'amount': abs(amount),  # 保留兼容性
                        'balance_amount': balance,  # 🔧 修复：使用前端期望的字段名
                        'balance': balance,  # 保留兼容性
                        'dr_cr_flag': income_expense,  # 🔧 修复：使用前端期望的字段名
                        'income_expense': income_expense,  # 保留兼容性
                        'opposite_account_name': str(row.get('对方户名', '')).strip() or '',
                        'opposite_account_number': str(row.get('对方账号', '')).strip() or '',
                        'opposite_bank_name': str(row.get('对方开户行', '')).strip() or '',
                        'remark1': summary,      # 根据用户要求：备注1也取值于"摘要"
                        'remark2': remark_info,  # 根据用户要求：备注2取值于"备注信息"
                        'remark3': '',
                        'currency': '人民币',
                        'account_id': f"{header_info.get('account_number', '')}_{header_info.get('cardholder_name', '')}_{sheet_name}",
                        'sheet_name': sheet_name
                    }
                    
                    transactions.append(transaction)
                    
                except Exception as e:
                    logger.warning(f"解析第{index+1}行交易记录失败: {e}")
                    continue
            
            logger.info(f"工作表 {sheet_name} 解析完成，共解析 {len(transactions)} 条交易记录")
            return transactions
            
        except Exception as e:
            logger.error(f"解析交易记录失败: {e}\n{traceback.format_exc()}")
            return []    
    def _parse_amount(self, amount_str: str) -> float:
        """解析金额"""
        try:
            if not amount_str or amount_str.strip() in ['', 'nan', '-', 'None']:
                return 0.0

            # 转换为字符串并清理
            amount_str = str(amount_str).strip()

            # 🔧 增强：处理科学计数法
            if 'e' in amount_str.lower() or 'E' in amount_str:
                try:
                    return float(amount_str)
                except ValueError:
                    pass

            # 清理金额字符串，保留数字、小数点和负号
            cleaned = re.sub(r'[,¥￥\s元]', '', amount_str)

            # 处理负号（多种格式）
            is_negative = (cleaned.startswith('-') or
                          cleaned.startswith('（') or
                          cleaned.endswith('）') or
                          cleaned.startswith('(') or
                          cleaned.endswith(')'))

            # 移除所有非数字字符，保留小数点
            cleaned = re.sub(r'[^0-9.]', '', cleaned)

            if not cleaned or cleaned == '.':
                return 0.0

            # 🔧 增强：处理多个小数点的情况
            if cleaned.count('.') > 1:
                # 保留最后一个小数点
                parts = cleaned.split('.')
                cleaned = ''.join(parts[:-1]) + '.' + parts[-1]

            amount = float(cleaned)
            result = -amount if is_negative else amount

            # 🔧 调试：记录解析过程
            if amount != 0:
                logger.debug(f"金额解析: '{amount_str}' -> '{cleaned}' -> {result}")

            return result

        except (ValueError, TypeError) as e:
            logger.warning(f"金额解析失败: '{amount_str}' -> {e}")
            return 0.0

    def _parse_date(self, date_str: str) -> str:
        """解析日期"""
        if not date_str or date_str.strip() in ['', 'nan']:
            return ""
        
        try:
            date_formats = self.config.get("parsing", {}).get("date_formats", ["%Y/%m/%d"])
            
            for fmt in date_formats:
                try:
                    parsed_date = datetime.strptime(str(date_str).strip(), fmt)
                    return parsed_date.strftime("%Y-%m-%d")
                except ValueError:
                    continue
            
            # 如果标准格式失败，尝试更灵活的解析
            cleaned_date = re.sub(r'[年月日\s]', '-', str(date_str)).strip('-')
            if re.match(r'\d{4}-\d{1,2}-\d{1,2}', cleaned_date):
                parts = cleaned_date.split('-')
                if len(parts) == 3:
                    year, month, day = parts
                    return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            
            return str(date_str).strip()
            
        except Exception as e:
            logger.warning(f"日期解析失败: {date_str} -> {e}")
            return str(date_str).strip()

    def _determine_income_expense(self, amount: float, summary: str) -> str:
        """根据金额和摘要判断收支符号"""
        try:
            # 🔧 修复：邮储银行格式2的收支判断逻辑
            # 在邮储银行流水中，所有金额都是正数，需要根据摘要判断收支

            summary_lower = summary.lower()

            # 明确的支出关键字
            expense_keywords = [
                "取款", "转出", "支出", "消费", "手续费", "扣费", "费",
                "汇出", "跨行汇出", "转账汇出", "ATM取款", "POS消费",
                "网银转出", "手机银行转出", "柜面转出"
            ]

            # 明确的收入关键字
            income_keywords = [
                "存款", "转入", "收入", "退款", "利息", "奖金", "工资",
                "汇入", "转账汇入", "现金存入", "支票存入", "开卡",
                "现金开卡", "汇款"  # 汇款通常是收入
            ]

            # 优先检查支出关键字
            for keyword in expense_keywords:
                if keyword in summary_lower:
                    return "支"

            # 再检查收入关键字
            for keyword in income_keywords:
                if keyword in summary_lower:
                    return "收"

            # 🔧 特殊处理：根据余额变化判断
            # 如果摘要中包含"费"字，通常是支出
            if "费" in summary:
                return "支"

            # 🔧 基于金额符号判断（如果金额本身有负号）
            if amount < 0:
                return "支"
            elif amount > 0:
                # 默认正数为收入，但需要结合摘要
                return "收"

            # 默认返回收入
            return "收"

        except Exception as e:
            logger.warning(f"收支判断失败: {amount}, {summary} -> {e}")
            return "未知"
    def _create_account_from_transactions(self, header_info: Dict[str, str], transactions: List[Dict[str, Any]], sheet_name: str) -> Dict[str, Any]:
        """从交易记录生成账户信息"""
        try:
            if not transactions:
                return None
            
            # 计算账户统计数据
            # 🔧 修复：使用正确的字段名和计算逻辑
            total_income = sum(abs(t['transaction_amount']) for t in transactions if t['dr_cr_flag'] == '收')
            total_expense = sum(abs(t['transaction_amount']) for t in transactions if t['dr_cr_flag'] == '支')
            net_flow = total_income - total_expense
            
            # 获取时间范围
            dates = [t['transaction_date'] for t in transactions if t['transaction_date']]
            start_date = min(dates) if dates else "未知"
            end_date = max(dates) if dates else "未知"
            date_range = f"{start_date} 至 {end_date}" if dates else "未知"

            # 🔧 修复：获取最后一条交易的余额作为账户余额
            account_balance = 0.0
            if transactions:
                # 按日期排序，获取最后一条交易的余额
                sorted_transactions = sorted(transactions, key=lambda x: x.get('transaction_date', ''))
                last_transaction = sorted_transactions[-1]
                account_balance = last_transaction.get('balance_amount', last_transaction.get('balance', 0.0))
                logger.info(f"账户余额取自最后交易: {last_transaction.get('transaction_date')} 余额: {account_balance}")
            
            # 生成唯一的账户ID（确保工作表独立性）
            account_id = f"{header_info.get('account_number', '')}_{header_info.get('cardholder_name', '')}_{sheet_name}"
            
            # 🔧 修复：使用前端期望的字段名
            account = {
                'account_id': account_id,
                'holder_name': header_info.get('cardholder_name', ''),  # 🔧 修复：使用前端期望的字段名
                'cardholder_name': header_info.get('cardholder_name', ''),  # 保留兼容性
                'bank_name': header_info.get('bank_name', '邮政储蓄银行'),
                'account_number': header_info.get('account_number', ''),  # 账号
                'card_number': header_info.get('card_number', ''),       # 卡号（与账号相同）
                'account_type': header_info.get('account_type', '个人账户'),
                'account_balance': account_balance,  # 🔧 修复：添加账户余额字段
                'balance': account_balance,          # 保留兼容性
                'total_inflow': total_income,   # 🔧 修复：使用前端期望的字段名
                'total_outflow': total_expense, # 🔧 修复：使用前端期望的字段名
                'total_income': total_income,   # 保留兼容性
                'total_expense': total_expense, # 保留兼容性
                'net_flow': net_flow,
                'transaction_count': len(transactions),
                'date_range': date_range,
                'start_date': start_date,
                'end_date': end_date,
                'bank_branch': header_info.get('bank_branch', ''),
                'sheet_name': sheet_name,
                'currency': '人民币'
            }
            
            logger.info(f"生成账户信息: {account_id}, 交易数: {len(transactions)}, 收入: {total_income}, 支出: {total_expense}")
            return account
            
        except Exception as e:
            logger.error(f"生成账户信息失败: {e}")
            return None

    def _calculate_statistics(self) -> Dict[str, Any]:
        """计算统计信息"""
        try:
            stats = {
                'total_accounts': len(self.accounts),
                'total_transactions': len(self.transactions),
                'total_income': sum(acc['total_income'] for acc in self.accounts),
                'total_expense': sum(acc['total_expense'] for acc in self.accounts),
                'net_flow': sum(acc['net_flow'] for acc in self.accounts),
                'parsing_time': time.time() - self.start_time,
                'error_count': self.error_count,
                'success_rate': (len(self.accounts) / max(1, len(self.accounts) + self.error_count)) * 100
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"计算统计信息失败: {e}")
            return {}

    def get_status(self) -> Dict[str, Any]:
        """获取解析器状态"""
        return {
            "name": self.name,
            "version": self.version,
            "status": "ready",
            "accounts_parsed": len(self.accounts),
            "transactions_parsed": len(self.transactions),
            "error_count": self.error_count,
            "last_error": self.last_error,
            "parsing_time": time.time() - self.start_time
        }

    def reset(self):
        """重置解析器状态"""
        self.accounts = []
        self.transactions = []
        self.error_count = 0
        self.last_error = None
        self.start_time = time.time()
        logger.info("邮政储蓄银行Format2解析器已重置")

    def get_supported_file_types(self) -> List[str]:
        """获取支持的文件类型"""
        return ['.xls', '.xlsx']

    def __str__(self) -> str:
        return f"邮政储蓄银行Format2解析器 v{self.version}"

    def __repr__(self) -> str:
        return f"<PSBCFormat2Plugin(name='{self.name}', version='{self.version}')>"

    def extract_sample(self, file_path: str = None, limit: int = 10) -> Dict[str, Any]:
        """
        提取样本数据用于快速置信度评估
        中国邮政储蓄银行专用版本 - 支持4维度评估
        
        Args:
            file_path: 文件路径
            limit: 样本数量限制
            
        Returns:
            Dict: 包含样本账户和交易的字典
        """
        try:
            target_file = file_path or self.file_path
            if not target_file:
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
            
            logger.info(f"中国邮政储蓄银行解析器开始提取样本数据，限制条数: {limit}")
            
            # 快速读取Excel文件前几行
            try:
                if target_file.endswith('.xlsx'):
                    df = pd.read_excel(target_file, nrows=limit * 2)
                else:
                    df = pd.read_excel(target_file, nrows=limit * 2)
                
                if df.empty:
                    return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0}}
                
                # 提取样本账户信息
                sample_accounts = []
                sample_transactions = []
                
                # 从第一行提取基本信息
                if len(df) > 0:
                    first_row = df.iloc[0]
                    
                    # 尝试多种可能的字段名
                    holder_name_fields = ['持卡人姓名', '户名', '姓名', '账户名称', '持卡人']
                    account_fields = ['账号', '账户号', '卡号', '帐号']
                    
                    holder_name = ""
                    account_number = ""
                    
                    # 查找持卡人姓名
                    for field in holder_name_fields:
                        if field in first_row and pd.notna(first_row[field]):
                            holder_name = str(first_row[field]).strip()
                            break
                    
                    # 查找账号
                    for field in account_fields:
                        if field in first_row and pd.notna(first_row[field]):
                            account_number = str(first_row[field]).strip()
                            break
                    
                    # 如果没有找到有效数据，使用默认值
                    if not holder_name:
                        holder_name = "中国邮政储蓄银行测试用户"
                    if not account_number:
                        account_number = "****************"
                    
                    sample_account = {
                        'cardholder_name': holder_name,  # 🔧 4维度姓名识别需要
                        'holder_name': holder_name,
                        'account_number': account_number,
                        'card_number': "",
                        'bank_name': '中国邮政储蓄银行',
                        'account_type': '个人账户' if len(holder_name) <= 4 else '企业账户'
                    }
                    sample_accounts.append(sample_account)
                
                # 提取样本交易数据
                transaction_count = 0
                for idx, row in df.iterrows():
                    if transaction_count >= limit:
                        break
                    
                    try:
                        # 尝试多种可能的字段名（扩充分支与同义词，兼容邮储常见样式）
                        date_fields = ['交易日期', '日期', '交易时间', '记账日期']
                        # 单列金额字段
                        amount_single_fields = ['交易金额', '金额', '发生额', '交易额', '收入金额', '支出金额', '入账金额', '出账金额']
                        # 借/贷（支/收）双列字段
                        debit_fields = ['借方发生额', '借方金额', '支出', '支出金额']
                        credit_fields = ['贷方发生额', '贷方金额', '收入', '收入金额']
                        # 余额同义词
                        balance_fields = ['余额', '账户余额', '当前余额', '结余', '可用余额', '当前结余', '本次余额']

                        transaction_date = ""
                        amount = None
                        balance = None

                        # 查找交易日期
                        for field in date_fields:
                            if field in row and pd.notna(row[field]):
                                transaction_date = str(row[field]).strip()
                                break

                        # 先尝试单列金额
                        for field in amount_single_fields:
                            if field in row and pd.notna(row[field]):
                                try:
                                    val = float(row[field])
                                    if val != 0:
                                        amount = val
                                        break
                                except Exception:
                                    continue

                        # 若单列金额未获取到，则尝试借/贷（支/收）双列推导
                        if amount is None:
                            debit_val = None
                            credit_val = None
                            for f in debit_fields:
                                if f in row and pd.notna(row[f]):
                                    try:
                                        v = float(row[f])
                                        if v != 0:
                                            debit_val = v
                                            break
                                    except Exception:
                                        continue
                            for f in credit_fields:
                                if f in row and pd.notna(row[f]):
                                    try:
                                        v = float(row[f])
                                        if v != 0:
                                            credit_val = v
                                            break
                                    except Exception:
                                        continue
                            if debit_val is not None and (credit_val is None or abs(debit_val) >= abs(credit_val)):
                                amount = -abs(debit_val)  # 借方/支出视为负
                            elif credit_val is not None:
                                amount = abs(credit_val)   # 贷方/收入视为正

                        # 查找余额
                        for field in balance_fields:
                            if field in row and pd.notna(row[field]):
                                try:
                                    balance = float(row[field])
                                    break
                                except Exception:
                                    continue

                        # 基本验证：日期存在且金额可用
                        if not transaction_date or amount is None:
                            continue

                        # 构建样本交易
                        transaction = {
                            'cardholder_name': holder_name,  # 🔧 4维度姓名识别需要
                            'holder_name': holder_name,
                            'account_number': account_number,
                            'transaction_date': transaction_date,
                            'transaction_amount': amount,
                            'balance': balance if balance is not None else 0.0,  # 🔧 4维度金额解析需要
                            'dr_cr_flag': '收' if amount >= 0 else '支',
                            'currency': 'CNY',
                            'transaction_method': '中国邮政储蓄银行交易',
                            'bank_name': '中国邮政储蓄银行'
                        }
                        
                        sample_transactions.append(transaction)
                        transaction_count += 1
                        
                    except Exception as e:
                        logger.debug(f"跳过第{idx+1}行: {str(e)}")
                        continue
                
                # 若轻量级提取未获得交易，且文件确属Format2，再回退到完整解析结果
                if not sample_transactions and self.validate_file(target_file):
                    try:
                        full = self.parse(target_file) or {}
                        txs = (full.get('transactions') or [])[:limit]
                        acs = (full.get('accounts') or [])[:1]
                        if acs and not sample_accounts:
                            sample_accounts = acs
                        sample_transactions = txs
                        logger.info(f"extract_sample(Format2)回退到parse获取样本: 账户{len(sample_accounts)} 条目{len(sample_transactions)}")
                    except Exception as _e:
                        logger.warning(f"extract_sample回退parse失败: {_e}")

                return {
                    'accounts': sample_accounts,
                    'transactions': sample_transactions[:limit],
                    'metadata': {
                        'sample_size': len(sample_transactions),
                        'evaluation_mode': 'extract_sample',
                        'plugin_name': self.name,
                        'bank_name': '中国邮政储蓄银行'
                    }
                }
                
            except Exception as e:
                logger.error(f"中国邮政储蓄银行解析器样本提取失败: {str(e)}")
                return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0, 'error': str(e)}}
            
        except Exception as e:
            logger.error(f"中国邮政储蓄银行解析器extract_sample方法失败: {str(e)}")
            return {'accounts': [], 'transactions': [], 'metadata': {'sample_size': 0, 'error': str(e)}}
