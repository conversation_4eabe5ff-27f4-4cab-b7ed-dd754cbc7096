"""
问题线索相关的数据模型
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class AttachmentInfo(BaseModel):
    """附件信息模型"""
    name: str = Field(..., description="文件名")
    type: str = Field(..., description="文件类型(pdf/image)")
    url: Optional[str] = Field(None, description="文件URL")
    size: Optional[int] = Field(None, description="文件大小(字节)")


class ClueCreate(BaseModel):
    """创建问题线索的请求模型"""
    project_id: str = Field(..., description="项目ID")
    clue_number: str = Field(..., description="线索编号")
    subject_name: str = Field(..., description="被反映人姓名")
    subject_position: Optional[str] = Field(None, description="岗位职务")
    source: Optional[str] = Field(None, description="线索来源")
    content: str = Field(..., description="线索内容")
    attachments: Optional[List[AttachmentInfo]] = Field(default=[], description="附件列表")
    created_by: Optional[str] = Field(None, description="创建人")


class ClueUpdate(BaseModel):
    """更新问题线索的请求模型"""
    clue_number: Optional[str] = Field(None, description="线索编号")
    subject_name: Optional[str] = Field(None, description="被反映人姓名")
    subject_position: Optional[str] = Field(None, description="岗位职务")
    source: Optional[str] = Field(None, description="线索来源")
    content: Optional[str] = Field(None, description="线索内容")
    attachments: Optional[List[AttachmentInfo]] = Field(None, description="附件列表")
    updated_by: Optional[str] = Field(None, description="更新人")


class ClueResponse(BaseModel):
    """问题线索响应模型"""
    clue_id: str = Field(..., description="线索ID")
    project_id: str = Field(..., description="项目ID")
    clue_number: str = Field(..., description="线索编号")
    subject_name: str = Field(..., description="被反映人姓名")
    subject_position: Optional[str] = Field(None, description="岗位职务")
    source: Optional[str] = Field(None, description="线索来源")
    content: str = Field(..., description="线索内容")
    attachments: Optional[List[Dict[str, Any]]] = Field(default=[], description="附件列表")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    created_by: Optional[str] = Field(None, description="创建人")
    updated_by: Optional[str] = Field(None, description="更新人")

    class Config:
        from_attributes = True


class ClueListResponse(BaseModel):
    """问题线索列表响应模型"""
    clues: List[ClueResponse] = Field(..., description="线索列表")
    total: int = Field(..., description="总数")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页数量")


class ClueQueryParams(BaseModel):
    """问题线索查询参数"""
    project_id: str = Field(..., description="项目ID")
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(10, ge=1, le=100, description="每页数量")
    subject_name: Optional[str] = Field(None, description="被反映人姓名筛选")
    source: Optional[str] = Field(None, description="线索来源筛选")
    keyword: Optional[str] = Field(None, description="关键词搜索(在线索内容中)") 